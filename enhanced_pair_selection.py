#!/usr/bin/env python3
"""
增強的配對選擇邏輯 - 解決共線性問題
Enhanced Pair Selection Logic - Solve Collinearity Issues
"""

import numpy as np
from logging_config import get_logger

logger = get_logger(__name__)

class EnhancedPairSelector:
    """增強的配對選擇器"""
    
    def __init__(self, min_correlation: float = 0.7,
                 cointegration_pvalue_threshold: float = 0.05):
        self.min_correlation = min_correlation
        self.cointegration_pvalue_threshold = cointegration_pvalue_threshold

    def enhanced_pair_selection(self, prices1: np.ndarray, prices2: np.ndarray) -> bool:
        """
        增強的配對選擇邏輯，解決共線性問題

        Args:
            prices1: 第一個資產的價格序列
            prices2: 第二個資產的價格序列

        Returns:
            bool: 是否適合配對交易
        """
        try:
            # 1. 檢查基本相關性
            correlation = np.corrcoef(prices1, prices2)[0, 1]

            # 2. 檢查是否過度相關（共線性問題）
            if abs(correlation) > 0.995:
                logger.warning(f"配對過度相關，存在共線性風險: {correlation:.6f}")
                return False

            # 3. 檢查相關性是否足夠
            if abs(correlation) < self.min_correlation:
                logger.debug(f"配對相關性不足: {correlation:.3f}")
                return False

            # 4. 使用收益率序列進行協整檢驗（避免共線性）
            returns1 = np.diff(np.log(prices1))
            returns2 = np.diff(np.log(prices2))

            # 5. 檢查收益率序列的穩定性
            if len(returns1) < 30 or len(returns2) < 30:
                logger.warning("數據點不足，無法進行可靠的協整檢驗")
                return False

            # 6. 進行協整檢驗
            try:
                from statsmodels.tsa.stattools import coint
                _, p_value, _ = coint(prices1, prices2)

                if p_value > self.cointegration_pvalue_threshold:
                    logger.debug(f"協整檢驗失敗: p-value={p_value:.3f}")
                    return False

            except Exception as e:
                logger.warning(f"協整檢驗異常: {e}")
                return False

            # 7. 檢查價差的波動性
            spread = np.log(prices1) - np.log(prices2)
            spread_volatility = np.std(spread)

            if spread_volatility < 0.001:  # 價差波動性太小
                logger.warning(f"價差波動性過小: {spread_volatility:.6f}")
                return False

            # 8. 檢查價差的均值回歸特性
            # 使用 Augmented Dickey-Fuller 檢驗
            try:
                from statsmodels.tsa.stattools import adfuller
                adf_result = adfuller(spread)

                if adf_result[1] > 0.05:  # p-value > 0.05，非平穩
                    logger.debug(f"價差非平穩，不適合配對交易: ADF p-value={adf_result[1]:.3f}")
                    return False

            except Exception as e:
                logger.warning(f"ADF檢驗異常: {e}")

            logger.info(f"配對通過所有檢驗: 相關性={correlation:.3f}, 協整p值={p_value:.3f}")
            return True

        except Exception as e:
            logger.error(f"配對選擇檢驗失敗: {e}")
            return False

