{"description": "高槓桿配對交易配置 - 50倍槓桿全倉模式", "version": "3.0", "last_updated": "2025-06-30", "risk_level": "EXTREME", "exchange": {"name": "gate", "sandbox": true}, "trading_mode": "futures", "futures_type": "linear", "leverage": 50, "margin_mode": "cross", "trading_pairs": [["BTC/USDT:USDT", "ETH/USDT:USDT"]], "strategy": {"timeframe": "1m", "lookback_period": 30, "entry_threshold_high": 1.5, "entry_threshold_low": -1.5, "confirmation_threshold_high": 1.2, "confirmation_threshold_low": -1.2, "exit_threshold": 0.05, "cooldown_period": 5, "stop_loss_pct": 0.005, "take_profit_target": "zero_crossing"}, "capital_management": {"total_capital": 10000, "risk_level": "moderate", "max_utilization_rate": 0.15, "effective_leverage_target": 7.5, "max_single_position_pct": 5.0, "risk_buffer_pct": 3.0}, "position_sizing": {"use_smart_sizing": true, "position_size_usd": 500, "max_position_size": 0.05, "risk_per_trade": 0.01}, "risk_management": {"max_daily_trades": 20, "max_concurrent_positions": 5, "max_drawdown_pct": 0.01, "stop_loss_enabled": true, "take_profit_enabled": true, "funding_rate_threshold": 0.005, "margin_ratio_threshold": 0.95, "liquidation_buffer": 0.02, "emergency_close_threshold": 0.98}, "high_leverage_settings": {"auto_add_margin": true, "margin_call_threshold": 0.9, "force_close_threshold": 0.95, "position_monitoring_interval": 5, "price_change_alert": 0.005, "correlation_monitoring": true, "volatility_filter": true, "max_spread": 0.001}, "futures_specific": {"auto_add_margin": true, "reduce_only_orders": false, "time_in_force": "IOC", "position_side": "both", "hedge_mode": true}, "alerts": {"telegram": {"enabled": true, "authorized_users": [], "urgent_alerts": true}, "funding_rate_alerts": true, "margin_alerts": true, "liquidation_alerts": true, "price_alerts": true, "correlation_alerts": true, "volatility_alerts": true}, "monitoring": {"health_check_interval": 10, "memory_threshold_mb": 500, "cpu_threshold_pct": 80, "disk_threshold_pct": 85, "position_check_interval": 5, "margin_check_interval": 3, "price_monitoring_interval": 1}, "emergency_procedures": {"auto_emergency_close": true, "emergency_close_conditions": ["margin_ratio > 0.95", "correlation_breakdown > 0.5", "volatility_spike > 0.1"], "emergency_contact_enabled": true}, "performance": {"enable_caching": true, "cache_ttl_seconds": 60, "max_concurrent_requests": 15, "request_timeout_seconds": 10, "retry_attempts": 5}}