{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.8.0", "globals": "41c69aa8d94d60b769b42f8e51ea6d66", "files": {"z_145eef247bfb46b6_advanced_dynamic_allocation_py": {"hash": "6fb99e4c5437ca6917b1be4f5397d0c2", "index": {"url": "z_145eef247bfb46b6_advanced_dynamic_allocation_py.html", "file": "src/advanced_dynamic_allocation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 284, "n_excluded": 4, "n_missing": 284, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_alert_manager_py": {"hash": "daddd243075f396687f745cf792b806d", "index": {"url": "z_145eef247bfb46b6_alert_manager_py.html", "file": "src/alert_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 157, "n_excluded": 8, "n_missing": 55, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_async_data_handler_py": {"hash": "3cac16814f80a2da472192442278ea35", "index": {"url": "z_145eef247bfb46b6_async_data_handler_py.html", "file": "src/async_data_handler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 189, "n_excluded": 36, "n_missing": 156, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_async_optimization_py": {"hash": "5e0ced09ded699dc8e39505361166cdf", "index": {"url": "z_145eef247bfb46b6_async_optimization_py.html", "file": "src/async_optimization.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 217, "n_excluded": 2, "n_missing": 217, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_async_resource_manager_py": {"hash": "9bd18af511728efa94dc0c4aeff9565e", "index": {"url": "z_145eef247bfb46b6_async_resource_manager_py.html", "file": "src/async_resource_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 253, "n_excluded": 2, "n_missing": 178, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_async_state_persistence_py": {"hash": "fbcd6ca0706de06d722c38821f5d097f", "index": {"url": "z_145eef247bfb46b6_async_state_persistence_py.html", "file": "src/async_state_persistence.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 314, "n_excluded": 2, "n_missing": 220, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_backtesting_py": {"hash": "4d2e1e9ce98a03a35b6e3d887ddee84b", "index": {"url": "z_145eef247bfb46b6_backtesting_py.html", "file": "src/backtesting.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 250, "n_excluded": 0, "n_missing": 230, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_command_system_py": {"hash": "1d86527fb0fd4900dc1d52d6427f28f9", "index": {"url": "z_145eef247bfb46b6_command_system_py.html", "file": "src/command_system.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 217, "n_excluded": 10, "n_missing": 217, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_comprehensive_monitoring_py": {"hash": "e8d60f0b6df4acc1df43f06c607f0c8b", "index": {"url": "z_145eef247bfb46b6_comprehensive_monitoring_py.html", "file": "src/comprehensive_monitoring.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 236, "n_excluded": 2, "n_missing": 236, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_comprehensive_optimization_py": {"hash": "2c1df03ec7de727e7486b9c998c0bc67", "index": {"url": "z_145eef247bfb46b6_comprehensive_optimization_py.html", "file": "src/comprehensive_optimization.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 187, "n_excluded": 2, "n_missing": 187, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_config_loader_py": {"hash": "bb94ad7210cdad6b5af915e9c37843c0", "index": {"url": "z_145eef247bfb46b6_config_loader_py.html", "file": "src/config_loader.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 124, "n_excluded": 13, "n_missing": 100, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_config_manager_py": {"hash": "4bbc41ec04d6e75c906491c773e4810b", "index": {"url": "z_145eef247bfb46b6_config_manager_py.html", "file": "src/config_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 173, "n_excluded": 2, "n_missing": 173, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_config_validation_py": {"hash": "2e44e39c606a2ce982684c6d3e910382", "index": {"url": "z_145eef247bfb46b6_config_validation_py.html", "file": "src/config_validation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 314, "n_excluded": 15, "n_missing": 131, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_data_directory_manager_py": {"hash": "c3935c0e6bf845be1955810b999d670a", "index": {"url": "z_145eef247bfb46b6_data_directory_manager_py.html", "file": "src/data_directory_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 231, "n_excluded": 3, "n_missing": 169, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_data_handler_py": {"hash": "be02de811abfcf2c5ac5459e112e5625", "index": {"url": "z_145eef247bfb46b6_data_handler_py.html", "file": "src/data_handler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 191, "n_excluded": 0, "n_missing": 150, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_database_manager_py": {"hash": "1ed4366e69023e8c7d0cc477b80490f4", "index": {"url": "z_145eef247bfb46b6_database_manager_py.html", "file": "src/database_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 246, "n_excluded": 11, "n_missing": 212, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_debug_routes_py": {"hash": "cb32ea5f906f3807c7ba23bc5de07823", "index": {"url": "z_145eef247bfb46b6_debug_routes_py.html", "file": "src/debug_routes.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 39, "n_excluded": 3, "n_missing": 39, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_demo_multi_strategy_system_py": {"hash": "61bf62fe51254675084db0f75978f80c", "index": {"url": "z_145eef247bfb46b6_demo_multi_strategy_system_py.html", "file": "src/demo_multi_strategy_system.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 145, "n_excluded": 2, "n_missing": 145, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_demo_smart_leverage_py": {"hash": "735e6bff424b3788beaa3211abc10646", "index": {"url": "z_145eef247bfb46b6_demo_smart_leverage_py.html", "file": "src/demo_smart_leverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 111, "n_excluded": 2, "n_missing": 111, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_dynamic_allocation_engine_py": {"hash": "45054f5cff4e619b1b0feb594181f2f1", "index": {"url": "z_145eef247bfb46b6_dynamic_allocation_engine_py.html", "file": "src/dynamic_allocation_engine.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 207, "n_excluded": 3, "n_missing": 207, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_dynamic_config_py": {"hash": "47d1583a2424d1013229b73bfb1ea6e3", "index": {"url": "z_145eef247bfb46b6_dynamic_config_py.html", "file": "src/dynamic_config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 177, "n_excluded": 14, "n_missing": 97, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_enhanced_event_integration_py": {"hash": "7eeb586d256e886ee115813434bc3d36", "index": {"url": "z_145eef247bfb46b6_enhanced_event_integration_py.html", "file": "src/enhanced_event_integration.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 112, "n_excluded": 23, "n_missing": 112, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_enhanced_portfolio_demo_py": {"hash": "6e1477888ae0858124617adcb3762339", "index": {"url": "z_145eef247bfb46b6_enhanced_portfolio_demo_py.html", "file": "src/enhanced_portfolio_demo.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 150, "n_excluded": 2, "n_missing": 150, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_enhanced_retry_handler_py": {"hash": "b053d07be3bf259856228fc2a98e2c22", "index": {"url": "z_145eef247bfb46b6_enhanced_retry_handler_py.html", "file": "src/enhanced_retry_handler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 316, "n_excluded": 26, "n_missing": 217, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_event_driven_core_py": {"hash": "0b8da2d066990d2727f31c984f65ffab", "index": {"url": "z_145eef247bfb46b6_event_driven_core_py.html", "file": "src/event_driven_core.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 213, "n_excluded": 9, "n_missing": 213, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_event_publisher_decorators_py": {"hash": "1b99fd4f64a02866d7d507be978cc222", "index": {"url": "z_145eef247bfb46b6_event_publisher_decorators_py.html", "file": "src/event_publisher_decorators.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 40, "n_excluded": 0, "n_missing": 40, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_example_usage_py": {"hash": "118519224f5d6ee787ef9ac4a6720ac4", "index": {"url": "z_145eef247bfb46b6_example_usage_py.html", "file": "src/example_usage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 148, "n_excluded": 8, "n_missing": 148, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_exception_system_py": {"hash": "c189c536ed807eb850e194c5bcdbddb9", "index": {"url": "z_145eef247bfb46b6_exception_system_py.html", "file": "src/exception_system.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 182, "n_excluded": 8, "n_missing": 182, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_exchange_factory_py": {"hash": "7f80e356e400cfd955897f8e6b5fd9bf", "index": {"url": "z_145eef247bfb46b6_exchange_factory_py.html", "file": "src/exchange_factory.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 102, "n_excluded": 27, "n_missing": 77, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_final_optimization_validation_py": {"hash": "a7f32e6a317229881c55caa4548968e9", "index": {"url": "z_145eef247bfb46b6_final_optimization_validation_py.html", "file": "src/final_optimization_validation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 215, "n_excluded": 2, "n_missing": 215, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_final_system_validation_py": {"hash": "b3756ec7c8fd337118b1c8a172f819ba", "index": {"url": "z_145eef247bfb46b6_final_system_validation_py.html", "file": "src/final_system_validation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 151, "n_excluded": 2, "n_missing": 151, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_fix_critical_issues_py": {"hash": "4124c3f96f8c2e7c6832670dad7962f2", "index": {"url": "z_145eef247bfb46b6_fix_critical_issues_py.html", "file": "src/fix_critical_issues.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 95, "n_excluded": 2, "n_missing": 95, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_fix_resource_management_py": {"hash": "b9e5fad48cb1e762eec0b531e506b066", "index": {"url": "z_145eef247bfb46b6_fix_resource_management_py.html", "file": "src/fix_resource_management.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 127, "n_excluded": 3, "n_missing": 127, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_global_event_bus_py": {"hash": "da76a1cc80f68b146c5a773cc9595b2e", "index": {"url": "z_145eef247bfb46b6_global_event_bus_py.html", "file": "src/global_event_bus.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 163, "n_excluded": 36, "n_missing": 63, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_graceful_shutdown_py": {"hash": "56468814e8e951b76b3802cf4267e7d4", "index": {"url": "z_145eef247bfb46b6_graceful_shutdown_py.html", "file": "src/graceful_shutdown.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 153, "n_excluded": 14, "n_missing": 105, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_health_checker_py": {"hash": "2901aa6d711fc06336f9b935c9aab88f", "index": {"url": "z_145eef247bfb46b6_health_checker_py.html", "file": "src/health_checker.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 208, "n_excluded": 6, "n_missing": 208, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_health_server_py": {"hash": "94f5a1aaa6dca1dc7bdc327f6698335c", "index": {"url": "z_145eef247bfb46b6_health_server_py.html", "file": "src/health_server.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 194, "n_excluded": 15, "n_missing": 69, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_high_leverage_risk_checker_py": {"hash": "72938932ef163df25d6d9da77818c323", "index": {"url": "z_145eef247bfb46b6_high_leverage_risk_checker_py.html", "file": "src/high_leverage_risk_checker.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 117, "n_excluded": 2, "n_missing": 94, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_improved_backtest_py": {"hash": "3183f6811b2e2925d15fca0070ed0009", "index": {"url": "z_145eef247bfb46b6_improved_backtest_py.html", "file": "src/improved_backtest.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 161, "n_excluded": 2, "n_missing": 161, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_integrated_trading_executor_py": {"hash": "90380c1bca408b6cbd269f42be5b1190", "index": {"url": "z_145eef247bfb46b6_integrated_trading_executor_py.html", "file": "src/integrated_trading_executor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 312, "n_excluded": 2, "n_missing": 208, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_intelligent_cache_manager_py": {"hash": "737b3aac075f70f975b7546be7e3b595", "index": {"url": "z_145eef247bfb46b6_intelligent_cache_manager_py.html", "file": "src/intelligent_cache_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 321, "n_excluded": 2, "n_missing": 252, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_intelligent_portfolio_system_py": {"hash": "5fa1b0534510af8db33921c1369eddf4", "index": {"url": "z_145eef247bfb46b6_intelligent_portfolio_system_py.html", "file": "src/intelligent_portfolio_system.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 186, "n_excluded": 2, "n_missing": 186, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_logging_config_py": {"hash": "ede2232fe9c7efd88c68bfad29d817bf", "index": {"url": "z_145eef247bfb46b6_logging_config_py.html", "file": "src/logging_config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 40, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_math_utils_py": {"hash": "157052754fc50b9a9b74a410240e655f", "index": {"url": "z_145eef247bfb46b6_math_utils_py.html", "file": "src/math_utils.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 165, "n_excluded": 15, "n_missing": 134, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_multi_executor_manager_py": {"hash": "255d404e3cae38f7874e09623dcfa154", "index": {"url": "z_145eef247bfb46b6_multi_executor_manager_py.html", "file": "src/multi_executor_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 295, "n_excluded": 2, "n_missing": 194, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_multi_strategy_engine_py": {"hash": "992080e4da0fe293efdd149d7e5e8808", "index": {"url": "z_145eef247bfb46b6_multi_strategy_engine_py.html", "file": "src/multi_strategy_engine.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 220, "n_excluded": 19, "n_missing": 220, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_pair_trading_bot_py": {"hash": "d921b28f71f3d0f769f7fbaa22d6be4f", "index": {"url": "z_145eef247bfb46b6_pair_trading_bot_py.html", "file": "src/pair_trading_bot.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 240, "n_excluded": 0, "n_missing": 111, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_pairs_trading_strategy_py": {"hash": "a4e6236722f9884e6db1ee794352c197", "index": {"url": "z_145eef247bfb46b6_pairs_trading_strategy_py.html", "file": "src/pairs_trading_strategy.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 159, "n_excluded": 16, "n_missing": 159, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_perfect_validation_py": {"hash": "93b1c1fb59a64a8dbece3ff43d5011d8", "index": {"url": "z_145eef247bfb46b6_perfect_validation_py.html", "file": "src/perfect_validation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 74, "n_excluded": 2, "n_missing": 74, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_portfolio_backtester_py": {"hash": "d86096c8530e6dab9a1e70d4b2280344", "index": {"url": "z_145eef247bfb46b6_portfolio_backtester_py.html", "file": "src/portfolio_backtester.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 211, "n_excluded": 15, "n_missing": 185, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_portfolio_manager_py": {"hash": "6292fd8f3d5cee32464f78618f2c9422", "index": {"url": "z_145eef247bfb46b6_portfolio_manager_py.html", "file": "src/portfolio_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 196, "n_excluded": 25, "n_missing": 82, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_production_readiness_check_py": {"hash": "51844dc16660525677f50ecf2be34bf2", "index": {"url": "z_145eef247bfb46b6_production_readiness_check_py.html", "file": "src/production_readiness_check.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 159, "n_excluded": 2, "n_missing": 159, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_quick_fix_final_issues_py": {"hash": "3c3cc8412deb51ee0b0801c3bb7ddef2", "index": {"url": "z_145eef247bfb46b6_quick_fix_final_issues_py.html", "file": "src/quick_fix_final_issues.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 30, "n_excluded": 3, "n_missing": 30, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_quick_start_production_py": {"hash": "fd11215e05ddb061c9a5d569c40f680c", "index": {"url": "z_145eef247bfb46b6_quick_start_production_py.html", "file": "src/quick_start_production.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 159, "n_excluded": 8, "n_missing": 159, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_resource_manager_py": {"hash": "c8337bff7394719c2536d2c7e922046f", "index": {"url": "z_145eef247bfb46b6_resource_manager_py.html", "file": "src/resource_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 73, "n_excluded": 12, "n_missing": 73, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_retry_handler_py": {"hash": "397ddcaf9769f0d76da2e2a5b6a8d326", "index": {"url": "z_145eef247bfb46b6_retry_handler_py.html", "file": "src/retry_handler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 89, "n_excluded": 31, "n_missing": 89, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_security_manager_py": {"hash": "8105f72d0da4ad1b5d1f524027ab8011", "index": {"url": "z_145eef247bfb46b6_security_manager_py.html", "file": "src/security_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 195, "n_excluded": 14, "n_missing": 167, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_simplified_optimization_validation_py": {"hash": "f049fa05b8487a24b7d5d0c4cbf82a80", "index": {"url": "z_145eef247bfb46b6_simplified_optimization_validation_py.html", "file": "src/simplified_optimization_validation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 196, "n_excluded": 2, "n_missing": 196, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_smart_capital_management_py": {"hash": "db9b9610a2eaadab72f5f34863700b4d", "index": {"url": "z_145eef247bfb46b6_smart_capital_management_py.html", "file": "src/smart_capital_management.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 85, "n_excluded": 19, "n_missing": 56, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_strategy_config_models_py": {"hash": "d5efdef83f46c2a30b8de9ac1593f074", "index": {"url": "z_145eef247bfb46b6_strategy_config_models_py.html", "file": "src/strategy_config_models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 169, "n_excluded": 22, "n_missing": 52, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_strategy_framework_py": {"hash": "866ec8b0f89878356f3e583cd738c6aa", "index": {"url": "z_145eef247bfb46b6_strategy_framework_py.html", "file": "src/strategy_framework.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 147, "n_excluded": 53, "n_missing": 75, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_strategy_health_monitor_py": {"hash": "1b227e60b469a1e93c7513a7682ed66a", "index": {"url": "z_145eef247bfb46b6_strategy_health_monitor_py.html", "file": "src/strategy_health_monitor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 198, "n_excluded": 11, "n_missing": 143, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_telegram_bot_py": {"hash": "69f5b5353e60ec94936f0fd8557edd2d", "index": {"url": "z_145eef247bfb46b6_telegram_bot_py.html", "file": "src/telegram_bot.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 230, "n_excluded": 15, "n_missing": 230, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_trading_exceptions_py": {"hash": "afb43a49eb41c877aef9be4e1817e688", "index": {"url": "z_145eef247bfb46b6_trading_exceptions_py.html", "file": "src/trading_exceptions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 26, "n_excluded": 0, "n_missing": 8, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_trading_safety_guard_py": {"hash": "fd02fa183eefbd7072c005b42e475177", "index": {"url": "z_145eef247bfb46b6_trading_safety_guard_py.html", "file": "src/trading_safety_guard.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 142, "n_excluded": 20, "n_missing": 106, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_trend_following_strategy_py": {"hash": "a0f8a811ead54569f10486ef4da658ea", "index": {"url": "z_145eef247bfb46b6_trend_following_strategy_py.html", "file": "src/trend_following_strategy.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 192, "n_excluded": 19, "n_missing": 192, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_ultimate_system_validation_py": {"hash": "752e733ca4a0f1bac517c79ebea1e0f9", "index": {"url": "z_145eef247bfb46b6_ultimate_system_validation_py.html", "file": "src/ultimate_system_validation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 195, "n_excluded": 2, "n_missing": 195, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_unified_client_manager_py": {"hash": "a4f0c5b7fc765424ece5ee0e595e9924", "index": {"url": "z_145eef247bfb46b6_unified_client_manager_py.html", "file": "src/unified_client_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 152, "n_excluded": 2, "n_missing": 101, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_unified_config_manager_py": {"hash": "0136c2f33674c9a555b7158f5a46b5b2", "index": {"url": "z_145eef247bfb46b6_unified_config_manager_py.html", "file": "src/unified_config_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 222, "n_excluded": 3, "n_missing": 104, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_unified_pair_selector_py": {"hash": "81c5fca0000ae7833176cf193567406f", "index": {"url": "z_145eef247bfb46b6_unified_pair_selector_py.html", "file": "src/unified_pair_selector.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 257, "n_excluded": 3, "n_missing": 173, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_unified_portfolio_manager_py": {"hash": "c04634b3259d0a22f080dc8c30a8393f", "index": {"url": "z_145eef247bfb46b6_unified_portfolio_manager_py.html", "file": "src/unified_portfolio_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 273, "n_excluded": 22, "n_missing": 88, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_utils_py": {"hash": "1663bd449febd0605cf4a3a899a9b1c4", "index": {"url": "z_145eef247bfb46b6_utils_py.html", "file": "src/utils.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 134, "n_excluded": 0, "n_missing": 58, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_validate_improvements_py": {"hash": "252cb9ab0a3e8c460c37bcdff25ef17c", "index": {"url": "z_145eef247bfb46b6_validate_improvements_py.html", "file": "src/validate_improvements.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 201, "n_excluded": 3, "n_missing": 201, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}