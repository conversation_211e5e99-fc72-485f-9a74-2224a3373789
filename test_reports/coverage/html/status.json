{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.8.0", "globals": "c9eb8faf8afc16b5f290583209932bd6", "files": {"z_98bac5cf84189e43_event_bus_py": {"hash": "23ab3be6824a75286e0d2270ae31df59", "index": {"url": "z_98bac5cf84189e43_event_bus_py.html", "file": "event_system/event_bus.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 206, "n_excluded": 37, "n_missing": 154, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ba0086a4fb26a981_alpha_factors_py": {"hash": "0f656ce8c1041b302c509add1e8cc22c", "index": {"url": "z_ba0086a4fb26a981_alpha_factors_py.html", "file": "factor_factory/alpha_factors.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 253, "n_excluded": 18, "n_missing": 213, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "main_py": {"hash": "63e36b0badb4607dc23811f1aaf11902", "index": {"url": "main_py.html", "file": "main.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 159, "n_excluded": 2, "n_missing": 159, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4c6e76699e82afdf_feature_engineering_py": {"hash": "4ba3cb6d616d580f24d377c0fe9b4517", "index": {"url": "z_4c6e76699e82afdf_feature_engineering_py.html", "file": "ml_predictor/feature_engineering.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 260, "n_excluded": 11, "n_missing": 232, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4c6e76699e82afdf_predictor_py": {"hash": "6828a9a728319e175462dc2c5380fe38", "index": {"url": "z_4c6e76699e82afdf_predictor_py.html", "file": "ml_predictor/predictor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 242, "n_excluded": 18, "n_missing": 217, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5a6f50894c2f9185_memory_optimizer_py": {"hash": "d686763c7fcae9a47c3c6673f5ac4a6e", "index": {"url": "z_5a6f50894c2f9185_memory_optimizer_py.html", "file": "performance_optimization/memory_optimizer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 225, "n_excluded": 14, "n_missing": 174, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5a6f50894c2f9185_vectorized_calculations_py": {"hash": "52a333e9281bb4d846cc05fa2e1f4576", "index": {"url": "z_5a6f50894c2f9185_vectorized_calculations_py.html", "file": "performance_optimization/vectorized_calculations.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 283, "n_excluded": 18, "n_missing": 229, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ac1802d461d41d87_research_toolkit_py": {"hash": "7be059a4751a60dfe4b03979dad02b1d", "index": {"url": "z_ac1802d461d41d87_research_toolkit_py.html", "file": "research_environment/research_toolkit.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 284, "n_excluded": 16, "n_missing": 273, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bd6a586f8a803a04_advanced_risk_manager_py": {"hash": "73796c544c6ff321d1f52b14467b1c6b", "index": {"url": "z_bd6a586f8a803a04_advanced_risk_manager_py.html", "file": "risk_management/advanced_risk_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 267, "n_excluded": 13, "n_missing": 238, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_alert_manager_py": {"hash": "3189f57cc400f77d536149d5e527abe7", "index": {"url": "z_145eef247bfb46b6_alert_manager_py.html", "file": "src/alert_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 157, "n_excluded": 8, "n_missing": 125, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_async_data_handler_py": {"hash": "3cac16814f80a2da472192442278ea35", "index": {"url": "z_145eef247bfb46b6_async_data_handler_py.html", "file": "src/async_data_handler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 189, "n_excluded": 36, "n_missing": 156, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_async_resource_manager_py": {"hash": "556026afa88f514277b3074048a78165", "index": {"url": "z_145eef247bfb46b6_async_resource_manager_py.html", "file": "src/async_resource_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 253, "n_excluded": 2, "n_missing": 204, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_async_state_persistence_py": {"hash": "9238d6b60c05aa8a07f3980e4cedd6c7", "index": {"url": "z_145eef247bfb46b6_async_state_persistence_py.html", "file": "src/async_state_persistence.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 314, "n_excluded": 2, "n_missing": 242, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_backtesting_py": {"hash": "4d2e1e9ce98a03a35b6e3d887ddee84b", "index": {"url": "z_145eef247bfb46b6_backtesting_py.html", "file": "src/backtesting.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 250, "n_excluded": 0, "n_missing": 230, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_config_loader_py": {"hash": "bb94ad7210cdad6b5af915e9c37843c0", "index": {"url": "z_145eef247bfb46b6_config_loader_py.html", "file": "src/config_loader.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 124, "n_excluded": 13, "n_missing": 100, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_config_validation_py": {"hash": "53db94edc3360ebb4611fb1ae228dd7d", "index": {"url": "z_145eef247bfb46b6_config_validation_py.html", "file": "src/config_validation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 298, "n_excluded": 15, "n_missing": 137, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_data_handler_py": {"hash": "9bae57b2bd670b45f996b7f0a8dfc84a", "index": {"url": "z_145eef247bfb46b6_data_handler_py.html", "file": "src/data_handler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 191, "n_excluded": 0, "n_missing": 162, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_database_manager_py": {"hash": "1ed4366e69023e8c7d0cc477b80490f4", "index": {"url": "z_145eef247bfb46b6_database_manager_py.html", "file": "src/database_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 246, "n_excluded": 11, "n_missing": 212, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_dynamic_config_py": {"hash": "3a01180ab6df6d5b81d7d39247dbd906", "index": {"url": "z_145eef247bfb46b6_dynamic_config_py.html", "file": "src/dynamic_config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 177, "n_excluded": 14, "n_missing": 145, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_enhanced_retry_handler_py": {"hash": "b053d07be3bf259856228fc2a98e2c22", "index": {"url": "z_145eef247bfb46b6_enhanced_retry_handler_py.html", "file": "src/enhanced_retry_handler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 316, "n_excluded": 26, "n_missing": 217, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_exchange_factory_py": {"hash": "7f80e356e400cfd955897f8e6b5fd9bf", "index": {"url": "z_145eef247bfb46b6_exchange_factory_py.html", "file": "src/exchange_factory.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 102, "n_excluded": 27, "n_missing": 77, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_global_event_bus_py": {"hash": "ca91b0ae362bbc4a59e806e905f1debb", "index": {"url": "z_145eef247bfb46b6_global_event_bus_py.html", "file": "src/global_event_bus.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 163, "n_excluded": 36, "n_missing": 99, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_graceful_shutdown_py": {"hash": "47b45b301baadd23a8b07795d3320a3f", "index": {"url": "z_145eef247bfb46b6_graceful_shutdown_py.html", "file": "src/graceful_shutdown.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 153, "n_excluded": 14, "n_missing": 122, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_health_server_py": {"hash": "6cc2dbd693eb7f9e6baacb4a24c1fd3a", "index": {"url": "z_145eef247bfb46b6_health_server_py.html", "file": "src/health_server.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 194, "n_excluded": 15, "n_missing": 169, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_high_leverage_risk_checker_py": {"hash": "28c6a5f57f8ff2a2c8c205d14eb9e55d", "index": {"url": "z_145eef247bfb46b6_high_leverage_risk_checker_py.html", "file": "src/high_leverage_risk_checker.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 117, "n_excluded": 2, "n_missing": 97, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_integrated_trading_executor_py": {"hash": "3ffc155c7abb71d0cb6c81f92ff108f7", "index": {"url": "z_145eef247bfb46b6_integrated_trading_executor_py.html", "file": "src/integrated_trading_executor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 265, "n_excluded": 2, "n_missing": 208, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_intelligent_cache_manager_py": {"hash": "737b3aac075f70f975b7546be7e3b595", "index": {"url": "z_145eef247bfb46b6_intelligent_cache_manager_py.html", "file": "src/intelligent_cache_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 321, "n_excluded": 2, "n_missing": 252, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_logging_config_py": {"hash": "ede2232fe9c7efd88c68bfad29d817bf", "index": {"url": "z_145eef247bfb46b6_logging_config_py.html", "file": "src/logging_config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 40, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_multi_executor_manager_py": {"hash": "5d25bb71deae77079747bc58d0147e55", "index": {"url": "z_145eef247bfb46b6_multi_executor_manager_py.html", "file": "src/multi_executor_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 295, "n_excluded": 2, "n_missing": 225, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_pair_trading_bot_py": {"hash": "401ece5cb57f68e19aab58bf5141e887", "index": {"url": "z_145eef247bfb46b6_pair_trading_bot_py.html", "file": "src/pair_trading_bot.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 240, "n_excluded": 0, "n_missing": 234, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_portfolio_backtester_py": {"hash": "841e187f672c7e3bf18685477b4b1ada", "index": {"url": "z_145eef247bfb46b6_portfolio_backtester_py.html", "file": "src/portfolio_backtester.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 211, "n_excluded": 15, "n_missing": 202, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_portfolio_manager_py": {"hash": "4c47cb07b24ce13aa7a75e0dd041f237", "index": {"url": "z_145eef247bfb46b6_portfolio_manager_py.html", "file": "src/portfolio_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 196, "n_excluded": 25, "n_missing": 153, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_strategy_config_models_py": {"hash": "a5d9fc1bcddff3c53a707d9731590701", "index": {"url": "z_145eef247bfb46b6_strategy_config_models_py.html", "file": "src/strategy_config_models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 169, "n_excluded": 22, "n_missing": 60, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_strategy_framework_py": {"hash": "866ec8b0f89878356f3e583cd738c6aa", "index": {"url": "z_145eef247bfb46b6_strategy_framework_py.html", "file": "src/strategy_framework.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 147, "n_excluded": 53, "n_missing": 75, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_strategy_health_monitor_py": {"hash": "052cd8824bc845bb01d8a53e927d3703", "index": {"url": "z_145eef247bfb46b6_strategy_health_monitor_py.html", "file": "src/strategy_health_monitor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 198, "n_excluded": 11, "n_missing": 165, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_trading_exceptions_py": {"hash": "afb43a49eb41c877aef9be4e1817e688", "index": {"url": "z_145eef247bfb46b6_trading_exceptions_py.html", "file": "src/trading_exceptions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 26, "n_excluded": 0, "n_missing": 8, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_trading_safety_guard_py": {"hash": "668a445dbbb2ca1c092e31a109b917a7", "index": {"url": "z_145eef247bfb46b6_trading_safety_guard_py.html", "file": "src/trading_safety_guard.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 142, "n_excluded": 20, "n_missing": 113, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_unified_client_manager_py": {"hash": "1c8d4bc5dbce298114104d9c7c03700d", "index": {"url": "z_145eef247bfb46b6_unified_client_manager_py.html", "file": "src/unified_client_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 152, "n_excluded": 2, "n_missing": 117, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_unified_portfolio_manager_py": {"hash": "951e0b9e322c990762716880c9baf084", "index": {"url": "z_145eef247bfb46b6_unified_portfolio_manager_py.html", "file": "src/unified_portfolio_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 273, "n_excluded": 22, "n_missing": 232, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_145eef247bfb46b6_utils_py": {"hash": "e8e3cb2bea9bcdd491dce8d122bdb5b7", "index": {"url": "z_145eef247bfb46b6_utils_py.html", "file": "src/utils.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 134, "n_excluded": 0, "n_missing": 113, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_d58350e995d6b7cc_state_manager_py": {"hash": "d7f992597cdd985beb6110225bbc1569", "index": {"url": "z_d58350e995d6b7cc_state_manager_py.html", "file": "state_management/state_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 274, "n_excluded": 14, "n_missing": 217, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "unified_test_runner_py": {"hash": "ba5b413be702098180d4313a8b2fa13d", "index": {"url": "unified_test_runner_py.html", "file": "unified_test_runner.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 221, "n_excluded": 3, "n_missing": 221, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}