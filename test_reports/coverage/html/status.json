{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.8.0", "globals": "682022e9fab5fbcc990a6b25bb61699a", "files": {"advanced_dynamic_allocation_py": {"hash": "6fb99e4c5437ca6917b1be4f5397d0c2", "index": {"url": "advanced_dynamic_allocation_py.html", "file": "advanced_dynamic_allocation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 284, "n_excluded": 4, "n_missing": 284, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "alert_manager_py": {"hash": "d3e1e4dddd2486bb8883acc35cfa15d4", "index": {"url": "alert_manager_py.html", "file": "alert_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 157, "n_excluded": 8, "n_missing": 157, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "async_data_handler_py": {"hash": "667898d8fdcfa6b88d5cf7921b40f7cf", "index": {"url": "async_data_handler_py.html", "file": "async_data_handler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 189, "n_excluded": 36, "n_missing": 189, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "async_optimization_py": {"hash": "5e0ced09ded699dc8e39505361166cdf", "index": {"url": "async_optimization_py.html", "file": "async_optimization.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 217, "n_excluded": 2, "n_missing": 217, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "async_resource_manager_py": {"hash": "556026afa88f514277b3074048a78165", "index": {"url": "async_resource_manager_py.html", "file": "async_resource_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 253, "n_excluded": 2, "n_missing": 204, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "async_state_persistence_py": {"hash": "9238d6b60c05aa8a07f3980e4cedd6c7", "index": {"url": "async_state_persistence_py.html", "file": "async_state_persistence.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 314, "n_excluded": 2, "n_missing": 242, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "backtesting_py": {"hash": "9c3870c31716eddb41afeda11ca08a4c", "index": {"url": "backtesting_py.html", "file": "backtesting.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 250, "n_excluded": 0, "n_missing": 250, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "command_system_py": {"hash": "1d86527fb0fd4900dc1d52d6427f28f9", "index": {"url": "command_system_py.html", "file": "command_system.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 217, "n_excluded": 10, "n_missing": 217, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "comprehensive_monitoring_py": {"hash": "e8d60f0b6df4acc1df43f06c607f0c8b", "index": {"url": "comprehensive_monitoring_py.html", "file": "comprehensive_monitoring.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 236, "n_excluded": 2, "n_missing": 236, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "comprehensive_optimization_py": {"hash": "2c1df03ec7de727e7486b9c998c0bc67", "index": {"url": "comprehensive_optimization_py.html", "file": "comprehensive_optimization.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 187, "n_excluded": 2, "n_missing": 187, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "config_loader_py": {"hash": "3d407a82d2106e8c5a280b630cb97b79", "index": {"url": "config_loader_py.html", "file": "config_loader.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 124, "n_excluded": 13, "n_missing": 124, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "config_manager_py": {"hash": "4bbc41ec04d6e75c906491c773e4810b", "index": {"url": "config_manager_py.html", "file": "config_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 173, "n_excluded": 2, "n_missing": 173, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "config_validation_py": {"hash": "d7a60ed81d407245b20414a175907cd7", "index": {"url": "config_validation_py.html", "file": "config_validation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 298, "n_excluded": 15, "n_missing": 298, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "data_directory_manager_py": {"hash": "fdd27aa43b2181f23cdb683d4103c167", "index": {"url": "data_directory_manager_py.html", "file": "data_directory_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 231, "n_excluded": 3, "n_missing": 231, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "data_handler_py": {"hash": "cdce9b873b6e38721d1c36da52dec8ee", "index": {"url": "data_handler_py.html", "file": "data_handler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 191, "n_excluded": 0, "n_missing": 191, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "database_manager_py": {"hash": "9b691e9ac43fd3e44e1271e214df5eb0", "index": {"url": "database_manager_py.html", "file": "database_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 246, "n_excluded": 11, "n_missing": 246, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "debug_routes_py": {"hash": "cb32ea5f906f3807c7ba23bc5de07823", "index": {"url": "debug_routes_py.html", "file": "debug_routes.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 39, "n_excluded": 3, "n_missing": 39, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "demo_multi_strategy_system_py": {"hash": "61bf62fe51254675084db0f75978f80c", "index": {"url": "demo_multi_strategy_system_py.html", "file": "demo_multi_strategy_system.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 145, "n_excluded": 2, "n_missing": 145, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "demo_smart_leverage_py": {"hash": "735e6bff424b3788beaa3211abc10646", "index": {"url": "demo_smart_leverage_py.html", "file": "demo_smart_leverage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 111, "n_excluded": 2, "n_missing": 111, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "dynamic_allocation_engine_py": {"hash": "45054f5cff4e619b1b0feb594181f2f1", "index": {"url": "dynamic_allocation_engine_py.html", "file": "dynamic_allocation_engine.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 207, "n_excluded": 3, "n_missing": 207, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "dynamic_config_py": {"hash": "d7bfdd17c8d4b933ffc3d9d95c3ce371", "index": {"url": "dynamic_config_py.html", "file": "dynamic_config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 177, "n_excluded": 14, "n_missing": 177, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "enhanced_event_integration_py": {"hash": "7eeb586d256e886ee115813434bc3d36", "index": {"url": "enhanced_event_integration_py.html", "file": "enhanced_event_integration.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 112, "n_excluded": 23, "n_missing": 112, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "enhanced_portfolio_demo_py": {"hash": "6e1477888ae0858124617adcb3762339", "index": {"url": "enhanced_portfolio_demo_py.html", "file": "enhanced_portfolio_demo.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 150, "n_excluded": 2, "n_missing": 150, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "enhanced_retry_handler_py": {"hash": "c73bde645047cfa3aa9bfa953f9b8604", "index": {"url": "enhanced_retry_handler_py.html", "file": "enhanced_retry_handler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 316, "n_excluded": 26, "n_missing": 316, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "event_driven_core_py": {"hash": "0b8da2d066990d2727f31c984f65ffab", "index": {"url": "event_driven_core_py.html", "file": "event_driven_core.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 213, "n_excluded": 9, "n_missing": 213, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "event_publisher_decorators_py": {"hash": "1b99fd4f64a02866d7d507be978cc222", "index": {"url": "event_publisher_decorators_py.html", "file": "event_publisher_decorators.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 40, "n_excluded": 0, "n_missing": 40, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "example_usage_py": {"hash": "118519224f5d6ee787ef9ac4a6720ac4", "index": {"url": "example_usage_py.html", "file": "example_usage.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 148, "n_excluded": 8, "n_missing": 148, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "exception_system_py": {"hash": "c189c536ed807eb850e194c5bcdbddb9", "index": {"url": "exception_system_py.html", "file": "exception_system.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 182, "n_excluded": 8, "n_missing": 182, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "exchange_factory_py": {"hash": "87771544547673ef5ed7fffe7658a672", "index": {"url": "exchange_factory_py.html", "file": "exchange_factory.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 102, "n_excluded": 27, "n_missing": 102, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "final_optimization_validation_py": {"hash": "a7f32e6a317229881c55caa4548968e9", "index": {"url": "final_optimization_validation_py.html", "file": "final_optimization_validation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 215, "n_excluded": 2, "n_missing": 215, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "final_system_validation_py": {"hash": "b3756ec7c8fd337118b1c8a172f819ba", "index": {"url": "final_system_validation_py.html", "file": "final_system_validation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 151, "n_excluded": 2, "n_missing": 151, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "fix_critical_issues_py": {"hash": "4124c3f96f8c2e7c6832670dad7962f2", "index": {"url": "fix_critical_issues_py.html", "file": "fix_critical_issues.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 95, "n_excluded": 2, "n_missing": 95, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "fix_resource_management_py": {"hash": "b9e5fad48cb1e762eec0b531e506b066", "index": {"url": "fix_resource_management_py.html", "file": "fix_resource_management.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 127, "n_excluded": 3, "n_missing": 127, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "global_event_bus_py": {"hash": "ca91b0ae362bbc4a59e806e905f1debb", "index": {"url": "global_event_bus_py.html", "file": "global_event_bus.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 163, "n_excluded": 36, "n_missing": 99, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "graceful_shutdown_py": {"hash": "47b45b301baadd23a8b07795d3320a3f", "index": {"url": "graceful_shutdown_py.html", "file": "graceful_shutdown.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 153, "n_excluded": 14, "n_missing": 122, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "health_checker_py": {"hash": "2901aa6d711fc06336f9b935c9aab88f", "index": {"url": "health_checker_py.html", "file": "health_checker.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 208, "n_excluded": 6, "n_missing": 208, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "health_server_py": {"hash": "593a609d0b7f4b61b270f4ef764898a4", "index": {"url": "health_server_py.html", "file": "health_server.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 194, "n_excluded": 15, "n_missing": 194, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "high_leverage_risk_checker_py": {"hash": "ec2f1500a8613f1707d647661177f26d", "index": {"url": "high_leverage_risk_checker_py.html", "file": "high_leverage_risk_checker.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 117, "n_excluded": 2, "n_missing": 117, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "improved_backtest_py": {"hash": "3183f6811b2e2925d15fca0070ed0009", "index": {"url": "improved_backtest_py.html", "file": "improved_backtest.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 161, "n_excluded": 2, "n_missing": 161, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "integrated_trading_executor_py": {"hash": "56af0f6d06e74700593bbadbc339bd58", "index": {"url": "integrated_trading_executor_py.html", "file": "integrated_trading_executor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 265, "n_excluded": 2, "n_missing": 265, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "intelligent_cache_manager_py": {"hash": "737b3aac075f70f975b7546be7e3b595", "index": {"url": "intelligent_cache_manager_py.html", "file": "intelligent_cache_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 321, "n_excluded": 2, "n_missing": 252, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "intelligent_portfolio_system_py": {"hash": "5fa1b0534510af8db33921c1369eddf4", "index": {"url": "intelligent_portfolio_system_py.html", "file": "intelligent_portfolio_system.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 186, "n_excluded": 2, "n_missing": 186, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "logging_config_py": {"hash": "ede2232fe9c7efd88c68bfad29d817bf", "index": {"url": "logging_config_py.html", "file": "logging_config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 40, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "main_py": {"hash": "63e36b0badb4607dc23811f1aaf11902", "index": {"url": "main_py.html", "file": "main.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 159, "n_excluded": 2, "n_missing": 159, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "math_utils_py": {"hash": "0a11e54a5f8e97a3dedca128303b102c", "index": {"url": "math_utils_py.html", "file": "math_utils.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 165, "n_excluded": 15, "n_missing": 165, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "multi_executor_manager_py": {"hash": "5d25bb71deae77079747bc58d0147e55", "index": {"url": "multi_executor_manager_py.html", "file": "multi_executor_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 295, "n_excluded": 2, "n_missing": 225, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "multi_strategy_engine_py": {"hash": "992080e4da0fe293efdd149d7e5e8808", "index": {"url": "multi_strategy_engine_py.html", "file": "multi_strategy_engine.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 220, "n_excluded": 19, "n_missing": 220, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "pair_trading_bot_py": {"hash": "5983d4af39a0f19d21a2041401f24cdf", "index": {"url": "pair_trading_bot_py.html", "file": "pair_trading_bot.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 240, "n_excluded": 0, "n_missing": 240, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "pairs_trading_strategy_py": {"hash": "a4e6236722f9884e6db1ee794352c197", "index": {"url": "pairs_trading_strategy_py.html", "file": "pairs_trading_strategy.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 159, "n_excluded": 16, "n_missing": 159, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "perfect_validation_py": {"hash": "93b1c1fb59a64a8dbece3ff43d5011d8", "index": {"url": "perfect_validation_py.html", "file": "perfect_validation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 74, "n_excluded": 2, "n_missing": 74, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "portfolio_backtester_py": {"hash": "288b343784eca3230ef9803962f99d70", "index": {"url": "portfolio_backtester_py.html", "file": "portfolio_backtester.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 211, "n_excluded": 15, "n_missing": 211, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "portfolio_manager_py": {"hash": "7a61816cd3f40a6632ab4cb1f01384a2", "index": {"url": "portfolio_manager_py.html", "file": "portfolio_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 196, "n_excluded": 25, "n_missing": 196, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "production_readiness_check_py": {"hash": "51844dc16660525677f50ecf2be34bf2", "index": {"url": "production_readiness_check_py.html", "file": "production_readiness_check.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 159, "n_excluded": 2, "n_missing": 159, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "quick_fix_final_issues_py": {"hash": "3c3cc8412deb51ee0b0801c3bb7ddef2", "index": {"url": "quick_fix_final_issues_py.html", "file": "quick_fix_final_issues.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 30, "n_excluded": 3, "n_missing": 30, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "quick_start_production_py": {"hash": "fd11215e05ddb061c9a5d569c40f680c", "index": {"url": "quick_start_production_py.html", "file": "quick_start_production.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 159, "n_excluded": 8, "n_missing": 159, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "resource_manager_py": {"hash": "c8337bff7394719c2536d2c7e922046f", "index": {"url": "resource_manager_py.html", "file": "resource_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 73, "n_excluded": 12, "n_missing": 73, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "retry_handler_py": {"hash": "397ddcaf9769f0d76da2e2a5b6a8d326", "index": {"url": "retry_handler_py.html", "file": "retry_handler.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 89, "n_excluded": 31, "n_missing": 89, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "security_manager_py": {"hash": "228533c27b21322ba53914ecc55a76b4", "index": {"url": "security_manager_py.html", "file": "security_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 195, "n_excluded": 14, "n_missing": 195, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "simplified_optimization_validation_py": {"hash": "f049fa05b8487a24b7d5d0c4cbf82a80", "index": {"url": "simplified_optimization_validation_py.html", "file": "simplified_optimization_validation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 196, "n_excluded": 2, "n_missing": 196, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "smart_capital_management_py": {"hash": "4b6c5c505f8dc91cd23357cd6215c73b", "index": {"url": "smart_capital_management_py.html", "file": "smart_capital_management.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 85, "n_excluded": 19, "n_missing": 85, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "strategy_config_models_py": {"hash": "a5d9fc1bcddff3c53a707d9731590701", "index": {"url": "strategy_config_models_py.html", "file": "strategy_config_models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 169, "n_excluded": 22, "n_missing": 60, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "strategy_framework_py": {"hash": "e314424adcfa04457fc57e3caef5e71e", "index": {"url": "strategy_framework_py.html", "file": "strategy_framework.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 147, "n_excluded": 53, "n_missing": 147, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "strategy_health_monitor_py": {"hash": "c53071c75cd8206e3bef606345a49551", "index": {"url": "strategy_health_monitor_py.html", "file": "strategy_health_monitor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 198, "n_excluded": 11, "n_missing": 198, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "telegram_bot_py": {"hash": "69f5b5353e60ec94936f0fd8557edd2d", "index": {"url": "telegram_bot_py.html", "file": "telegram_bot.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 230, "n_excluded": 15, "n_missing": 230, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "trading_exceptions_py": {"hash": "3fd3bd664221cac123720026f54adec1", "index": {"url": "trading_exceptions_py.html", "file": "trading_exceptions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 26, "n_excluded": 0, "n_missing": 26, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "trading_safety_guard_py": {"hash": "c61fdc713fd4b8dc112b529a3cda44d0", "index": {"url": "trading_safety_guard_py.html", "file": "trading_safety_guard.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 142, "n_excluded": 20, "n_missing": 142, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "trend_following_strategy_py": {"hash": "a0f8a811ead54569f10486ef4da658ea", "index": {"url": "trend_following_strategy_py.html", "file": "trend_following_strategy.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 192, "n_excluded": 19, "n_missing": 192, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "ultimate_system_validation_py": {"hash": "752e733ca4a0f1bac517c79ebea1e0f9", "index": {"url": "ultimate_system_validation_py.html", "file": "ultimate_system_validation.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 195, "n_excluded": 2, "n_missing": 195, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "unified_client_manager_py": {"hash": "1c8d4bc5dbce298114104d9c7c03700d", "index": {"url": "unified_client_manager_py.html", "file": "unified_client_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 152, "n_excluded": 2, "n_missing": 117, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "unified_config_manager_py": {"hash": "a4f37c5789daf31232f8429002075430", "index": {"url": "unified_config_manager_py.html", "file": "unified_config_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 226, "n_excluded": 3, "n_missing": 226, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "unified_pair_selector_py": {"hash": "327cdc178bcead251846af689a1626a5", "index": {"url": "unified_pair_selector_py.html", "file": "unified_pair_selector.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 257, "n_excluded": 3, "n_missing": 257, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "unified_portfolio_manager_py": {"hash": "2b3cb22f2eef543ae97753d0ecd616fd", "index": {"url": "unified_portfolio_manager_py.html", "file": "unified_portfolio_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 273, "n_excluded": 22, "n_missing": 273, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "unified_test_runner_py": {"hash": "1048bbfe787d3c9b5a31f614c7249737", "index": {"url": "unified_test_runner_py.html", "file": "unified_test_runner.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 226, "n_excluded": 3, "n_missing": 226, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "utils_py": {"hash": "1fa0eade465fbca33dbdc7dad3d9ec8b", "index": {"url": "utils_py.html", "file": "utils.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 134, "n_excluded": 0, "n_missing": 134, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "validate_improvements_py": {"hash": "252cb9ab0a3e8c460c37bcdff25ef17c", "index": {"url": "validate_improvements_py.html", "file": "validate_improvements.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 201, "n_excluded": 3, "n_missing": 201, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}