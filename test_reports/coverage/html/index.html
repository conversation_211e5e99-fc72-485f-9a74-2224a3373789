<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_8e611ae1.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">20%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-07-06 12:16 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html">event_system/event_bus.py</a></td>
                <td>206</td>
                <td>154</td>
                <td>37</td>
                <td class="right" data-ratio="52 206">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html">factor_factory/alpha_factors.py</a></td>
                <td>253</td>
                <td>213</td>
                <td>18</td>
                <td class="right" data-ratio="40 253">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="main_py.html">main.py</a></td>
                <td>159</td>
                <td>159</td>
                <td>2</td>
                <td class="right" data-ratio="0 159">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c6e76699e82afdf_feature_engineering_py.html">ml_predictor/feature_engineering.py</a></td>
                <td>260</td>
                <td>232</td>
                <td>11</td>
                <td class="right" data-ratio="28 260">11%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c6e76699e82afdf_predictor_py.html">ml_predictor/predictor.py</a></td>
                <td>242</td>
                <td>217</td>
                <td>18</td>
                <td class="right" data-ratio="25 242">10%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html">performance_optimization/memory_optimizer.py</a></td>
                <td>225</td>
                <td>174</td>
                <td>14</td>
                <td class="right" data-ratio="51 225">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html">performance_optimization/vectorized_calculations.py</a></td>
                <td>283</td>
                <td>229</td>
                <td>18</td>
                <td class="right" data-ratio="54 283">19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac1802d461d41d87_research_toolkit_py.html">research_environment/research_toolkit.py</a></td>
                <td>284</td>
                <td>273</td>
                <td>16</td>
                <td class="right" data-ratio="11 284">4%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bd6a586f8a803a04_advanced_risk_manager_py.html">risk_management/advanced_risk_manager.py</a></td>
                <td>267</td>
                <td>238</td>
                <td>13</td>
                <td class="right" data-ratio="29 267">11%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html">src/alert_manager.py</a></td>
                <td>157</td>
                <td>125</td>
                <td>8</td>
                <td class="right" data-ratio="32 157">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_data_handler_py.html">src/async_data_handler.py</a></td>
                <td>189</td>
                <td>156</td>
                <td>36</td>
                <td class="right" data-ratio="33 189">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html">src/async_resource_manager.py</a></td>
                <td>253</td>
                <td>204</td>
                <td>2</td>
                <td class="right" data-ratio="49 253">19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html">src/async_state_persistence.py</a></td>
                <td>314</td>
                <td>242</td>
                <td>2</td>
                <td class="right" data-ratio="72 314">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_backtesting_py.html">src/backtesting.py</a></td>
                <td>250</td>
                <td>230</td>
                <td>0</td>
                <td class="right" data-ratio="20 250">8%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_loader_py.html">src/config_loader.py</a></td>
                <td>124</td>
                <td>100</td>
                <td>13</td>
                <td class="right" data-ratio="24 124">19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html">src/config_validation.py</a></td>
                <td>298</td>
                <td>137</td>
                <td>15</td>
                <td class="right" data-ratio="161 298">54%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_data_handler_py.html">src/data_handler.py</a></td>
                <td>191</td>
                <td>162</td>
                <td>0</td>
                <td class="right" data-ratio="29 191">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html">src/database_manager.py</a></td>
                <td>246</td>
                <td>212</td>
                <td>11</td>
                <td class="right" data-ratio="34 246">14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html">src/dynamic_config.py</a></td>
                <td>177</td>
                <td>145</td>
                <td>14</td>
                <td class="right" data-ratio="32 177">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html">src/enhanced_retry_handler.py</a></td>
                <td>316</td>
                <td>217</td>
                <td>26</td>
                <td class="right" data-ratio="99 316">31%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html">src/exchange_factory.py</a></td>
                <td>102</td>
                <td>77</td>
                <td>27</td>
                <td class="right" data-ratio="25 102">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html">src/global_event_bus.py</a></td>
                <td>163</td>
                <td>99</td>
                <td>36</td>
                <td class="right" data-ratio="64 163">39%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html">src/graceful_shutdown.py</a></td>
                <td>153</td>
                <td>122</td>
                <td>14</td>
                <td class="right" data-ratio="31 153">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html">src/health_server.py</a></td>
                <td>194</td>
                <td>169</td>
                <td>15</td>
                <td class="right" data-ratio="25 194">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_high_leverage_risk_checker_py.html">src/high_leverage_risk_checker.py</a></td>
                <td>117</td>
                <td>97</td>
                <td>2</td>
                <td class="right" data-ratio="20 117">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html">src/integrated_trading_executor.py</a></td>
                <td>265</td>
                <td>208</td>
                <td>2</td>
                <td class="right" data-ratio="57 265">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html">src/intelligent_cache_manager.py</a></td>
                <td>321</td>
                <td>252</td>
                <td>2</td>
                <td class="right" data-ratio="69 321">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_logging_config_py.html">src/logging_config.py</a></td>
                <td>40</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="39 40">98%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html">src/multi_executor_manager.py</a></td>
                <td>295</td>
                <td>225</td>
                <td>2</td>
                <td class="right" data-ratio="70 295">24%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html">src/pair_trading_bot.py</a></td>
                <td>240</td>
                <td>234</td>
                <td>0</td>
                <td class="right" data-ratio="6 240">2%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_backtester_py.html">src/portfolio_backtester.py</a></td>
                <td>211</td>
                <td>202</td>
                <td>15</td>
                <td class="right" data-ratio="9 211">4%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html">src/portfolio_manager.py</a></td>
                <td>196</td>
                <td>153</td>
                <td>25</td>
                <td class="right" data-ratio="43 196">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html">src/strategy_config_models.py</a></td>
                <td>169</td>
                <td>60</td>
                <td>22</td>
                <td class="right" data-ratio="109 169">64%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html">src/strategy_framework.py</a></td>
                <td>147</td>
                <td>75</td>
                <td>53</td>
                <td class="right" data-ratio="72 147">49%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html">src/strategy_health_monitor.py</a></td>
                <td>198</td>
                <td>165</td>
                <td>11</td>
                <td class="right" data-ratio="33 198">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_exceptions_py.html">src/trading_exceptions.py</a></td>
                <td>26</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="18 26">69%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_safety_guard_py.html">src/trading_safety_guard.py</a></td>
                <td>142</td>
                <td>113</td>
                <td>20</td>
                <td class="right" data-ratio="29 142">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html">src/unified_client_manager.py</a></td>
                <td>152</td>
                <td>117</td>
                <td>2</td>
                <td class="right" data-ratio="35 152">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html">src/unified_portfolio_manager.py</a></td>
                <td>273</td>
                <td>232</td>
                <td>22</td>
                <td class="right" data-ratio="41 273">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_utils_py.html">src/utils.py</a></td>
                <td>134</td>
                <td>113</td>
                <td>0</td>
                <td class="right" data-ratio="21 134">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html">state_management/state_manager.py</a></td>
                <td>274</td>
                <td>217</td>
                <td>14</td>
                <td class="right" data-ratio="57 274">21%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_test_runner_py.html">unified_test_runner.py</a></td>
                <td>221</td>
                <td>221</td>
                <td>3</td>
                <td class="right" data-ratio="0 221">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>8727</td>
                <td>6979</td>
                <td>561</td>
                <td class="right" data-ratio="1748 8727">20%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-07-06 12:16 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="unified_test_runner_py.html"></a>
        <a id="nextFileLink" class="nav" href="z_98bac5cf84189e43_event_bus_py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
