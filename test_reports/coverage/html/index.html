<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_8e611ae1.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">7%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-07-06 11:40 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="advanced_dynamic_allocation_py.html">advanced_dynamic_allocation.py</a></td>
                <td>284</td>
                <td>284</td>
                <td>4</td>
                <td class="right" data-ratio="0 284">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="alert_manager_py.html">alert_manager.py</a></td>
                <td>157</td>
                <td>125</td>
                <td>8</td>
                <td class="right" data-ratio="32 157">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="async_data_handler_py.html">async_data_handler.py</a></td>
                <td>189</td>
                <td>189</td>
                <td>36</td>
                <td class="right" data-ratio="0 189">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="async_optimization_py.html">async_optimization.py</a></td>
                <td>217</td>
                <td>217</td>
                <td>2</td>
                <td class="right" data-ratio="0 217">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="async_resource_manager_py.html">async_resource_manager.py</a></td>
                <td>253</td>
                <td>178</td>
                <td>2</td>
                <td class="right" data-ratio="75 253">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="async_state_persistence_py.html">async_state_persistence.py</a></td>
                <td>314</td>
                <td>220</td>
                <td>2</td>
                <td class="right" data-ratio="94 314">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="backtesting_py.html">backtesting.py</a></td>
                <td>250</td>
                <td>250</td>
                <td>0</td>
                <td class="right" data-ratio="0 250">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="command_system_py.html">command_system.py</a></td>
                <td>217</td>
                <td>217</td>
                <td>10</td>
                <td class="right" data-ratio="0 217">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="comprehensive_monitoring_py.html">comprehensive_monitoring.py</a></td>
                <td>236</td>
                <td>236</td>
                <td>2</td>
                <td class="right" data-ratio="0 236">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="comprehensive_optimization_py.html">comprehensive_optimization.py</a></td>
                <td>187</td>
                <td>187</td>
                <td>2</td>
                <td class="right" data-ratio="0 187">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html">config_loader.py</a></td>
                <td>124</td>
                <td>124</td>
                <td>13</td>
                <td class="right" data-ratio="0 124">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_manager_py.html">config_manager.py</a></td>
                <td>173</td>
                <td>173</td>
                <td>2</td>
                <td class="right" data-ratio="0 173">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_validation_py.html">config_validation.py</a></td>
                <td>298</td>
                <td>298</td>
                <td>15</td>
                <td class="right" data-ratio="0 298">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="data_directory_manager_py.html">data_directory_manager.py</a></td>
                <td>231</td>
                <td>169</td>
                <td>3</td>
                <td class="right" data-ratio="62 231">27%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="data_handler_py.html">data_handler.py</a></td>
                <td>191</td>
                <td>191</td>
                <td>0</td>
                <td class="right" data-ratio="0 191">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="database_manager_py.html">database_manager.py</a></td>
                <td>246</td>
                <td>246</td>
                <td>11</td>
                <td class="right" data-ratio="0 246">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="debug_routes_py.html">debug_routes.py</a></td>
                <td>39</td>
                <td>39</td>
                <td>3</td>
                <td class="right" data-ratio="0 39">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="demo_multi_strategy_system_py.html">demo_multi_strategy_system.py</a></td>
                <td>145</td>
                <td>145</td>
                <td>2</td>
                <td class="right" data-ratio="0 145">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="demo_smart_leverage_py.html">demo_smart_leverage.py</a></td>
                <td>111</td>
                <td>111</td>
                <td>2</td>
                <td class="right" data-ratio="0 111">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="dynamic_allocation_engine_py.html">dynamic_allocation_engine.py</a></td>
                <td>207</td>
                <td>207</td>
                <td>3</td>
                <td class="right" data-ratio="0 207">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="dynamic_config_py.html">dynamic_config.py</a></td>
                <td>177</td>
                <td>145</td>
                <td>14</td>
                <td class="right" data-ratio="32 177">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="enhanced_event_integration_py.html">enhanced_event_integration.py</a></td>
                <td>112</td>
                <td>112</td>
                <td>23</td>
                <td class="right" data-ratio="0 112">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="enhanced_portfolio_demo_py.html">enhanced_portfolio_demo.py</a></td>
                <td>150</td>
                <td>150</td>
                <td>2</td>
                <td class="right" data-ratio="0 150">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="enhanced_retry_handler_py.html">enhanced_retry_handler.py</a></td>
                <td>316</td>
                <td>316</td>
                <td>26</td>
                <td class="right" data-ratio="0 316">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="event_driven_core_py.html">event_driven_core.py</a></td>
                <td>213</td>
                <td>213</td>
                <td>9</td>
                <td class="right" data-ratio="0 213">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="event_publisher_decorators_py.html">event_publisher_decorators.py</a></td>
                <td>40</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="0 40">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="example_usage_py.html">example_usage.py</a></td>
                <td>148</td>
                <td>148</td>
                <td>8</td>
                <td class="right" data-ratio="0 148">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="exception_system_py.html">exception_system.py</a></td>
                <td>182</td>
                <td>182</td>
                <td>8</td>
                <td class="right" data-ratio="0 182">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="exchange_factory_py.html">exchange_factory.py</a></td>
                <td>102</td>
                <td>102</td>
                <td>27</td>
                <td class="right" data-ratio="0 102">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="final_optimization_validation_py.html">final_optimization_validation.py</a></td>
                <td>215</td>
                <td>215</td>
                <td>2</td>
                <td class="right" data-ratio="0 215">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="final_system_validation_py.html">final_system_validation.py</a></td>
                <td>151</td>
                <td>151</td>
                <td>2</td>
                <td class="right" data-ratio="0 151">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fix_critical_issues_py.html">fix_critical_issues.py</a></td>
                <td>95</td>
                <td>95</td>
                <td>2</td>
                <td class="right" data-ratio="0 95">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fix_resource_management_py.html">fix_resource_management.py</a></td>
                <td>127</td>
                <td>127</td>
                <td>3</td>
                <td class="right" data-ratio="0 127">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="global_event_bus_py.html">global_event_bus.py</a></td>
                <td>163</td>
                <td>86</td>
                <td>36</td>
                <td class="right" data-ratio="77 163">47%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="graceful_shutdown_py.html">graceful_shutdown.py</a></td>
                <td>153</td>
                <td>105</td>
                <td>14</td>
                <td class="right" data-ratio="48 153">31%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="health_checker_py.html">health_checker.py</a></td>
                <td>208</td>
                <td>208</td>
                <td>6</td>
                <td class="right" data-ratio="0 208">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="health_server_py.html">health_server.py</a></td>
                <td>194</td>
                <td>194</td>
                <td>15</td>
                <td class="right" data-ratio="0 194">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="high_leverage_risk_checker_py.html">high_leverage_risk_checker.py</a></td>
                <td>117</td>
                <td>117</td>
                <td>2</td>
                <td class="right" data-ratio="0 117">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="improved_backtest_py.html">improved_backtest.py</a></td>
                <td>161</td>
                <td>161</td>
                <td>2</td>
                <td class="right" data-ratio="0 161">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="integrated_trading_executor_py.html">integrated_trading_executor.py</a></td>
                <td>265</td>
                <td>265</td>
                <td>2</td>
                <td class="right" data-ratio="0 265">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="intelligent_cache_manager_py.html">intelligent_cache_manager.py</a></td>
                <td>321</td>
                <td>321</td>
                <td>2</td>
                <td class="right" data-ratio="0 321">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="intelligent_portfolio_system_py.html">intelligent_portfolio_system.py</a></td>
                <td>186</td>
                <td>186</td>
                <td>2</td>
                <td class="right" data-ratio="0 186">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="logging_config_py.html">logging_config.py</a></td>
                <td>40</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="7 40">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="main_py.html">main.py</a></td>
                <td>159</td>
                <td>159</td>
                <td>2</td>
                <td class="right" data-ratio="0 159">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="math_utils_py.html">math_utils.py</a></td>
                <td>165</td>
                <td>165</td>
                <td>15</td>
                <td class="right" data-ratio="0 165">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="multi_executor_manager_py.html">multi_executor_manager.py</a></td>
                <td>295</td>
                <td>194</td>
                <td>2</td>
                <td class="right" data-ratio="101 295">34%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="multi_strategy_engine_py.html">multi_strategy_engine.py</a></td>
                <td>220</td>
                <td>220</td>
                <td>19</td>
                <td class="right" data-ratio="0 220">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pair_trading_bot_py.html">pair_trading_bot.py</a></td>
                <td>240</td>
                <td>240</td>
                <td>0</td>
                <td class="right" data-ratio="0 240">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pairs_trading_strategy_py.html">pairs_trading_strategy.py</a></td>
                <td>159</td>
                <td>159</td>
                <td>16</td>
                <td class="right" data-ratio="0 159">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="perfect_validation_py.html">perfect_validation.py</a></td>
                <td>74</td>
                <td>74</td>
                <td>2</td>
                <td class="right" data-ratio="0 74">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="portfolio_backtester_py.html">portfolio_backtester.py</a></td>
                <td>211</td>
                <td>211</td>
                <td>15</td>
                <td class="right" data-ratio="0 211">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="portfolio_manager_py.html">portfolio_manager.py</a></td>
                <td>196</td>
                <td>196</td>
                <td>25</td>
                <td class="right" data-ratio="0 196">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="production_readiness_check_py.html">production_readiness_check.py</a></td>
                <td>159</td>
                <td>159</td>
                <td>2</td>
                <td class="right" data-ratio="0 159">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="quick_fix_final_issues_py.html">quick_fix_final_issues.py</a></td>
                <td>30</td>
                <td>30</td>
                <td>3</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="quick_start_production_py.html">quick_start_production.py</a></td>
                <td>159</td>
                <td>159</td>
                <td>8</td>
                <td class="right" data-ratio="0 159">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="resource_manager_py.html">resource_manager.py</a></td>
                <td>73</td>
                <td>73</td>
                <td>12</td>
                <td class="right" data-ratio="0 73">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="retry_handler_py.html">retry_handler.py</a></td>
                <td>89</td>
                <td>89</td>
                <td>31</td>
                <td class="right" data-ratio="0 89">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="security_manager_py.html">security_manager.py</a></td>
                <td>195</td>
                <td>195</td>
                <td>14</td>
                <td class="right" data-ratio="0 195">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="simplified_optimization_validation_py.html">simplified_optimization_validation.py</a></td>
                <td>196</td>
                <td>196</td>
                <td>2</td>
                <td class="right" data-ratio="0 196">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="smart_capital_management_py.html">smart_capital_management.py</a></td>
                <td>85</td>
                <td>85</td>
                <td>19</td>
                <td class="right" data-ratio="0 85">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="strategy_config_models_py.html">strategy_config_models.py</a></td>
                <td>169</td>
                <td>52</td>
                <td>22</td>
                <td class="right" data-ratio="117 169">69%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="strategy_framework_py.html">strategy_framework.py</a></td>
                <td>147</td>
                <td>147</td>
                <td>53</td>
                <td class="right" data-ratio="0 147">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="strategy_health_monitor_py.html">strategy_health_monitor.py</a></td>
                <td>198</td>
                <td>165</td>
                <td>11</td>
                <td class="right" data-ratio="33 198">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="telegram_bot_py.html">telegram_bot.py</a></td>
                <td>230</td>
                <td>230</td>
                <td>15</td>
                <td class="right" data-ratio="0 230">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="trading_exceptions_py.html">trading_exceptions.py</a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="trading_safety_guard_py.html">trading_safety_guard.py</a></td>
                <td>142</td>
                <td>142</td>
                <td>20</td>
                <td class="right" data-ratio="0 142">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="trend_following_strategy_py.html">trend_following_strategy.py</a></td>
                <td>192</td>
                <td>192</td>
                <td>19</td>
                <td class="right" data-ratio="0 192">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="ultimate_system_validation_py.html">ultimate_system_validation.py</a></td>
                <td>195</td>
                <td>195</td>
                <td>2</td>
                <td class="right" data-ratio="0 195">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_client_manager_py.html">unified_client_manager.py</a></td>
                <td>152</td>
                <td>152</td>
                <td>2</td>
                <td class="right" data-ratio="0 152">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_config_manager_py.html">unified_config_manager.py</a></td>
                <td>226</td>
                <td>104</td>
                <td>3</td>
                <td class="right" data-ratio="122 226">54%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_pair_selector_py.html">unified_pair_selector.py</a></td>
                <td>257</td>
                <td>205</td>
                <td>3</td>
                <td class="right" data-ratio="52 257">20%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_portfolio_manager_py.html">unified_portfolio_manager.py</a></td>
                <td>273</td>
                <td>273</td>
                <td>22</td>
                <td class="right" data-ratio="0 273">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_test_runner_py.html">unified_test_runner.py</a></td>
                <td>226</td>
                <td>176</td>
                <td>3</td>
                <td class="right" data-ratio="50 226">22%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="utils_py.html">utils.py</a></td>
                <td>134</td>
                <td>113</td>
                <td>0</td>
                <td class="right" data-ratio="21 134">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="validate_improvements_py.html">validate_improvements.py</a></td>
                <td>201</td>
                <td>201</td>
                <td>3</td>
                <td class="right" data-ratio="0 201">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>13378</td>
                <td>12455</td>
                <td>694</td>
                <td class="right" data-ratio="923 13378">7%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-07-06 11:40 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="validate_improvements_py.html"></a>
        <a id="nextFileLink" class="nav" href="advanced_dynamic_allocation_py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
