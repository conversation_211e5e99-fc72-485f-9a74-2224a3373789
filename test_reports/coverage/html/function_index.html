<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_8e611ae1.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">20%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-07-06 12:16 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t50">event_system/event_bus.py</a></td>
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t50"><data value='init__'>EventHandler.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t56">event_system/event_bus.py</a></td>
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t56"><data value='handle'>EventHandler.handle</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t76">event_system/event_bus.py</a></td>
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t76"><data value='process_event'>EventHandler._process_event</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t84">event_system/event_bus.py</a></td>
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t84"><data value='init__'>EventBus.__init__</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t113">event_system/event_bus.py</a></td>
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t113"><data value='subscribe'>EventBus.subscribe</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t121">event_system/event_bus.py</a></td>
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t121"><data value='unsubscribe'>EventBus.unsubscribe</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t128">event_system/event_bus.py</a></td>
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t128"><data value='publish'>EventBus.publish</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t148">event_system/event_bus.py</a></td>
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t148"><data value='publish_to_redis'>EventBus._publish_to_redis</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t168">event_system/event_bus.py</a></td>
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t168"><data value='process_event_immediately'>EventBus._process_event_immediately</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t187">event_system/event_bus.py</a></td>
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t187"><data value='start'>EventBus.start</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t205">event_system/event_bus.py</a></td>
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t205"><data value='stop'>EventBus.stop</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t223">event_system/event_bus.py</a></td>
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t223"><data value='event_worker'>EventBus._event_worker</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t245">event_system/event_bus.py</a></td>
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t245"><data value='process_event'>EventBus._process_event</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t270">event_system/event_bus.py</a></td>
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t270"><data value='redis_subscriber'>EventBus._redis_subscriber</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t296">event_system/event_bus.py</a></td>
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t296"><data value='handle_redis_message'>EventBus._handle_redis_message</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t315">event_system/event_bus.py</a></td>
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t315"><data value='get_statistics'>EventBus.get_statistics</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t331">event_system/event_bus.py</a></td>
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t331"><data value='get_handler_stats'>EventBus._get_handler_stats</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t351">event_system/event_bus.py</a></td>
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t351"><data value='get_event_bus'>get_event_bus</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t360">event_system/event_bus.py</a></td>
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t360"><data value='publish_event'>publish_event</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t381">event_system/event_bus.py</a></td>
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t381"><data value='process_event'>TestHandler._process_event</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>3</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t386">event_system/event_bus.py</a></td>
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t386"><data value='test_event_bus'>test_event_bus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>26</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html">event_system/event_bus.py</a></td>
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>52</td>
                <td>0</td>
                <td>7</td>
                <td class="right" data-ratio="52 52">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t25">factor_factory/alpha_factors.py</a></td>
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t25"><data value='init__'>AlphaFactor.__init__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t34">factor_factory/alpha_factors.py</a></td>
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t34"><data value='calculate'>AlphaFactor.calculate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t38">factor_factory/alpha_factors.py</a></td>
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t38"><data value='evaluate'>AlphaFactor.evaluate</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t86">factor_factory/alpha_factors.py</a></td>
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t86"><data value='init__'>SpreadMomentumFactor.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t93">factor_factory/alpha_factors.py</a></td>
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t93"><data value='calculate'>SpreadMomentumFactor.calculate</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t114">factor_factory/alpha_factors.py</a></td>
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t114"><data value='init__'>VolatilityRegimeFactor.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t121">factor_factory/alpha_factors.py</a></td>
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t121"><data value='calculate'>VolatilityRegimeFactor.calculate</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t151">factor_factory/alpha_factors.py</a></td>
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t151"><data value='init__'>CointegrationStrengthFactor.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t158">factor_factory/alpha_factors.py</a></td>
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t158"><data value='calculate'>CointegrationStrengthFactor.calculate</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t213">factor_factory/alpha_factors.py</a></td>
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t213"><data value='init__'>VolumeProfileFactor.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t220">factor_factory/alpha_factors.py</a></td>
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t220"><data value='calculate'>VolumeProfileFactor.calculate</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t256">factor_factory/alpha_factors.py</a></td>
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t256"><data value='init__'>MarketRegimeFactor.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t263">factor_factory/alpha_factors.py</a></td>
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t263"><data value='calculate'>MarketRegimeFactor.calculate</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t305">factor_factory/alpha_factors.py</a></td>
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t305"><data value='init__'>AlphaFactoryManager.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t314">factor_factory/alpha_factors.py</a></td>
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t314"><data value='register_factors'>AlphaFactoryManager._register_factors</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t330">factor_factory/alpha_factors.py</a></td>
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t330"><data value='calculate_all_factors'>AlphaFactoryManager.calculate_all_factors</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t356">factor_factory/alpha_factors.py</a></td>
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t356"><data value='standardize_factors'>AlphaFactoryManager._standardize_factors</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t376">factor_factory/alpha_factors.py</a></td>
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t376"><data value='evaluate_factors'>AlphaFactoryManager.evaluate_factors</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t395">factor_factory/alpha_factors.py</a></td>
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t395"><data value='get_top_factors'>AlphaFactoryManager.get_top_factors</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t426">factor_factory/alpha_factors.py</a></td>
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t426"><data value='create_composite_factor'>AlphaFactoryManager.create_composite_factor</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t451">factor_factory/alpha_factors.py</a></td>
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t451"><data value='get_factor_summary'>AlphaFactoryManager.get_factor_summary</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t481">factor_factory/alpha_factors.py</a></td>
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t481"><data value='get_alpha_factory'>get_alpha_factory</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html">factor_factory/alpha_factors.py</a></td>
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>40</td>
                <td>0</td>
                <td>16</td>
                <td class="right" data-ratio="40 40">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="main_py.html#t26">main.py</a></td>
                <td class="name left"><a href="main_py.html#t26"><data value='create_argument_parser'>create_argument_parser</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="main_py.html#t85">main.py</a></td>
                <td class="name left"><a href="main_py.html#t85"><data value='validate_config'>validate_config</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="main_py.html#t119">main.py</a></td>
                <td class="name left"><a href="main_py.html#t119"><data value='execute_command'>execute_command</data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="main_py.html#t203">main.py</a></td>
                <td class="name left"><a href="main_py.html#t203"><data value='print_system_info'>print_system_info</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="main_py.html#t226">main.py</a></td>
                <td class="name left"><a href="main_py.html#t226"><data value='print_final_stats'>print_final_stats</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="main_py.html#t250">main.py</a></td>
                <td class="name left"><a href="main_py.html#t250"><data value='main'>main</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="main_py.html#t285">main.py</a></td>
                <td class="name left"><a href="main_py.html#t285"><data value='run'>run</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="main_py.html">main.py</a></td>
                <td class="name left"><a href="main_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>19</td>
                <td>19</td>
                <td>2</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c6e76699e82afdf_feature_engineering_py.html#t28">ml_predictor/feature_engineering.py</a></td>
                <td class="name left"><a href="z_4c6e76699e82afdf_feature_engineering_py.html#t28"><data value='init__'>FeatureEngineer.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c6e76699e82afdf_feature_engineering_py.html#t34">ml_predictor/feature_engineering.py</a></td>
                <td class="name left"><a href="z_4c6e76699e82afdf_feature_engineering_py.html#t34"><data value='generate_features'>FeatureEngineer.generate_features</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c6e76699e82afdf_feature_engineering_py.html#t82">ml_predictor/feature_engineering.py</a></td>
                <td class="name left"><a href="z_4c6e76699e82afdf_feature_engineering_py.html#t82"><data value='generate_price_features'>FeatureEngineer._generate_price_features</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c6e76699e82afdf_feature_engineering_py.html#t115">ml_predictor/feature_engineering.py</a></td>
                <td class="name left"><a href="z_4c6e76699e82afdf_feature_engineering_py.html#t115"><data value='generate_spread_features'>FeatureEngineer._generate_spread_features</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c6e76699e82afdf_feature_engineering_py.html#t158">ml_predictor/feature_engineering.py</a></td>
                <td class="name left"><a href="z_4c6e76699e82afdf_feature_engineering_py.html#t158"><data value='generate_technical_features'>FeatureEngineer._generate_technical_features</data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c6e76699e82afdf_feature_engineering_py.html#t212">ml_predictor/feature_engineering.py</a></td>
                <td class="name left"><a href="z_4c6e76699e82afdf_feature_engineering_py.html#t212"><data value='generate_statistical_features'>FeatureEngineer._generate_statistical_features</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c6e76699e82afdf_feature_engineering_py.html#t253">ml_predictor/feature_engineering.py</a></td>
                <td class="name left"><a href="z_4c6e76699e82afdf_feature_engineering_py.html#t253"><data value='generate_microstructure_features'>FeatureEngineer._generate_microstructure_features</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c6e76699e82afdf_feature_engineering_py.html#t289">ml_predictor/feature_engineering.py</a></td>
                <td class="name left"><a href="z_4c6e76699e82afdf_feature_engineering_py.html#t289"><data value='generate_time_features'>FeatureEngineer._generate_time_features</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c6e76699e82afdf_feature_engineering_py.html#t314">ml_predictor/feature_engineering.py</a></td>
                <td class="name left"><a href="z_4c6e76699e82afdf_feature_engineering_py.html#t314"><data value='calculate_rolling_half_life'>FeatureEngineer._calculate_rolling_half_life</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c6e76699e82afdf_feature_engineering_py.html#t317">ml_predictor/feature_engineering.py</a></td>
                <td class="name left"><a href="z_4c6e76699e82afdf_feature_engineering_py.html#t317"><data value='half_life'>FeatureEngineer._calculate_rolling_half_life.half_life</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c6e76699e82afdf_feature_engineering_py.html#t339">ml_predictor/feature_engineering.py</a></td>
                <td class="name left"><a href="z_4c6e76699e82afdf_feature_engineering_py.html#t339"><data value='calculate_rolling_adf'>FeatureEngineer._calculate_rolling_adf</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c6e76699e82afdf_feature_engineering_py.html#t342">ml_predictor/feature_engineering.py</a></td>
                <td class="name left"><a href="z_4c6e76699e82afdf_feature_engineering_py.html#t342"><data value='adf_pvalue'>FeatureEngineer._calculate_rolling_adf.adf_pvalue</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c6e76699e82afdf_feature_engineering_py.html#t357">ml_predictor/feature_engineering.py</a></td>
                <td class="name left"><a href="z_4c6e76699e82afdf_feature_engineering_py.html#t357"><data value='clean_features'>FeatureEngineer._clean_features</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c6e76699e82afdf_feature_engineering_py.html#t384">ml_predictor/feature_engineering.py</a></td>
                <td class="name left"><a href="z_4c6e76699e82afdf_feature_engineering_py.html#t384"><data value='calculate_rsi'>FeatureEngineer._calculate_rsi</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c6e76699e82afdf_feature_engineering_py.html#t396">ml_predictor/feature_engineering.py</a></td>
                <td class="name left"><a href="z_4c6e76699e82afdf_feature_engineering_py.html#t396"><data value='calculate_sma'>FeatureEngineer._calculate_sma</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c6e76699e82afdf_feature_engineering_py.html#t400">ml_predictor/feature_engineering.py</a></td>
                <td class="name left"><a href="z_4c6e76699e82afdf_feature_engineering_py.html#t400"><data value='calculate_ema'>FeatureEngineer._calculate_ema</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c6e76699e82afdf_feature_engineering_py.html#t404">ml_predictor/feature_engineering.py</a></td>
                <td class="name left"><a href="z_4c6e76699e82afdf_feature_engineering_py.html#t404"><data value='get_feature_importance'>FeatureEngineer.get_feature_importance</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c6e76699e82afdf_feature_engineering_py.html">ml_predictor/feature_engineering.py</a></td>
                <td class="name left"><a href="z_4c6e76699e82afdf_feature_engineering_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>29</td>
                <td>1</td>
                <td>11</td>
                <td class="right" data-ratio="28 29">97%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c6e76699e82afdf_predictor_py.html#t26">ml_predictor/predictor.py</a></td>
                <td class="name left"><a href="z_4c6e76699e82afdf_predictor_py.html#t26"><data value='init__'>MLPredictor.__init__</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c6e76699e82afdf_predictor_py.html#t48">ml_predictor/predictor.py</a></td>
                <td class="name left"><a href="z_4c6e76699e82afdf_predictor_py.html#t48"><data value='initialize_default_models'>MLPredictor._initialize_default_models</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c6e76699e82afdf_predictor_py.html#t85">ml_predictor/predictor.py</a></td>
                <td class="name left"><a href="z_4c6e76699e82afdf_predictor_py.html#t85"><data value='prepare_training_data'>MLPredictor.prepare_training_data</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c6e76699e82afdf_predictor_py.html#t131">ml_predictor/predictor.py</a></td>
                <td class="name left"><a href="z_4c6e76699e82afdf_predictor_py.html#t131"><data value='train_models'>MLPredictor.train_models</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c6e76699e82afdf_predictor_py.html#t176">ml_predictor/predictor.py</a></td>
                <td class="name left"><a href="z_4c6e76699e82afdf_predictor_py.html#t176"><data value='predict'>MLPredictor.predict</data></a></td>
                <td>41</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="0 41">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c6e76699e82afdf_predictor_py.html#t249">ml_predictor/predictor.py</a></td>
                <td class="name left"><a href="z_4c6e76699e82afdf_predictor_py.html#t249"><data value='ensemble_predictions'>MLPredictor._ensemble_predictions</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c6e76699e82afdf_predictor_py.html#t293">ml_predictor/predictor.py</a></td>
                <td class="name left"><a href="z_4c6e76699e82afdf_predictor_py.html#t293"><data value='save_models'>MLPredictor.save_models</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c6e76699e82afdf_predictor_py.html#t325">ml_predictor/predictor.py</a></td>
                <td class="name left"><a href="z_4c6e76699e82afdf_predictor_py.html#t325"><data value='load_models'>MLPredictor.load_models</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c6e76699e82afdf_predictor_py.html#t363">ml_predictor/predictor.py</a></td>
                <td class="name left"><a href="z_4c6e76699e82afdf_predictor_py.html#t363"><data value='evaluate_models'>MLPredictor.evaluate_models</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c6e76699e82afdf_predictor_py.html#t401">ml_predictor/predictor.py</a></td>
                <td class="name left"><a href="z_4c6e76699e82afdf_predictor_py.html#t401"><data value='get_model_summary'>MLPredictor.get_model_summary</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c6e76699e82afdf_predictor_py.html#t449">ml_predictor/predictor.py</a></td>
                <td class="name left"><a href="z_4c6e76699e82afdf_predictor_py.html#t449"><data value='get_ml_predictor'>get_ml_predictor</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c6e76699e82afdf_predictor_py.html">ml_predictor/predictor.py</a></td>
                <td class="name left"><a href="z_4c6e76699e82afdf_predictor_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>25</td>
                <td>0</td>
                <td>18</td>
                <td class="right" data-ratio="25 25">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t40">performance_optimization/memory_optimizer.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t40"><data value='init__'>MemoryProfiler.__init__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t51">performance_optimization/memory_optimizer.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t51"><data value='start_tracemalloc'>MemoryProfiler._start_tracemalloc</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t60">performance_optimization/memory_optimizer.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t60"><data value='take_snapshot'>MemoryProfiler.take_snapshot</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t99">performance_optimization/memory_optimizer.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t99"><data value='get_memory_growth'>MemoryProfiler.get_memory_growth</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t106">performance_optimization/memory_optimizer.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t106"><data value='get_top_memory_consumers'>MemoryProfiler.get_top_memory_consumers</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t129">performance_optimization/memory_optimizer.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t129"><data value='profile_function'>MemoryProfiler.profile_function</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t132">performance_optimization/memory_optimizer.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t132"><data value='wrapper'>MemoryProfiler.profile_function.wrapper</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t155">performance_optimization/memory_optimizer.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t155"><data value='optimize_dtypes'>DataFrameOptimizer.optimize_dtypes</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t190">performance_optimization/memory_optimizer.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t190"><data value='optimize_numeric_column'>DataFrameOptimizer._optimize_numeric_column</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t218">performance_optimization/memory_optimizer.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t218"><data value='can_convert_to_float32'>DataFrameOptimizer._can_convert_to_float32</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t237">performance_optimization/memory_optimizer.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t237"><data value='optimize_object_column'>DataFrameOptimizer._optimize_object_column</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t256">performance_optimization/memory_optimizer.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t256"><data value='init__'>ChunkedDataProcessor.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t260">performance_optimization/memory_optimizer.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t260"><data value='process_large_dataset'>ChunkedDataProcessor.process_large_dataset</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t302">performance_optimization/memory_optimizer.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t302"><data value='init__'>MemoryMonitor.__init__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t311">performance_optimization/memory_optimizer.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t311"><data value='start'>MemoryMonitor.start</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t322">performance_optimization/memory_optimizer.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t322"><data value='stop'>MemoryMonitor.stop</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t330">performance_optimization/memory_optimizer.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t330"><data value='monitor_loop'>MemoryMonitor._monitor_loop</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t356">performance_optimization/memory_optimizer.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t356"><data value='get_memory_report'>MemoryMonitor.get_memory_report</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t382">performance_optimization/memory_optimizer.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t382"><data value='get_memory_monitor'>get_memory_monitor</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t391">performance_optimization/memory_optimizer.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t391"><data value='memory_profile'>memory_profile</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html">performance_optimization/memory_optimizer.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>51</td>
                <td>0</td>
                <td>14</td>
                <td class="right" data-ratio="51 51">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t22">performance_optimization/vectorized_calculations.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t22"><data value='init__'>VectorizedCalculator.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t31">performance_optimization/vectorized_calculations.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t31"><data value='calculate_log_spread_numba'>VectorizedCalculator.calculate_log_spread_numba</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t37">performance_optimization/vectorized_calculations.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t37"><data value='calculate_rolling_mean_numba'>VectorizedCalculator.calculate_rolling_mean_numba</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t50">performance_optimization/vectorized_calculations.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t50"><data value='calculate_rolling_std_numba'>VectorizedCalculator.calculate_rolling_std_numba</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t64">performance_optimization/vectorized_calculations.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t64"><data value='calculate_zscore_numba'>VectorizedCalculator.calculate_zscore_numba</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t76">performance_optimization/vectorized_calculations.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t76"><data value='calculate_half_life_numba'>VectorizedCalculator.calculate_half_life_numba</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t107">performance_optimization/vectorized_calculations.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t107"><data value='calculate_correlation_numba'>VectorizedCalculator.calculate_correlation_numba</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t138">performance_optimization/vectorized_calculations.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t138"><data value='calculate_momentum_numba'>VectorizedCalculator.calculate_momentum_numba</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t152">performance_optimization/vectorized_calculations.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t152"><data value='calculate_spread_statistics_vectorized'>VectorizedCalculator.calculate_spread_statistics_vectorized</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t216">performance_optimization/vectorized_calculations.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t216"><data value='calculate_technical_indicators_vectorized'>VectorizedCalculator.calculate_technical_indicators_vectorized</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t265">performance_optimization/vectorized_calculations.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t265"><data value='calculate_rsi_vectorized'>VectorizedCalculator._calculate_rsi_vectorized</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t299">performance_optimization/vectorized_calculations.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t299"><data value='calculate_sma_vectorized'>VectorizedCalculator._calculate_sma_vectorized</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t311">performance_optimization/vectorized_calculations.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t311"><data value='calculate_ema_vectorized'>VectorizedCalculator._calculate_ema_vectorized</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t331">performance_optimization/vectorized_calculations.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t331"><data value='calculate_bollinger_bands_vectorized'>VectorizedCalculator._calculate_bollinger_bands_vectorized</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t363">performance_optimization/vectorized_calculations.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t363"><data value='calculate_atr_vectorized'>VectorizedCalculator._calculate_atr_vectorized</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t386">performance_optimization/vectorized_calculations.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t386"><data value='batch_calculate_features'>VectorizedCalculator.batch_calculate_features</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t420">performance_optimization/vectorized_calculations.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t420"><data value='optimize_dtype'>VectorizedCalculator._optimize_dtype</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t438">performance_optimization/vectorized_calculations.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t438"><data value='check_memory_usage'>VectorizedCalculator._check_memory_usage</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t455">performance_optimization/vectorized_calculations.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t455"><data value='has_sufficient_memory'>VectorizedCalculator._has_sufficient_memory</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t465">performance_optimization/vectorized_calculations.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t465"><data value='cleanup_memory'>VectorizedCalculator._cleanup_memory</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t483">performance_optimization/vectorized_calculations.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t483"><data value='clear_cache'>VectorizedCalculator.clear_cache</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t492">performance_optimization/vectorized_calculations.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t492"><data value='get_vectorized_calculator'>get_vectorized_calculator</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html">performance_optimization/vectorized_calculations.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>54</td>
                <td>0</td>
                <td>18</td>
                <td class="right" data-ratio="54 54">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac1802d461d41d87_research_toolkit_py.html#t32">research_environment/research_toolkit.py</a></td>
                <td class="name left"><a href="z_ac1802d461d41d87_research_toolkit_py.html#t32"><data value='init__'>ResearchToolkit.__init__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac1802d461d41d87_research_toolkit_py.html#t43">research_environment/research_toolkit.py</a></td>
                <td class="name left"><a href="z_ac1802d461d41d87_research_toolkit_py.html#t43"><data value='load_research_data'>ResearchToolkit.load_research_data</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac1802d461d41d87_research_toolkit_py.html#t80">research_environment/research_toolkit.py</a></td>
                <td class="name left"><a href="z_ac1802d461d41d87_research_toolkit_py.html#t80"><data value='analyze_pair_relationship'>ResearchToolkit.analyze_pair_relationship</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac1802d461d41d87_research_toolkit_py.html#t133">research_environment/research_toolkit.py</a></td>
                <td class="name left"><a href="z_ac1802d461d41d87_research_toolkit_py.html#t133"><data value='calculate_half_life'>ResearchToolkit._calculate_half_life</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac1802d461d41d87_research_toolkit_py.html#t158">research_environment/research_toolkit.py</a></td>
                <td class="name left"><a href="z_ac1802d461d41d87_research_toolkit_py.html#t158"><data value='analyze_trading_opportunities'>ResearchToolkit._analyze_trading_opportunities</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac1802d461d41d87_research_toolkit_py.html#t197">research_environment/research_toolkit.py</a></td>
                <td class="name left"><a href="z_ac1802d461d41d87_research_toolkit_py.html#t197"><data value='factor_research'>ResearchToolkit.factor_research</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac1802d461d41d87_research_toolkit_py.html#t236">research_environment/research_toolkit.py</a></td>
                <td class="name left"><a href="z_ac1802d461d41d87_research_toolkit_py.html#t236"><data value='analyze_factor_stability'>ResearchToolkit._analyze_factor_stability</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac1802d461d41d87_research_toolkit_py.html#t270">research_environment/research_toolkit.py</a></td>
                <td class="name left"><a href="z_ac1802d461d41d87_research_toolkit_py.html#t270"><data value='optimize_factor_combination'>ResearchToolkit._optimize_factor_combination</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac1802d461d41d87_research_toolkit_py.html#t313">research_environment/research_toolkit.py</a></td>
                <td class="name left"><a href="z_ac1802d461d41d87_research_toolkit_py.html#t313"><data value='strategy_optimization'>ResearchToolkit.strategy_optimization</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac1802d461d41d87_research_toolkit_py.html#t351">research_environment/research_toolkit.py</a></td>
                <td class="name left"><a href="z_ac1802d461d41d87_research_toolkit_py.html#t351"><data value='generate_param_combinations'>ResearchToolkit._generate_param_combinations</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac1802d461d41d87_research_toolkit_py.html#t370">research_environment/research_toolkit.py</a></td>
                <td class="name left"><a href="z_ac1802d461d41d87_research_toolkit_py.html#t370"><data value='run_single_backtest'>ResearchToolkit._run_single_backtest</data></a></td>
                <td>36</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 36">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac1802d461d41d87_research_toolkit_py.html#t434">research_environment/research_toolkit.py</a></td>
                <td class="name left"><a href="z_ac1802d461d41d87_research_toolkit_py.html#t434"><data value='calculate_max_drawdown'>ResearchToolkit._calculate_max_drawdown</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac1802d461d41d87_research_toolkit_py.html#t443">research_environment/research_toolkit.py</a></td>
                <td class="name left"><a href="z_ac1802d461d41d87_research_toolkit_py.html#t443"><data value='analyze_parameter_sensitivity'>ResearchToolkit._analyze_parameter_sensitivity</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac1802d461d41d87_research_toolkit_py.html#t476">research_environment/research_toolkit.py</a></td>
                <td class="name left"><a href="z_ac1802d461d41d87_research_toolkit_py.html#t476"><data value='generate_research_report'>ResearchToolkit.generate_research_report</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac1802d461d41d87_research_toolkit_py.html#t533">research_environment/research_toolkit.py</a></td>
                <td class="name left"><a href="z_ac1802d461d41d87_research_toolkit_py.html#t533"><data value='generate_research_recommendations'>ResearchToolkit._generate_research_recommendations</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac1802d461d41d87_research_toolkit_py.html#t576">research_environment/research_toolkit.py</a></td>
                <td class="name left"><a href="z_ac1802d461d41d87_research_toolkit_py.html#t576"><data value='get_research_toolkit'>get_research_toolkit</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac1802d461d41d87_research_toolkit_py.html">research_environment/research_toolkit.py</a></td>
                <td class="name left"><a href="z_ac1802d461d41d87_research_toolkit_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>34</td>
                <td>23</td>
                <td>16</td>
                <td class="right" data-ratio="11 34">32%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bd6a586f8a803a04_advanced_risk_manager_py.html#t24">risk_management/advanced_risk_manager.py</a></td>
                <td class="name left"><a href="z_bd6a586f8a803a04_advanced_risk_manager_py.html#t24"><data value='init__'>AdvancedRiskManager.__init__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bd6a586f8a803a04_advanced_risk_manager_py.html#t37">risk_management/advanced_risk_manager.py</a></td>
                <td class="name left"><a href="z_bd6a586f8a803a04_advanced_risk_manager_py.html#t37"><data value='load_stress_scenarios'>AdvancedRiskManager._load_stress_scenarios</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bd6a586f8a803a04_advanced_risk_manager_py.html#t70">risk_management/advanced_risk_manager.py</a></td>
                <td class="name left"><a href="z_bd6a586f8a803a04_advanced_risk_manager_py.html#t70"><data value='calculate_var'>AdvancedRiskManager.calculate_var</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bd6a586f8a803a04_advanced_risk_manager_py.html#t112">risk_management/advanced_risk_manager.py</a></td>
                <td class="name left"><a href="z_bd6a586f8a803a04_advanced_risk_manager_py.html#t112"><data value='calculate_cvar'>AdvancedRiskManager.calculate_cvar</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bd6a586f8a803a04_advanced_risk_manager_py.html#t141">risk_management/advanced_risk_manager.py</a></td>
                <td class="name left"><a href="z_bd6a586f8a803a04_advanced_risk_manager_py.html#t141"><data value='stress_test'>AdvancedRiskManager.stress_test</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bd6a586f8a803a04_advanced_risk_manager_py.html#t202">risk_management/advanced_risk_manager.py</a></td>
                <td class="name left"><a href="z_bd6a586f8a803a04_advanced_risk_manager_py.html#t202"><data value='calculate_correlation_impact'>AdvancedRiskManager._calculate_correlation_impact</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bd6a586f8a803a04_advanced_risk_manager_py.html#t219">risk_management/advanced_risk_manager.py</a></td>
                <td class="name left"><a href="z_bd6a586f8a803a04_advanced_risk_manager_py.html#t219"><data value='risk_attribution'>AdvancedRiskManager.risk_attribution</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bd6a586f8a803a04_advanced_risk_manager_py.html#t268">risk_management/advanced_risk_manager.py</a></td>
                <td class="name left"><a href="z_bd6a586f8a803a04_advanced_risk_manager_py.html#t268"><data value='attribute_market_risk'>AdvancedRiskManager._attribute_market_risk</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bd6a586f8a803a04_advanced_risk_manager_py.html#t285">risk_management/advanced_risk_manager.py</a></td>
                <td class="name left"><a href="z_bd6a586f8a803a04_advanced_risk_manager_py.html#t285"><data value='attribute_idiosyncratic_risk'>AdvancedRiskManager._attribute_idiosyncratic_risk</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bd6a586f8a803a04_advanced_risk_manager_py.html#t319">risk_management/advanced_risk_manager.py</a></td>
                <td class="name left"><a href="z_bd6a586f8a803a04_advanced_risk_manager_py.html#t319"><data value='attribute_pair_specific_risk'>AdvancedRiskManager._attribute_pair_specific_risk</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bd6a586f8a803a04_advanced_risk_manager_py.html#t352">risk_management/advanced_risk_manager.py</a></td>
                <td class="name left"><a href="z_bd6a586f8a803a04_advanced_risk_manager_py.html#t352"><data value='attribute_liquidity_risk'>AdvancedRiskManager._attribute_liquidity_risk</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bd6a586f8a803a04_advanced_risk_manager_py.html#t381">risk_management/advanced_risk_manager.py</a></td>
                <td class="name left"><a href="z_bd6a586f8a803a04_advanced_risk_manager_py.html#t381"><data value='attribute_model_risk'>AdvancedRiskManager._attribute_model_risk</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bd6a586f8a803a04_advanced_risk_manager_py.html#t418">risk_management/advanced_risk_manager.py</a></td>
                <td class="name left"><a href="z_bd6a586f8a803a04_advanced_risk_manager_py.html#t418"><data value='generate_risk_report'>AdvancedRiskManager.generate_risk_report</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bd6a586f8a803a04_advanced_risk_manager_py.html#t463">risk_management/advanced_risk_manager.py</a></td>
                <td class="name left"><a href="z_bd6a586f8a803a04_advanced_risk_manager_py.html#t463"><data value='calculate_risk_score'>AdvancedRiskManager._calculate_risk_score</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bd6a586f8a803a04_advanced_risk_manager_py.html#t501">risk_management/advanced_risk_manager.py</a></td>
                <td class="name left"><a href="z_bd6a586f8a803a04_advanced_risk_manager_py.html#t501"><data value='get_risk_level'>AdvancedRiskManager._get_risk_level</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bd6a586f8a803a04_advanced_risk_manager_py.html#t514">risk_management/advanced_risk_manager.py</a></td>
                <td class="name left"><a href="z_bd6a586f8a803a04_advanced_risk_manager_py.html#t514"><data value='generate_risk_recommendations'>AdvancedRiskManager._generate_risk_recommendations</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bd6a586f8a803a04_advanced_risk_manager_py.html#t547">risk_management/advanced_risk_manager.py</a></td>
                <td class="name left"><a href="z_bd6a586f8a803a04_advanced_risk_manager_py.html#t547"><data value='get_advanced_risk_manager'>get_advanced_risk_manager</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bd6a586f8a803a04_advanced_risk_manager_py.html">risk_management/advanced_risk_manager.py</a></td>
                <td class="name left"><a href="z_bd6a586f8a803a04_advanced_risk_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>29</td>
                <td>0</td>
                <td>13</td>
                <td class="right" data-ratio="29 29">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html#t30">src/alert_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html#t30"><data value='init__'>AlertManager.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html#t44">src/alert_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html#t44"><data value='load_alert_config'>AlertManager._load_alert_config</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html#t68">src/alert_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html#t68"><data value='send_alert'>AlertManager.send_alert</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html#t108">src/alert_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html#t108"><data value='format_alert_message'>AlertManager._format_alert_message</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html#t135">src/alert_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html#t135"><data value='send_telegram_alert'>AlertManager._send_telegram_alert</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html#t168">src/alert_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html#t168"><data value='send_discord_alert'>AlertManager._send_discord_alert</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html#t207">src/alert_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html#t207"><data value='log_alert_to_file'>AlertManager._log_alert_to_file</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html#t242">src/alert_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html#t242"><data value='update_health_status'>AlertManager.update_health_status</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html#t253">src/alert_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html#t253"><data value='get_health_status'>AlertManager.get_health_status</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html#t272">src/alert_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html#t272"><data value='send_startup_alert'>AlertManager.send_startup_alert</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html#t284">src/alert_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html#t284"><data value='send_shutdown_alert'>AlertManager.send_shutdown_alert</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html#t299">src/alert_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html#t299"><data value='send_trade_alert'>AlertManager.send_trade_alert</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html#t317">src/alert_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html#t317"><data value='send_error_alert'>AlertManager.send_error_alert</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html#t326">src/alert_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html#t326"><data value='send_critical_alert'>AlertManager.send_critical_alert</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html#t340">src/alert_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html#t340"><data value='get_alert_manager'>get_alert_manager</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html#t348">src/alert_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html#t348"><data value='send_alert'>send_alert</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html">src/alert_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>32</td>
                <td>0</td>
                <td>8</td>
                <td class="right" data-ratio="32 32">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_data_handler_py.html#t28">src/async_data_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_data_handler_py.html#t28"><data value='init__'>AsyncDataHandler.__init__</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_data_handler_py.html#t53">src/async_data_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_data_handler_py.html#t53"><data value='initialize'>AsyncDataHandler.initialize</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_data_handler_py.html#t78">src/async_data_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_data_handler_py.html#t78"><data value='cleanup'>AsyncDataHandler.cleanup</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_data_handler_py.html#t101">src/async_data_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_data_handler_py.html#t101"><data value='fetch_ohlcv_async'>AsyncDataHandler.fetch_ohlcv_async</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_data_handler_py.html#t136">src/async_data_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_data_handler_py.html#t136"><data value='process_ohlcv_data'>AsyncDataHandler._process_ohlcv_data</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_data_handler_py.html#t146">src/async_data_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_data_handler_py.html#t146"><data value='fetch_multiple_ohlcv_async'>AsyncDataHandler.fetch_multiple_ohlcv_async</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_data_handler_py.html#t194">src/async_data_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_data_handler_py.html#t194"><data value='fetch_ticker_async'>AsyncDataHandler.fetch_ticker_async</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_data_handler_py.html#t209">src/async_data_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_data_handler_py.html#t209"><data value='fetch_multiple_tickers_async'>AsyncDataHandler.fetch_multiple_tickers_async</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_data_handler_py.html#t244">src/async_data_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_data_handler_py.html#t244"><data value='calculate_features_async'>AsyncDataHandler.calculate_features_async</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_data_handler_py.html#t286">src/async_data_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_data_handler_py.html#t286"><data value='calculate_pair_features'>AsyncDataHandler._calculate_pair_features</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_data_handler_py.html#t339">src/async_data_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_data_handler_py.html#t339"><data value='is_cache_valid'>AsyncDataHandler._is_cache_valid</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_data_handler_py.html#t349">src/async_data_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_data_handler_py.html#t349"><data value='clear_cache'>AsyncDataHandler.clear_cache</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_data_handler_py.html#t355">src/async_data_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_data_handler_py.html#t355"><data value='get_performance_stats'>AsyncDataHandler.get_performance_stats</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_data_handler_py.html#t366">src/async_data_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_data_handler_py.html#t366"><data value='calculate_cache_hit_rate'>AsyncDataHandler._calculate_cache_hit_rate</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_data_handler_py.html#t375">src/async_data_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_data_handler_py.html#t375"><data value='get_async_data_handler'>get_async_data_handler</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_data_handler_py.html#t386">src/async_data_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_data_handler_py.html#t386"><data value='test_async_data_handler'>test_async_data_handler</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>33</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_data_handler_py.html">src/async_data_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_data_handler_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>33</td>
                <td>0</td>
                <td>3</td>
                <td class="right" data-ratio="33 33">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t25">src/async_resource_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t25"><data value='init__'>AsyncDatabaseConnectionManager.__init__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t35">src/async_resource_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t35"><data value='component_name'>AsyncDatabaseConnectionManager.component_name</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t38">src/async_resource_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t38"><data value='create_connection_pool'>AsyncDatabaseConnectionManager._create_connection_pool</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t61">src/async_resource_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t61"><data value='get_connection'>AsyncDatabaseConnectionManager.get_connection</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t110">src/async_resource_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t110"><data value='close_pool'>AsyncDatabaseConnectionManager.close_pool</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t144">src/async_resource_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t144"><data value='shutdown'>AsyncDatabaseConnectionManager.shutdown</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t160">src/async_resource_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t160"><data value='get_stats'>AsyncDatabaseConnectionManager.get_stats</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t171">src/async_resource_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t171"><data value='init__'>SystemMonitor.__init__</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t188">src/async_resource_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t188"><data value='component_name'>SystemMonitor.component_name</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t191">src/async_resource_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t191"><data value='start_monitoring'>SystemMonitor.start_monitoring</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t201">src/async_resource_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t201"><data value='stop_monitoring'>SystemMonitor.stop_monitoring</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t212">src/async_resource_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t212"><data value='monitoring_loop'>SystemMonitor._monitoring_loop</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t239">src/async_resource_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t239"><data value='collect_system_metrics'>SystemMonitor._collect_system_metrics</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t244">src/async_resource_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t244"><data value='get_system_info'>SystemMonitor._collect_system_metrics.get_system_info</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t255">src/async_resource_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t255"><data value='check_thresholds'>SystemMonitor._check_thresholds</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t275">src/async_resource_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t275"><data value='record_metrics'>SystemMonitor._record_metrics</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t291">src/async_resource_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t291"><data value='shutdown'>SystemMonitor.shutdown</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t300">src/async_resource_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t300"><data value='get_current_metrics'>SystemMonitor.get_current_metrics</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t306">src/async_resource_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t306"><data value='get_metrics_history'>SystemMonitor.get_metrics_history</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t319">src/async_resource_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t319"><data value='init__'>AsyncResourceManager.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t334">src/async_resource_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t334"><data value='component_name'>AsyncResourceManager.component_name</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t337">src/async_resource_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t337"><data value='start'>AsyncResourceManager.start</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t342">src/async_resource_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t342"><data value='shutdown'>AsyncResourceManager.shutdown</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t372">src/async_resource_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t372"><data value='get_db_connection'>AsyncResourceManager.get_db_connection</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t377">src/async_resource_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t377"><data value='get_system_metrics'>AsyncResourceManager.get_system_metrics</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t381">src/async_resource_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t381"><data value='get_resource_stats'>AsyncResourceManager.get_resource_stats</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t394">src/async_resource_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t394"><data value='get_async_resource_manager'>get_async_resource_manager</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t412">src/async_resource_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t412"><data value='get_db_connection'>get_db_connection</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t419">src/async_resource_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t419"><data value='main'>main</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html">src/async_resource_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>49</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="49 49">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t56">src/async_state_persistence.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t56"><data value='post_init__'>PersistenceTask.__post_init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t60">src/async_state_persistence.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t60"><data value='lt__'>PersistenceTask.__lt__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t68">src/async_state_persistence.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t68"><data value='init__'>AsyncStatePersistenceManager.__init__</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t110">src/async_state_persistence.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t110"><data value='component_name'>AsyncStatePersistenceManager.component_name</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t113">src/async_state_persistence.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t113"><data value='start'>AsyncStatePersistenceManager.start</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t137">src/async_state_persistence.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t137"><data value='init_database'>AsyncStatePersistenceManager._init_database</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t191">src/async_state_persistence.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t191"><data value='register_event_listeners'>AsyncStatePersistenceManager._register_event_listeners</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t211">src/async_state_persistence.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t211"><data value='save_strategy_state'>AsyncStatePersistenceManager.save_strategy_state</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t227">src/async_state_persistence.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t227"><data value='save_portfolio_allocation'>AsyncStatePersistenceManager.save_portfolio_allocation</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t245">src/async_state_persistence.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t245"><data value='save_trade_record'>AsyncStatePersistenceManager.save_trade_record</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t263">src/async_state_persistence.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t263"><data value='save_system_snapshot'>AsyncStatePersistenceManager.save_system_snapshot</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t279">src/async_state_persistence.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t279"><data value='queue_task'>AsyncStatePersistenceManager._queue_task</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t308">src/async_state_persistence.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t308"><data value='writer_loop'>AsyncStatePersistenceManager._writer_loop</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t331">src/async_state_persistence.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t331"><data value='flush_loop'>AsyncStatePersistenceManager._flush_loop</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t345">src/async_state_persistence.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t345"><data value='process_batch'>AsyncStatePersistenceManager._process_batch</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t391">src/async_state_persistence.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t391"><data value='process_table_tasks'>AsyncStatePersistenceManager._process_table_tasks</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t409">src/async_state_persistence.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t409"><data value='process_strategy_states'>AsyncStatePersistenceManager._process_strategy_states</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t431">src/async_state_persistence.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t431"><data value='process_portfolio_allocations'>AsyncStatePersistenceManager._process_portfolio_allocations</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t454">src/async_state_persistence.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t454"><data value='process_trade_records'>AsyncStatePersistenceManager._process_trade_records</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t475">src/async_state_persistence.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t475"><data value='process_system_snapshots'>AsyncStatePersistenceManager._process_system_snapshots</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t495">src/async_state_persistence.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t495"><data value='serialize_data'>AsyncStatePersistenceManager._serialize_data</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t500">src/async_state_persistence.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t500"><data value='json_serializer'>AsyncStatePersistenceManager._json_serializer</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t504">src/async_state_persistence.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t504"><data value='pickle_serializer'>AsyncStatePersistenceManager._pickle_serializer</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t510">src/async_state_persistence.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t510"><data value='compressed_pickle_serializer'>AsyncStatePersistenceManager._compressed_pickle_serializer</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t517">src/async_state_persistence.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t517"><data value='on_strategy_state_changed'>AsyncStatePersistenceManager._on_strategy_state_changed</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t529">src/async_state_persistence.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t529"><data value='on_portfolio_rebalanced'>AsyncStatePersistenceManager._on_portfolio_rebalanced</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t545">src/async_state_persistence.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t545"><data value='on_trade_executed'>AsyncStatePersistenceManager._on_trade_executed</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t558">src/async_state_persistence.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t558"><data value='flush_all'>AsyncStatePersistenceManager.flush_all</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t567">src/async_state_persistence.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t567"><data value='get_persistence_stats'>AsyncStatePersistenceManager.get_persistence_stats</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t574">src/async_state_persistence.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t574"><data value='shutdown'>AsyncStatePersistenceManager.shutdown</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t611">src/async_state_persistence.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t611"><data value='get_async_persistence_manager'>get_async_persistence_manager</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t627">src/async_state_persistence.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t627"><data value='main'>main</data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html">src/async_state_persistence.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>72</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="72 72">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_backtesting_py.html#t17">src/backtesting.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_backtesting_py.html#t17"><data value='init__'>BacktestEngine.__init__</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_backtesting_py.html#t45">src/backtesting.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_backtesting_py.html#t45"><data value='load_historical_data'>BacktestEngine.load_historical_data</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_backtesting_py.html#t67">src/backtesting.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_backtesting_py.html#t67"><data value='prepare_data'>BacktestEngine.prepare_data</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_backtesting_py.html#t90">src/backtesting.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_backtesting_py.html#t90"><data value='run_backtest'>BacktestEngine.run_backtest</data></a></td>
                <td>56</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="0 56">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_backtesting_py.html#t184">src/backtesting.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_backtesting_py.html#t184"><data value='calculate_position_pnl'>BacktestEngine._calculate_position_pnl</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_backtesting_py.html#t214">src/backtesting.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_backtesting_py.html#t214"><data value='execute_exit'>BacktestEngine._execute_exit</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_backtesting_py.html#t248">src/backtesting.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_backtesting_py.html#t248"><data value='calculate_performance_metrics'>BacktestEngine._calculate_performance_metrics</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_backtesting_py.html#t308">src/backtesting.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_backtesting_py.html#t308"><data value='generate_report'>BacktestEngine.generate_report</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_backtesting_py.html#t350">src/backtesting.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_backtesting_py.html#t350"><data value='plot_results'>BacktestEngine.plot_results</data></a></td>
                <td>41</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="0 41">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_backtesting_py.html#t409">src/backtesting.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_backtesting_py.html#t409"><data value='save_results'>BacktestEngine.save_results</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_backtesting_py.html">src/backtesting.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_backtesting_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_loader_py.html#t22">src/config_loader.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_loader_py.html#t22"><data value='load_env_file'>ConfigLoader.load_env_file</data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_loader_py.html#t82">src/config_loader.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_loader_py.html#t82"><data value='load_json_config'>ConfigLoader.load_json_config</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_loader_py.html#t115">src/config_loader.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_loader_py.html#t115"><data value='merge_configs'>ConfigLoader.merge_configs</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_loader_py.html#t173">src/config_loader.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_loader_py.html#t173"><data value='set_nested_config'>ConfigLoader._set_nested_config</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_loader_py.html#t187">src/config_loader.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_loader_py.html#t187"><data value='load_complete_config'>ConfigLoader.load_complete_config</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_loader_py.html#t217">src/config_loader.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_loader_py.html#t217"><data value='validate_required_config'>ConfigLoader.validate_required_config</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_loader_py.html#t259">src/config_loader.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_loader_py.html#t259"><data value='get_nested_config'>ConfigLoader._get_nested_config</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_loader_py.html#t271">src/config_loader.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_loader_py.html#t271"><data value='create_env_template'>ConfigLoader.create_env_template</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_loader_py.html">src/config_loader.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_loader_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>13</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t117">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t117"><data value='validate_trading_pairs'>TradingConfig.validate_trading_pairs</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t133">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t133"><data value='validate_half_life_range'>TradingConfig.validate_half_life_range</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t157">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t157"><data value='validate_db_path'>DatabaseConfig.validate_db_path</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t193">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t193"><data value='validate_log_path'>MonitoringConfig.validate_log_path</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t229">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t229"><data value='validate_retry_delays'>PerformanceConfig.validate_retry_delays</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t292">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t292"><data value='validate_config_consistency'>ComprehensiveConfig.validate_config_consistency</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t304">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t304"><data value='save_to_file'>ComprehensiveConfig.save_to_file</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t336">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t336"><data value='load_and_validate_config'>ConfigValidator.load_and_validate_config</data></a></td>
                <td>43</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="0 43">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t398">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t398"><data value='validate_runtime_config'>ConfigValidator.validate_runtime_config</data></a></td>
                <td>41</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="0 41">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t483">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t483"><data value='get_validated_config'>get_validated_config</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>171</td>
                <td>10</td>
                <td>15</td>
                <td class="right" data-ratio="161 171">94%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_data_handler_py.html#t18">src/data_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_data_handler_py.html#t18"><data value='init__'>DataHandler.__init__</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_data_handler_py.html#t46">src/data_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_data_handler_py.html#t46"><data value='fetch_ohlcv_data'>DataHandler.fetch_ohlcv_data</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_data_handler_py.html#t91">src/data_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_data_handler_py.html#t91"><data value='fetch_current_prices'>DataHandler.fetch_current_prices</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_data_handler_py.html#t122">src/data_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_data_handler_py.html#t122"><data value='load_backtest_data'>DataHandler.load_backtest_data</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_data_handler_py.html#t139">src/data_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_data_handler_py.html#t139"><data value='update_data'>DataHandler.update_data</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_data_handler_py.html#t146">src/data_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_data_handler_py.html#t146"><data value='update_live_data'>DataHandler._update_live_data</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_data_handler_py.html#t195">src/data_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_data_handler_py.html#t195"><data value='update_backtest_data'>DataHandler._update_backtest_data</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_data_handler_py.html#t236">src/data_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_data_handler_py.html#t236"><data value='has_more_data'>DataHandler.has_more_data</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_data_handler_py.html#t242">src/data_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_data_handler_py.html#t242"><data value='get_current_prices'>DataHandler.get_current_prices</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_data_handler_py.html#t254">src/data_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_data_handler_py.html#t254"><data value='get_current_spread'>DataHandler.get_current_spread</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_data_handler_py.html#t260">src/data_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_data_handler_py.html#t260"><data value='get_current_zscore'>DataHandler.get_current_zscore</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_data_handler_py.html#t266">src/data_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_data_handler_py.html#t266"><data value='get_historical_data'>DataHandler.get_historical_data</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_data_handler_py.html#t273">src/data_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_data_handler_py.html#t273"><data value='get_spread_statistics'>DataHandler.get_spread_statistics</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_data_handler_py.html#t299">src/data_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_data_handler_py.html#t299"><data value='save_data_to_csv'>DataHandler.save_data_to_csv</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_data_handler_py.html#t322">src/data_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_data_handler_py.html#t322"><data value='load_data_from_csv'>DataHandler.load_data_from_csv</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_data_handler_py.html">src/data_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_data_handler_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>29</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 29">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html#t27">src/database_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html#t27"><data value='init__'>DatabaseManager.__init__</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html#t46">src/database_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html#t46"><data value='start_batch_processor'>DatabaseManager._start_batch_processor</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html#t53">src/database_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html#t53"><data value='batch_processor'>DatabaseManager._batch_processor</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html#t83">src/database_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html#t83"><data value='execute_batch_operations'>DatabaseManager._execute_batch_operations</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html#t104">src/database_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html#t104"><data value='get_connection'>DatabaseManager.get_connection</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html#t117">src/database_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html#t117"><data value='initialize_database'>DatabaseManager._initialize_database</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html#t249">src/database_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html#t249"><data value='record_trade'>DatabaseManager.record_trade</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html#t290">src/database_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html#t290"><data value='get_trades'>DatabaseManager.get_trades</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html#t334">src/database_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html#t334"><data value='update_pair_performance'>DatabaseManager.update_pair_performance</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html#t408">src/database_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html#t408"><data value='get_pair_performance'>DatabaseManager.get_pair_performance</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html#t432">src/database_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html#t432"><data value='record_portfolio_snapshot'>DatabaseManager.record_portfolio_snapshot</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html#t467">src/database_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html#t467"><data value='get_portfolio_history'>DatabaseManager.get_portfolio_history</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html#t490">src/database_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html#t490"><data value='record_alert'>DatabaseManager.record_alert</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html#t518">src/database_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html#t518"><data value='get_recent_alerts'>DatabaseManager.get_recent_alerts</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html#t543">src/database_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html#t543"><data value='cleanup_old_data'>DatabaseManager.cleanup_old_data</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html#t574">src/database_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html#t574"><data value='get_database_stats'>DatabaseManager.get_database_stats</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html#t601">src/database_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html#t601"><data value='get_database_manager'>get_database_manager</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html">src/database_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>34</td>
                <td>0</td>
                <td>11</td>
                <td class="right" data-ratio="34 34">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t24">src/dynamic_config.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t24"><data value='init__'>ConfigChangeHandler.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t28">src/dynamic_config.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t28"><data value='on_modified'>ConfigChangeHandler.on_modified</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t37">src/dynamic_config.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t37"><data value='init__'>DynamicConfigManager.__init__</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t62">src/dynamic_config.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t62"><data value='load_config'>DynamicConfigManager._load_config</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t102">src/dynamic_config.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t102"><data value='reload_config'>DynamicConfigManager._reload_config</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t108">src/dynamic_config.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t108"><data value='start_file_watcher'>DynamicConfigManager._start_file_watcher</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t125">src/dynamic_config.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t125"><data value='stop_file_watcher'>DynamicConfigManager._stop_file_watcher</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t136">src/dynamic_config.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t136"><data value='trigger_change_callbacks'>DynamicConfigManager._trigger_change_callbacks</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t144">src/dynamic_config.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t144"><data value='get_config'>DynamicConfigManager.get_config</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t149">src/dynamic_config.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t149"><data value='get_value'>DynamicConfigManager.get_value</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t168">src/dynamic_config.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t168"><data value='set_value'>DynamicConfigManager.set_value</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t210">src/dynamic_config.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t210"><data value='save_config'>DynamicConfigManager._save_config</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t233">src/dynamic_config.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t233"><data value='add_change_callback'>DynamicConfigManager.add_change_callback</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t238">src/dynamic_config.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t238"><data value='remove_change_callback'>DynamicConfigManager.remove_change_callback</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t244">src/dynamic_config.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t244"><data value='execute_remote_command'>DynamicConfigManager.execute_remote_command</data></a></td>
                <td>24</td>
                <td>24</td>
                <td>0</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t304">src/dynamic_config.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t304"><data value='get_status'>DynamicConfigManager.get_status</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t314">src/dynamic_config.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t314"><data value='shutdown'>DynamicConfigManager.shutdown</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t327">src/dynamic_config.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t327"><data value='get_dynamic_config_manager'>get_dynamic_config_manager</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html">src/dynamic_config.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>32</td>
                <td>0</td>
                <td>14</td>
                <td class="right" data-ratio="32 32">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t67">src/enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t67"><data value='init__'>RetryMetrics.__init__</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t77">src/enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t77"><data value='record_call'>RetryMetrics.record_call</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t92">src/enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t92"><data value='get_statistics'>RetryMetrics.get_statistics</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t112">src/enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t112"><data value='init__'>EnhancedCircuitBreaker.__init__</data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t130">src/enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t130"><data value='can_execute'>EnhancedCircuitBreaker.can_execute</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t153">src/enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t153"><data value='record_success'>EnhancedCircuitBreaker.record_success</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t199">src/enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t199"><data value='record_failure'>EnhancedCircuitBreaker.record_failure</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t244">src/enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t244"><data value='should_attempt_reset'>EnhancedCircuitBreaker._should_attempt_reset</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t249">src/enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t249"><data value='time_until_reset'>EnhancedCircuitBreaker._time_until_reset</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t256">src/enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t256"><data value='get_state'>EnhancedCircuitBreaker.get_state</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t275">src/enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t275"><data value='init__'>EnhancedRetryHandler.__init__</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t282">src/enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t282"><data value='calculate_delay'>EnhancedRetryHandler._calculate_delay</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t306">src/enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t306"><data value='is_permanent_error'>EnhancedRetryHandler._is_permanent_error</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t361">src/enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t361"><data value='should_trigger_circuit_breaker'>EnhancedRetryHandler._should_trigger_circuit_breaker</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t370">src/enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t370"><data value='fibonacci'>EnhancedRetryHandler._fibonacci</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t379">src/enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t379"><data value='retry_sync'>EnhancedRetryHandler.retry_sync</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t382">src/enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t382"><data value='wrapper'>EnhancedRetryHandler.retry_sync.wrapper</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t386">src/enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t386"><data value='retry_async'>EnhancedRetryHandler.retry_async</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t389">src/enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t389"><data value='wrapper'>EnhancedRetryHandler.retry_async.wrapper</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t393">src/enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t393"><data value='execute_with_retry'>EnhancedRetryHandler._execute_with_retry</data></a></td>
                <td>41</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="0 41">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t457">src/enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t457"><data value='execute_with_retry_async'>EnhancedRetryHandler._execute_with_retry_async</data></a></td>
                <td>31</td>
                <td>31</td>
                <td>0</td>
                <td class="right" data-ratio="0 31">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t509">src/enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t509"><data value='get_statistics'>EnhancedRetryHandler.get_statistics</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t523">src/enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t523"><data value='enhanced_retry'>enhanced_retry</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t549">src/enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t549"><data value='enhanced_async_retry'>enhanced_async_retry</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t580">src/enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t580"><data value='test_function'>test_function</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>4</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t587">src/enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t587"><data value='test_async_function'>test_async_function</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>4</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t601">src/enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t601"><data value='test_async'>test_async</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>5</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html">src/enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>60</td>
                <td>0</td>
                <td>13</td>
                <td class="right" data-ratio="60 60">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t20">src/exchange_factory.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t20"><data value='create_market_order'>ExchangeGateway.create_market_order</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t24">src/exchange_factory.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t24"><data value='fetch_ticker'>ExchangeGateway.fetch_ticker</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t28">src/exchange_factory.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t28"><data value='fetch_ohlcv'>ExchangeGateway.fetch_ohlcv</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t32">src/exchange_factory.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t32"><data value='fetch_balance'>ExchangeGateway.fetch_balance</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t40">src/exchange_factory.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t40"><data value='init__'>CCXTExchangeGateway.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t43">src/exchange_factory.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t43"><data value='create_market_order'>CCXTExchangeGateway.create_market_order</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t47">src/exchange_factory.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t47"><data value='fetch_ticker'>CCXTExchangeGateway.fetch_ticker</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t51">src/exchange_factory.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t51"><data value='fetch_ohlcv'>CCXTExchangeGateway.fetch_ohlcv</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t55">src/exchange_factory.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t55"><data value='fetch_balance'>CCXTExchangeGateway.fetch_balance</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t63">src/exchange_factory.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t63"><data value='init__'>MockExchangeGateway.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t67">src/exchange_factory.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t67"><data value='create_market_order'>MockExchangeGateway.create_market_order</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t85">src/exchange_factory.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t85"><data value='fetch_ticker'>MockExchangeGateway.fetch_ticker</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t95">src/exchange_factory.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t95"><data value='fetch_ohlcv'>MockExchangeGateway.fetch_ohlcv</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t101">src/exchange_factory.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t101"><data value='fetch_balance'>MockExchangeGateway.fetch_balance</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t114">src/exchange_factory.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t114"><data value='create_exchange_gateway'>ExchangeFactory.create_exchange_gateway</data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t187">src/exchange_factory.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t187"><data value='get_supported_exchanges'>ExchangeFactory.get_supported_exchanges</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t192">src/exchange_factory.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t192"><data value='validate_exchange_config'>ExchangeFactory.validate_exchange_config</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html">src/exchange_factory.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>25</td>
                <td>0</td>
                <td>19</td>
                <td class="right" data-ratio="25 25">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t68">src/global_event_bus.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t68"><data value='to_dict'>Event.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t80">src/global_event_bus.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t80"><data value='from_dict'>Event.from_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t95">src/global_event_bus.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t95"><data value='init__'>EventSubscription.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t104">src/global_event_bus.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t104"><data value='matches'>EventSubscription.matches</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t118">src/global_event_bus.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t118"><data value='init__'>GlobalEventBus.__init__</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t142">src/global_event_bus.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t142"><data value='start'>GlobalEventBus.start</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t152">src/global_event_bus.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t152"><data value='stop'>GlobalEventBus.stop</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t163">src/global_event_bus.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t163"><data value='subscribe'>GlobalEventBus.subscribe</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t186">src/global_event_bus.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t186"><data value='unsubscribe'>GlobalEventBus.unsubscribe</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t197">src/global_event_bus.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t197"><data value='publish'>GlobalEventBus.publish</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t219">src/global_event_bus.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t219"><data value='publish_sync'>GlobalEventBus.publish_sync</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t230">src/global_event_bus.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t230"><data value='process_events'>GlobalEventBus._process_events</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t252">src/global_event_bus.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t252"><data value='dispatch_event'>GlobalEventBus._dispatch_event</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t271">src/global_event_bus.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t271"><data value='safe_callback'>GlobalEventBus._safe_callback</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t283">src/global_event_bus.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t283"><data value='add_to_history'>GlobalEventBus._add_to_history</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t291">src/global_event_bus.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t291"><data value='get_stats'>GlobalEventBus.get_stats</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t300">src/global_event_bus.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t300"><data value='get_recent_events'>GlobalEventBus.get_recent_events</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t316">src/global_event_bus.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t316"><data value='get_global_event_bus'>get_global_event_bus</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t324">src/global_event_bus.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t324"><data value='publish_event'>publish_event</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t347">src/global_event_bus.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t347"><data value='test_event_bus'>test_event_bus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>30</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t354">src/global_event_bus.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t354"><data value='callback'>test_event_bus.callback</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html">src/global_event_bus.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>64</td>
                <td>0</td>
                <td>4</td>
                <td class="right" data-ratio="64 64">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t24">src/graceful_shutdown.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t24"><data value='shutdown'>ShutdownComponent.shutdown</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>5</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t33">src/graceful_shutdown.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t33"><data value='component_name'>ShutdownComponent.component_name</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t41">src/graceful_shutdown.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t41"><data value='init__'>GracefulShutdownManager.__init__</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t53">src/graceful_shutdown.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t53"><data value='setup_signal_handlers'>GracefulShutdownManager._setup_signal_handlers</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t67">src/graceful_shutdown.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t67"><data value='signal_handler'>GracefulShutdownManager._signal_handler</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t76">src/graceful_shutdown.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t76"><data value='register_component'>GracefulShutdownManager.register_component</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t82">src/graceful_shutdown.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t82"><data value='unregister_component'>GracefulShutdownManager.unregister_component</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t88">src/graceful_shutdown.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t88"><data value='register_callback'>GracefulShutdownManager.register_callback</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t93">src/graceful_shutdown.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t93"><data value='shutdown'>GracefulShutdownManager.shutdown</data></a></td>
                <td>36</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 36">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t157">src/graceful_shutdown.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t157"><data value='shutdown_component_with_timeout'>GracefulShutdownManager._shutdown_component_with_timeout</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t175">src/graceful_shutdown.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t175"><data value='execute_shutdown_callbacks'>GracefulShutdownManager._execute_shutdown_callbacks</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t192">src/graceful_shutdown.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t192"><data value='wait_for_shutdown'>GracefulShutdownManager.wait_for_shutdown</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t196">src/graceful_shutdown.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t196"><data value='is_shutdown_requested'>GracefulShutdownManager.is_shutdown_requested</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t205">src/graceful_shutdown.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t205"><data value='get_shutdown_manager'>get_shutdown_manager</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t214">src/graceful_shutdown.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t214"><data value='register_for_shutdown'>register_for_shutdown</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t218">src/graceful_shutdown.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t218"><data value='new_init'>register_for_shutdown.new_init</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t231">src/graceful_shutdown.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t231"><data value='init__'>ExampleShutdownComponent.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t237">src/graceful_shutdown.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t237"><data value='component_name'>ExampleShutdownComponent.component_name</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t240">src/graceful_shutdown.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t240"><data value='shutdown'>ExampleShutdownComponent.shutdown</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t253">src/graceful_shutdown.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t253"><data value='main'>main</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html">src/graceful_shutdown.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>31</td>
                <td>0</td>
                <td>7</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t34">src/health_server.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t34"><data value='get_portfolio_manager'>get_portfolio_manager</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t45">src/health_server.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t45"><data value='init__'>HealthServer.__init__</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t63">src/health_server.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t63"><data value='setup_routes'>HealthServer._setup_routes</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t67">src/health_server.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t67"><data value='health_check'>HealthServer._setup_routes.health_check</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t89">src/health_server.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t89"><data value='detailed_status'>HealthServer._setup_routes.detailed_status</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t121">src/health_server.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t121"><data value='control'>HealthServer._setup_routes.control</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t162">src/health_server.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t162"><data value='not_found'>HealthServer._setup_routes.not_found</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t170">src/health_server.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t170"><data value='portfolio_status'>HealthServer._setup_routes.portfolio_status</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t190">src/health_server.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t190"><data value='dashboard'>HealthServer._setup_routes.dashboard</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t205">src/health_server.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t205"><data value='prometheus_metrics'>HealthServer._setup_routes.prometheus_metrics</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t250">src/health_server.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t250"><data value='memory_status'>HealthServer._setup_routes.memory_status</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t299">src/health_server.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t299"><data value='performance_metrics'>HealthServer._setup_routes.performance_metrics</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t343">src/health_server.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t343"><data value='internal_error'>HealthServer._setup_routes.internal_error</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t349">src/health_server.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t349"><data value='start'>HealthServer.start</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t356">src/health_server.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t356"><data value='run_server'>HealthServer.start.run_server</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t375">src/health_server.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t375"><data value='stop'>HealthServer.stop</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t387">src/health_server.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t387"><data value='generate_dashboard_html'>HealthServer._generate_dashboard_html</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t587">src/health_server.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t587"><data value='get_server_info'>HealthServer.get_server_info</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t609">src/health_server.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t609"><data value='get_health_server'>get_health_server</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t617">src/health_server.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t617"><data value='start_health_server'>start_health_server</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html">src/health_server.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>31</td>
                <td>6</td>
                <td>15</td>
                <td class="right" data-ratio="25 31">81%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_high_leverage_risk_checker_py.html#t25">src/high_leverage_risk_checker.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_high_leverage_risk_checker_py.html#t25"><data value='init__'>HighLeverageRiskChecker.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_high_leverage_risk_checker_py.html#t30">src/high_leverage_risk_checker.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_high_leverage_risk_checker_py.html#t30"><data value='check_high_leverage_risks'>HighLeverageRiskChecker.check_high_leverage_risks</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_high_leverage_risk_checker_py.html#t75">src/high_leverage_risk_checker.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_high_leverage_risk_checker_py.html#t75"><data value='check_leverage_risks'>HighLeverageRiskChecker._check_leverage_risks</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_high_leverage_risk_checker_py.html#t107">src/high_leverage_risk_checker.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_high_leverage_risk_checker_py.html#t107"><data value='check_margin_mode_risks'>HighLeverageRiskChecker._check_margin_mode_risks</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_high_leverage_risk_checker_py.html#t127">src/high_leverage_risk_checker.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_high_leverage_risk_checker_py.html#t127"><data value='calculate_liquidation_scenarios'>HighLeverageRiskChecker._calculate_liquidation_scenarios</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_high_leverage_risk_checker_py.html#t163">src/high_leverage_risk_checker.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_high_leverage_risk_checker_py.html#t163"><data value='generate_safety_requirements'>HighLeverageRiskChecker._generate_safety_requirements</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_high_leverage_risk_checker_py.html#t190">src/high_leverage_risk_checker.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_high_leverage_risk_checker_py.html#t190"><data value='provide_risk_mitigation'>HighLeverageRiskChecker._provide_risk_mitigation</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_high_leverage_risk_checker_py.html#t210">src/high_leverage_risk_checker.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_high_leverage_risk_checker_py.html#t210"><data value='generate_risk_acknowledgment'>HighLeverageRiskChecker.generate_risk_acknowledgment</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_high_leverage_risk_checker_py.html#t250">src/high_leverage_risk_checker.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_high_leverage_risk_checker_py.html#t250"><data value='main'>main</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_high_leverage_risk_checker_py.html">src/high_leverage_risk_checker.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_high_leverage_risk_checker_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t42">src/integrated_trading_executor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t42"><data value='post_init__'>TradingSignal.__post_init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t57">src/integrated_trading_executor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t57"><data value='post_init__'>RiskCheckReport.__post_init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t65">src/integrated_trading_executor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t65"><data value='init__'>IntegratedTradingExecutor.__init__</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t96">src/integrated_trading_executor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t96"><data value='component_name'>IntegratedTradingExecutor.component_name</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t99">src/integrated_trading_executor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t99"><data value='execute_signal'>IntegratedTradingExecutor.execute_signal</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t171">src/integrated_trading_executor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t171"><data value='comprehensive_risk_check'>IntegratedTradingExecutor._comprehensive_risk_check</data></a></td>
                <td>50</td>
                <td>50</td>
                <td>0</td>
                <td class="right" data-ratio="0 50">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t266">src/integrated_trading_executor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t266"><data value='check_position_limits'>IntegratedTradingExecutor._check_position_limits</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t304">src/integrated_trading_executor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t304"><data value='check_daily_loss_limit'>IntegratedTradingExecutor._check_daily_loss_limit</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t336">src/integrated_trading_executor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t336"><data value='check_leverage_risk'>IntegratedTradingExecutor._check_leverage_risk</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t374">src/integrated_trading_executor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t374"><data value='check_market_conditions'>IntegratedTradingExecutor._check_market_conditions</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t408">src/integrated_trading_executor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t408"><data value='check_liquidity'>IntegratedTradingExecutor._check_liquidity</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t443">src/integrated_trading_executor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t443"><data value='apply_risk_mitigation'>IntegratedTradingExecutor._apply_risk_mitigation</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t460">src/integrated_trading_executor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t460"><data value='execute_trade'>IntegratedTradingExecutor._execute_trade</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t491">src/integrated_trading_executor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t491"><data value='record_risk_check'>IntegratedTradingExecutor._record_risk_check</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t515">src/integrated_trading_executor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t515"><data value='get_current_position'>IntegratedTradingExecutor._get_current_position</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t527">src/integrated_trading_executor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t527"><data value='get_daily_loss'>IntegratedTradingExecutor._get_daily_loss</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t533">src/integrated_trading_executor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t533"><data value='shutdown'>IntegratedTradingExecutor.shutdown</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t543">src/integrated_trading_executor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t543"><data value='get_execution_stats'>IntegratedTradingExecutor.get_execution_stats</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t552">src/integrated_trading_executor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t552"><data value='get_integrated_trading_executor'>get_integrated_trading_executor</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t568">src/integrated_trading_executor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t568"><data value='main'>main</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html">src/integrated_trading_executor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>57</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="57 57">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t45">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t45"><data value='post_init__'>CacheEntry.__post_init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t50">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t50"><data value='age'>CacheEntry.age</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t55">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t55"><data value='idle_time'>CacheEntry.idle_time</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t59">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t59"><data value='is_expired'>CacheEntry.is_expired</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t65">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t65"><data value='touch'>CacheEntry.touch</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t74">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t74"><data value='init__'>IntelligentCacheManager.__init__</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t114">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t114"><data value='component_name'>IntelligentCacheManager.component_name</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t117">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t117"><data value='start'>IntelligentCacheManager.start</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t130">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t130"><data value='register_event_listeners'>IntelligentCacheManager._register_event_listeners</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t144">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t144"><data value='start_websocket_monitoring'>IntelligentCacheManager._start_websocket_monitoring</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t159">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t159"><data value='websocket_monitor'>IntelligentCacheManager._websocket_monitor</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t202">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t202"><data value='get'>IntelligentCacheManager.get</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t223">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t223"><data value='set'>IntelligentCacheManager.set</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t264">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t264"><data value='invalidate'>IntelligentCacheManager.invalidate</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t277">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t277"><data value='invalidate_by_tag'>IntelligentCacheManager.invalidate_by_tag</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t292">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t292"><data value='invalidate_by_pattern'>IntelligentCacheManager.invalidate_by_pattern</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t310">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t310"><data value='register_invalidation_callback'>IntelligentCacheManager.register_invalidation_callback</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t317">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t317"><data value='call_invalidation_callbacks'>IntelligentCacheManager._call_invalidation_callbacks</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t329">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t329"><data value='subscribe_to_events'>IntelligentCacheManager._subscribe_to_events</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t341">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t341"><data value='remove_entry'>IntelligentCacheManager._remove_entry</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t352">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t352"><data value='add_to_tag_index'>IntelligentCacheManager._add_to_tag_index</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t359">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t359"><data value='remove_from_tag_index'>IntelligentCacheManager._remove_from_tag_index</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t369">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t369"><data value='evict_entries'>IntelligentCacheManager._evict_entries</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t389">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t389"><data value='cleanup_loop'>IntelligentCacheManager._cleanup_loop</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t401">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t401"><data value='cleanup_expired_entries'>IntelligentCacheManager._cleanup_expired_entries</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t415">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t415"><data value='on_market_data_updated'>IntelligentCacheManager._on_market_data_updated</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t434">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t434"><data value='on_order_filled'>IntelligentCacheManager._on_order_filled</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t451">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t451"><data value='get_cache_stats'>IntelligentCacheManager.get_cache_stats</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t465">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t465"><data value='clear'>IntelligentCacheManager.clear</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t477">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t477"><data value='shutdown'>IntelligentCacheManager.shutdown</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t513">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t513"><data value='get_intelligent_cache_manager'>get_intelligent_cache_manager</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t530">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t530"><data value='cache_get'>cache_get</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t536">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t536"><data value='cache_set'>cache_set</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t543">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t543"><data value='cache_invalidate'>cache_invalidate</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t549">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t549"><data value='main'>main</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>69</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="69 69">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_logging_config_py.html#t7">src/logging_config.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_logging_config_py.html#t7"><data value='setup_logging'>setup_logging</data></a></td>
                <td>33</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="32 33">97%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_logging_config_py.html#t71">src/logging_config.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_logging_config_py.html#t71"><data value='get_logger'>get_logger</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_logging_config_py.html">src/logging_config.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_logging_config_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t56">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t56"><data value='queue_time'>TaskMetrics.queue_time</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t63">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t63"><data value='total_time'>TaskMetrics.total_time</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t73">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t73"><data value='init__'>MultiExecutorManager.__init__</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t113">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t113"><data value='component_name'>MultiExecutorManager.component_name</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t116">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t116"><data value='create_executors'>MultiExecutorManager._create_executors</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t147">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t147"><data value='init_task_queues'>MultiExecutorManager._init_task_queues</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t152">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t152"><data value='start'>MultiExecutorManager.start</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t160">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t160"><data value='submit_task'>MultiExecutorManager.submit_task</data></a></td>
                <td>33</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t236">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t236"><data value='update_avg_times'>MultiExecutorManager._update_avg_times</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t256">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t256"><data value='submit_cpu_task'>MultiExecutorManager.submit_cpu_task</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t260">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t260"><data value='submit_io_task'>MultiExecutorManager.submit_io_task</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t264">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t264"><data value='submit_network_task'>MultiExecutorManager.submit_network_task</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t268">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t268"><data value='submit_database_task'>MultiExecutorManager.submit_database_task</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t272">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t272"><data value='monitor_loop'>MultiExecutorManager._monitor_loop</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t284">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t284"><data value='adaptive_adjustment'>MultiExecutorManager._adaptive_adjustment</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t312">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t312"><data value='suggest_executor_adjustment'>MultiExecutorManager._suggest_executor_adjustment</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t323">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t323"><data value='get_executor_size'>MultiExecutorManager._get_executor_size</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t330">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t330"><data value='get_executor_stats'>MultiExecutorManager.get_executor_stats</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t369">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t369"><data value='shutdown'>MultiExecutorManager.shutdown</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t403">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t403"><data value='get_multi_executor_manager'>get_multi_executor_manager</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t420">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t420"><data value='submit_cpu_task'>submit_cpu_task</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t426">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t426"><data value='submit_io_task'>submit_io_task</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t432">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t432"><data value='submit_network_task'>submit_network_task</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t438">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t438"><data value='submit_database_task'>submit_database_task</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t445">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t445"><data value='cpu_task'>cpu_task</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t448">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t448"><data value='wrapper'>cpu_task.wrapper</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t453">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t453"><data value='io_task'>io_task</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t456">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t456"><data value='wrapper'>io_task.wrapper</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t461">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t461"><data value='network_task'>network_task</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t464">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t464"><data value='wrapper'>network_task.wrapper</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t469">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t469"><data value='database_task'>database_task</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t472">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t472"><data value='wrapper'>database_task.wrapper</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t477">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t477"><data value='main'>main</data></a></td>
                <td>34</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t500">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t500"><data value='cpu_intensive_task'>main.cpu_intensive_task</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t515">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t515"><data value='io_intensive_task'>main.io_intensive_task</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>70</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="70 70">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t40">src/pair_trading_bot.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t40"><data value='init__'>PairTradingBot.__init__</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t83">src/pair_trading_bot.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t83"><data value='update_data_and_signals'>PairTradingBot.update_data_and_signals</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t105">src/pair_trading_bot.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t105"><data value='check_entry_signals'>PairTradingBot.check_entry_signals</data></a></td>
                <td>42</td>
                <td>42</td>
                <td>0</td>
                <td class="right" data-ratio="0 42">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t171">src/pair_trading_bot.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t171"><data value='check_exit_signals'>PairTradingBot.check_exit_signals</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t217">src/pair_trading_bot.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t217"><data value='update_stats_on_exit'>PairTradingBot._update_stats_on_exit</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t246">src/pair_trading_bot.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t246"><data value='enter_cooldown'>PairTradingBot._enter_cooldown</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t253">src/pair_trading_bot.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t253"><data value='run_single_iteration'>PairTradingBot.run_single_iteration</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t280">src/pair_trading_bot.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t280"><data value='perform_strategy_health_check'>PairTradingBot._perform_strategy_health_check</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t298">src/pair_trading_bot.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t298"><data value='trigger_adaptive_adjustment'>PairTradingBot._trigger_adaptive_adjustment</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t342">src/pair_trading_bot.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t342"><data value='reinitialize_for_new_pair'>PairTradingBot._reinitialize_for_new_pair</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t362">src/pair_trading_bot.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t362"><data value='record_trade_to_health_monitor'>PairTradingBot._record_trade_to_health_monitor</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t394">src/pair_trading_bot.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t394"><data value='get_status_report'>PairTradingBot.get_status_report</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t422">src/pair_trading_bot.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t422"><data value='get_statistics'>PairTradingBot.get_statistics</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t429">src/pair_trading_bot.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t429"><data value='get_win_rate'>PairTradingBot.get_win_rate</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t435">src/pair_trading_bot.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t435"><data value='get_profit_loss_ratio'>PairTradingBot.get_profit_loss_ratio</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html">src/pair_trading_bot.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>39</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="6 39">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_backtester_py.html#t26">src/portfolio_backtester.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_backtester_py.html#t26"><data value='init__'>PortfolioBacktester.__init__</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_backtester_py.html#t43">src/portfolio_backtester.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_backtester_py.html#t43"><data value='load_config'>PortfolioBacktester._load_config</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_backtester_py.html#t52">src/portfolio_backtester.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_backtester_py.html#t52"><data value='run_portfolio_backtest'>PortfolioBacktester.run_portfolio_backtest</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_backtester_py.html#t96">src/portfolio_backtester.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_backtester_py.html#t96"><data value='select_backtest_pairs'>PortfolioBacktester._select_backtest_pairs</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_backtester_py.html#t128">src/portfolio_backtester.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_backtester_py.html#t128"><data value='run_parallel_backtests'>PortfolioBacktester._run_parallel_backtests</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_backtester_py.html#t162">src/portfolio_backtester.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_backtester_py.html#t162"><data value='run_single_pair_backtest'>PortfolioBacktester._run_single_pair_backtest</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_backtester_py.html#t189">src/portfolio_backtester.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_backtester_py.html#t189"><data value='calculate_portfolio_results'>PortfolioBacktester._calculate_portfolio_results</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_backtester_py.html#t232">src/portfolio_backtester.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_backtester_py.html#t232"><data value='combine_returns'>PortfolioBacktester._combine_returns</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_backtester_py.html#t250">src/portfolio_backtester.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_backtester_py.html#t250"><data value='calculate_portfolio_equity'>PortfolioBacktester._calculate_portfolio_equity</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_backtester_py.html#t268">src/portfolio_backtester.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_backtester_py.html#t268"><data value='calculate_portfolio_stats'>PortfolioBacktester._calculate_portfolio_stats</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_backtester_py.html#t330">src/portfolio_backtester.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_backtester_py.html#t330"><data value='generate_backtest_report'>PortfolioBacktester._generate_backtest_report</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_backtester_py.html#t348">src/portfolio_backtester.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_backtester_py.html#t348"><data value='compare_portfolio_vs_individual'>PortfolioBacktester._compare_portfolio_vs_individual</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_backtester_py.html#t400">src/portfolio_backtester.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_backtester_py.html#t400"><data value='save_backtest_results'>PortfolioBacktester._save_backtest_results</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_backtester_py.html">src/portfolio_backtester.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_backtester_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>26</td>
                <td>17</td>
                <td>15</td>
                <td class="right" data-ratio="9 26">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t42">src/portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t42"><data value='get_correlation'>CorrelationMatrix.get_correlation</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t49">src/portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t49"><data value='get_max_correlation'>CorrelationMatrix.get_max_correlation</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t61">src/portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t61"><data value='init__'>PortfolioManager.__init__</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t99">src/portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t99"><data value='setup_event_subscriptions'>PortfolioManager._setup_event_subscriptions</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t119">src/portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t119"><data value='add_strategy'>PortfolioManager.add_strategy</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t167">src/portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t167"><data value='calculate_optimal_allocation'>PortfolioManager.calculate_optimal_allocation</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t212">src/portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t212"><data value='calculate_momentum_score'>PortfolioManager._calculate_momentum_score</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t238">src/portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t238"><data value='calculate_risk_adjusted_score'>PortfolioManager._calculate_risk_adjusted_score</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t271">src/portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t271"><data value='calculate_correlation_penalty'>PortfolioManager._calculate_correlation_penalty</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t291">src/portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t291"><data value='get_market_environment_adjustment'>PortfolioManager._get_market_environment_adjustment</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t312">src/portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t312"><data value='normalize_allocations'>PortfolioManager._normalize_allocations</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t342">src/portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t342"><data value='rebalance_portfolio'>PortfolioManager.rebalance_portfolio</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t398">src/portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t398"><data value='on_strategy_health_changed'>PortfolioManager._on_strategy_health_changed</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t407">src/portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t407"><data value='on_order_filled'>PortfolioManager._on_order_filled</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t411">src/portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t411"><data value='on_risk_limit_exceeded'>PortfolioManager._on_risk_limit_exceeded</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t415">src/portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t415"><data value='get_portfolio_status'>PortfolioManager.get_portfolio_status</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t446">src/portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t446"><data value='get_strategy_type'>MockStrategy.get_strategy_type</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t449">src/portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t449"><data value='analyze_market'>MockStrategy.analyze_market</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t452">src/portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t452"><data value='calculate_position_size'>MockStrategy.calculate_position_size</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t455">src/portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t455"><data value='validate_signal'>MockStrategy.validate_signal</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html">src/portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>43</td>
                <td>0</td>
                <td>21</td>
                <td class="right" data-ratio="43 43">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t78">src/strategy_config_models.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t78"><data value='validate_max_trade_amount'>BaseStrategyConfig.validate_max_trade_amount</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t87">src/strategy_config_models.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t87"><data value='validate_take_profit'>BaseStrategyConfig.validate_take_profit</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t95">src/strategy_config_models.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t95"><data value='validate_risk_consistency'>BaseStrategyConfig.validate_risk_consistency</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t112">src/strategy_config_models.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t112"><data value='update_timestamp'>BaseStrategyConfig.update_timestamp</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t147">src/strategy_config_models.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t147"><data value='validate_zscore_exit'>PairsTradingConfig.validate_zscore_exit</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t174">src/strategy_config_models.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t174"><data value='validate_ma_periods'>MomentumConfig.validate_ma_periods</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t200">src/strategy_config_models.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t200"><data value='validate_exchanges'>ArbitrageConfig.validate_exchanges</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t218">src/strategy_config_models.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t218"><data value='create_config'>StrategyConfigFactory.create_config</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t224">src/strategy_config_models.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t224"><data value='from_dict'>StrategyConfigFactory.from_dict</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t230">src/strategy_config_models.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t230"><data value='from_json'>StrategyConfigFactory.from_json</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t236">src/strategy_config_models.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t236"><data value='from_file'>StrategyConfigFactory.from_file</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t246">src/strategy_config_models.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t246"><data value='validate_config'>StrategyConfigValidator.validate_config</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t288">src/strategy_config_models.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t288"><data value='create_default_configs'>create_default_configs</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html">src/strategy_config_models.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>109</td>
                <td>0</td>
                <td>22</td>
                <td class="right" data-ratio="109 109">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t60">src/strategy_framework.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t60"><data value='is_valid'>TradingSignal.is_valid</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t66">src/strategy_framework.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t66"><data value='is_strong_enough'>TradingSignal.is_strong_enough</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t91">src/strategy_framework.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t91"><data value='init__'>BaseStrategy.__init__</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t120">src/strategy_framework.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t120"><data value='get_strategy_type'>BaseStrategy.get_strategy_type</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t125">src/strategy_framework.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t125"><data value='analyze_market'>BaseStrategy.analyze_market</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>10</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t138">src/strategy_framework.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t138"><data value='calculate_position_size'>BaseStrategy.calculate_position_size</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>11</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t153">src/strategy_framework.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t153"><data value='validate_signal'>BaseStrategy.validate_signal</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>11</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t167">src/strategy_framework.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t167"><data value='update_performance'>BaseStrategy.update_performance</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t205">src/strategy_framework.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t205"><data value='get_health_score'>BaseStrategy.get_health_score</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t234">src/strategy_framework.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t234"><data value='should_pause'>BaseStrategy.should_pause</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t250">src/strategy_framework.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t250"><data value='get_consecutive_losses'>BaseStrategy._get_consecutive_losses</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t256">src/strategy_framework.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t256"><data value='get_required_symbols'>BaseStrategy.get_required_symbols</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t260">src/strategy_framework.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t260"><data value='get_timeframes'>BaseStrategy.get_timeframes</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t264">src/strategy_framework.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t264"><data value='get_config_summary'>BaseStrategy.get_config_summary</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t279">src/strategy_framework.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t279"><data value='init__'>StrategyManager.__init__</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t283">src/strategy_framework.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t283"><data value='register_strategy'>StrategyManager.register_strategy</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t291">src/strategy_framework.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t291"><data value='get_strategy'>StrategyManager.get_strategy</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t295">src/strategy_framework.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t295"><data value='get_active_strategies'>StrategyManager.get_active_strategies</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t300">src/strategy_framework.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t300"><data value='pause_strategy'>StrategyManager.pause_strategy</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t308">src/strategy_framework.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t308"><data value='resume_strategy'>StrategyManager.resume_strategy</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t316">src/strategy_framework.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t316"><data value='get_all_required_symbols'>StrategyManager.get_all_required_symbols</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t323">src/strategy_framework.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t323"><data value='get_strategy_performance_summary'>StrategyManager.get_strategy_performance_summary</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html">src/strategy_framework.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>72</td>
                <td>0</td>
                <td>19</td>
                <td class="right" data-ratio="72 72">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html#t26">src/strategy_health_monitor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html#t26"><data value='init__'>StrategyHealthMonitor.__init__</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html#t52">src/strategy_health_monitor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html#t52"><data value='load_health_config'>StrategyHealthMonitor._load_health_config</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html#t73">src/strategy_health_monitor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html#t73"><data value='load_trade_history'>StrategyHealthMonitor._load_trade_history</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html#t87">src/strategy_health_monitor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html#t87"><data value='save_trade_history'>StrategyHealthMonitor._save_trade_history</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html#t104">src/strategy_health_monitor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html#t104"><data value='record_trade'>StrategyHealthMonitor.record_trade</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html#t135">src/strategy_health_monitor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html#t135"><data value='update_consecutive_losses'>StrategyHealthMonitor._update_consecutive_losses</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html#t148">src/strategy_health_monitor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html#t148"><data value='calculate_sharpe_ratio'>StrategyHealthMonitor.calculate_sharpe_ratio</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html#t175">src/strategy_health_monitor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html#t175"><data value='check_cointegration'>StrategyHealthMonitor.check_cointegration</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html#t203">src/strategy_health_monitor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html#t203"><data value='check_correlation'>StrategyHealthMonitor.check_correlation</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html#t225">src/strategy_health_monitor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html#t225"><data value='perform_health_check'>StrategyHealthMonitor.perform_health_check</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html#t289">src/strategy_health_monitor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html#t289"><data value='send_health_alert'>StrategyHealthMonitor._send_health_alert</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html#t317">src/strategy_health_monitor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html#t317"><data value='trigger_strategy_degradation'>StrategyHealthMonitor._trigger_strategy_degradation</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html#t346">src/strategy_health_monitor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html#t346"><data value='get_degradation_reasons'>StrategyHealthMonitor._get_degradation_reasons</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html#t364">src/strategy_health_monitor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html#t364"><data value='should_check_health'>StrategyHealthMonitor.should_check_health</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html#t374">src/strategy_health_monitor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html#t374"><data value='is_pair_degraded'>StrategyHealthMonitor.is_pair_degraded</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html#t382">src/strategy_health_monitor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html#t382"><data value='get_health_status'>StrategyHealthMonitor.get_health_status</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html#t395">src/strategy_health_monitor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html#t395"><data value='reset_pair_degradation'>StrategyHealthMonitor.reset_pair_degradation</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html#t407">src/strategy_health_monitor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html#t407"><data value='get_pair_key'>StrategyHealthMonitor._get_pair_key</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html#t415">src/strategy_health_monitor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html#t415"><data value='get_strategy_health_monitor'>get_strategy_health_monitor</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html">src/strategy_health_monitor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>33</td>
                <td>0</td>
                <td>11</td>
                <td class="right" data-ratio="33 33">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_exceptions_py.html#t14">src/trading_exceptions.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trading_exceptions_py.html#t14"><data value='init__'>TradingException.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_exceptions_py.html#t39">src/trading_exceptions.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trading_exceptions_py.html#t39"><data value='init__'>PartialFillError.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_exceptions_py.html">src/trading_exceptions.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trading_exceptions_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_safety_guard_py.html#t35">src/trading_safety_guard.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trading_safety_guard_py.html#t35"><data value='init__'>TradingSafetyGuard.__init__</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_safety_guard_py.html#t45">src/trading_safety_guard.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trading_safety_guard_py.html#t45"><data value='validate_trading_environment'>TradingSafetyGuard.validate_trading_environment</data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_safety_guard_py.html#t111">src/trading_safety_guard.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trading_safety_guard_py.html#t111"><data value='is_test_api_key'>TradingSafetyGuard._is_test_api_key</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_safety_guard_py.html#t127">src/trading_safety_guard.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trading_safety_guard_py.html#t127"><data value='require_live_trading_confirmation'>TradingSafetyGuard._require_live_trading_confirmation</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_safety_guard_py.html#t167">src/trading_safety_guard.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trading_safety_guard_py.html#t167"><data value='generate_safety_recommendations'>TradingSafetyGuard._generate_safety_recommendations</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_safety_guard_py.html#t188">src/trading_safety_guard.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trading_safety_guard_py.html#t188"><data value='check_position_limits'>TradingSafetyGuard.check_position_limits</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_safety_guard_py.html#t196">src/trading_safety_guard.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trading_safety_guard_py.html#t196"><data value='check_daily_loss_limit'>TradingSafetyGuard.check_daily_loss_limit</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_safety_guard_py.html#t216">src/trading_safety_guard.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trading_safety_guard_py.html#t216"><data value='create_trading_confirmation_file'>TradingSafetyGuard.create_trading_confirmation_file</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_safety_guard_py.html#t228">src/trading_safety_guard.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trading_safety_guard_py.html#t228"><data value='remove_trading_confirmation_file'>TradingSafetyGuard.remove_trading_confirmation_file</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_safety_guard_py.html#t238">src/trading_safety_guard.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trading_safety_guard_py.html#t238"><data value='get_safety_status'>TradingSafetyGuard.get_safety_status</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_safety_guard_py.html#t254">src/trading_safety_guard.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trading_safety_guard_py.html#t254"><data value='get_safety_guard'>get_safety_guard</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_safety_guard_py.html#t262">src/trading_safety_guard.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trading_safety_guard_py.html#t262"><data value='validate_trading_safety'>validate_trading_safety</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_safety_guard_py.html">src/trading_safety_guard.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trading_safety_guard_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>29</td>
                <td>0</td>
                <td>20</td>
                <td class="right" data-ratio="29 29">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html#t23">src/unified_client_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html#t23"><data value='init__'>UnifiedClientManager.__init__</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html#t42">src/unified_client_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html#t42"><data value='component_name'>UnifiedClientManager.component_name</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html#t45">src/unified_client_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html#t45"><data value='get_http_session'>UnifiedClientManager.get_http_session</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html#t54">src/unified_client_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html#t54"><data value='create_http_session'>UnifiedClientManager._create_http_session</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html#t79">src/unified_client_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html#t79"><data value='get_exchange_instance'>UnifiedClientManager.get_exchange_instance</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html#t94">src/unified_client_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html#t94"><data value='create_exchange_instance'>UnifiedClientManager._create_exchange_instance</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html#t131">src/unified_client_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html#t131"><data value='http_session_context'>UnifiedClientManager.http_session_context</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html#t141">src/unified_client_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html#t141"><data value='exchange_context'>UnifiedClientManager.exchange_context</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html#t151">src/unified_client_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html#t151"><data value='close_http_session'>UnifiedClientManager.close_http_session</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html#t164">src/unified_client_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html#t164"><data value='close_exchange_instance'>UnifiedClientManager.close_exchange_instance</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html#t183">src/unified_client_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html#t183"><data value='close_all_exchanges'>UnifiedClientManager.close_all_exchanges</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html#t190">src/unified_client_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html#t190"><data value='shutdown'>UnifiedClientManager.shutdown</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html#t208">src/unified_client_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html#t208"><data value='get_client_stats'>UnifiedClientManager.get_client_stats</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html#t229">src/unified_client_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html#t229"><data value='get_client_manager'>get_client_manager</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html#t246">src/unified_client_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html#t246"><data value='get_shared_http_session'>get_shared_http_session</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html#t252">src/unified_client_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html#t252"><data value='get_shared_exchange'>get_shared_exchange</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html#t261">src/unified_client_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html#t261"><data value='shared_http_session'>shared_http_session</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html#t269">src/unified_client_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html#t269"><data value='shared_exchange'>shared_exchange</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html#t276">src/unified_client_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html#t276"><data value='main'>main</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html">src/unified_client_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>35</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="35 35">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t41">src/unified_portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t41"><data value='init__'>UnifiedPortfolioManager.__init__</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t71">src/unified_portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t71"><data value='component_name'>UnifiedPortfolioManager.component_name</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t75">src/unified_portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t75"><data value='register_event_listeners'>UnifiedPortfolioManager._register_event_listeners</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t101">src/unified_portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t101"><data value='start_system'>UnifiedPortfolioManager.start_system</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t135">src/unified_portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t135"><data value='stop_system'>UnifiedPortfolioManager.stop_system</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t159">src/unified_portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t159"><data value='perform_initial_allocation'>UnifiedPortfolioManager._perform_initial_allocation</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t204">src/unified_portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t204"><data value='rebalance_loop'>UnifiedPortfolioManager._rebalance_loop</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t224">src/unified_portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t224"><data value='should_rebalance'>UnifiedPortfolioManager._should_rebalance</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t232">src/unified_portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t232"><data value='perform_rebalancing'>UnifiedPortfolioManager._perform_rebalancing</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t285">src/unified_portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t285"><data value='on_strategy_health_changed'>UnifiedPortfolioManager._on_strategy_health_changed</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t303">src/unified_portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t303"><data value='on_order_filled'>UnifiedPortfolioManager._on_order_filled</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t349">src/unified_portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t349"><data value='on_risk_alert'>UnifiedPortfolioManager._on_risk_alert</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t394">src/unified_portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t394"><data value='update_strategy_health_score'>UnifiedPortfolioManager._update_strategy_health_score</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t460">src/unified_portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t460"><data value='get_system_overview'>UnifiedPortfolioManager.get_system_overview</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t537">src/unified_portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t537"><data value='calculate_allocation_concentration'>UnifiedPortfolioManager._calculate_allocation_concentration</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t550">src/unified_portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t550"><data value='get_health_data'>UnifiedPortfolioManager.get_health_data</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t572">src/unified_portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t572"><data value='shutdown'>UnifiedPortfolioManager.shutdown</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t611">src/unified_portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t611"><data value='get_unified_portfolio_manager'>get_unified_portfolio_manager</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t622">src/unified_portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t622"><data value='test_unified_manager'>test_unified_manager</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>19</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html">src/unified_portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>41</td>
                <td>0</td>
                <td>3</td>
                <td class="right" data-ratio="41 41">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_utils_py.html#t15">src/utils.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_utils_py.html#t15"><data value='load_config'>load_config</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_utils_py.html#t43">src/utils.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_utils_py.html#t43"><data value='override_config_with_env'>_override_config_with_env</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_utils_py.html#t96">src/utils.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_utils_py.html#t96"><data value='calculate_log_spread'>calculate_log_spread</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_utils_py.html#t107">src/utils.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_utils_py.html#t107"><data value='calculate_zscore'>calculate_zscore</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_utils_py.html#t130">src/utils.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_utils_py.html#t130"><data value='get_exchange_instance'>get_exchange_instance</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_utils_py.html#t151">src/utils.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_utils_py.html#t151"><data value='format_timestamp'>format_timestamp</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_utils_py.html#t156">src/utils.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_utils_py.html#t156"><data value='calculate_position_size'>calculate_position_size</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_utils_py.html#t167">src/utils.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_utils_py.html#t167"><data value='validate_trading_pair'>validate_trading_pair</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_utils_py.html#t179">src/utils.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_utils_py.html#t179"><data value='calculate_pnl'>calculate_pnl</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_utils_py.html#t200">src/utils.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_utils_py.html#t200"><data value='save_trade_record'>save_trade_record</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_utils_py.html">src/utils.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_utils_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t44">state_management/state_manager.py</a></td>
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t44"><data value='to_dict'>StateSnapshot.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t56">state_management/state_manager.py</a></td>
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t56"><data value='from_dict'>StateSnapshot.from_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t71">state_management/state_manager.py</a></td>
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t71"><data value='init__'>StateManager.__init__</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t114">state_management/state_manager.py</a></td>
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t114"><data value='start'>StateManager.start</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t133">state_management/state_manager.py</a></td>
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t133"><data value='stop'>StateManager.stop</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t149">state_management/state_manager.py</a></td>
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t149"><data value='save_state'>StateManager.save_state</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t202">state_management/state_manager.py</a></td>
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t202"><data value='load_state'>StateManager.load_state</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t229">state_management/state_manager.py</a></td>
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t229"><data value='get_state_data'>StateManager.get_state_data</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t234">state_management/state_manager.py</a></td>
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t234"><data value='delete_state'>StateManager.delete_state</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t257">state_management/state_manager.py</a></td>
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t257"><data value='list_states'>StateManager.list_states</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t268">state_management/state_manager.py</a></td>
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t268"><data value='get_state_history'>StateManager.get_state_history</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t277">state_management/state_manager.py</a></td>
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t277"><data value='rollback_state'>StateManager.rollback_state</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t304">state_management/state_manager.py</a></td>
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t304"><data value='register_change_callback'>StateManager.register_change_callback</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t312">state_management/state_manager.py</a></td>
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t312"><data value='trigger_change_callbacks'>StateManager._trigger_change_callbacks</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t324">state_management/state_manager.py</a></td>
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t324"><data value='calculate_checksum'>StateManager._calculate_checksum</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t337">state_management/state_manager.py</a></td>
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t337"><data value='verify_checksum'>StateManager._verify_checksum</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t346">state_management/state_manager.py</a></td>
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t346"><data value='persistence_worker'>StateManager._persistence_worker</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t364">state_management/state_manager.py</a></td>
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t364"><data value='persist_all_states'>StateManager._persist_all_states</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t376">state_management/state_manager.py</a></td>
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t376"><data value='persist_state'>StateManager._persist_state</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t395">state_management/state_manager.py</a></td>
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t395"><data value='create_backup'>StateManager._create_backup</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t410">state_management/state_manager.py</a></td>
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t410"><data value='cleanup_old_backups'>StateManager._cleanup_old_backups</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t427">state_management/state_manager.py</a></td>
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t427"><data value='persist_to_database'>StateManager._persist_to_database</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t436">state_management/state_manager.py</a></td>
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t436"><data value='load_state_from_disk'>StateManager._load_state_from_disk</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t453">state_management/state_manager.py</a></td>
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t453"><data value='recover_states'>StateManager._recover_states</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t479">state_management/state_manager.py</a></td>
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t479"><data value='get_statistics'>StateManager.get_statistics</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t495">state_management/state_manager.py</a></td>
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t495"><data value='get_state_manager'>get_state_manager</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html">state_management/state_manager.py</a></td>
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>57</td>
                <td>0</td>
                <td>14</td>
                <td class="right" data-ratio="57 57">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_test_runner_py.html#t62">unified_test_runner.py</a></td>
                <td class="name left"><a href="unified_test_runner_py.html#t62"><data value='init__'>UnifiedTestRunner.__init__</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_test_runner_py.html#t91">unified_test_runner.py</a></td>
                <td class="name left"><a href="unified_test_runner_py.html#t91"><data value='run_all_tests'>UnifiedTestRunner.run_all_tests</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_test_runner_py.html#t131">unified_test_runner.py</a></td>
                <td class="name left"><a href="unified_test_runner_py.html#t131"><data value='run_test_suite'>UnifiedTestRunner.run_test_suite</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_test_runner_py.html#t195">unified_test_runner.py</a></td>
                <td class="name left"><a href="unified_test_runner_py.html#t195"><data value='build_test_command'>UnifiedTestRunner._build_test_command</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_test_runner_py.html#t223">unified_test_runner.py</a></td>
                <td class="name left"><a href="unified_test_runner_py.html#t223"><data value='parse_test_output'>UnifiedTestRunner._parse_test_output</data></a></td>
                <td>36</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 36">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_test_runner_py.html#t295">unified_test_runner.py</a></td>
                <td class="name left"><a href="unified_test_runner_py.html#t295"><data value='extract_coverage_percentage'>UnifiedTestRunner._extract_coverage_percentage</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_test_runner_py.html#t310">unified_test_runner.py</a></td>
                <td class="name left"><a href="unified_test_runner_py.html#t310"><data value='generate_comprehensive_report'>UnifiedTestRunner._generate_comprehensive_report</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_test_runner_py.html#t370">unified_test_runner.py</a></td>
                <td class="name left"><a href="unified_test_runner_py.html#t370"><data value='print_test_summary'>UnifiedTestRunner._print_test_summary</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_test_runner_py.html#t393">unified_test_runner.py</a></td>
                <td class="name left"><a href="unified_test_runner_py.html#t393"><data value='run_specific_test'>UnifiedTestRunner.run_specific_test</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_test_runner_py.html#t432">unified_test_runner.py</a></td>
                <td class="name left"><a href="unified_test_runner_py.html#t432"><data value='generate_coverage_report'>UnifiedTestRunner.generate_coverage_report</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_test_runner_py.html#t454">unified_test_runner.py</a></td>
                <td class="name left"><a href="unified_test_runner_py.html#t454"><data value='get_unified_test_runner'>get_unified_test_runner</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_test_runner_py.html#t462">unified_test_runner.py</a></td>
                <td class="name left"><a href="unified_test_runner_py.html#t462"><data value='main'>main</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_test_runner_py.html">unified_test_runner.py</a></td>
                <td class="name left"><a href="unified_test_runner_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>51</td>
                <td>51</td>
                <td>3</td>
                <td class="right" data-ratio="0 51">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>8727</td>
                <td>6979</td>
                <td>561</td>
                <td class="right" data-ratio="1748 8727">20%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-07-06 12:16 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
