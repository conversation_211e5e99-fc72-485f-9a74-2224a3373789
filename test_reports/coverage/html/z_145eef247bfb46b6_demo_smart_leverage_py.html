<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage for src/demo_smart_leverage.py: 0%</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_8e611ae1.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="pyfile">
<header>
    <div class="content">
        <h1>
            <span class="text">Coverage for </span><b>src/demo_smart_leverage.py</b>:
            <span class="pc_cov">0%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>r</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        &nbsp; toggle line displays
                    </p>
                    <p>
                        <kbd>j</kbd>
                        <kbd>k</kbd>
                        &nbsp; next/prev highlighted chunk
                    </p>
                    <p>
                        <kbd>0</kbd> &nbsp; (zero) top of page
                    </p>
                    <p>
                        <kbd>1</kbd> &nbsp; (one) first highlighted chunk
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>u</kbd> &nbsp; up to the index
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <h2>
            <span class="text">111 statements &nbsp;</span>
            <button type="button" class="run button_toggle_run" value="run" data-shortcut="r" title="Toggle lines run">0<span class="text"> run</span></button>
            <button type="button" class="mis show_mis button_toggle_mis" value="mis" data-shortcut="m" title="Toggle lines missing">111<span class="text"> missing</span></button>
            <button type="button" class="exc show_exc button_toggle_exc" value="exc" data-shortcut="x" title="Toggle lines excluded">2<span class="text"> excluded</span></button>
        </h2>
        <p class="text">
            <a id="prevFileLink" class="nav" href="z_145eef247bfb46b6_demo_multi_strategy_system_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a id="indexLink" class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a id="nextFileLink" class="nav" href="z_145eef247bfb46b6_dynamic_allocation_engine_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-07-06 13:04 +0800
        </p>
        <aside class="hidden">
            <button type="button" class="button_next_chunk" data-shortcut="j"></button>
            <button type="button" class="button_prev_chunk" data-shortcut="k"></button>
            <button type="button" class="button_top_of_page" data-shortcut="0"></button>
            <button type="button" class="button_first_chunk" data-shortcut="1"></button>
            <button type="button" class="button_prev_file" data-shortcut="["></button>
            <button type="button" class="button_next_file" data-shortcut="]"></button>
            <button type="button" class="button_to_index" data-shortcut="u"></button>
            <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
        </aside>
    </div>
</header>
<main id="source">
    <p class="pln"><span class="n"><a id="t1" href="#t1">1</a></span><span class="t"><span class="com">#!/usr/bin/env python3</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t2" href="#t2">2</a></span><span class="t"><span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t3" href="#t3">3</a></span><span class="t"><span class="str">&#26234;&#33021;&#27091;&#26751;&#28436;&#31034; - &#23637;&#31034;50&#20493;&#27091;&#26751;&#20294;&#38480;&#21046;&#23526;&#38555;&#39080;&#38570;&#30340;&#25928;&#26524;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t4" href="#t4">4</a></span><span class="t"><span class="str">Smart Leverage Demo - Demonstrate 50x leverage with controlled risk</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t5" href="#t5">5</a></span><span class="t"><span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t6" href="#t6">6</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t7" href="#t7">7</a></span><span class="t"><span class="key">import</span> <span class="nam">sys</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t8" href="#t8">8</a></span><span class="t"><span class="key">from</span> <span class="nam">typing</span> <span class="key">import</span> <span class="nam">Dict</span><span class="op">,</span> <span class="nam">Any</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t9" href="#t9">9</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t10" href="#t10">10</a></span><span class="t"><span class="nam">sys</span><span class="op">.</span><span class="nam">path</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="str">'.'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t11" href="#t11">11</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t12" href="#t12">12</a></span><span class="t"><span class="key">from</span> <span class="nam">smart_capital_management</span> <span class="key">import</span> <span class="nam">SmartCapitalManager</span><span class="op">,</span> <span class="nam">RiskLevel</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t13" href="#t13">13</a></span><span class="t"><span class="key">from</span> <span class="nam">logging_config</span> <span class="key">import</span> <span class="nam">setup_logging</span><span class="op">,</span> <span class="nam">get_logger</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t14" href="#t14">14</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t15" href="#t15">15</a></span><span class="t"><span class="nam">setup_logging</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t16" href="#t16">16</a></span><span class="t"><span class="nam">logger</span> <span class="op">=</span> <span class="nam">get_logger</span><span class="op">(</span><span class="nam">__name__</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t17" href="#t17">17</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t18" href="#t18">18</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t19" href="#t19">19</a></span><span class="t"><span class="key">def</span> <span class="nam">demo_smart_leverage</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t20" href="#t20">20</a></span><span class="t">    <span class="str">"""&#28436;&#31034;&#26234;&#33021;&#27091;&#26751;&#31995;&#32113;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t21" href="#t21">21</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="str">"&#127919; &#26234;&#33021;&#27091;&#26751;&#31995;&#32113;&#28436;&#31034;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t22" href="#t22">22</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="str">"="</span> <span class="op">*</span> <span class="num">60</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t23" href="#t23">23</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="str">"&#31574;&#30053;: 50&#20493;&#27091;&#26751;&#35373;&#23450;&#65292;&#20294;&#38480;&#21046;&#23526;&#38555;&#39080;&#38570;&#25950;&#21475;&#21040;5-10&#20493;&#31561;&#25928;&#27091;&#26751;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t24" href="#t24">24</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="str">"="</span> <span class="op">*</span> <span class="num">60</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t25" href="#t25">25</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t26" href="#t26">26</a></span><span class="t">    <span class="com"># &#21109;&#24314;&#19981;&#21516;&#39080;&#38570;&#31561;&#32026;&#30340;&#31649;&#29702;&#22120;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t27" href="#t27">27</a></span><span class="t">    <span class="nam">scenarios</span> <span class="op">=</span> <span class="op">[</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t28" href="#t28">28</a></span><span class="t">        <span class="op">(</span><span class="str">"&#20445;&#23432;&#22411;"</span><span class="op">,</span> <span class="nam">RiskLevel</span><span class="op">.</span><span class="nam">CONSERVATIVE</span><span class="op">,</span> <span class="num">10000</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t29" href="#t29">29</a></span><span class="t">        <span class="op">(</span><span class="str">"&#31337;&#20581;&#22411;"</span><span class="op">,</span> <span class="nam">RiskLevel</span><span class="op">.</span><span class="nam">MODERATE</span><span class="op">,</span> <span class="num">10000</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t30" href="#t30">30</a></span><span class="t">        <span class="op">(</span><span class="str">"&#31309;&#26997;&#22411;"</span><span class="op">,</span> <span class="nam">RiskLevel</span><span class="op">.</span><span class="nam">AGGRESSIVE</span><span class="op">,</span> <span class="num">10000</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t31" href="#t31">31</a></span><span class="t">    <span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t32" href="#t32">32</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t33" href="#t33">33</a></span><span class="t">    <span class="key">for</span> <span class="nam">scenario_name</span><span class="op">,</span> <span class="nam">risk_level</span><span class="op">,</span> <span class="nam">capital</span> <span class="key">in</span> <span class="nam">scenarios</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t34" href="#t34">34</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">\n&#128202; </span><span class="op">{</span><span class="nam">scenario_name</span><span class="op">}</span><span class="fst">&#31574;&#30053; (&#32317;&#36039;&#37329;: $</span><span class="op">{</span><span class="nam">capital</span><span class="op">:</span><span class="fst">,</span><span class="op">}</span><span class="fst">)</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t35" href="#t35">35</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="str">"-"</span> <span class="op">*</span> <span class="num">40</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t36" href="#t36">36</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t37" href="#t37">37</a></span><span class="t">        <span class="com"># &#21109;&#24314;&#31649;&#29702;&#22120;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t38" href="#t38">38</a></span><span class="t">        <span class="nam">manager</span> <span class="op">=</span> <span class="nam">SmartCapitalManager</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t39" href="#t39">39</a></span><span class="t">            <span class="nam">total_capital</span><span class="op">=</span><span class="nam">capital</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t40" href="#t40">40</a></span><span class="t">            <span class="nam">risk_level</span><span class="op">=</span><span class="nam">risk_level</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t41" href="#t41">41</a></span><span class="t">            <span class="nam">exchange_leverage</span><span class="op">=</span><span class="num">50</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t42" href="#t42">42</a></span><span class="t">        <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t43" href="#t43">43</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t44" href="#t44">44</a></span><span class="t">        <span class="com"># &#29554;&#21462;&#39080;&#38570;&#25351;&#27161;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t45" href="#t45">45</a></span><span class="t">        <span class="nam">metrics</span> <span class="op">=</span> <span class="nam">manager</span><span class="op">.</span><span class="nam">get_risk_metrics</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t46" href="#t46">46</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t47" href="#t47">47</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#128295; &#37197;&#32622;&#21443;&#25976;:</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t48" href="#t48">48</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">  &#20132;&#26131;&#25152;&#27091;&#26751;: </span><span class="op">{</span><span class="nam">metrics</span><span class="op">[</span><span class="str">'exchange_leverage'</span><span class="op">]</span><span class="op">}</span><span class="fst">x</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t49" href="#t49">49</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">  &#26377;&#25928;&#27091;&#26751;&#30446;&#27161;: </span><span class="op">{</span><span class="nam">metrics</span><span class="op">[</span><span class="str">'effective_leverage_target'</span><span class="op">]</span><span class="op">}</span><span class="fst">x</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t50" href="#t50">50</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">  &#26368;&#22823;&#36039;&#37329;&#21033;&#29992;&#29575;: </span><span class="op">{</span><span class="nam">metrics</span><span class="op">[</span><span class="str">'max_utilization_rate'</span><span class="op">]</span><span class="op">:</span><span class="fst">.1%</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t51" href="#t51">51</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">  &#26368;&#22823;&#21934;&#31558;&#20489;&#20301;: </span><span class="op">{</span><span class="nam">metrics</span><span class="op">[</span><span class="str">'max_single_position_pct'</span><span class="op">]</span><span class="op">:</span><span class="fst">.1f</span><span class="op">}</span><span class="fst">%</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t52" href="#t52">52</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t53" href="#t53">53</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">\n&#128176; &#36039;&#37329;&#37197;&#32622;:</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t54" href="#t54">54</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">  &#32317;&#36039;&#37329;: $</span><span class="op">{</span><span class="nam">metrics</span><span class="op">[</span><span class="str">'total_capital'</span><span class="op">]</span><span class="op">:</span><span class="fst">,</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t55" href="#t55">55</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">  &#26368;&#22823;&#21487;&#29992;&#20445;&#35657;&#37329;: $</span><span class="op">{</span><span class="nam">metrics</span><span class="op">[</span><span class="str">'max_margin_used'</span><span class="op">]</span><span class="op">:</span><span class="fst">,</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t56" href="#t56">56</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">  &#26368;&#22823;&#20489;&#20301;&#20729;&#20540;: $</span><span class="op">{</span><span class="nam">metrics</span><span class="op">[</span><span class="str">'max_position_value'</span><span class="op">]</span><span class="op">:</span><span class="fst">,</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t57" href="#t57">57</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">  &#39080;&#38570;&#32233;&#34909;: $</span><span class="op">{</span><span class="nam">metrics</span><span class="op">[</span><span class="str">'risk_buffer'</span><span class="op">]</span><span class="op">:</span><span class="fst">,</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t58" href="#t58">58</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t59" href="#t59">59</a></span><span class="t">        <span class="com"># &#28436;&#31034;&#37197;&#23565;&#20132;&#26131;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t60" href="#t60">60</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">\n&#127919; &#37197;&#23565;&#20132;&#26131;&#31034;&#20363;:</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t61" href="#t61">61</a></span><span class="t">        <span class="nam">pair_result</span> <span class="op">=</span> <span class="nam">manager</span><span class="op">.</span><span class="nam">calculate_pair_position</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t62" href="#t62">62</a></span><span class="t">            <span class="str">'BTC/USDT:USDT'</span><span class="op">,</span> <span class="num">50000</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t63" href="#t63">63</a></span><span class="t">            <span class="str">'ETH/USDT:USDT'</span><span class="op">,</span> <span class="num">3000</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t64" href="#t64">64</a></span><span class="t">            <span class="nam">signal_strength</span><span class="op">=</span><span class="num">0.8</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t65" href="#t65">65</a></span><span class="t">        <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t66" href="#t66">66</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t67" href="#t67">67</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">  BTC&#22810;&#38957;: $</span><span class="op">{</span><span class="nam">pair_result</span><span class="op">[</span><span class="str">'long_position'</span><span class="op">]</span><span class="op">[</span><span class="str">'position_value'</span><span class="op">]</span><span class="op">:</span><span class="fst">,.0f</span><span class="op">}</span><span class="fst"> </span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t68" href="#t68">68</a></span><span class="t">              <span class="fst">f"</span><span class="fst">(&#20445;&#35657;&#37329;: $</span><span class="op">{</span><span class="nam">pair_result</span><span class="op">[</span><span class="str">'long_position'</span><span class="op">]</span><span class="op">[</span><span class="str">'required_margin'</span><span class="op">]</span><span class="op">:</span><span class="fst">.0f</span><span class="op">}</span><span class="fst">)</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t69" href="#t69">69</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">  ETH&#31354;&#38957;: $</span><span class="op">{</span><span class="nam">pair_result</span><span class="op">[</span><span class="str">'short_position'</span><span class="op">]</span><span class="op">[</span><span class="str">'position_value'</span><span class="op">]</span><span class="op">:</span><span class="fst">,.0f</span><span class="op">}</span><span class="fst"> </span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t70" href="#t70">70</a></span><span class="t">              <span class="fst">f"</span><span class="fst">(&#20445;&#35657;&#37329;: $</span><span class="op">{</span><span class="nam">pair_result</span><span class="op">[</span><span class="str">'short_position'</span><span class="op">]</span><span class="op">[</span><span class="str">'required_margin'</span><span class="op">]</span><span class="op">:</span><span class="fst">.0f</span><span class="op">}</span><span class="fst">)</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t71" href="#t71">71</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">  &#32317;&#39080;&#38570;: </span><span class="op">{</span><span class="nam">pair_result</span><span class="op">[</span><span class="str">'total_risk_percentage'</span><span class="op">]</span><span class="op">:</span><span class="fst">.2f</span><span class="op">}</span><span class="fst">% of capital</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t72" href="#t72">72</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">  &#23526;&#38555;&#27091;&#26751;: </span><span class="op">{</span><span class="nam">pair_result</span><span class="op">[</span><span class="str">'long_position'</span><span class="op">]</span><span class="op">[</span><span class="str">'effective_leverage_used'</span><span class="op">]</span><span class="op">:</span><span class="fst">.1f</span><span class="op">}</span><span class="fst">x</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t73" href="#t73">73</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">  &#39080;&#38570;&#29376;&#24907;: </span><span class="op">{</span><span class="str">'&#9989; &#23433;&#20840;'</span> <span class="key">if</span> <span class="nam">pair_result</span><span class="op">[</span><span class="str">'within_limits'</span><span class="op">]</span> <span class="key">else</span> <span class="str">'&#10060; &#36229;&#38480;'</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t74" href="#t74">74</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t75" href="#t75">75</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t76" href="#t76">76</a></span><span class="t"><span class="key">def</span> <span class="nam">compare_traditional_vs_smart</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t77" href="#t77">77</a></span><span class="t">    <span class="str">"""&#23565;&#27604;&#20659;&#32113;&#27091;&#26751; vs &#26234;&#33021;&#27091;&#26751;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t78" href="#t78">78</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">\n&#128260; &#20659;&#32113;&#27091;&#26751; vs &#26234;&#33021;&#27091;&#26751;&#23565;&#27604;</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t79" href="#t79">79</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="str">"="</span> <span class="op">*</span> <span class="num">60</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t80" href="#t80">80</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t81" href="#t81">81</a></span><span class="t">    <span class="nam">capital</span> <span class="op">=</span> <span class="num">10000</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t82" href="#t82">82</a></span><span class="t">    <span class="nam">position_value</span> <span class="op">=</span> <span class="num">5000</span>  <span class="com"># $5000&#20489;&#20301;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t83" href="#t83">83</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t84" href="#t84">84</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#20551;&#35373;&#22580;&#26223;: $</span><span class="op">{</span><span class="nam">capital</span><span class="op">:</span><span class="fst">,</span><span class="op">}</span><span class="fst">&#36039;&#37329;&#65292;&#38283;$</span><span class="op">{</span><span class="nam">position_value</span><span class="op">:</span><span class="fst">,</span><span class="op">}</span><span class="fst">&#20489;&#20301;</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t85" href="#t85">85</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="str">"-"</span> <span class="op">*</span> <span class="num">40</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t86" href="#t86">86</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t87" href="#t87">87</a></span><span class="t">    <span class="com"># &#20659;&#32113;50&#20493;&#27091;&#26751;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t88" href="#t88">88</a></span><span class="t">    <span class="nam">traditional_margin</span> <span class="op">=</span> <span class="nam">position_value</span> <span class="op">/</span> <span class="num">50</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t89" href="#t89">89</a></span><span class="t">    <span class="nam">traditional_risk</span> <span class="op">=</span> <span class="op">(</span><span class="nam">traditional_margin</span> <span class="op">/</span> <span class="nam">capital</span><span class="op">)</span> <span class="op">*</span> <span class="num">100</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t90" href="#t90">90</a></span><span class="t">    <span class="nam">traditional_liquidation</span> <span class="op">=</span> <span class="num">1</span><span class="op">/</span><span class="num">50</span> <span class="op">*</span> <span class="num">100</span>  <span class="com"># 2%</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t91" href="#t91">91</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t92" href="#t92">92</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#128200; &#20659;&#32113;50&#20493;&#27091;&#26751;:</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t93" href="#t93">93</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">  &#25152;&#38656;&#20445;&#35657;&#37329;: $</span><span class="op">{</span><span class="nam">traditional_margin</span><span class="op">:</span><span class="fst">.0f</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t94" href="#t94">94</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">  &#36039;&#37329;&#39080;&#38570;: </span><span class="op">{</span><span class="nam">traditional_risk</span><span class="op">:</span><span class="fst">.2f</span><span class="op">}</span><span class="fst">%</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t95" href="#t95">95</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">  &#24375;&#21046;&#24179;&#20489;&#36317;&#38626;: </span><span class="op">{</span><span class="nam">traditional_liquidation</span><span class="op">:</span><span class="fst">.1f</span><span class="op">}</span><span class="fst">%</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t96" href="#t96">96</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">  &#39080;&#38570;&#31561;&#32026;: &#128680; &#26997;&#39640;</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t97" href="#t97">97</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t98" href="#t98">98</a></span><span class="t">    <span class="com"># &#26234;&#33021;&#27091;&#26751;&#31995;&#32113;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t99" href="#t99">99</a></span><span class="t">    <span class="nam">manager</span> <span class="op">=</span> <span class="nam">SmartCapitalManager</span><span class="op">(</span><span class="nam">capital</span><span class="op">,</span> <span class="nam">RiskLevel</span><span class="op">.</span><span class="nam">MODERATE</span><span class="op">,</span> <span class="num">50</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t100" href="#t100">100</a></span><span class="t">    <span class="nam">smart_result</span> <span class="op">=</span> <span class="nam">manager</span><span class="op">.</span><span class="nam">calculate_position_size</span><span class="op">(</span><span class="str">'BTC/USDT:USDT'</span><span class="op">,</span> <span class="num">50000</span><span class="op">,</span> <span class="num">1.0</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t101" href="#t101">101</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t102" href="#t102">102</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">\n&#129504; &#26234;&#33021;&#27091;&#26751;&#31995;&#32113;:</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t103" href="#t103">103</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">  &#24314;&#35696;&#20489;&#20301;: $</span><span class="op">{</span><span class="nam">smart_result</span><span class="op">[</span><span class="str">'position_value'</span><span class="op">]</span><span class="op">:</span><span class="fst">.0f</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t104" href="#t104">104</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">  &#25152;&#38656;&#20445;&#35657;&#37329;: $</span><span class="op">{</span><span class="nam">smart_result</span><span class="op">[</span><span class="str">'required_margin'</span><span class="op">]</span><span class="op">:</span><span class="fst">.0f</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t105" href="#t105">105</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">  &#36039;&#37329;&#39080;&#38570;: </span><span class="op">{</span><span class="nam">smart_result</span><span class="op">[</span><span class="str">'risk_percentage'</span><span class="op">]</span><span class="op">:</span><span class="fst">.2f</span><span class="op">}</span><span class="fst">%</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t106" href="#t106">106</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">  &#23526;&#38555;&#27091;&#26751;: </span><span class="op">{</span><span class="nam">smart_result</span><span class="op">[</span><span class="str">'effective_leverage_used'</span><span class="op">]</span><span class="op">:</span><span class="fst">.1f</span><span class="op">}</span><span class="fst">x</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t107" href="#t107">107</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">  &#39080;&#38570;&#31561;&#32026;: &#9989; &#21487;&#25511;</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t108" href="#t108">108</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t109" href="#t109">109</a></span><span class="t">    <span class="com"># &#39080;&#38570;&#23565;&#27604;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t110" href="#t110">110</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">\n&#9878;&#65039; &#39080;&#38570;&#23565;&#27604;:</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t111" href="#t111">111</a></span><span class="t">    <span class="nam">risk_reduction</span> <span class="op">=</span> <span class="op">(</span><span class="nam">traditional_risk</span> <span class="op">-</span> <span class="nam">smart_result</span><span class="op">[</span><span class="str">'risk_percentage'</span><span class="op">]</span><span class="op">)</span> <span class="op">/</span> <span class="nam">traditional_risk</span> <span class="op">*</span> <span class="num">100</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t112" href="#t112">112</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">  &#39080;&#38570;&#38477;&#20302;: </span><span class="op">{</span><span class="nam">risk_reduction</span><span class="op">:</span><span class="fst">.1f</span><span class="op">}</span><span class="fst">%</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t113" href="#t113">113</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">  &#27091;&#26751;&#38477;&#20302;: </span><span class="op">{</span><span class="num">50</span> <span class="op">/</span> <span class="nam">smart_result</span><span class="op">[</span><span class="str">'effective_leverage_used'</span><span class="op">]</span><span class="op">:</span><span class="fst">.1f</span><span class="op">}</span><span class="fst">&#20493;</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t114" href="#t114">114</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">  &#23433;&#20840;&#25552;&#21319;: &#128680; &#26997;&#39640;&#39080;&#38570; &#8594; &#9989; &#21487;&#25511;&#39080;&#38570;</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t115" href="#t115">115</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t116" href="#t116">116</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t117" href="#t117">117</a></span><span class="t"><span class="key">def</span> <span class="nam">demo_position_scaling</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t118" href="#t118">118</a></span><span class="t">    <span class="str">"""&#28436;&#31034;&#20489;&#20301;&#32302;&#25918;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t119" href="#t119">119</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">\n&#128207; &#26234;&#33021;&#20489;&#20301;&#32302;&#25918;&#28436;&#31034;</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t120" href="#t120">120</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="str">"="</span> <span class="op">*</span> <span class="num">60</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t121" href="#t121">121</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t122" href="#t122">122</a></span><span class="t">    <span class="nam">manager</span> <span class="op">=</span> <span class="nam">SmartCapitalManager</span><span class="op">(</span><span class="num">10000</span><span class="op">,</span> <span class="nam">RiskLevel</span><span class="op">.</span><span class="nam">MODERATE</span><span class="op">,</span> <span class="num">50</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t123" href="#t123">123</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t124" href="#t124">124</a></span><span class="t">    <span class="com"># &#19981;&#21516;&#20449;&#34399;&#24375;&#24230;&#30340;&#20489;&#20301;&#35336;&#31639;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t125" href="#t125">125</a></span><span class="t">    <span class="nam">signal_strengths</span> <span class="op">=</span> <span class="op">[</span><span class="num">0.5</span><span class="op">,</span> <span class="num">0.7</span><span class="op">,</span> <span class="num">0.9</span><span class="op">,</span> <span class="num">1.0</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t126" href="#t126">126</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t127" href="#t127">127</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">BTC&#20729;&#26684;: $50,000</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t128" href="#t128">128</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#20449;&#34399;&#24375;&#24230;&#23565;&#20489;&#20301;&#22823;&#23567;&#30340;&#24433;&#38911;:</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t129" href="#t129">129</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="str">"-"</span> <span class="op">*</span> <span class="num">40</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t130" href="#t130">130</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t131" href="#t131">131</a></span><span class="t">    <span class="key">for</span> <span class="nam">strength</span> <span class="key">in</span> <span class="nam">signal_strengths</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t132" href="#t132">132</a></span><span class="t">        <span class="nam">result</span> <span class="op">=</span> <span class="nam">manager</span><span class="op">.</span><span class="nam">calculate_position_size</span><span class="op">(</span><span class="str">'BTC/USDT:USDT'</span><span class="op">,</span> <span class="num">50000</span><span class="op">,</span> <span class="nam">strength</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t133" href="#t133">133</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t134" href="#t134">134</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#20449;&#34399;&#24375;&#24230; </span><span class="op">{</span><span class="nam">strength</span><span class="op">:</span><span class="fst">.1f</span><span class="op">}</span><span class="fst">: </span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t135" href="#t135">135</a></span><span class="t">              <span class="fst">f"</span><span class="fst">&#20489;&#20301; $</span><span class="op">{</span><span class="nam">result</span><span class="op">[</span><span class="str">'position_value'</span><span class="op">]</span><span class="op">:</span><span class="fst">,.0f</span><span class="op">}</span><span class="fst">, </span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t136" href="#t136">136</a></span><span class="t">              <span class="fst">f"</span><span class="fst">&#39080;&#38570; </span><span class="op">{</span><span class="nam">result</span><span class="op">[</span><span class="str">'risk_percentage'</span><span class="op">]</span><span class="op">:</span><span class="fst">.2f</span><span class="op">}</span><span class="fst">%, </span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t137" href="#t137">137</a></span><span class="t">              <span class="fst">f"</span><span class="fst">&#27091;&#26751; </span><span class="op">{</span><span class="nam">result</span><span class="op">[</span><span class="str">'effective_leverage_used'</span><span class="op">]</span><span class="op">:</span><span class="fst">.1f</span><span class="op">}</span><span class="fst">x</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t138" href="#t138">138</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t139" href="#t139">139</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t140" href="#t140">140</a></span><span class="t"><span class="key">def</span> <span class="nam">demo_risk_scenarios</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t141" href="#t141">141</a></span><span class="t">    <span class="str">"""&#28436;&#31034;&#39080;&#38570;&#22580;&#26223;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t142" href="#t142">142</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">\n&#9888;&#65039; &#39080;&#38570;&#22580;&#26223;&#20998;&#26512;</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t143" href="#t143">143</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="str">"="</span> <span class="op">*</span> <span class="num">60</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t144" href="#t144">144</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t145" href="#t145">145</a></span><span class="t">    <span class="nam">manager</span> <span class="op">=</span> <span class="nam">SmartCapitalManager</span><span class="op">(</span><span class="num">10000</span><span class="op">,</span> <span class="nam">RiskLevel</span><span class="op">.</span><span class="nam">MODERATE</span><span class="op">,</span> <span class="num">50</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t146" href="#t146">146</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t147" href="#t147">147</a></span><span class="t">    <span class="nam">scenarios</span> <span class="op">=</span> <span class="op">[</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t148" href="#t148">148</a></span><span class="t">        <span class="op">(</span><span class="str">"&#27491;&#24120;&#24066;&#22580;"</span><span class="op">,</span> <span class="num">0.02</span><span class="op">,</span> <span class="str">"&#31337;&#23450;&#20132;&#26131;&#29872;&#22659;"</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t149" href="#t149">149</a></span><span class="t">        <span class="op">(</span><span class="str">"&#39640;&#27874;&#21205;"</span><span class="op">,</span> <span class="num">0.05</span><span class="op">,</span> <span class="str">"&#24066;&#22580;&#27874;&#21205;&#21152;&#21127;"</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t150" href="#t150">150</a></span><span class="t">        <span class="op">(</span><span class="str">"&#26997;&#31471;&#27874;&#21205;"</span><span class="op">,</span> <span class="num">0.10</span><span class="op">,</span> <span class="str">"&#40657;&#22825;&#40285;&#20107;&#20214;"</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t151" href="#t151">151</a></span><span class="t">    <span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t152" href="#t152">152</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t153" href="#t153">153</a></span><span class="t">    <span class="key">for</span> <span class="nam">scenario_name</span><span class="op">,</span> <span class="nam">volatility</span><span class="op">,</span> <span class="nam">description</span> <span class="key">in</span> <span class="nam">scenarios</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t154" href="#t154">154</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">\n&#128202; </span><span class="op">{</span><span class="nam">scenario_name</span><span class="op">}</span><span class="fst"> (</span><span class="op">{</span><span class="nam">description</span><span class="op">}</span><span class="fst">):</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t155" href="#t155">155</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t156" href="#t156">156</a></span><span class="t">        <span class="com"># &#26681;&#25818;&#27874;&#21205;&#24615;&#35519;&#25972;&#20489;&#20301;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t157" href="#t157">157</a></span><span class="t">        <span class="nam">volatility_factor</span> <span class="op">=</span> <span class="nam">max</span><span class="op">(</span><span class="num">0.5</span><span class="op">,</span> <span class="num">1</span> <span class="op">-</span> <span class="nam">volatility</span> <span class="op">*</span> <span class="num">5</span><span class="op">)</span>  <span class="com"># &#27874;&#21205;&#36234;&#39640;&#65292;&#20489;&#20301;&#36234;&#23567;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t158" href="#t158">158</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t159" href="#t159">159</a></span><span class="t">        <span class="nam">result</span> <span class="op">=</span> <span class="nam">manager</span><span class="op">.</span><span class="nam">calculate_position_size</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t160" href="#t160">160</a></span><span class="t">            <span class="str">'BTC/USDT:USDT'</span><span class="op">,</span> <span class="num">50000</span><span class="op">,</span> <span class="nam">volatility_factor</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t161" href="#t161">161</a></span><span class="t">        <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t162" href="#t162">162</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t163" href="#t163">163</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">  &#24314;&#35696;&#20489;&#20301;: $</span><span class="op">{</span><span class="nam">result</span><span class="op">[</span><span class="str">'position_value'</span><span class="op">]</span><span class="op">:</span><span class="fst">,.0f</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t164" href="#t164">164</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">  &#39080;&#38570;&#25950;&#21475;: </span><span class="op">{</span><span class="nam">result</span><span class="op">[</span><span class="str">'risk_percentage'</span><span class="op">]</span><span class="op">:</span><span class="fst">.2f</span><span class="op">}</span><span class="fst">%</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t165" href="#t165">165</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">  &#26368;&#22823;&#25613;&#22833;: $</span><span class="op">{</span><span class="nam">result</span><span class="op">[</span><span class="str">'max_loss_potential'</span><span class="op">]</span><span class="op">:</span><span class="fst">,.0f</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t166" href="#t166">166</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">  &#35519;&#25972;&#22240;&#23376;: </span><span class="op">{</span><span class="nam">volatility_factor</span><span class="op">:</span><span class="fst">.2f</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t167" href="#t167">167</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t168" href="#t168">168</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t169" href="#t169">169</a></span><span class="t"><span class="key">def</span> <span class="nam">main</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t170" href="#t170">170</a></span><span class="t">    <span class="str">"""&#20027;&#20989;&#25976;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t171" href="#t171">171</a></span><span class="t">    <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t172" href="#t172">172</a></span><span class="t">        <span class="com"># &#22522;&#26412;&#28436;&#31034;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t173" href="#t173">173</a></span><span class="t">        <span class="nam">demo_smart_leverage</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t174" href="#t174">174</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t175" href="#t175">175</a></span><span class="t">        <span class="com"># &#23565;&#27604;&#28436;&#31034;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t176" href="#t176">176</a></span><span class="t">        <span class="nam">compare_traditional_vs_smart</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t177" href="#t177">177</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t178" href="#t178">178</a></span><span class="t">        <span class="com"># &#20489;&#20301;&#32302;&#25918;&#28436;&#31034;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t179" href="#t179">179</a></span><span class="t">        <span class="nam">demo_position_scaling</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t180" href="#t180">180</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t181" href="#t181">181</a></span><span class="t">        <span class="com"># &#39080;&#38570;&#22580;&#26223;&#28436;&#31034;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t182" href="#t182">182</a></span><span class="t">        <span class="nam">demo_risk_scenarios</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t183" href="#t183">183</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t184" href="#t184">184</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">\n</span><span class="fst">"</span> <span class="op">+</span> <span class="str">"="</span> <span class="op">*</span> <span class="num">60</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t185" href="#t185">185</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="str">"&#127881; &#26234;&#33021;&#27091;&#26751;&#31995;&#32113;&#28436;&#31034;&#23436;&#25104;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t186" href="#t186">186</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="str">"="</span> <span class="op">*</span> <span class="num">60</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t187" href="#t187">187</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="str">"&#9989; &#20778;&#21218;&#32317;&#32080;:"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t188" href="#t188">188</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="str">"  &#8226; 50&#20493;&#27091;&#26751;&#25552;&#39640;&#36039;&#37329;&#25928;&#29575;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t189" href="#t189">189</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="str">"  &#8226; &#26234;&#33021;&#38480;&#21046;&#23526;&#38555;&#39080;&#38570;&#25950;&#21475;&#21040;5-10&#20493;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t190" href="#t190">190</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="str">"  &#8226; &#21205;&#24907;&#20489;&#20301;&#35519;&#25972;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t191" href="#t191">191</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="str">"  &#8226; &#22810;&#37325;&#39080;&#38570;&#25511;&#21046;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t192" href="#t192">192</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="str">"  &#8226; &#36969;&#25033;&#19981;&#21516;&#24066;&#22580;&#29872;&#22659;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t193" href="#t193">193</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t194" href="#t194">194</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">\n&#128161; &#20351;&#29992;&#24314;&#35696;:</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t195" href="#t195">195</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="str">"  1. &#24478;&#20445;&#23432;&#22411;&#38283;&#22987;&#28204;&#35430;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t196" href="#t196">196</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="str">"  2. &#26681;&#25818;&#32147;&#39511;&#35519;&#25972;&#39080;&#38570;&#31561;&#32026;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t197" href="#t197">197</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="str">"  3. &#23494;&#20999;&#30435;&#25511;&#23526;&#38555;&#27091;&#26751;&#20351;&#29992;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t198" href="#t198">198</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="str">"  4. &#23450;&#26399;&#27298;&#26597;&#39080;&#38570;&#25351;&#27161;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t199" href="#t199">199</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t200" href="#t200">200</a></span><span class="t">    <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t201" href="#t201">201</a></span><span class="t">        <span class="nam">logger</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#28436;&#31034;&#22833;&#25943;: </span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t202" href="#t202">202</a></span><span class="t">        <span class="key">return</span> <span class="num">1</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t203" href="#t203">203</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t204" href="#t204">204</a></span><span class="t">    <span class="key">return</span> <span class="num">0</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t205" href="#t205">205</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t206" href="#t206">206</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t207" href="#t207">207</a></span><span class="t"><span class="key">if</span> <span class="nam">__name__</span> <span class="op">==</span> <span class="str">"__main__"</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t208" href="#t208">208</a></span><span class="t">    <span class="nam">sys</span><span class="op">.</span><span class="nam">exit</span><span class="op">(</span><span class="nam">main</span><span class="op">(</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="z_145eef247bfb46b6_demo_multi_strategy_system_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a class="nav" href="z_145eef247bfb46b6_dynamic_allocation_engine_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-07-06 13:04 +0800
        </p>
    </div>
</footer>
</body>
</html>
