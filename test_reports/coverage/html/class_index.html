<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_8e611ae1.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">7%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-07-06 11:40 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="advanced_dynamic_allocation_py.html#t40">advanced_dynamic_allocation.py</a></td>
                <td class="name left"><a href="advanced_dynamic_allocation_py.html#t40"><data value='StrategyMetrics'>StrategyMetrics</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="advanced_dynamic_allocation_py.html#t61">advanced_dynamic_allocation.py</a></td>
                <td class="name left"><a href="advanced_dynamic_allocation_py.html#t61"><data value='AdvancedRiskModel'>AdvancedRiskModel</data></a></td>
                <td>41</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="0 41">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="advanced_dynamic_allocation_py.html#t151">advanced_dynamic_allocation.py</a></td>
                <td class="name left"><a href="advanced_dynamic_allocation_py.html#t151"><data value='AdvancedDynamicAllocator'>AdvancedDynamicAllocator</data></a></td>
                <td>179</td>
                <td>179</td>
                <td>0</td>
                <td class="right" data-ratio="0 179">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="advanced_dynamic_allocation_py.html">advanced_dynamic_allocation.py</a></td>
                <td class="name left"><a href="advanced_dynamic_allocation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>64</td>
                <td>64</td>
                <td>4</td>
                <td class="right" data-ratio="0 64">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="alert_manager_py.html#t19">alert_manager.py</a></td>
                <td class="name left"><a href="alert_manager_py.html#t19"><data value='AlertLevel'>AlertLevel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="alert_manager_py.html#t27">alert_manager.py</a></td>
                <td class="name left"><a href="alert_manager_py.html#t27"><data value='AlertManager'>AlertManager</data></a></td>
                <td>120</td>
                <td>120</td>
                <td>0</td>
                <td class="right" data-ratio="0 120">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="alert_manager_py.html">alert_manager.py</a></td>
                <td class="name left"><a href="alert_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>37</td>
                <td>5</td>
                <td>8</td>
                <td class="right" data-ratio="32 37">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="async_data_handler_py.html#t25">async_data_handler.py</a></td>
                <td class="name left"><a href="async_data_handler_py.html#t25"><data value='AsyncDataHandler'>AsyncDataHandler</data></a></td>
                <td>152</td>
                <td>152</td>
                <td>0</td>
                <td class="right" data-ratio="0 152">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="async_data_handler_py.html">async_data_handler.py</a></td>
                <td class="name left"><a href="async_data_handler_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>37</td>
                <td>37</td>
                <td>36</td>
                <td class="right" data-ratio="0 37">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="async_optimization_py.html#t18">async_optimization.py</a></td>
                <td class="name left"><a href="async_optimization_py.html#t18"><data value='AsyncTaskResult'>AsyncTaskResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="async_optimization_py.html#t27">async_optimization.py</a></td>
                <td class="name left"><a href="async_optimization_py.html#t27"><data value='AsyncTaskManager'>AsyncTaskManager</data></a></td>
                <td>41</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="0 41">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="async_optimization_py.html#t128">async_optimization.py</a></td>
                <td class="name left"><a href="async_optimization_py.html#t128"><data value='AsyncTradingLoop'>AsyncTradingLoop</data></a></td>
                <td>129</td>
                <td>129</td>
                <td>0</td>
                <td class="right" data-ratio="0 129">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="async_optimization_py.html">async_optimization.py</a></td>
                <td class="name left"><a href="async_optimization_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>47</td>
                <td>47</td>
                <td>2</td>
                <td class="right" data-ratio="0 47">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="async_resource_manager_py.html#t22">async_resource_manager.py</a></td>
                <td class="name left"><a href="async_resource_manager_py.html#t22"><data value='AsyncDatabaseConnectionManager'>AsyncDatabaseConnectionManager</data></a></td>
                <td>79</td>
                <td>73</td>
                <td>0</td>
                <td class="right" data-ratio="6 79">8%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="async_resource_manager_py.html#t168">async_resource_manager.py</a></td>
                <td class="name left"><a href="async_resource_manager_py.html#t168"><data value='SystemMonitor'>SystemMonitor</data></a></td>
                <td>67</td>
                <td>58</td>
                <td>0</td>
                <td class="right" data-ratio="9 67">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="async_resource_manager_py.html#t316">async_resource_manager.py</a></td>
                <td class="name left"><a href="async_resource_manager_py.html#t316"><data value='AsyncResourceManager'>AsyncResourceManager</data></a></td>
                <td>22</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="5 22">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="async_resource_manager_py.html">async_resource_manager.py</a></td>
                <td class="name left"><a href="async_resource_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>85</td>
                <td>30</td>
                <td>2</td>
                <td class="right" data-ratio="55 85">65%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="async_state_persistence_py.html#t27">async_state_persistence.py</a></td>
                <td class="name left"><a href="async_state_persistence_py.html#t27"><data value='PersistenceOperation'>PersistenceOperation</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="async_state_persistence_py.html#t36">async_state_persistence.py</a></td>
                <td class="name left"><a href="async_state_persistence_py.html#t36"><data value='SerializationFormat'>SerializationFormat</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="async_state_persistence_py.html#t44">async_state_persistence.py</a></td>
                <td class="name left"><a href="async_state_persistence_py.html#t44"><data value='PersistenceTask'>PersistenceTask</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="async_state_persistence_py.html#t65">async_state_persistence.py</a></td>
                <td class="name left"><a href="async_state_persistence_py.html#t65"><data value='AsyncStatePersistenceManager'>AsyncStatePersistenceManager</data></a></td>
                <td>201</td>
                <td>185</td>
                <td>0</td>
                <td class="right" data-ratio="16 201">8%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="async_state_persistence_py.html">async_state_persistence.py</a></td>
                <td class="name left"><a href="async_state_persistence_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>110</td>
                <td>32</td>
                <td>2</td>
                <td class="right" data-ratio="78 110">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="backtesting_py.html#t14">backtesting.py</a></td>
                <td class="name left"><a href="backtesting_py.html#t14"><data value='BacktestEngine'>BacktestEngine</data></a></td>
                <td>230</td>
                <td>230</td>
                <td>0</td>
                <td class="right" data-ratio="0 230">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="backtesting_py.html">backtesting.py</a></td>
                <td class="name left"><a href="backtesting_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="command_system_py.html#t21">command_system.py</a></td>
                <td class="name left"><a href="command_system_py.html#t21"><data value='CommandType'>CommandType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="command_system_py.html#t32">command_system.py</a></td>
                <td class="name left"><a href="command_system_py.html#t32"><data value='CommandContext'>CommandContext</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="command_system_py.html#t41">command_system.py</a></td>
                <td class="name left"><a href="command_system_py.html#t41"><data value='Command'>Command</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>4</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="command_system_py.html#t92">command_system.py</a></td>
                <td class="name left"><a href="command_system_py.html#t92"><data value='LiveTradingCommand'>LiveTradingCommand</data></a></td>
                <td>36</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 36">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="command_system_py.html#t170">command_system.py</a></td>
                <td class="name left"><a href="command_system_py.html#t170"><data value='BacktestCommand'>BacktestCommand</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="command_system_py.html#t227">command_system.py</a></td>
                <td class="name left"><a href="command_system_py.html#t227"><data value='PortfolioTradingCommand'>PortfolioTradingCommand</data></a></td>
                <td>36</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 36">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="command_system_py.html#t303">command_system.py</a></td>
                <td class="name left"><a href="command_system_py.html#t303"><data value='HealthCheckCommand'>HealthCheckCommand</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="command_system_py.html#t337">command_system.py</a></td>
                <td class="name left"><a href="command_system_py.html#t337"><data value='CommandFactory'>CommandFactory</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="command_system_py.html#t368">command_system.py</a></td>
                <td class="name left"><a href="command_system_py.html#t368"><data value='CommandExecutor'>CommandExecutor</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="command_system_py.html">command_system.py</a></td>
                <td class="name left"><a href="command_system_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>67</td>
                <td>67</td>
                <td>6</td>
                <td class="right" data-ratio="0 67">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="comprehensive_monitoring_py.html#t81">comprehensive_monitoring.py</a></td>
                <td class="name left"><a href="comprehensive_monitoring_py.html#t81"><data value='MetricDefinition'>MetricDefinition</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="comprehensive_monitoring_py.html#t90">comprehensive_monitoring.py</a></td>
                <td class="name left"><a href="comprehensive_monitoring_py.html#t90"><data value='PrometheusMetricsManager'>PrometheusMetricsManager</data></a></td>
                <td>37</td>
                <td>37</td>
                <td>0</td>
                <td class="right" data-ratio="0 37">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="comprehensive_monitoring_py.html#t204">comprehensive_monitoring.py</a></td>
                <td class="name left"><a href="comprehensive_monitoring_py.html#t204"><data value='StructuredLogger'>StructuredLogger</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="comprehensive_monitoring_py.html#t276">comprehensive_monitoring.py</a></td>
                <td class="name left"><a href="comprehensive_monitoring_py.html#t276"><data value='ComprehensiveMonitor'>ComprehensiveMonitor</data></a></td>
                <td>100</td>
                <td>100</td>
                <td>0</td>
                <td class="right" data-ratio="0 100">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="comprehensive_monitoring_py.html#t571">comprehensive_monitoring.py</a></td>
                <td class="name left"><a href="comprehensive_monitoring_py.html#t571"><data value='MockPortfolioSystem'>main.MockPortfolioSystem</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="comprehensive_monitoring_py.html">comprehensive_monitoring.py</a></td>
                <td class="name left"><a href="comprehensive_monitoring_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>93</td>
                <td>93</td>
                <td>2</td>
                <td class="right" data-ratio="0 93">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="comprehensive_optimization_py.html#t24">comprehensive_optimization.py</a></td>
                <td class="name left"><a href="comprehensive_optimization_py.html#t24"><data value='EnhancedResourceManager'>EnhancedResourceManager</data></a></td>
                <td>91</td>
                <td>91</td>
                <td>0</td>
                <td class="right" data-ratio="0 91">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="comprehensive_optimization_py.html#t204">comprehensive_optimization.py</a></td>
                <td class="name left"><a href="comprehensive_optimization_py.html#t204"><data value='ConfigurationOptimizer'>ConfigurationOptimizer</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="comprehensive_optimization_py.html#t269">comprehensive_optimization.py</a></td>
                <td class="name left"><a href="comprehensive_optimization_py.html#t269"><data value='DataQualityEnhancer'>DataQualityEnhancer</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="comprehensive_optimization_py.html">comprehensive_optimization.py</a></td>
                <td class="name left"><a href="comprehensive_optimization_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>56</td>
                <td>56</td>
                <td>2</td>
                <td class="right" data-ratio="0 56">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html#t18">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html#t18"><data value='ConfigLoader'>ConfigLoader</data></a></td>
                <td>100</td>
                <td>100</td>
                <td>0</td>
                <td class="right" data-ratio="0 100">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_loader_py.html">config_loader.py</a></td>
                <td class="name left"><a href="config_loader_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>24</td>
                <td>13</td>
                <td class="right" data-ratio="0 24">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_manager_py.html#t18">config_manager.py</a></td>
                <td class="name left"><a href="config_manager_py.html#t18"><data value='ConfigManager'>ConfigManager</data></a></td>
                <td>128</td>
                <td>128</td>
                <td>0</td>
                <td class="right" data-ratio="0 128">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_manager_py.html">config_manager.py</a></td>
                <td class="name left"><a href="config_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>45</td>
                <td>45</td>
                <td>2</td>
                <td class="right" data-ratio="0 45">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_validation_py.html#t32">config_validation.py</a></td>
                <td class="name left"><a href="config_validation_py.html#t32"><data value='ExchangeType'>ExchangeType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_validation_py.html#t42">config_validation.py</a></td>
                <td class="name left"><a href="config_validation_py.html#t42"><data value='TimeframeType'>TimeframeType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_validation_py.html#t53">config_validation.py</a></td>
                <td class="name left"><a href="config_validation_py.html#t53"><data value='LogLevel'>LogLevel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_validation_py.html#t62">config_validation.py</a></td>
                <td class="name left"><a href="config_validation_py.html#t62"><data value='TradingMode'>TradingMode</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_validation_py.html#t68">config_validation.py</a></td>
                <td class="name left"><a href="config_validation_py.html#t68"><data value='FuturesType'>FuturesType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_validation_py.html#t74">config_validation.py</a></td>
                <td class="name left"><a href="config_validation_py.html#t74"><data value='MarginMode'>MarginMode</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_validation_py.html#t80">config_validation.py</a></td>
                <td class="name left"><a href="config_validation_py.html#t80"><data value='TradingConfig'>TradingConfig</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_validation_py.html#t139">config_validation.py</a></td>
                <td class="name left"><a href="config_validation_py.html#t139"><data value='Config'>TradingConfig.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_validation_py.html#t144">config_validation.py</a></td>
                <td class="name left"><a href="config_validation_py.html#t144"><data value='DatabaseConfig'>DatabaseConfig</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_validation_py.html#t163">config_validation.py</a></td>
                <td class="name left"><a href="config_validation_py.html#t163"><data value='Config'>DatabaseConfig.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_validation_py.html#t167">config_validation.py</a></td>
                <td class="name left"><a href="config_validation_py.html#t167"><data value='MonitoringConfig'>MonitoringConfig</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_validation_py.html#t199">config_validation.py</a></td>
                <td class="name left"><a href="config_validation_py.html#t199"><data value='Config'>MonitoringConfig.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_validation_py.html#t203">config_validation.py</a></td>
                <td class="name left"><a href="config_validation_py.html#t203"><data value='PerformanceConfig'>PerformanceConfig</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_validation_py.html#t235">config_validation.py</a></td>
                <td class="name left"><a href="config_validation_py.html#t235"><data value='Config'>PerformanceConfig.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_validation_py.html#t239">config_validation.py</a></td>
                <td class="name left"><a href="config_validation_py.html#t239"><data value='EventDrivenConfig'>EventDrivenConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_validation_py.html#t252">config_validation.py</a></td>
                <td class="name left"><a href="config_validation_py.html#t252"><data value='Config'>EventDrivenConfig.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_validation_py.html#t256">config_validation.py</a></td>
                <td class="name left"><a href="config_validation_py.html#t256"><data value='StrategyConfig'>StrategyConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_validation_py.html#t272">config_validation.py</a></td>
                <td class="name left"><a href="config_validation_py.html#t272"><data value='Config'>StrategyConfig.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_validation_py.html#t276">config_validation.py</a></td>
                <td class="name left"><a href="config_validation_py.html#t276"><data value='ComprehensiveConfig'>ComprehensiveConfig</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_validation_py.html#t325">config_validation.py</a></td>
                <td class="name left"><a href="config_validation_py.html#t325"><data value='Config'>ComprehensiveConfig.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_validation_py.html#t332">config_validation.py</a></td>
                <td class="name left"><a href="config_validation_py.html#t332"><data value='ConfigValidator'>ConfigValidator</data></a></td>
                <td>84</td>
                <td>84</td>
                <td>0</td>
                <td class="right" data-ratio="0 84">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="config_validation_py.html">config_validation.py</a></td>
                <td class="name left"><a href="config_validation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>174</td>
                <td>174</td>
                <td>15</td>
                <td class="right" data-ratio="0 174">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="data_directory_manager_py.html#t21">data_directory_manager.py</a></td>
                <td class="name left"><a href="data_directory_manager_py.html#t21"><data value='DataType'>DataType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="data_directory_manager_py.html#t34">data_directory_manager.py</a></td>
                <td class="name left"><a href="data_directory_manager_py.html#t34"><data value='DirectoryInfo'>DirectoryInfo</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="data_directory_manager_py.html#t43">data_directory_manager.py</a></td>
                <td class="name left"><a href="data_directory_manager_py.html#t43"><data value='DataDirectoryManager'>DataDirectoryManager</data></a></td>
                <td>159</td>
                <td>147</td>
                <td>0</td>
                <td class="right" data-ratio="12 159">8%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="data_directory_manager_py.html">data_directory_manager.py</a></td>
                <td class="name left"><a href="data_directory_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>72</td>
                <td>22</td>
                <td>3</td>
                <td class="right" data-ratio="50 72">69%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="data_handler_py.html#t15">data_handler.py</a></td>
                <td class="name left"><a href="data_handler_py.html#t15"><data value='DataHandler'>DataHandler</data></a></td>
                <td>162</td>
                <td>162</td>
                <td>0</td>
                <td class="right" data-ratio="0 162">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="data_handler_py.html">data_handler.py</a></td>
                <td class="name left"><a href="data_handler_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="database_manager_py.html#t24">database_manager.py</a></td>
                <td class="name left"><a href="database_manager_py.html#t24"><data value='DatabaseManager'>DatabaseManager</data></a></td>
                <td>209</td>
                <td>209</td>
                <td>0</td>
                <td class="right" data-ratio="0 209">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="database_manager_py.html">database_manager.py</a></td>
                <td class="name left"><a href="database_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>37</td>
                <td>37</td>
                <td>11</td>
                <td class="right" data-ratio="0 37">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="debug_routes_py.html">debug_routes.py</a></td>
                <td class="name left"><a href="debug_routes_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>39</td>
                <td>39</td>
                <td>3</td>
                <td class="right" data-ratio="0 39">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="demo_multi_strategy_system_py.html#t24">demo_multi_strategy_system.py</a></td>
                <td class="name left"><a href="demo_multi_strategy_system_py.html#t24"><data value='MultiStrategyDemo'>MultiStrategyDemo</data></a></td>
                <td>106</td>
                <td>106</td>
                <td>0</td>
                <td class="right" data-ratio="0 106">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="demo_multi_strategy_system_py.html">demo_multi_strategy_system.py</a></td>
                <td class="name left"><a href="demo_multi_strategy_system_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>39</td>
                <td>39</td>
                <td>2</td>
                <td class="right" data-ratio="0 39">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="demo_smart_leverage_py.html">demo_smart_leverage.py</a></td>
                <td class="name left"><a href="demo_smart_leverage_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>111</td>
                <td>111</td>
                <td>2</td>
                <td class="right" data-ratio="0 111">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="dynamic_allocation_engine_py.html#t23">dynamic_allocation_engine.py</a></td>
                <td class="name left"><a href="dynamic_allocation_engine_py.html#t23"><data value='StrategyPerformance'>StrategyPerformance</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="dynamic_allocation_engine_py.html#t38">dynamic_allocation_engine.py</a></td>
                <td class="name left"><a href="dynamic_allocation_engine_py.html#t38"><data value='DynamicAllocationEngine'>DynamicAllocationEngine</data></a></td>
                <td>171</td>
                <td>171</td>
                <td>0</td>
                <td class="right" data-ratio="0 171">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="dynamic_allocation_engine_py.html">dynamic_allocation_engine.py</a></td>
                <td class="name left"><a href="dynamic_allocation_engine_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>36</td>
                <td>36</td>
                <td>3</td>
                <td class="right" data-ratio="0 36">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="dynamic_config_py.html#t21">dynamic_config.py</a></td>
                <td class="name left"><a href="dynamic_config_py.html#t21"><data value='ConfigChangeHandler'>ConfigChangeHandler</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="dynamic_config_py.html#t34">dynamic_config.py</a></td>
                <td class="name left"><a href="dynamic_config_py.html#t34"><data value='DynamicConfigManager'>DynamicConfigManager</data></a></td>
                <td>137</td>
                <td>137</td>
                <td>0</td>
                <td class="right" data-ratio="0 137">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="dynamic_config_py.html">dynamic_config.py</a></td>
                <td class="name left"><a href="dynamic_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>35</td>
                <td>3</td>
                <td>14</td>
                <td class="right" data-ratio="32 35">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="enhanced_event_integration_py.html#t19">enhanced_event_integration.py</a></td>
                <td class="name left"><a href="enhanced_event_integration_py.html#t19"><data value='SystemMetrics'>SystemMetrics</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="enhanced_event_integration_py.html#t31">enhanced_event_integration.py</a></td>
                <td class="name left"><a href="enhanced_event_integration_py.html#t31"><data value='EnhancedEventIntegrator'>EnhancedEventIntegrator</data></a></td>
                <td>69</td>
                <td>69</td>
                <td>0</td>
                <td class="right" data-ratio="0 69">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="enhanced_event_integration_py.html">enhanced_event_integration.py</a></td>
                <td class="name left"><a href="enhanced_event_integration_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>43</td>
                <td>43</td>
                <td>23</td>
                <td class="right" data-ratio="0 43">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="enhanced_portfolio_demo_py.html#t22">enhanced_portfolio_demo.py</a></td>
                <td class="name left"><a href="enhanced_portfolio_demo_py.html#t22"><data value='EnhancedPortfolioDemo'>EnhancedPortfolioDemo</data></a></td>
                <td>124</td>
                <td>124</td>
                <td>0</td>
                <td class="right" data-ratio="0 124">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="enhanced_portfolio_demo_py.html">enhanced_portfolio_demo.py</a></td>
                <td class="name left"><a href="enhanced_portfolio_demo_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>26</td>
                <td>26</td>
                <td>2</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="enhanced_retry_handler_py.html#t23">enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="enhanced_retry_handler_py.html#t23"><data value='RetryStrategy'>RetryStrategy</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="enhanced_retry_handler_py.html#t32">enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="enhanced_retry_handler_py.html#t32"><data value='RetryConfig'>RetryConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="enhanced_retry_handler_py.html#t64">enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="enhanced_retry_handler_py.html#t64"><data value='RetryMetrics'>RetryMetrics</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="enhanced_retry_handler_py.html#t109">enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="enhanced_retry_handler_py.html#t109"><data value='EnhancedCircuitBreaker'>EnhancedCircuitBreaker</data></a></td>
                <td>92</td>
                <td>92</td>
                <td>0</td>
                <td class="right" data-ratio="0 92">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="enhanced_retry_handler_py.html#t272">enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="enhanced_retry_handler_py.html#t272"><data value='EnhancedRetryHandler'>EnhancedRetryHandler</data></a></td>
                <td>136</td>
                <td>136</td>
                <td>0</td>
                <td class="right" data-ratio="0 136">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="enhanced_retry_handler_py.html">enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="enhanced_retry_handler_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>66</td>
                <td>66</td>
                <td>26</td>
                <td class="right" data-ratio="0 66">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="event_driven_core_py.html#t23">event_driven_core.py</a></td>
                <td class="name left"><a href="event_driven_core_py.html#t23"><data value='EventType'>EventType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="event_driven_core_py.html#t48">event_driven_core.py</a></td>
                <td class="name left"><a href="event_driven_core_py.html#t48"><data value='Event'>Event</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="event_driven_core_py.html#t81">event_driven_core.py</a></td>
                <td class="name left"><a href="event_driven_core_py.html#t81"><data value='EventHandler'>EventHandler</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>5</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="event_driven_core_py.html#t136">event_driven_core.py</a></td>
                <td class="name left"><a href="event_driven_core_py.html#t136"><data value='EventBus'>EventBus</data></a></td>
                <td>80</td>
                <td>80</td>
                <td>0</td>
                <td class="right" data-ratio="0 80">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="event_driven_core_py.html#t303">event_driven_core.py</a></td>
                <td class="name left"><a href="event_driven_core_py.html#t303"><data value='SignalGeneratedHandler'>SignalGeneratedHandler</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="event_driven_core_py.html#t369">event_driven_core.py</a></td>
                <td class="name left"><a href="event_driven_core_py.html#t369"><data value='HealthCheckHandler'>HealthCheckHandler</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="event_driven_core_py.html">event_driven_core.py</a></td>
                <td class="name left"><a href="event_driven_core_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>82</td>
                <td>82</td>
                <td>4</td>
                <td class="right" data-ratio="0 82">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="event_publisher_decorators_py.html#t82">event_publisher_decorators.py</a></td>
                <td class="name left"><a href="event_publisher_decorators_py.html#t82"><data value='AutoEventPublisher'>AutoEventPublisher</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="event_publisher_decorators_py.html">event_publisher_decorators.py</a></td>
                <td class="name left"><a href="event_publisher_decorators_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="example_usage_py.html">example_usage.py</a></td>
                <td class="name left"><a href="example_usage_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>148</td>
                <td>148</td>
                <td>8</td>
                <td class="right" data-ratio="0 148">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="exception_system_py.html#t19">exception_system.py</a></td>
                <td class="name left"><a href="exception_system_py.html#t19"><data value='ErrorSeverity'>ErrorSeverity</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="exception_system_py.html#t27">exception_system.py</a></td>
                <td class="name left"><a href="exception_system_py.html#t27"><data value='ErrorCategory'>ErrorCategory</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="exception_system_py.html#t56">exception_system.py</a></td>
                <td class="name left"><a href="exception_system_py.html#t56"><data value='TradingException'>TradingException</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="exception_system_py.html#t125">exception_system.py</a></td>
                <td class="name left"><a href="exception_system_py.html#t125"><data value='DataUnavailableError'>DataUnavailableError</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="exception_system_py.html#t140">exception_system.py</a></td>
                <td class="name left"><a href="exception_system_py.html#t140"><data value='DataInvalidError'>DataInvalidError</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="exception_system_py.html#t155">exception_system.py</a></td>
                <td class="name left"><a href="exception_system_py.html#t155"><data value='DataStaleError'>DataStaleError</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="exception_system_py.html#t172">exception_system.py</a></td>
                <td class="name left"><a href="exception_system_py.html#t172"><data value='OrderExecutionError'>OrderExecutionError</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="exception_system_py.html#t191">exception_system.py</a></td>
                <td class="name left"><a href="exception_system_py.html#t191"><data value='InsufficientFundsError'>InsufficientFundsError</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="exception_system_py.html#t210">exception_system.py</a></td>
                <td class="name left"><a href="exception_system_py.html#t210"><data value='PositionLimitExceededError'>PositionLimitExceededError</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="exception_system_py.html#t230">exception_system.py</a></td>
                <td class="name left"><a href="exception_system_py.html#t230"><data value='NetworkError'>NetworkError</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="exception_system_py.html#t248">exception_system.py</a></td>
                <td class="name left"><a href="exception_system_py.html#t248"><data value='APIRateLimitError'>APIRateLimitError</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="exception_system_py.html#t266">exception_system.py</a></td>
                <td class="name left"><a href="exception_system_py.html#t266"><data value='APIAuthenticationError'>APIAuthenticationError</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="exception_system_py.html#t286">exception_system.py</a></td>
                <td class="name left"><a href="exception_system_py.html#t286"><data value='ConfigurationError'>ConfigurationError</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="exception_system_py.html#t305">exception_system.py</a></td>
                <td class="name left"><a href="exception_system_py.html#t305"><data value='ResourceExhaustedError'>ResourceExhaustedError</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="exception_system_py.html#t321">exception_system.py</a></td>
                <td class="name left"><a href="exception_system_py.html#t321"><data value='StrategyError'>StrategyError</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="exception_system_py.html#t336">exception_system.py</a></td>
                <td class="name left"><a href="exception_system_py.html#t336"><data value='RiskViolationError'>RiskViolationError</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="exception_system_py.html#t355">exception_system.py</a></td>
                <td class="name left"><a href="exception_system_py.html#t355"><data value='ExceptionManager'>ExceptionManager</data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="exception_system_py.html">exception_system.py</a></td>
                <td class="name left"><a href="exception_system_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>81</td>
                <td>81</td>
                <td>8</td>
                <td class="right" data-ratio="0 81">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="exchange_factory_py.html#t17">exchange_factory.py</a></td>
                <td class="name left"><a href="exchange_factory_py.html#t17"><data value='ExchangeGateway'>ExchangeGateway</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>8</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="exchange_factory_py.html#t37">exchange_factory.py</a></td>
                <td class="name left"><a href="exchange_factory_py.html#t37"><data value='CCXTExchangeGateway'>CCXTExchangeGateway</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="exchange_factory_py.html#t60">exchange_factory.py</a></td>
                <td class="name left"><a href="exchange_factory_py.html#t60"><data value='MockExchangeGateway'>MockExchangeGateway</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="exchange_factory_py.html#t110">exchange_factory.py</a></td>
                <td class="name left"><a href="exchange_factory_py.html#t110"><data value='ExchangeFactory'>ExchangeFactory</data></a></td>
                <td>61</td>
                <td>61</td>
                <td>0</td>
                <td class="right" data-ratio="0 61">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="exchange_factory_py.html">exchange_factory.py</a></td>
                <td class="name left"><a href="exchange_factory_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>25</td>
                <td>25</td>
                <td>19</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="final_optimization_validation_py.html#t25">final_optimization_validation.py</a></td>
                <td class="name left"><a href="final_optimization_validation_py.html#t25"><data value='FinalOptimizationValidator'>FinalOptimizationValidator</data></a></td>
                <td>187</td>
                <td>187</td>
                <td>0</td>
                <td class="right" data-ratio="0 187">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="final_optimization_validation_py.html#t375">final_optimization_validation.py</a></td>
                <td class="name left"><a href="final_optimization_validation_py.html#t375"><data value='MockPortfolioSystem'>FinalOptimizationValidator._validate_comprehensive_monitoring.MockPortfolioSystem</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="final_optimization_validation_py.html">final_optimization_validation.py</a></td>
                <td class="name left"><a href="final_optimization_validation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>28</td>
                <td>28</td>
                <td>2</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="final_system_validation_py.html#t27">final_system_validation.py</a></td>
                <td class="name left"><a href="final_system_validation_py.html#t27"><data value='FinalSystemValidator'>FinalSystemValidator</data></a></td>
                <td>123</td>
                <td>123</td>
                <td>0</td>
                <td class="right" data-ratio="0 123">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="final_system_validation_py.html">final_system_validation.py</a></td>
                <td class="name left"><a href="final_system_validation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>28</td>
                <td>28</td>
                <td>2</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fix_critical_issues_py.html#t22">fix_critical_issues.py</a></td>
                <td class="name left"><a href="fix_critical_issues_py.html#t22"><data value='CriticalIssuesFixer'>CriticalIssuesFixer</data></a></td>
                <td>68</td>
                <td>68</td>
                <td>0</td>
                <td class="right" data-ratio="0 68">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fix_critical_issues_py.html">fix_critical_issues.py</a></td>
                <td class="name left"><a href="fix_critical_issues_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>27</td>
                <td>27</td>
                <td>2</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fix_resource_management_py.html#t15">fix_resource_management.py</a></td>
                <td class="name left"><a href="fix_resource_management_py.html#t15"><data value='DatabaseManager'>DatabaseManager</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fix_resource_management_py.html#t52">fix_resource_management.py</a></td>
                <td class="name left"><a href="fix_resource_management_py.html#t52"><data value='ResourceManagementFixer'>ResourceManagementFixer</data></a></td>
                <td>81</td>
                <td>81</td>
                <td>1</td>
                <td class="right" data-ratio="0 81">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="fix_resource_management_py.html">fix_resource_management.py</a></td>
                <td class="name left"><a href="fix_resource_management_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>28</td>
                <td>28</td>
                <td>2</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="global_event_bus_py.html#t21">global_event_bus.py</a></td>
                <td class="name left"><a href="global_event_bus_py.html#t21"><data value='EventType'>EventType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="global_event_bus_py.html#t59">global_event_bus.py</a></td>
                <td class="name left"><a href="global_event_bus_py.html#t59"><data value='Event'>Event</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="global_event_bus_py.html#t92">global_event_bus.py</a></td>
                <td class="name left"><a href="global_event_bus_py.html#t92"><data value='EventSubscription'>EventSubscription</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="global_event_bus_py.html#t115">global_event_bus.py</a></td>
                <td class="name left"><a href="global_event_bus_py.html#t115"><data value='GlobalEventBus'>GlobalEventBus</data></a></td>
                <td>82</td>
                <td>72</td>
                <td>0</td>
                <td class="right" data-ratio="10 82">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="global_event_bus_py.html">global_event_bus.py</a></td>
                <td class="name left"><a href="global_event_bus_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>70</td>
                <td>3</td>
                <td>36</td>
                <td class="right" data-ratio="67 70">96%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="graceful_shutdown_py.html#t20">graceful_shutdown.py</a></td>
                <td class="name left"><a href="graceful_shutdown_py.html#t20"><data value='ShutdownComponent'>ShutdownComponent</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>7</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="graceful_shutdown_py.html#t38">graceful_shutdown.py</a></td>
                <td class="name left"><a href="graceful_shutdown_py.html#t38"><data value='GracefulShutdownManager'>GracefulShutdownManager</data></a></td>
                <td>84</td>
                <td>70</td>
                <td>0</td>
                <td class="right" data-ratio="14 84">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="graceful_shutdown_py.html#t228">graceful_shutdown.py</a></td>
                <td class="name left"><a href="graceful_shutdown_py.html#t228"><data value='ExampleShutdownComponent'>ExampleShutdownComponent</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="graceful_shutdown_py.html">graceful_shutdown.py</a></td>
                <td class="name left"><a href="graceful_shutdown_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>60</td>
                <td>26</td>
                <td>7</td>
                <td class="right" data-ratio="34 60">57%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="health_checker_py.html#t20">health_checker.py</a></td>
                <td class="name left"><a href="health_checker_py.html#t20"><data value='SystemHealthChecker'>SystemHealthChecker</data></a></td>
                <td>188</td>
                <td>188</td>
                <td>0</td>
                <td class="right" data-ratio="0 188">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="health_checker_py.html">health_checker.py</a></td>
                <td class="name left"><a href="health_checker_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>20</td>
                <td>6</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="health_server_py.html#t42">health_server.py</a></td>
                <td class="name left"><a href="health_server_py.html#t42"><data value='HealthServer'>HealthServer</data></a></td>
                <td>154</td>
                <td>154</td>
                <td>0</td>
                <td class="right" data-ratio="0 154">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="health_server_py.html">health_server.py</a></td>
                <td class="name left"><a href="health_server_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>40</td>
                <td>40</td>
                <td>15</td>
                <td class="right" data-ratio="0 40">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="high_leverage_risk_checker_py.html#t22">high_leverage_risk_checker.py</a></td>
                <td class="name left"><a href="high_leverage_risk_checker_py.html#t22"><data value='HighLeverageRiskChecker'>HighLeverageRiskChecker</data></a></td>
                <td>68</td>
                <td>68</td>
                <td>0</td>
                <td class="right" data-ratio="0 68">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="high_leverage_risk_checker_py.html">high_leverage_risk_checker.py</a></td>
                <td class="name left"><a href="high_leverage_risk_checker_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>49</td>
                <td>49</td>
                <td>2</td>
                <td class="right" data-ratio="0 49">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="improved_backtest_py.html#t24">improved_backtest.py</a></td>
                <td class="name left"><a href="improved_backtest_py.html#t24"><data value='ImprovedBacktestEngine'>ImprovedBacktestEngine</data></a></td>
                <td>112</td>
                <td>112</td>
                <td>0</td>
                <td class="right" data-ratio="0 112">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="improved_backtest_py.html#t290">improved_backtest.py</a></td>
                <td class="name left"><a href="improved_backtest_py.html#t290"><data value='MockExchange'>MockExchange</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="improved_backtest_py.html">improved_backtest.py</a></td>
                <td class="name left"><a href="improved_backtest_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>45</td>
                <td>45</td>
                <td>2</td>
                <td class="right" data-ratio="0 45">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="integrated_trading_executor_py.html#t23">integrated_trading_executor.py</a></td>
                <td class="name left"><a href="integrated_trading_executor_py.html#t23"><data value='RiskCheckResult'>RiskCheckResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="integrated_trading_executor_py.html#t31">integrated_trading_executor.py</a></td>
                <td class="name left"><a href="integrated_trading_executor_py.html#t31"><data value='TradingSignal'>TradingSignal</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="integrated_trading_executor_py.html#t48">integrated_trading_executor.py</a></td>
                <td class="name left"><a href="integrated_trading_executor_py.html#t48"><data value='RiskCheckReport'>RiskCheckReport</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="integrated_trading_executor_py.html#t62">integrated_trading_executor.py</a></td>
                <td class="name left"><a href="integrated_trading_executor_py.html#t62"><data value='IntegratedTradingExecutor'>IntegratedTradingExecutor</data></a></td>
                <td>178</td>
                <td>178</td>
                <td>0</td>
                <td class="right" data-ratio="0 178">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="integrated_trading_executor_py.html">integrated_trading_executor.py</a></td>
                <td class="name left"><a href="integrated_trading_executor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>83</td>
                <td>83</td>
                <td>2</td>
                <td class="right" data-ratio="0 83">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="intelligent_cache_manager_py.html#t25">intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="intelligent_cache_manager_py.html#t25"><data value='CacheStrategy'>CacheStrategy</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="intelligent_cache_manager_py.html#t34">intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="intelligent_cache_manager_py.html#t34"><data value='CacheEntry'>CacheEntry</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="intelligent_cache_manager_py.html#t71">intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="intelligent_cache_manager_py.html#t71"><data value='IntelligentCacheManager'>IntelligentCacheManager</data></a></td>
                <td>208</td>
                <td>208</td>
                <td>0</td>
                <td class="right" data-ratio="0 208">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="intelligent_cache_manager_py.html">intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="intelligent_cache_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>104</td>
                <td>104</td>
                <td>2</td>
                <td class="right" data-ratio="0 104">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="intelligent_portfolio_system_py.html#t25">intelligent_portfolio_system.py</a></td>
                <td class="name left"><a href="intelligent_portfolio_system_py.html#t25"><data value='IntelligentPortfolioSystem'>IntelligentPortfolioSystem</data></a></td>
                <td>115</td>
                <td>115</td>
                <td>0</td>
                <td class="right" data-ratio="0 115">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="intelligent_portfolio_system_py.html">intelligent_portfolio_system.py</a></td>
                <td class="name left"><a href="intelligent_portfolio_system_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>71</td>
                <td>71</td>
                <td>2</td>
                <td class="right" data-ratio="0 71">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="logging_config_py.html">logging_config.py</a></td>
                <td class="name left"><a href="logging_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>40</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="7 40">18%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="main_py.html">main.py</a></td>
                <td class="name left"><a href="main_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>159</td>
                <td>159</td>
                <td>2</td>
                <td class="right" data-ratio="0 159">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="math_utils_py.html#t22">math_utils.py</a></td>
                <td class="name left"><a href="math_utils_py.html#t22"><data value='PairsTradingMath'>PairsTradingMath</data></a></td>
                <td>126</td>
                <td>126</td>
                <td>0</td>
                <td class="right" data-ratio="0 126">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="math_utils_py.html">math_utils.py</a></td>
                <td class="name left"><a href="math_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>39</td>
                <td>39</td>
                <td>15</td>
                <td class="right" data-ratio="0 39">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="multi_executor_manager_py.html#t25">multi_executor_manager.py</a></td>
                <td class="name left"><a href="multi_executor_manager_py.html#t25"><data value='TaskType'>TaskType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="multi_executor_manager_py.html#t34">multi_executor_manager.py</a></td>
                <td class="name left"><a href="multi_executor_manager_py.html#t34"><data value='TaskPriority'>TaskPriority</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="multi_executor_manager_py.html#t43">multi_executor_manager.py</a></td>
                <td class="name left"><a href="multi_executor_manager_py.html#t43"><data value='TaskMetrics'>TaskMetrics</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="multi_executor_manager_py.html#t70">multi_executor_manager.py</a></td>
                <td class="name left"><a href="multi_executor_manager_py.html#t70"><data value='MultiExecutorManager'>MultiExecutorManager</data></a></td>
                <td>146</td>
                <td>121</td>
                <td>0</td>
                <td class="right" data-ratio="25 146">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="multi_executor_manager_py.html">multi_executor_manager.py</a></td>
                <td class="name left"><a href="multi_executor_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>143</td>
                <td>67</td>
                <td>2</td>
                <td class="right" data-ratio="76 143">53%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="multi_strategy_engine_py.html#t27">multi_strategy_engine.py</a></td>
                <td class="name left"><a href="multi_strategy_engine_py.html#t27"><data value='EngineConfig'>EngineConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="multi_strategy_engine_py.html#t37">multi_strategy_engine.py</a></td>
                <td class="name left"><a href="multi_strategy_engine_py.html#t37"><data value='SignalAggregator'>SignalAggregator</data></a></td>
                <td>50</td>
                <td>50</td>
                <td>0</td>
                <td class="right" data-ratio="0 50">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="multi_strategy_engine_py.html#t143">multi_strategy_engine.py</a></td>
                <td class="name left"><a href="multi_strategy_engine_py.html#t143"><data value='MultiStrategyEngine'>MultiStrategyEngine</data></a></td>
                <td>124</td>
                <td>124</td>
                <td>0</td>
                <td class="right" data-ratio="0 124">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="multi_strategy_engine_py.html">multi_strategy_engine.py</a></td>
                <td class="name left"><a href="multi_strategy_engine_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>46</td>
                <td>46</td>
                <td>19</td>
                <td class="right" data-ratio="0 46">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pair_trading_bot_py.html#t18">pair_trading_bot.py</a></td>
                <td class="name left"><a href="pair_trading_bot_py.html#t18"><data value='TradingState'>TradingState</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pair_trading_bot_py.html#t25">pair_trading_bot.py</a></td>
                <td class="name left"><a href="pair_trading_bot_py.html#t25"><data value='SignalState'>SignalState</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pair_trading_bot_py.html#t32">pair_trading_bot.py</a></td>
                <td class="name left"><a href="pair_trading_bot_py.html#t32"><data value='TakeProfitTarget'>TakeProfitTarget</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pair_trading_bot_py.html#t37">pair_trading_bot.py</a></td>
                <td class="name left"><a href="pair_trading_bot_py.html#t37"><data value='PairTradingBot'>PairTradingBot</data></a></td>
                <td>201</td>
                <td>201</td>
                <td>0</td>
                <td class="right" data-ratio="0 201">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pair_trading_bot_py.html">pair_trading_bot.py</a></td>
                <td class="name left"><a href="pair_trading_bot_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>39</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="0 39">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pairs_trading_strategy_py.html#t21">pairs_trading_strategy.py</a></td>
                <td class="name left"><a href="pairs_trading_strategy_py.html#t21"><data value='PairsTradingStrategy'>PairsTradingStrategy</data></a></td>
                <td>139</td>
                <td>139</td>
                <td>0</td>
                <td class="right" data-ratio="0 139">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pairs_trading_strategy_py.html">pairs_trading_strategy.py</a></td>
                <td class="name left"><a href="pairs_trading_strategy_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>20</td>
                <td>16</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="perfect_validation_py.html">perfect_validation.py</a></td>
                <td class="name left"><a href="perfect_validation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>74</td>
                <td>74</td>
                <td>2</td>
                <td class="right" data-ratio="0 74">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="portfolio_backtester_py.html#t23">portfolio_backtester.py</a></td>
                <td class="name left"><a href="portfolio_backtester_py.html#t23"><data value='PortfolioBacktester'>PortfolioBacktester</data></a></td>
                <td>185</td>
                <td>185</td>
                <td>0</td>
                <td class="right" data-ratio="0 185">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="portfolio_backtester_py.html">portfolio_backtester.py</a></td>
                <td class="name left"><a href="portfolio_backtester_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>26</td>
                <td>26</td>
                <td>15</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="portfolio_manager_py.html#t23">portfolio_manager.py</a></td>
                <td class="name left"><a href="portfolio_manager_py.html#t23"><data value='StrategyAllocation'>StrategyAllocation</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="portfolio_manager_py.html#t36">portfolio_manager.py</a></td>
                <td class="name left"><a href="portfolio_manager_py.html#t36"><data value='CorrelationMatrix'>CorrelationMatrix</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="portfolio_manager_py.html#t58">portfolio_manager.py</a></td>
                <td class="name left"><a href="portfolio_manager_py.html#t58"><data value='PortfolioManager'>PortfolioManager</data></a></td>
                <td>144</td>
                <td>144</td>
                <td>0</td>
                <td class="right" data-ratio="0 144">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="portfolio_manager_py.html#t445">portfolio_manager.py</a></td>
                <td class="name left"><a href="portfolio_manager_py.html#t445"><data value='MockStrategy'>MockStrategy</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>4</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="portfolio_manager_py.html">portfolio_manager.py</a></td>
                <td class="name left"><a href="portfolio_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>43</td>
                <td>43</td>
                <td>21</td>
                <td class="right" data-ratio="0 43">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="production_readiness_check_py.html#t23">production_readiness_check.py</a></td>
                <td class="name left"><a href="production_readiness_check_py.html#t23"><data value='ProductionReadinessChecker'>ProductionReadinessChecker</data></a></td>
                <td>110</td>
                <td>110</td>
                <td>0</td>
                <td class="right" data-ratio="0 110">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="production_readiness_check_py.html">production_readiness_check.py</a></td>
                <td class="name left"><a href="production_readiness_check_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>49</td>
                <td>49</td>
                <td>2</td>
                <td class="right" data-ratio="0 49">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="quick_fix_final_issues_py.html">quick_fix_final_issues.py</a></td>
                <td class="name left"><a href="quick_fix_final_issues_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>30</td>
                <td>30</td>
                <td>3</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="quick_start_production_py.html#t27">quick_start_production.py</a></td>
                <td class="name left"><a href="quick_start_production_py.html#t27"><data value='ProductionLauncher'>ProductionLauncher</data></a></td>
                <td>129</td>
                <td>129</td>
                <td>0</td>
                <td class="right" data-ratio="0 129">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="quick_start_production_py.html">quick_start_production.py</a></td>
                <td class="name left"><a href="quick_start_production_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>30</td>
                <td>30</td>
                <td>8</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="resource_manager_py.html#t14">resource_manager.py</a></td>
                <td class="name left"><a href="resource_manager_py.html#t14"><data value='ResourceManager'>ResourceManager</data></a></td>
                <td>48</td>
                <td>48</td>
                <td>0</td>
                <td class="right" data-ratio="0 48">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="resource_manager_py.html">resource_manager.py</a></td>
                <td class="name left"><a href="resource_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>25</td>
                <td>25</td>
                <td>12</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="retry_handler_py.html#t17">retry_handler.py</a></td>
                <td class="name left"><a href="retry_handler_py.html#t17"><data value='RetryConfig'>RetryConfig</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="retry_handler_py.html#t35">retry_handler.py</a></td>
                <td class="name left"><a href="retry_handler_py.html#t35"><data value='RetryHandler'>RetryHandler</data></a></td>
                <td>44</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="0 44">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="retry_handler_py.html#t169">retry_handler.py</a></td>
                <td class="name left"><a href="retry_handler_py.html#t169"><data value='CommonRetryConfigs'>CommonRetryConfigs</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="retry_handler_py.html">retry_handler.py</a></td>
                <td class="name left"><a href="retry_handler_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>39</td>
                <td>39</td>
                <td>31</td>
                <td class="right" data-ratio="0 39">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="security_manager_py.html#t21">security_manager.py</a></td>
                <td class="name left"><a href="security_manager_py.html#t21"><data value='SecurityManager'>SecurityManager</data></a></td>
                <td>141</td>
                <td>141</td>
                <td>0</td>
                <td class="right" data-ratio="0 141">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="security_manager_py.html">security_manager.py</a></td>
                <td class="name left"><a href="security_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>54</td>
                <td>54</td>
                <td>14</td>
                <td class="right" data-ratio="0 54">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="simplified_optimization_validation_py.html#t20">simplified_optimization_validation.py</a></td>
                <td class="name left"><a href="simplified_optimization_validation_py.html#t20"><data value='SimplifiedOptimizationValidator'>SimplifiedOptimizationValidator</data></a></td>
                <td>173</td>
                <td>173</td>
                <td>0</td>
                <td class="right" data-ratio="0 173">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="simplified_optimization_validation_py.html">simplified_optimization_validation.py</a></td>
                <td class="name left"><a href="simplified_optimization_validation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>23</td>
                <td>23</td>
                <td>2</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="smart_capital_management_py.html#t17">smart_capital_management.py</a></td>
                <td class="name left"><a href="smart_capital_management_py.html#t17"><data value='RiskLevel'>RiskLevel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="smart_capital_management_py.html#t25">smart_capital_management.py</a></td>
                <td class="name left"><a href="smart_capital_management_py.html#t25"><data value='CapitalAllocation'>CapitalAllocation</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="smart_capital_management_py.html#t35">smart_capital_management.py</a></td>
                <td class="name left"><a href="smart_capital_management_py.html#t35"><data value='SmartCapitalManager'>SmartCapitalManager</data></a></td>
                <td>51</td>
                <td>51</td>
                <td>0</td>
                <td class="right" data-ratio="0 51">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="smart_capital_management_py.html">smart_capital_management.py</a></td>
                <td class="name left"><a href="smart_capital_management_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>34</td>
                <td>34</td>
                <td>19</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="strategy_config_models_py.html#t14">strategy_config_models.py</a></td>
                <td class="name left"><a href="strategy_config_models_py.html#t14"><data value='StrategyType'>StrategyType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="strategy_config_models_py.html#t24">strategy_config_models.py</a></td>
                <td class="name left"><a href="strategy_config_models_py.html#t24"><data value='RiskLevel'>RiskLevel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="strategy_config_models_py.html#t32">strategy_config_models.py</a></td>
                <td class="name left"><a href="strategy_config_models_py.html#t32"><data value='TimeFrame'>TimeFrame</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="strategy_config_models_py.html#t43">strategy_config_models.py</a></td>
                <td class="name left"><a href="strategy_config_models_py.html#t43"><data value='BaseStrategyConfig'>BaseStrategyConfig</data></a></td>
                <td>21</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="6 21">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="strategy_config_models_py.html#t116">strategy_config_models.py</a></td>
                <td class="name left"><a href="strategy_config_models_py.html#t116"><data value='Config'>BaseStrategyConfig.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="strategy_config_models_py.html#t122">strategy_config_models.py</a></td>
                <td class="name left"><a href="strategy_config_models_py.html#t122"><data value='PairsTradingConfig'>PairsTradingConfig</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="strategy_config_models_py.html#t154">strategy_config_models.py</a></td>
                <td class="name left"><a href="strategy_config_models_py.html#t154"><data value='MomentumConfig'>MomentumConfig</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="strategy_config_models_py.html#t181">strategy_config_models.py</a></td>
                <td class="name left"><a href="strategy_config_models_py.html#t181"><data value='ArbitrageConfig'>ArbitrageConfig</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="strategy_config_models_py.html#t207">strategy_config_models.py</a></td>
                <td class="name left"><a href="strategy_config_models_py.html#t207"><data value='StrategyConfigFactory'>StrategyConfigFactory</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="strategy_config_models_py.html#t242">strategy_config_models.py</a></td>
                <td class="name left"><a href="strategy_config_models_py.html#t242"><data value='StrategyConfigValidator'>StrategyConfigValidator</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="strategy_config_models_py.html">strategy_config_models.py</a></td>
                <td class="name left"><a href="strategy_config_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>114</td>
                <td>5</td>
                <td>22</td>
                <td class="right" data-ratio="109 114">96%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="strategy_framework_py.html#t20">strategy_framework.py</a></td>
                <td class="name left"><a href="strategy_framework_py.html#t20"><data value='StrategyType'>StrategyType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="strategy_framework_py.html#t30">strategy_framework.py</a></td>
                <td class="name left"><a href="strategy_framework_py.html#t30"><data value='SignalType'>SignalType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="strategy_framework_py.html#t40">strategy_framework.py</a></td>
                <td class="name left"><a href="strategy_framework_py.html#t40"><data value='SignalStrength'>SignalStrength</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="strategy_framework_py.html#t49">strategy_framework.py</a></td>
                <td class="name left"><a href="strategy_framework_py.html#t49"><data value='TradingSignal'>TradingSignal</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="strategy_framework_py.html#t72">strategy_framework.py</a></td>
                <td class="name left"><a href="strategy_framework_py.html#t72"><data value='StrategyPerformance'>StrategyPerformance</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="strategy_framework_py.html#t88">strategy_framework.py</a></td>
                <td class="name left"><a href="strategy_framework_py.html#t88"><data value='BaseStrategy'>BaseStrategy</data></a></td>
                <td>45</td>
                <td>45</td>
                <td>34</td>
                <td class="right" data-ratio="0 45">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="strategy_framework_py.html#t276">strategy_framework.py</a></td>
                <td class="name left"><a href="strategy_framework_py.html#t276"><data value='StrategyManager'>StrategyManager</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="strategy_framework_py.html">strategy_framework.py</a></td>
                <td class="name left"><a href="strategy_framework_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>72</td>
                <td>72</td>
                <td>19</td>
                <td class="right" data-ratio="0 72">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="strategy_health_monitor_py.html#t23">strategy_health_monitor.py</a></td>
                <td class="name left"><a href="strategy_health_monitor_py.html#t23"><data value='StrategyHealthMonitor'>StrategyHealthMonitor</data></a></td>
                <td>162</td>
                <td>162</td>
                <td>0</td>
                <td class="right" data-ratio="0 162">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="strategy_health_monitor_py.html">strategy_health_monitor.py</a></td>
                <td class="name left"><a href="strategy_health_monitor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>36</td>
                <td>3</td>
                <td>11</td>
                <td class="right" data-ratio="33 36">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="telegram_bot_py.html#t20">telegram_bot.py</a></td>
                <td class="name left"><a href="telegram_bot_py.html#t20"><data value='TelegramBotController'>TelegramBotController</data></a></td>
                <td>190</td>
                <td>190</td>
                <td>0</td>
                <td class="right" data-ratio="0 190">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="telegram_bot_py.html">telegram_bot.py</a></td>
                <td class="name left"><a href="telegram_bot_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>40</td>
                <td>40</td>
                <td>15</td>
                <td class="right" data-ratio="0 40">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="trading_exceptions_py.html#t11">trading_exceptions.py</a></td>
                <td class="name left"><a href="trading_exceptions_py.html#t11"><data value='TradingException'>TradingException</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="trading_exceptions_py.html#t21">trading_exceptions.py</a></td>
                <td class="name left"><a href="trading_exceptions_py.html#t21"><data value='ConfigError'>ConfigError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="trading_exceptions_py.html#t26">trading_exceptions.py</a></td>
                <td class="name left"><a href="trading_exceptions_py.html#t26"><data value='DataError'>DataError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="trading_exceptions_py.html#t31">trading_exceptions.py</a></td>
                <td class="name left"><a href="trading_exceptions_py.html#t31"><data value='ExecutionError'>ExecutionError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="trading_exceptions_py.html#t36">trading_exceptions.py</a></td>
                <td class="name left"><a href="trading_exceptions_py.html#t36"><data value='PartialFillError'>PartialFillError</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="trading_exceptions_py.html#t47">trading_exceptions.py</a></td>
                <td class="name left"><a href="trading_exceptions_py.html#t47"><data value='NetworkError'>NetworkError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="trading_exceptions_py.html#t52">trading_exceptions.py</a></td>
                <td class="name left"><a href="trading_exceptions_py.html#t52"><data value='InsufficientFundsError'>InsufficientFundsError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="trading_exceptions_py.html#t57">trading_exceptions.py</a></td>
                <td class="name left"><a href="trading_exceptions_py.html#t57"><data value='RiskLimitExceededError'>RiskLimitExceededError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="trading_exceptions_py.html">trading_exceptions.py</a></td>
                <td class="name left"><a href="trading_exceptions_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="trading_safety_guard_py.html#t18">trading_safety_guard.py</a></td>
                <td class="name left"><a href="trading_safety_guard_py.html#t18"><data value='TradingMode'>TradingMode</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="trading_safety_guard_py.html#t25">trading_safety_guard.py</a></td>
                <td class="name left"><a href="trading_safety_guard_py.html#t25"><data value='SafetyLevel'>SafetyLevel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="trading_safety_guard_py.html#t32">trading_safety_guard.py</a></td>
                <td class="name left"><a href="trading_safety_guard_py.html#t32"><data value='TradingSafetyGuard'>TradingSafetyGuard</data></a></td>
                <td>108</td>
                <td>108</td>
                <td>0</td>
                <td class="right" data-ratio="0 108">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="trading_safety_guard_py.html">trading_safety_guard.py</a></td>
                <td class="name left"><a href="trading_safety_guard_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>34</td>
                <td>34</td>
                <td>20</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="trend_following_strategy_py.html#t20">trend_following_strategy.py</a></td>
                <td class="name left"><a href="trend_following_strategy_py.html#t20"><data value='TrendFollowingStrategy'>TrendFollowingStrategy</data></a></td>
                <td>170</td>
                <td>170</td>
                <td>0</td>
                <td class="right" data-ratio="0 170">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="trend_following_strategy_py.html">trend_following_strategy.py</a></td>
                <td class="name left"><a href="trend_following_strategy_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>22</td>
                <td>22</td>
                <td>19</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="ultimate_system_validation_py.html#t28">ultimate_system_validation.py</a></td>
                <td class="name left"><a href="ultimate_system_validation_py.html#t28"><data value='UltimateSystemValidator'>UltimateSystemValidator</data></a></td>
                <td>166</td>
                <td>166</td>
                <td>0</td>
                <td class="right" data-ratio="0 166">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="ultimate_system_validation_py.html">ultimate_system_validation.py</a></td>
                <td class="name left"><a href="ultimate_system_validation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>29</td>
                <td>29</td>
                <td>2</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_client_manager_py.html#t20">unified_client_manager.py</a></td>
                <td class="name left"><a href="unified_client_manager_py.html#t20"><data value='UnifiedClientManager'>UnifiedClientManager</data></a></td>
                <td>78</td>
                <td>78</td>
                <td>0</td>
                <td class="right" data-ratio="0 78">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_client_manager_py.html">unified_client_manager.py</a></td>
                <td class="name left"><a href="unified_client_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>74</td>
                <td>74</td>
                <td>2</td>
                <td class="right" data-ratio="0 74">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_config_manager_py.html#t22">unified_config_manager.py</a></td>
                <td class="name left"><a href="unified_config_manager_py.html#t22"><data value='Environment'>Environment</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_config_manager_py.html#t31">unified_config_manager.py</a></td>
                <td class="name left"><a href="unified_config_manager_py.html#t31"><data value='ExchangeType'>ExchangeType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_config_manager_py.html#t39">unified_config_manager.py</a></td>
                <td class="name left"><a href="unified_config_manager_py.html#t39"><data value='TradingMode'>TradingMode</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_config_manager_py.html#t46">unified_config_manager.py</a></td>
                <td class="name left"><a href="unified_config_manager_py.html#t46"><data value='RiskLevel'>RiskLevel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_config_manager_py.html#t54">unified_config_manager.py</a></td>
                <td class="name left"><a href="unified_config_manager_py.html#t54"><data value='ExchangeConfig'>ExchangeConfig</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_config_manager_py.html#t79">unified_config_manager.py</a></td>
                <td class="name left"><a href="unified_config_manager_py.html#t79"><data value='TradingConfig'>TradingConfig</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_config_manager_py.html#t117">unified_config_manager.py</a></td>
                <td class="name left"><a href="unified_config_manager_py.html#t117"><data value='PairSelectionConfig'>PairSelectionConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_config_manager_py.html#t126">unified_config_manager.py</a></td>
                <td class="name left"><a href="unified_config_manager_py.html#t126"><data value='BacktestConfig'>BacktestConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_config_manager_py.html#t135">unified_config_manager.py</a></td>
                <td class="name left"><a href="unified_config_manager_py.html#t135"><data value='MonitoringConfig'>MonitoringConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_config_manager_py.html#t149">unified_config_manager.py</a></td>
                <td class="name left"><a href="unified_config_manager_py.html#t149"><data value='UnifiedConfig'>UnifiedConfig</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_config_manager_py.html#t199">unified_config_manager.py</a></td>
                <td class="name left"><a href="unified_config_manager_py.html#t199"><data value='Config'>UnifiedConfig.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_config_manager_py.html#t206">unified_config_manager.py</a></td>
                <td class="name left"><a href="unified_config_manager_py.html#t206"><data value='UnifiedConfigManager'>UnifiedConfigManager</data></a></td>
                <td>65</td>
                <td>59</td>
                <td>0</td>
                <td class="right" data-ratio="6 65">9%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_config_manager_py.html">unified_config_manager.py</a></td>
                <td class="name left"><a href="unified_config_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>135</td>
                <td>19</td>
                <td>3</td>
                <td class="right" data-ratio="116 135">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_pair_selector_py.html#t29">unified_pair_selector.py</a></td>
                <td class="name left"><a href="unified_pair_selector_py.html#t29"><data value='PairSelectionMethod'>PairSelectionMethod</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_pair_selector_py.html#t38">unified_pair_selector.py</a></td>
                <td class="name left"><a href="unified_pair_selector_py.html#t38"><data value='PairAnalysisResult'>PairAnalysisResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_pair_selector_py.html#t53">unified_pair_selector.py</a></td>
                <td class="name left"><a href="unified_pair_selector_py.html#t53"><data value='UnifiedPairSelector'>UnifiedPairSelector</data></a></td>
                <td>188</td>
                <td>188</td>
                <td>0</td>
                <td class="right" data-ratio="0 188">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_pair_selector_py.html">unified_pair_selector.py</a></td>
                <td class="name left"><a href="unified_pair_selector_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>69</td>
                <td>17</td>
                <td>3</td>
                <td class="right" data-ratio="52 69">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_portfolio_manager_py.html#t24">unified_portfolio_manager.py</a></td>
                <td class="name left"><a href="unified_portfolio_manager_py.html#t24"><data value='SystemOverview'>SystemOverview</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_portfolio_manager_py.html#t34">unified_portfolio_manager.py</a></td>
                <td class="name left"><a href="unified_portfolio_manager_py.html#t34"><data value='UnifiedPortfolioManager'>UnifiedPortfolioManager</data></a></td>
                <td>229</td>
                <td>229</td>
                <td>0</td>
                <td class="right" data-ratio="0 229">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_portfolio_manager_py.html">unified_portfolio_manager.py</a></td>
                <td class="name left"><a href="unified_portfolio_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>44</td>
                <td>44</td>
                <td>22</td>
                <td class="right" data-ratio="0 44">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_test_runner_py.html#t22">unified_test_runner.py</a></td>
                <td class="name left"><a href="unified_test_runner_py.html#t22"><data value='TestType'>TestType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_test_runner_py.html#t31">unified_test_runner.py</a></td>
                <td class="name left"><a href="unified_test_runner_py.html#t31"><data value='TestResult'>TestResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_test_runner_py.html#t40">unified_test_runner.py</a></td>
                <td class="name left"><a href="unified_test_runner_py.html#t40"><data value='TestSuiteResult'>TestSuiteResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_test_runner_py.html#t56">unified_test_runner.py</a></td>
                <td class="name left"><a href="unified_test_runner_py.html#t56"><data value='UnifiedTestRunner'>UnifiedTestRunner</data></a></td>
                <td>161</td>
                <td>161</td>
                <td>0</td>
                <td class="right" data-ratio="0 161">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_test_runner_py.html">unified_test_runner.py</a></td>
                <td class="name left"><a href="unified_test_runner_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>65</td>
                <td>15</td>
                <td>3</td>
                <td class="right" data-ratio="50 65">77%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="utils_py.html">utils.py</a></td>
                <td class="name left"><a href="utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>134</td>
                <td>113</td>
                <td>0</td>
                <td class="right" data-ratio="21 134">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="validate_improvements_py.html#t26">validate_improvements.py</a></td>
                <td class="name left"><a href="validate_improvements_py.html#t26"><data value='ImprovementValidator'>ImprovementValidator</data></a></td>
                <td>167</td>
                <td>167</td>
                <td>0</td>
                <td class="right" data-ratio="0 167">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="validate_improvements_py.html">validate_improvements.py</a></td>
                <td class="name left"><a href="validate_improvements_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>34</td>
                <td>34</td>
                <td>3</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>13378</td>
                <td>12455</td>
                <td>694</td>
                <td class="right" data-ratio="923 13378">7%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-07-06 11:40 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
