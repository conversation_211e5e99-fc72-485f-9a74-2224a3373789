<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_8e611ae1.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">20%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-07-06 13:04 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_advanced_dynamic_allocation_py.html#t40">src/advanced_dynamic_allocation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_advanced_dynamic_allocation_py.html#t40"><data value='StrategyMetrics'>StrategyMetrics</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_advanced_dynamic_allocation_py.html#t61">src/advanced_dynamic_allocation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_advanced_dynamic_allocation_py.html#t61"><data value='AdvancedRiskModel'>AdvancedRiskModel</data></a></td>
                <td>41</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="0 41">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_advanced_dynamic_allocation_py.html#t151">src/advanced_dynamic_allocation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_advanced_dynamic_allocation_py.html#t151"><data value='AdvancedDynamicAllocator'>AdvancedDynamicAllocator</data></a></td>
                <td>179</td>
                <td>179</td>
                <td>0</td>
                <td class="right" data-ratio="0 179">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_advanced_dynamic_allocation_py.html">src/advanced_dynamic_allocation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_advanced_dynamic_allocation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>64</td>
                <td>64</td>
                <td>4</td>
                <td class="right" data-ratio="0 64">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html#t19">src/alert_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html#t19"><data value='AlertLevel'>AlertLevel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html#t27">src/alert_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html#t27"><data value='AlertManager'>AlertManager</data></a></td>
                <td>120</td>
                <td>53</td>
                <td>0</td>
                <td class="right" data-ratio="67 120">56%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html">src/alert_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>37</td>
                <td>2</td>
                <td>8</td>
                <td class="right" data-ratio="35 37">95%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_data_handler_py.html#t25">src/async_data_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_data_handler_py.html#t25"><data value='AsyncDataHandler'>AsyncDataHandler</data></a></td>
                <td>152</td>
                <td>152</td>
                <td>0</td>
                <td class="right" data-ratio="0 152">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_data_handler_py.html">src/async_data_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_data_handler_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>37</td>
                <td>4</td>
                <td>36</td>
                <td class="right" data-ratio="33 37">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_optimization_py.html#t18">src/async_optimization.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_optimization_py.html#t18"><data value='AsyncTaskResult'>AsyncTaskResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_optimization_py.html#t27">src/async_optimization.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_optimization_py.html#t27"><data value='AsyncTaskManager'>AsyncTaskManager</data></a></td>
                <td>41</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="0 41">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_optimization_py.html#t128">src/async_optimization.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_optimization_py.html#t128"><data value='AsyncTradingLoop'>AsyncTradingLoop</data></a></td>
                <td>129</td>
                <td>129</td>
                <td>0</td>
                <td class="right" data-ratio="0 129">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_optimization_py.html">src/async_optimization.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_optimization_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>47</td>
                <td>47</td>
                <td>2</td>
                <td class="right" data-ratio="0 47">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t22">src/async_resource_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t22"><data value='AsyncDatabaseConnectionManager'>AsyncDatabaseConnectionManager</data></a></td>
                <td>79</td>
                <td>73</td>
                <td>0</td>
                <td class="right" data-ratio="6 79">8%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t168">src/async_resource_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t168"><data value='SystemMonitor'>SystemMonitor</data></a></td>
                <td>67</td>
                <td>58</td>
                <td>0</td>
                <td class="right" data-ratio="9 67">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t316">src/async_resource_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t316"><data value='AsyncResourceManager'>AsyncResourceManager</data></a></td>
                <td>22</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="5 22">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html">src/async_resource_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>85</td>
                <td>30</td>
                <td>2</td>
                <td class="right" data-ratio="55 85">65%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t27">src/async_state_persistence.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t27"><data value='PersistenceOperation'>PersistenceOperation</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t36">src/async_state_persistence.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t36"><data value='SerializationFormat'>SerializationFormat</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t44">src/async_state_persistence.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t44"><data value='PersistenceTask'>PersistenceTask</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t65">src/async_state_persistence.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t65"><data value='AsyncStatePersistenceManager'>AsyncStatePersistenceManager</data></a></td>
                <td>201</td>
                <td>185</td>
                <td>0</td>
                <td class="right" data-ratio="16 201">8%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html">src/async_state_persistence.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>110</td>
                <td>32</td>
                <td>2</td>
                <td class="right" data-ratio="78 110">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_backtesting_py.html#t14">src/backtesting.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_backtesting_py.html#t14"><data value='BacktestEngine'>BacktestEngine</data></a></td>
                <td>230</td>
                <td>230</td>
                <td>0</td>
                <td class="right" data-ratio="0 230">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_backtesting_py.html">src/backtesting.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_backtesting_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_command_system_py.html#t21">src/command_system.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_command_system_py.html#t21"><data value='CommandType'>CommandType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_command_system_py.html#t32">src/command_system.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_command_system_py.html#t32"><data value='CommandContext'>CommandContext</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_command_system_py.html#t41">src/command_system.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_command_system_py.html#t41"><data value='Command'>Command</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>4</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_command_system_py.html#t92">src/command_system.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_command_system_py.html#t92"><data value='LiveTradingCommand'>LiveTradingCommand</data></a></td>
                <td>36</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 36">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_command_system_py.html#t170">src/command_system.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_command_system_py.html#t170"><data value='BacktestCommand'>BacktestCommand</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_command_system_py.html#t227">src/command_system.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_command_system_py.html#t227"><data value='PortfolioTradingCommand'>PortfolioTradingCommand</data></a></td>
                <td>36</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="0 36">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_command_system_py.html#t303">src/command_system.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_command_system_py.html#t303"><data value='HealthCheckCommand'>HealthCheckCommand</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_command_system_py.html#t337">src/command_system.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_command_system_py.html#t337"><data value='CommandFactory'>CommandFactory</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_command_system_py.html#t368">src/command_system.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_command_system_py.html#t368"><data value='CommandExecutor'>CommandExecutor</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_command_system_py.html">src/command_system.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_command_system_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>67</td>
                <td>67</td>
                <td>6</td>
                <td class="right" data-ratio="0 67">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_comprehensive_monitoring_py.html#t81">src/comprehensive_monitoring.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_comprehensive_monitoring_py.html#t81"><data value='MetricDefinition'>MetricDefinition</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_comprehensive_monitoring_py.html#t90">src/comprehensive_monitoring.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_comprehensive_monitoring_py.html#t90"><data value='PrometheusMetricsManager'>PrometheusMetricsManager</data></a></td>
                <td>37</td>
                <td>37</td>
                <td>0</td>
                <td class="right" data-ratio="0 37">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_comprehensive_monitoring_py.html#t204">src/comprehensive_monitoring.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_comprehensive_monitoring_py.html#t204"><data value='StructuredLogger'>StructuredLogger</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_comprehensive_monitoring_py.html#t276">src/comprehensive_monitoring.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_comprehensive_monitoring_py.html#t276"><data value='ComprehensiveMonitor'>ComprehensiveMonitor</data></a></td>
                <td>100</td>
                <td>100</td>
                <td>0</td>
                <td class="right" data-ratio="0 100">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_comprehensive_monitoring_py.html#t571">src/comprehensive_monitoring.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_comprehensive_monitoring_py.html#t571"><data value='MockPortfolioSystem'>main.MockPortfolioSystem</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_comprehensive_monitoring_py.html">src/comprehensive_monitoring.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_comprehensive_monitoring_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>93</td>
                <td>93</td>
                <td>2</td>
                <td class="right" data-ratio="0 93">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_comprehensive_optimization_py.html#t24">src/comprehensive_optimization.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_comprehensive_optimization_py.html#t24"><data value='EnhancedResourceManager'>EnhancedResourceManager</data></a></td>
                <td>91</td>
                <td>91</td>
                <td>0</td>
                <td class="right" data-ratio="0 91">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_comprehensive_optimization_py.html#t204">src/comprehensive_optimization.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_comprehensive_optimization_py.html#t204"><data value='ConfigurationOptimizer'>ConfigurationOptimizer</data></a></td>
                <td>29</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_comprehensive_optimization_py.html#t269">src/comprehensive_optimization.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_comprehensive_optimization_py.html#t269"><data value='DataQualityEnhancer'>DataQualityEnhancer</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_comprehensive_optimization_py.html">src/comprehensive_optimization.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_comprehensive_optimization_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>56</td>
                <td>56</td>
                <td>2</td>
                <td class="right" data-ratio="0 56">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_loader_py.html#t18">src/config_loader.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_loader_py.html#t18"><data value='ConfigLoader'>ConfigLoader</data></a></td>
                <td>100</td>
                <td>100</td>
                <td>0</td>
                <td class="right" data-ratio="0 100">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_loader_py.html">src/config_loader.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_loader_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>13</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_manager_py.html#t18">src/config_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_manager_py.html#t18"><data value='ConfigManager'>ConfigManager</data></a></td>
                <td>128</td>
                <td>128</td>
                <td>0</td>
                <td class="right" data-ratio="0 128">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_manager_py.html">src/config_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>45</td>
                <td>45</td>
                <td>2</td>
                <td class="right" data-ratio="0 45">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t44">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t44"><data value='ExchangeType'>ExchangeType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t54">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t54"><data value='TimeframeType'>TimeframeType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t65">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t65"><data value='LogLevel'>LogLevel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t74">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t74"><data value='TradingMode'>TradingMode</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t80">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t80"><data value='FuturesType'>FuturesType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t86">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t86"><data value='MarginMode'>MarginMode</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t92">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t92"><data value='TradingConfig'>TradingConfig</data></a></td>
                <td>15</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="10 15">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t159">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t159"><data value='Config'>TradingConfig.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t164">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t164"><data value='DatabaseConfig'>DatabaseConfig</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t183">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t183"><data value='Config'>DatabaseConfig.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t187">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t187"><data value='MonitoringConfig'>MonitoringConfig</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t219">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t219"><data value='Config'>MonitoringConfig.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t223">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t223"><data value='PerformanceConfig'>PerformanceConfig</data></a></td>
                <td>6</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="4 6">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t263">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t263"><data value='Config'>PerformanceConfig.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t267">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t267"><data value='EventDrivenConfig'>EventDrivenConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t280">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t280"><data value='Config'>EventDrivenConfig.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t284">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t284"><data value='StrategyConfig'>StrategyConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t300">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t300"><data value='Config'>StrategyConfig.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t304">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t304"><data value='ComprehensiveConfig'>ComprehensiveConfig</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t353">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t353"><data value='Config'>ComprehensiveConfig.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t360">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t360"><data value='ConfigValidator'>ConfigValidator</data></a></td>
                <td>84</td>
                <td>84</td>
                <td>0</td>
                <td class="right" data-ratio="0 84">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>184</td>
                <td>21</td>
                <td>15</td>
                <td class="right" data-ratio="163 184">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_data_directory_manager_py.html#t21">src/data_directory_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_data_directory_manager_py.html#t21"><data value='DataType'>DataType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_data_directory_manager_py.html#t34">src/data_directory_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_data_directory_manager_py.html#t34"><data value='DirectoryInfo'>DirectoryInfo</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_data_directory_manager_py.html#t43">src/data_directory_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_data_directory_manager_py.html#t43"><data value='DataDirectoryManager'>DataDirectoryManager</data></a></td>
                <td>159</td>
                <td>147</td>
                <td>0</td>
                <td class="right" data-ratio="12 159">8%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_data_directory_manager_py.html">src/data_directory_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_data_directory_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>72</td>
                <td>22</td>
                <td>3</td>
                <td class="right" data-ratio="50 72">69%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_data_handler_py.html#t15">src/data_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_data_handler_py.html#t15"><data value='DataHandler'>DataHandler</data></a></td>
                <td>162</td>
                <td>150</td>
                <td>0</td>
                <td class="right" data-ratio="12 162">7%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_data_handler_py.html">src/data_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_data_handler_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>29</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 29">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html#t24">src/database_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html#t24"><data value='DatabaseManager'>DatabaseManager</data></a></td>
                <td>209</td>
                <td>209</td>
                <td>0</td>
                <td class="right" data-ratio="0 209">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html">src/database_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>37</td>
                <td>3</td>
                <td>11</td>
                <td class="right" data-ratio="34 37">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_debug_routes_py.html">src/debug_routes.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_debug_routes_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>39</td>
                <td>39</td>
                <td>3</td>
                <td class="right" data-ratio="0 39">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_demo_multi_strategy_system_py.html#t24">src/demo_multi_strategy_system.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_demo_multi_strategy_system_py.html#t24"><data value='MultiStrategyDemo'>MultiStrategyDemo</data></a></td>
                <td>106</td>
                <td>106</td>
                <td>0</td>
                <td class="right" data-ratio="0 106">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_demo_multi_strategy_system_py.html">src/demo_multi_strategy_system.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_demo_multi_strategy_system_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>39</td>
                <td>39</td>
                <td>2</td>
                <td class="right" data-ratio="0 39">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_demo_smart_leverage_py.html">src/demo_smart_leverage.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_demo_smart_leverage_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>111</td>
                <td>111</td>
                <td>2</td>
                <td class="right" data-ratio="0 111">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_allocation_engine_py.html#t23">src/dynamic_allocation_engine.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_allocation_engine_py.html#t23"><data value='StrategyPerformance'>StrategyPerformance</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_allocation_engine_py.html#t38">src/dynamic_allocation_engine.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_allocation_engine_py.html#t38"><data value='DynamicAllocationEngine'>DynamicAllocationEngine</data></a></td>
                <td>171</td>
                <td>171</td>
                <td>0</td>
                <td class="right" data-ratio="0 171">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_allocation_engine_py.html">src/dynamic_allocation_engine.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_allocation_engine_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>36</td>
                <td>36</td>
                <td>3</td>
                <td class="right" data-ratio="0 36">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t21">src/dynamic_config.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t21"><data value='ConfigChangeHandler'>ConfigChangeHandler</data></a></td>
                <td>5</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="2 5">40%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t34">src/dynamic_config.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t34"><data value='DynamicConfigManager'>DynamicConfigManager</data></a></td>
                <td>137</td>
                <td>94</td>
                <td>0</td>
                <td class="right" data-ratio="43 137">31%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html">src/dynamic_config.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>35</td>
                <td>0</td>
                <td>14</td>
                <td class="right" data-ratio="35 35">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_event_integration_py.html#t19">src/enhanced_event_integration.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_event_integration_py.html#t19"><data value='SystemMetrics'>SystemMetrics</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_event_integration_py.html#t31">src/enhanced_event_integration.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_event_integration_py.html#t31"><data value='EnhancedEventIntegrator'>EnhancedEventIntegrator</data></a></td>
                <td>69</td>
                <td>69</td>
                <td>0</td>
                <td class="right" data-ratio="0 69">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_event_integration_py.html">src/enhanced_event_integration.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_event_integration_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>43</td>
                <td>43</td>
                <td>23</td>
                <td class="right" data-ratio="0 43">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_portfolio_demo_py.html#t22">src/enhanced_portfolio_demo.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_portfolio_demo_py.html#t22"><data value='EnhancedPortfolioDemo'>EnhancedPortfolioDemo</data></a></td>
                <td>124</td>
                <td>124</td>
                <td>0</td>
                <td class="right" data-ratio="0 124">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_portfolio_demo_py.html">src/enhanced_portfolio_demo.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_portfolio_demo_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>26</td>
                <td>26</td>
                <td>2</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t23">src/enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t23"><data value='RetryStrategy'>RetryStrategy</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t32">src/enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t32"><data value='RetryConfig'>RetryConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t64">src/enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t64"><data value='RetryMetrics'>RetryMetrics</data></a></td>
                <td>22</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="8 22">36%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t109">src/enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t109"><data value='EnhancedCircuitBreaker'>EnhancedCircuitBreaker</data></a></td>
                <td>92</td>
                <td>77</td>
                <td>0</td>
                <td class="right" data-ratio="15 92">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t272">src/enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t272"><data value='EnhancedRetryHandler'>EnhancedRetryHandler</data></a></td>
                <td>136</td>
                <td>126</td>
                <td>0</td>
                <td class="right" data-ratio="10 136">7%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html">src/enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>66</td>
                <td>0</td>
                <td>26</td>
                <td class="right" data-ratio="66 66">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_event_driven_core_py.html#t23">src/event_driven_core.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_event_driven_core_py.html#t23"><data value='EventType'>EventType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_event_driven_core_py.html#t48">src/event_driven_core.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_event_driven_core_py.html#t48"><data value='Event'>Event</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_event_driven_core_py.html#t81">src/event_driven_core.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_event_driven_core_py.html#t81"><data value='EventHandler'>EventHandler</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>5</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_event_driven_core_py.html#t136">src/event_driven_core.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_event_driven_core_py.html#t136"><data value='EventBus'>EventBus</data></a></td>
                <td>80</td>
                <td>80</td>
                <td>0</td>
                <td class="right" data-ratio="0 80">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_event_driven_core_py.html#t303">src/event_driven_core.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_event_driven_core_py.html#t303"><data value='SignalGeneratedHandler'>SignalGeneratedHandler</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_event_driven_core_py.html#t369">src/event_driven_core.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_event_driven_core_py.html#t369"><data value='HealthCheckHandler'>HealthCheckHandler</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_event_driven_core_py.html">src/event_driven_core.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_event_driven_core_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>82</td>
                <td>82</td>
                <td>4</td>
                <td class="right" data-ratio="0 82">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_event_publisher_decorators_py.html#t82">src/event_publisher_decorators.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_event_publisher_decorators_py.html#t82"><data value='AutoEventPublisher'>AutoEventPublisher</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_event_publisher_decorators_py.html">src/event_publisher_decorators.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_event_publisher_decorators_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_example_usage_py.html">src/example_usage.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_example_usage_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>148</td>
                <td>148</td>
                <td>8</td>
                <td class="right" data-ratio="0 148">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exception_system_py.html#t19">src/exception_system.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exception_system_py.html#t19"><data value='ErrorSeverity'>ErrorSeverity</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exception_system_py.html#t27">src/exception_system.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exception_system_py.html#t27"><data value='ErrorCategory'>ErrorCategory</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exception_system_py.html#t56">src/exception_system.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exception_system_py.html#t56"><data value='TradingException'>TradingException</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exception_system_py.html#t125">src/exception_system.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exception_system_py.html#t125"><data value='DataUnavailableError'>DataUnavailableError</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exception_system_py.html#t140">src/exception_system.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exception_system_py.html#t140"><data value='DataInvalidError'>DataInvalidError</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exception_system_py.html#t155">src/exception_system.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exception_system_py.html#t155"><data value='DataStaleError'>DataStaleError</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exception_system_py.html#t172">src/exception_system.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exception_system_py.html#t172"><data value='OrderExecutionError'>OrderExecutionError</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exception_system_py.html#t191">src/exception_system.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exception_system_py.html#t191"><data value='InsufficientFundsError'>InsufficientFundsError</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exception_system_py.html#t210">src/exception_system.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exception_system_py.html#t210"><data value='PositionLimitExceededError'>PositionLimitExceededError</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exception_system_py.html#t230">src/exception_system.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exception_system_py.html#t230"><data value='NetworkError'>NetworkError</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exception_system_py.html#t248">src/exception_system.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exception_system_py.html#t248"><data value='APIRateLimitError'>APIRateLimitError</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exception_system_py.html#t266">src/exception_system.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exception_system_py.html#t266"><data value='APIAuthenticationError'>APIAuthenticationError</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exception_system_py.html#t286">src/exception_system.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exception_system_py.html#t286"><data value='ConfigurationError'>ConfigurationError</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exception_system_py.html#t305">src/exception_system.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exception_system_py.html#t305"><data value='ResourceExhaustedError'>ResourceExhaustedError</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exception_system_py.html#t321">src/exception_system.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exception_system_py.html#t321"><data value='StrategyError'>StrategyError</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exception_system_py.html#t336">src/exception_system.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exception_system_py.html#t336"><data value='RiskViolationError'>RiskViolationError</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exception_system_py.html#t355">src/exception_system.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exception_system_py.html#t355"><data value='ExceptionManager'>ExceptionManager</data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exception_system_py.html">src/exception_system.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exception_system_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>81</td>
                <td>81</td>
                <td>8</td>
                <td class="right" data-ratio="0 81">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t17">src/exchange_factory.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t17"><data value='ExchangeGateway'>ExchangeGateway</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>8</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t37">src/exchange_factory.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t37"><data value='CCXTExchangeGateway'>CCXTExchangeGateway</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t60">src/exchange_factory.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t60"><data value='MockExchangeGateway'>MockExchangeGateway</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t110">src/exchange_factory.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t110"><data value='ExchangeFactory'>ExchangeFactory</data></a></td>
                <td>61</td>
                <td>61</td>
                <td>0</td>
                <td class="right" data-ratio="0 61">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html">src/exchange_factory.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>25</td>
                <td>0</td>
                <td>19</td>
                <td class="right" data-ratio="25 25">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_final_optimization_validation_py.html#t25">src/final_optimization_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_final_optimization_validation_py.html#t25"><data value='FinalOptimizationValidator'>FinalOptimizationValidator</data></a></td>
                <td>187</td>
                <td>187</td>
                <td>0</td>
                <td class="right" data-ratio="0 187">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_final_optimization_validation_py.html#t375">src/final_optimization_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_final_optimization_validation_py.html#t375"><data value='MockPortfolioSystem'>FinalOptimizationValidator._validate_comprehensive_monitoring.MockPortfolioSystem</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_final_optimization_validation_py.html">src/final_optimization_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_final_optimization_validation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>28</td>
                <td>28</td>
                <td>2</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_final_system_validation_py.html#t27">src/final_system_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_final_system_validation_py.html#t27"><data value='FinalSystemValidator'>FinalSystemValidator</data></a></td>
                <td>123</td>
                <td>123</td>
                <td>0</td>
                <td class="right" data-ratio="0 123">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_final_system_validation_py.html">src/final_system_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_final_system_validation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>28</td>
                <td>28</td>
                <td>2</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_fix_critical_issues_py.html#t22">src/fix_critical_issues.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_fix_critical_issues_py.html#t22"><data value='CriticalIssuesFixer'>CriticalIssuesFixer</data></a></td>
                <td>68</td>
                <td>68</td>
                <td>0</td>
                <td class="right" data-ratio="0 68">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_fix_critical_issues_py.html">src/fix_critical_issues.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_fix_critical_issues_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>27</td>
                <td>27</td>
                <td>2</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_fix_resource_management_py.html#t15">src/fix_resource_management.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_fix_resource_management_py.html#t15"><data value='DatabaseManager'>DatabaseManager</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_fix_resource_management_py.html#t52">src/fix_resource_management.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_fix_resource_management_py.html#t52"><data value='ResourceManagementFixer'>ResourceManagementFixer</data></a></td>
                <td>81</td>
                <td>81</td>
                <td>1</td>
                <td class="right" data-ratio="0 81">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_fix_resource_management_py.html">src/fix_resource_management.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_fix_resource_management_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>28</td>
                <td>28</td>
                <td>2</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t21">src/global_event_bus.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t21"><data value='EventType'>EventType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t59">src/global_event_bus.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t59"><data value='Event'>Event</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t92">src/global_event_bus.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t92"><data value='EventSubscription'>EventSubscription</data></a></td>
                <td>9</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="4 9">44%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t115">src/global_event_bus.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t115"><data value='GlobalEventBus'>GlobalEventBus</data></a></td>
                <td>82</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="26 82">32%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html">src/global_event_bus.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>70</td>
                <td>0</td>
                <td>36</td>
                <td class="right" data-ratio="70 70">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t20">src/graceful_shutdown.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t20"><data value='ShutdownComponent'>ShutdownComponent</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>7</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t38">src/graceful_shutdown.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t38"><data value='GracefulShutdownManager'>GracefulShutdownManager</data></a></td>
                <td>84</td>
                <td>70</td>
                <td>0</td>
                <td class="right" data-ratio="14 84">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t228">src/graceful_shutdown.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t228"><data value='ExampleShutdownComponent'>ExampleShutdownComponent</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html">src/graceful_shutdown.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>60</td>
                <td>26</td>
                <td>7</td>
                <td class="right" data-ratio="34 60">57%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_health_checker_py.html#t20">src/health_checker.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_health_checker_py.html#t20"><data value='SystemHealthChecker'>SystemHealthChecker</data></a></td>
                <td>188</td>
                <td>188</td>
                <td>0</td>
                <td class="right" data-ratio="0 188">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_health_checker_py.html">src/health_checker.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_health_checker_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>20</td>
                <td>6</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t42">src/health_server.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t42"><data value='HealthServer'>HealthServer</data></a></td>
                <td>154</td>
                <td>57</td>
                <td>0</td>
                <td class="right" data-ratio="97 154">63%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html">src/health_server.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>40</td>
                <td>12</td>
                <td>15</td>
                <td class="right" data-ratio="28 40">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_high_leverage_risk_checker_py.html#t22">src/high_leverage_risk_checker.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_high_leverage_risk_checker_py.html#t22"><data value='HighLeverageRiskChecker'>HighLeverageRiskChecker</data></a></td>
                <td>68</td>
                <td>65</td>
                <td>0</td>
                <td class="right" data-ratio="3 68">4%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_high_leverage_risk_checker_py.html">src/high_leverage_risk_checker.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_high_leverage_risk_checker_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>49</td>
                <td>29</td>
                <td>2</td>
                <td class="right" data-ratio="20 49">41%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_improved_backtest_py.html#t24">src/improved_backtest.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_improved_backtest_py.html#t24"><data value='ImprovedBacktestEngine'>ImprovedBacktestEngine</data></a></td>
                <td>112</td>
                <td>112</td>
                <td>0</td>
                <td class="right" data-ratio="0 112">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_improved_backtest_py.html#t290">src/improved_backtest.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_improved_backtest_py.html#t290"><data value='MockExchange'>MockExchange</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_improved_backtest_py.html">src/improved_backtest.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_improved_backtest_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>45</td>
                <td>45</td>
                <td>2</td>
                <td class="right" data-ratio="0 45">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t23">src/integrated_trading_executor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t23"><data value='RiskCheckResult'>RiskCheckResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t31">src/integrated_trading_executor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t31"><data value='TradingSignal'>TradingSignal</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t48">src/integrated_trading_executor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t48"><data value='RiskCheckReport'>RiskCheckReport</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t62">src/integrated_trading_executor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t62"><data value='IntegratedTradingExecutor'>IntegratedTradingExecutor</data></a></td>
                <td>221</td>
                <td>178</td>
                <td>0</td>
                <td class="right" data-ratio="43 221">19%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html">src/integrated_trading_executor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>87</td>
                <td>26</td>
                <td>2</td>
                <td class="right" data-ratio="61 87">70%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t25">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t25"><data value='CacheStrategy'>CacheStrategy</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t34">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t34"><data value='CacheEntry'>CacheEntry</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t71">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t71"><data value='IntelligentCacheManager'>IntelligentCacheManager</data></a></td>
                <td>208</td>
                <td>208</td>
                <td>0</td>
                <td class="right" data-ratio="0 208">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>104</td>
                <td>35</td>
                <td>2</td>
                <td class="right" data-ratio="69 104">66%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_portfolio_system_py.html#t25">src/intelligent_portfolio_system.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_portfolio_system_py.html#t25"><data value='IntelligentPortfolioSystem'>IntelligentPortfolioSystem</data></a></td>
                <td>115</td>
                <td>115</td>
                <td>0</td>
                <td class="right" data-ratio="0 115">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_portfolio_system_py.html">src/intelligent_portfolio_system.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_portfolio_system_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>71</td>
                <td>71</td>
                <td>2</td>
                <td class="right" data-ratio="0 71">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_logging_config_py.html">src/logging_config.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_logging_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>40</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="39 40">98%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_math_utils_py.html#t22">src/math_utils.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_math_utils_py.html#t22"><data value='PairsTradingMath'>PairsTradingMath</data></a></td>
                <td>126</td>
                <td>126</td>
                <td>0</td>
                <td class="right" data-ratio="0 126">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_math_utils_py.html">src/math_utils.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_math_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>39</td>
                <td>8</td>
                <td>15</td>
                <td class="right" data-ratio="31 39">79%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t25">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t25"><data value='TaskType'>TaskType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t34">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t34"><data value='TaskPriority'>TaskPriority</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t43">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t43"><data value='TaskMetrics'>TaskMetrics</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t70">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t70"><data value='MultiExecutorManager'>MultiExecutorManager</data></a></td>
                <td>146</td>
                <td>121</td>
                <td>0</td>
                <td class="right" data-ratio="25 146">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>143</td>
                <td>67</td>
                <td>2</td>
                <td class="right" data-ratio="76 143">53%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_strategy_engine_py.html#t27">src/multi_strategy_engine.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_strategy_engine_py.html#t27"><data value='EngineConfig'>EngineConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_strategy_engine_py.html#t37">src/multi_strategy_engine.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_strategy_engine_py.html#t37"><data value='SignalAggregator'>SignalAggregator</data></a></td>
                <td>50</td>
                <td>50</td>
                <td>0</td>
                <td class="right" data-ratio="0 50">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_strategy_engine_py.html#t143">src/multi_strategy_engine.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_strategy_engine_py.html#t143"><data value='MultiStrategyEngine'>MultiStrategyEngine</data></a></td>
                <td>124</td>
                <td>124</td>
                <td>0</td>
                <td class="right" data-ratio="0 124">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_strategy_engine_py.html">src/multi_strategy_engine.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_strategy_engine_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>46</td>
                <td>46</td>
                <td>19</td>
                <td class="right" data-ratio="0 46">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t18">src/pair_trading_bot.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t18"><data value='TradingState'>TradingState</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t25">src/pair_trading_bot.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t25"><data value='SignalState'>SignalState</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t32">src/pair_trading_bot.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t32"><data value='TakeProfitTarget'>TakeProfitTarget</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t37">src/pair_trading_bot.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t37"><data value='PairTradingBot'>PairTradingBot</data></a></td>
                <td>201</td>
                <td>111</td>
                <td>0</td>
                <td class="right" data-ratio="90 201">45%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html">src/pair_trading_bot.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>39</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="39 39">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_pairs_trading_strategy_py.html#t21">src/pairs_trading_strategy.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_pairs_trading_strategy_py.html#t21"><data value='PairsTradingStrategy'>PairsTradingStrategy</data></a></td>
                <td>139</td>
                <td>139</td>
                <td>0</td>
                <td class="right" data-ratio="0 139">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_pairs_trading_strategy_py.html">src/pairs_trading_strategy.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_pairs_trading_strategy_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>20</td>
                <td>16</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_perfect_validation_py.html">src/perfect_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_perfect_validation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>74</td>
                <td>74</td>
                <td>2</td>
                <td class="right" data-ratio="0 74">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_backtester_py.html#t23">src/portfolio_backtester.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_backtester_py.html#t23"><data value='PortfolioBacktester'>PortfolioBacktester</data></a></td>
                <td>185</td>
                <td>185</td>
                <td>0</td>
                <td class="right" data-ratio="0 185">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_backtester_py.html">src/portfolio_backtester.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_backtester_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>26</td>
                <td>0</td>
                <td>15</td>
                <td class="right" data-ratio="26 26">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t23">src/portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t23"><data value='StrategyAllocation'>StrategyAllocation</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t36">src/portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t36"><data value='CorrelationMatrix'>CorrelationMatrix</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t58">src/portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t58"><data value='PortfolioManager'>PortfolioManager</data></a></td>
                <td>144</td>
                <td>73</td>
                <td>0</td>
                <td class="right" data-ratio="71 144">49%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t445">src/portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t445"><data value='MockStrategy'>MockStrategy</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>4</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html">src/portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>43</td>
                <td>0</td>
                <td>21</td>
                <td class="right" data-ratio="43 43">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_production_readiness_check_py.html#t23">src/production_readiness_check.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_production_readiness_check_py.html#t23"><data value='ProductionReadinessChecker'>ProductionReadinessChecker</data></a></td>
                <td>110</td>
                <td>110</td>
                <td>0</td>
                <td class="right" data-ratio="0 110">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_production_readiness_check_py.html">src/production_readiness_check.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_production_readiness_check_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>49</td>
                <td>49</td>
                <td>2</td>
                <td class="right" data-ratio="0 49">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_quick_fix_final_issues_py.html">src/quick_fix_final_issues.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_quick_fix_final_issues_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>30</td>
                <td>30</td>
                <td>3</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_quick_start_production_py.html#t27">src/quick_start_production.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_quick_start_production_py.html#t27"><data value='ProductionLauncher'>ProductionLauncher</data></a></td>
                <td>129</td>
                <td>129</td>
                <td>0</td>
                <td class="right" data-ratio="0 129">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_quick_start_production_py.html">src/quick_start_production.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_quick_start_production_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>30</td>
                <td>30</td>
                <td>8</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_resource_manager_py.html#t14">src/resource_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_resource_manager_py.html#t14"><data value='ResourceManager'>ResourceManager</data></a></td>
                <td>48</td>
                <td>48</td>
                <td>0</td>
                <td class="right" data-ratio="0 48">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_resource_manager_py.html">src/resource_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_resource_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>25</td>
                <td>25</td>
                <td>12</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_retry_handler_py.html#t17">src/retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_retry_handler_py.html#t17"><data value='RetryConfig'>RetryConfig</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_retry_handler_py.html#t35">src/retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_retry_handler_py.html#t35"><data value='RetryHandler'>RetryHandler</data></a></td>
                <td>44</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="0 44">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_retry_handler_py.html#t169">src/retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_retry_handler_py.html#t169"><data value='CommonRetryConfigs'>CommonRetryConfigs</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_retry_handler_py.html">src/retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_retry_handler_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>39</td>
                <td>39</td>
                <td>31</td>
                <td class="right" data-ratio="0 39">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_security_manager_py.html#t21">src/security_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_security_manager_py.html#t21"><data value='SecurityManager'>SecurityManager</data></a></td>
                <td>141</td>
                <td>141</td>
                <td>0</td>
                <td class="right" data-ratio="0 141">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_security_manager_py.html">src/security_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_security_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>54</td>
                <td>26</td>
                <td>14</td>
                <td class="right" data-ratio="28 54">52%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_simplified_optimization_validation_py.html#t20">src/simplified_optimization_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_simplified_optimization_validation_py.html#t20"><data value='SimplifiedOptimizationValidator'>SimplifiedOptimizationValidator</data></a></td>
                <td>173</td>
                <td>173</td>
                <td>0</td>
                <td class="right" data-ratio="0 173">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_simplified_optimization_validation_py.html">src/simplified_optimization_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_simplified_optimization_validation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>23</td>
                <td>23</td>
                <td>2</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_smart_capital_management_py.html#t17">src/smart_capital_management.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_smart_capital_management_py.html#t17"><data value='RiskLevel'>RiskLevel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_smart_capital_management_py.html#t25">src/smart_capital_management.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_smart_capital_management_py.html#t25"><data value='CapitalAllocation'>CapitalAllocation</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_smart_capital_management_py.html#t35">src/smart_capital_management.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_smart_capital_management_py.html#t35"><data value='SmartCapitalManager'>SmartCapitalManager</data></a></td>
                <td>51</td>
                <td>51</td>
                <td>0</td>
                <td class="right" data-ratio="0 51">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_smart_capital_management_py.html">src/smart_capital_management.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_smart_capital_management_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>34</td>
                <td>5</td>
                <td>19</td>
                <td class="right" data-ratio="29 34">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t14">src/strategy_config_models.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t14"><data value='StrategyType'>StrategyType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t24">src/strategy_config_models.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t24"><data value='RiskLevel'>RiskLevel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t32">src/strategy_config_models.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t32"><data value='TimeFrame'>TimeFrame</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t43">src/strategy_config_models.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t43"><data value='BaseStrategyConfig'>BaseStrategyConfig</data></a></td>
                <td>21</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="6 21">29%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t116">src/strategy_config_models.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t116"><data value='Config'>BaseStrategyConfig.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t122">src/strategy_config_models.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t122"><data value='PairsTradingConfig'>PairsTradingConfig</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t154">src/strategy_config_models.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t154"><data value='MomentumConfig'>MomentumConfig</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t181">src/strategy_config_models.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t181"><data value='ArbitrageConfig'>ArbitrageConfig</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t207">src/strategy_config_models.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t207"><data value='StrategyConfigFactory'>StrategyConfigFactory</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t242">src/strategy_config_models.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t242"><data value='StrategyConfigValidator'>StrategyConfigValidator</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html">src/strategy_config_models.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>114</td>
                <td>5</td>
                <td>22</td>
                <td class="right" data-ratio="109 114">96%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t20">src/strategy_framework.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t20"><data value='StrategyType'>StrategyType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t30">src/strategy_framework.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t30"><data value='SignalType'>SignalType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t40">src/strategy_framework.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t40"><data value='SignalStrength'>SignalStrength</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t49">src/strategy_framework.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t49"><data value='TradingSignal'>TradingSignal</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t72">src/strategy_framework.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t72"><data value='StrategyPerformance'>StrategyPerformance</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t88">src/strategy_framework.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t88"><data value='BaseStrategy'>BaseStrategy</data></a></td>
                <td>45</td>
                <td>45</td>
                <td>34</td>
                <td class="right" data-ratio="0 45">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t276">src/strategy_framework.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t276"><data value='StrategyManager'>StrategyManager</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html">src/strategy_framework.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>72</td>
                <td>0</td>
                <td>19</td>
                <td class="right" data-ratio="72 72">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html#t23">src/strategy_health_monitor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html#t23"><data value='StrategyHealthMonitor'>StrategyHealthMonitor</data></a></td>
                <td>162</td>
                <td>143</td>
                <td>0</td>
                <td class="right" data-ratio="19 162">12%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html">src/strategy_health_monitor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>36</td>
                <td>0</td>
                <td>11</td>
                <td class="right" data-ratio="36 36">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_telegram_bot_py.html#t20">src/telegram_bot.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_telegram_bot_py.html#t20"><data value='TelegramBotController'>TelegramBotController</data></a></td>
                <td>190</td>
                <td>190</td>
                <td>0</td>
                <td class="right" data-ratio="0 190">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_telegram_bot_py.html">src/telegram_bot.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_telegram_bot_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>40</td>
                <td>40</td>
                <td>15</td>
                <td class="right" data-ratio="0 40">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_exceptions_py.html#t11">src/trading_exceptions.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trading_exceptions_py.html#t11"><data value='TradingException'>TradingException</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_exceptions_py.html#t21">src/trading_exceptions.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trading_exceptions_py.html#t21"><data value='ConfigError'>ConfigError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_exceptions_py.html#t26">src/trading_exceptions.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trading_exceptions_py.html#t26"><data value='DataError'>DataError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_exceptions_py.html#t31">src/trading_exceptions.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trading_exceptions_py.html#t31"><data value='ExecutionError'>ExecutionError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_exceptions_py.html#t36">src/trading_exceptions.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trading_exceptions_py.html#t36"><data value='PartialFillError'>PartialFillError</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_exceptions_py.html#t47">src/trading_exceptions.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trading_exceptions_py.html#t47"><data value='NetworkError'>NetworkError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_exceptions_py.html#t52">src/trading_exceptions.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trading_exceptions_py.html#t52"><data value='InsufficientFundsError'>InsufficientFundsError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_exceptions_py.html#t57">src/trading_exceptions.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trading_exceptions_py.html#t57"><data value='RiskLimitExceededError'>RiskLimitExceededError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_exceptions_py.html">src/trading_exceptions.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trading_exceptions_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_safety_guard_py.html#t18">src/trading_safety_guard.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trading_safety_guard_py.html#t18"><data value='TradingMode'>TradingMode</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_safety_guard_py.html#t25">src/trading_safety_guard.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trading_safety_guard_py.html#t25"><data value='SafetyLevel'>SafetyLevel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_safety_guard_py.html#t32">src/trading_safety_guard.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trading_safety_guard_py.html#t32"><data value='TradingSafetyGuard'>TradingSafetyGuard</data></a></td>
                <td>108</td>
                <td>101</td>
                <td>0</td>
                <td class="right" data-ratio="7 108">6%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_safety_guard_py.html">src/trading_safety_guard.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trading_safety_guard_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>34</td>
                <td>5</td>
                <td>20</td>
                <td class="right" data-ratio="29 34">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trend_following_strategy_py.html#t20">src/trend_following_strategy.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trend_following_strategy_py.html#t20"><data value='TrendFollowingStrategy'>TrendFollowingStrategy</data></a></td>
                <td>170</td>
                <td>170</td>
                <td>0</td>
                <td class="right" data-ratio="0 170">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trend_following_strategy_py.html">src/trend_following_strategy.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trend_following_strategy_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>22</td>
                <td>22</td>
                <td>19</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_ultimate_system_validation_py.html#t28">src/ultimate_system_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_ultimate_system_validation_py.html#t28"><data value='UltimateSystemValidator'>UltimateSystemValidator</data></a></td>
                <td>166</td>
                <td>166</td>
                <td>0</td>
                <td class="right" data-ratio="0 166">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_ultimate_system_validation_py.html">src/ultimate_system_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_ultimate_system_validation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>29</td>
                <td>29</td>
                <td>2</td>
                <td class="right" data-ratio="0 29">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html#t20">src/unified_client_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html#t20"><data value='UnifiedClientManager'>UnifiedClientManager</data></a></td>
                <td>78</td>
                <td>68</td>
                <td>0</td>
                <td class="right" data-ratio="10 78">13%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html">src/unified_client_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>74</td>
                <td>33</td>
                <td>2</td>
                <td class="right" data-ratio="41 74">55%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_config_manager_py.html#t22">src/unified_config_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_config_manager_py.html#t22"><data value='Environment'>Environment</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_config_manager_py.html#t31">src/unified_config_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_config_manager_py.html#t31"><data value='ExchangeType'>ExchangeType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_config_manager_py.html#t39">src/unified_config_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_config_manager_py.html#t39"><data value='TradingMode'>TradingMode</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_config_manager_py.html#t46">src/unified_config_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_config_manager_py.html#t46"><data value='RiskLevel'>RiskLevel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_config_manager_py.html#t54">src/unified_config_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_config_manager_py.html#t54"><data value='ExchangeConfig'>ExchangeConfig</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_config_manager_py.html#t79">src/unified_config_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_config_manager_py.html#t79"><data value='TradingConfig'>TradingConfig</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_config_manager_py.html#t117">src/unified_config_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_config_manager_py.html#t117"><data value='PairSelectionConfig'>PairSelectionConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_config_manager_py.html#t126">src/unified_config_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_config_manager_py.html#t126"><data value='BacktestConfig'>BacktestConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_config_manager_py.html#t135">src/unified_config_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_config_manager_py.html#t135"><data value='MonitoringConfig'>MonitoringConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_config_manager_py.html#t149">src/unified_config_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_config_manager_py.html#t149"><data value='UnifiedConfig'>UnifiedConfig</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_config_manager_py.html#t207">src/unified_config_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_config_manager_py.html#t207"><data value='UnifiedConfigManager'>UnifiedConfigManager</data></a></td>
                <td>65</td>
                <td>59</td>
                <td>0</td>
                <td class="right" data-ratio="6 65">9%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_config_manager_py.html">src/unified_config_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_config_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>131</td>
                <td>19</td>
                <td>3</td>
                <td class="right" data-ratio="112 131">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_pair_selector_py.html#t29">src/unified_pair_selector.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_pair_selector_py.html#t29"><data value='PairSelectionMethod'>PairSelectionMethod</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_pair_selector_py.html#t38">src/unified_pair_selector.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_pair_selector_py.html#t38"><data value='PairAnalysisResult'>PairAnalysisResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_pair_selector_py.html#t53">src/unified_pair_selector.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_pair_selector_py.html#t53"><data value='UnifiedPairSelector'>UnifiedPairSelector</data></a></td>
                <td>188</td>
                <td>156</td>
                <td>0</td>
                <td class="right" data-ratio="32 188">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_pair_selector_py.html">src/unified_pair_selector.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_pair_selector_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>69</td>
                <td>17</td>
                <td>3</td>
                <td class="right" data-ratio="52 69">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t24">src/unified_portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t24"><data value='SystemOverview'>SystemOverview</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t34">src/unified_portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t34"><data value='UnifiedPortfolioManager'>UnifiedPortfolioManager</data></a></td>
                <td>229</td>
                <td>88</td>
                <td>0</td>
                <td class="right" data-ratio="141 229">62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html">src/unified_portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>44</td>
                <td>0</td>
                <td>22</td>
                <td class="right" data-ratio="44 44">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_utils_py.html">src/utils.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>134</td>
                <td>58</td>
                <td>0</td>
                <td class="right" data-ratio="76 134">57%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_validate_improvements_py.html#t26">src/validate_improvements.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_validate_improvements_py.html#t26"><data value='ImprovementValidator'>ImprovementValidator</data></a></td>
                <td>167</td>
                <td>167</td>
                <td>0</td>
                <td class="right" data-ratio="0 167">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_validate_improvements_py.html">src/validate_improvements.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_validate_improvements_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>34</td>
                <td>34</td>
                <td>3</td>
                <td class="right" data-ratio="0 34">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>13052</td>
                <td>10402</td>
                <td>689</td>
                <td class="right" data-ratio="2650 13052">20%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-07-06 13:04 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
