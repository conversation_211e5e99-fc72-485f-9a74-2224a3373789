<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_8e611ae1.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">20%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-07-06 12:16 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t24">event_system/event_bus.py</a></td>
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t24"><data value='EventType'>EventType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t37">event_system/event_bus.py</a></td>
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t37"><data value='Event'>Event</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t47">event_system/event_bus.py</a></td>
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t47"><data value='EventHandler'>EventHandler</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>1</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t81">event_system/event_bus.py</a></td>
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t81"><data value='EventBus'>EventBus</data></a></td>
                <td>132</td>
                <td>132</td>
                <td>0</td>
                <td class="right" data-ratio="0 132">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t380">event_system/event_bus.py</a></td>
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html#t380"><data value='TestHandler'>TestHandler</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>3</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html">event_system/event_bus.py</a></td>
                <td class="name left"><a href="z_98bac5cf84189e43_event_bus_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>58</td>
                <td>6</td>
                <td>33</td>
                <td class="right" data-ratio="52 58">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t22">factor_factory/alpha_factors.py</a></td>
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t22"><data value='AlphaFactor'>AlphaFactor</data></a></td>
                <td>30</td>
                <td>30</td>
                <td>2</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t83">factor_factory/alpha_factors.py</a></td>
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t83"><data value='SpreadMomentumFactor'>SpreadMomentumFactor</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t111">factor_factory/alpha_factors.py</a></td>
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t111"><data value='VolatilityRegimeFactor'>VolatilityRegimeFactor</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t148">factor_factory/alpha_factors.py</a></td>
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t148"><data value='CointegrationStrengthFactor'>CointegrationStrengthFactor</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t210">factor_factory/alpha_factors.py</a></td>
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t210"><data value='VolumeProfileFactor'>VolumeProfileFactor</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t253">factor_factory/alpha_factors.py</a></td>
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t253"><data value='MarketRegimeFactor'>MarketRegimeFactor</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t302">factor_factory/alpha_factors.py</a></td>
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html#t302"><data value='AlphaFactoryManager'>AlphaFactoryManager</data></a></td>
                <td>90</td>
                <td>90</td>
                <td>0</td>
                <td class="right" data-ratio="0 90">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html">factor_factory/alpha_factors.py</a></td>
                <td class="name left"><a href="z_ba0086a4fb26a981_alpha_factors_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>43</td>
                <td>3</td>
                <td>16</td>
                <td class="right" data-ratio="40 43">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="main_py.html">main.py</a></td>
                <td class="name left"><a href="main_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>159</td>
                <td>159</td>
                <td>2</td>
                <td class="right" data-ratio="0 159">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c6e76699e82afdf_feature_engineering_py.html#t25">ml_predictor/feature_engineering.py</a></td>
                <td class="name left"><a href="z_4c6e76699e82afdf_feature_engineering_py.html#t25"><data value='FeatureEngineer'>FeatureEngineer</data></a></td>
                <td>231</td>
                <td>231</td>
                <td>0</td>
                <td class="right" data-ratio="0 231">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c6e76699e82afdf_feature_engineering_py.html">ml_predictor/feature_engineering.py</a></td>
                <td class="name left"><a href="z_4c6e76699e82afdf_feature_engineering_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>29</td>
                <td>1</td>
                <td>11</td>
                <td class="right" data-ratio="28 29">97%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c6e76699e82afdf_predictor_py.html#t23">ml_predictor/predictor.py</a></td>
                <td class="name left"><a href="z_4c6e76699e82afdf_predictor_py.html#t23"><data value='MLPredictor'>MLPredictor</data></a></td>
                <td>214</td>
                <td>214</td>
                <td>0</td>
                <td class="right" data-ratio="0 214">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_4c6e76699e82afdf_predictor_py.html">ml_predictor/predictor.py</a></td>
                <td class="name left"><a href="z_4c6e76699e82afdf_predictor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>28</td>
                <td>3</td>
                <td>18</td>
                <td class="right" data-ratio="25 28">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t26">performance_optimization/memory_optimizer.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t26"><data value='MemorySnapshot'>MemorySnapshot</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t37">performance_optimization/memory_optimizer.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t37"><data value='MemoryProfiler'>MemoryProfiler</data></a></td>
                <td>56</td>
                <td>56</td>
                <td>0</td>
                <td class="right" data-ratio="0 56">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t151">performance_optimization/memory_optimizer.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t151"><data value='DataFrameOptimizer'>DataFrameOptimizer</data></a></td>
                <td>51</td>
                <td>51</td>
                <td>0</td>
                <td class="right" data-ratio="0 51">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t253">performance_optimization/memory_optimizer.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t253"><data value='ChunkedDataProcessor'>ChunkedDataProcessor</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t299">performance_optimization/memory_optimizer.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html#t299"><data value='MemoryMonitor'>MemoryMonitor</data></a></td>
                <td>40</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="0 40">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html">performance_optimization/memory_optimizer.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_memory_optimizer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>56</td>
                <td>5</td>
                <td>14</td>
                <td class="right" data-ratio="51 56">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t19">performance_optimization/vectorized_calculations.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html#t19"><data value='VectorizedCalculator'>VectorizedCalculator</data></a></td>
                <td>226</td>
                <td>226</td>
                <td>0</td>
                <td class="right" data-ratio="0 226">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html">performance_optimization/vectorized_calculations.py</a></td>
                <td class="name left"><a href="z_5a6f50894c2f9185_vectorized_calculations_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>57</td>
                <td>3</td>
                <td>18</td>
                <td class="right" data-ratio="54 57">95%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac1802d461d41d87_research_toolkit_py.html#t29">research_environment/research_toolkit.py</a></td>
                <td class="name left"><a href="z_ac1802d461d41d87_research_toolkit_py.html#t29"><data value='ResearchToolkit'>ResearchToolkit</data></a></td>
                <td>247</td>
                <td>247</td>
                <td>0</td>
                <td class="right" data-ratio="0 247">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ac1802d461d41d87_research_toolkit_py.html">research_environment/research_toolkit.py</a></td>
                <td class="name left"><a href="z_ac1802d461d41d87_research_toolkit_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>37</td>
                <td>26</td>
                <td>16</td>
                <td class="right" data-ratio="11 37">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bd6a586f8a803a04_advanced_risk_manager_py.html#t21">risk_management/advanced_risk_manager.py</a></td>
                <td class="name left"><a href="z_bd6a586f8a803a04_advanced_risk_manager_py.html#t21"><data value='AdvancedRiskManager'>AdvancedRiskManager</data></a></td>
                <td>235</td>
                <td>235</td>
                <td>0</td>
                <td class="right" data-ratio="0 235">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bd6a586f8a803a04_advanced_risk_manager_py.html">risk_management/advanced_risk_manager.py</a></td>
                <td class="name left"><a href="z_bd6a586f8a803a04_advanced_risk_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>32</td>
                <td>3</td>
                <td>13</td>
                <td class="right" data-ratio="29 32">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html#t19">src/alert_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html#t19"><data value='AlertLevel'>AlertLevel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html#t27">src/alert_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html#t27"><data value='AlertManager'>AlertManager</data></a></td>
                <td>120</td>
                <td>120</td>
                <td>0</td>
                <td class="right" data-ratio="0 120">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html">src/alert_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_alert_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>37</td>
                <td>5</td>
                <td>8</td>
                <td class="right" data-ratio="32 37">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_data_handler_py.html#t25">src/async_data_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_data_handler_py.html#t25"><data value='AsyncDataHandler'>AsyncDataHandler</data></a></td>
                <td>152</td>
                <td>152</td>
                <td>0</td>
                <td class="right" data-ratio="0 152">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_data_handler_py.html">src/async_data_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_data_handler_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>37</td>
                <td>4</td>
                <td>36</td>
                <td class="right" data-ratio="33 37">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t22">src/async_resource_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t22"><data value='AsyncDatabaseConnectionManager'>AsyncDatabaseConnectionManager</data></a></td>
                <td>79</td>
                <td>79</td>
                <td>0</td>
                <td class="right" data-ratio="0 79">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t168">src/async_resource_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t168"><data value='SystemMonitor'>SystemMonitor</data></a></td>
                <td>67</td>
                <td>67</td>
                <td>0</td>
                <td class="right" data-ratio="0 67">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t316">src/async_resource_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html#t316"><data value='AsyncResourceManager'>AsyncResourceManager</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html">src/async_resource_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_resource_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>85</td>
                <td>36</td>
                <td>2</td>
                <td class="right" data-ratio="49 85">58%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t27">src/async_state_persistence.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t27"><data value='PersistenceOperation'>PersistenceOperation</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t36">src/async_state_persistence.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t36"><data value='SerializationFormat'>SerializationFormat</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t44">src/async_state_persistence.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t44"><data value='PersistenceTask'>PersistenceTask</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t65">src/async_state_persistence.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html#t65"><data value='AsyncStatePersistenceManager'>AsyncStatePersistenceManager</data></a></td>
                <td>201</td>
                <td>201</td>
                <td>0</td>
                <td class="right" data-ratio="0 201">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html">src/async_state_persistence.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_async_state_persistence_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>110</td>
                <td>38</td>
                <td>2</td>
                <td class="right" data-ratio="72 110">65%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_backtesting_py.html#t14">src/backtesting.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_backtesting_py.html#t14"><data value='BacktestEngine'>BacktestEngine</data></a></td>
                <td>230</td>
                <td>230</td>
                <td>0</td>
                <td class="right" data-ratio="0 230">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_backtesting_py.html">src/backtesting.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_backtesting_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_loader_py.html#t18">src/config_loader.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_loader_py.html#t18"><data value='ConfigLoader'>ConfigLoader</data></a></td>
                <td>100</td>
                <td>100</td>
                <td>0</td>
                <td class="right" data-ratio="0 100">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_loader_py.html">src/config_loader.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_loader_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>13</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t32">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t32"><data value='ExchangeType'>ExchangeType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t42">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t42"><data value='TimeframeType'>TimeframeType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t53">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t53"><data value='LogLevel'>LogLevel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t62">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t62"><data value='TradingMode'>TradingMode</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t68">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t68"><data value='FuturesType'>FuturesType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t74">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t74"><data value='MarginMode'>MarginMode</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t80">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t80"><data value='TradingConfig'>TradingConfig</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t139">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t139"><data value='Config'>TradingConfig.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t144">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t144"><data value='DatabaseConfig'>DatabaseConfig</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t163">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t163"><data value='Config'>DatabaseConfig.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t167">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t167"><data value='MonitoringConfig'>MonitoringConfig</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t199">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t199"><data value='Config'>MonitoringConfig.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t203">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t203"><data value='PerformanceConfig'>PerformanceConfig</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t235">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t235"><data value='Config'>PerformanceConfig.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t239">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t239"><data value='EventDrivenConfig'>EventDrivenConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t252">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t252"><data value='Config'>EventDrivenConfig.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t256">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t256"><data value='StrategyConfig'>StrategyConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t272">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t272"><data value='Config'>StrategyConfig.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t276">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t276"><data value='ComprehensiveConfig'>ComprehensiveConfig</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t325">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t325"><data value='Config'>ComprehensiveConfig.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t332">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html#t332"><data value='ConfigValidator'>ConfigValidator</data></a></td>
                <td>84</td>
                <td>84</td>
                <td>0</td>
                <td class="right" data-ratio="0 84">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html">src/config_validation.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_config_validation_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>174</td>
                <td>13</td>
                <td>15</td>
                <td class="right" data-ratio="161 174">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_data_handler_py.html#t15">src/data_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_data_handler_py.html#t15"><data value='DataHandler'>DataHandler</data></a></td>
                <td>162</td>
                <td>162</td>
                <td>0</td>
                <td class="right" data-ratio="0 162">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_data_handler_py.html">src/data_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_data_handler_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>29</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 29">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html#t24">src/database_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html#t24"><data value='DatabaseManager'>DatabaseManager</data></a></td>
                <td>209</td>
                <td>209</td>
                <td>0</td>
                <td class="right" data-ratio="0 209">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html">src/database_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_database_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>37</td>
                <td>3</td>
                <td>11</td>
                <td class="right" data-ratio="34 37">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t21">src/dynamic_config.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t21"><data value='ConfigChangeHandler'>ConfigChangeHandler</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t34">src/dynamic_config.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html#t34"><data value='DynamicConfigManager'>DynamicConfigManager</data></a></td>
                <td>137</td>
                <td>137</td>
                <td>0</td>
                <td class="right" data-ratio="0 137">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html">src/dynamic_config.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_dynamic_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>35</td>
                <td>3</td>
                <td>14</td>
                <td class="right" data-ratio="32 35">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t23">src/enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t23"><data value='RetryStrategy'>RetryStrategy</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t32">src/enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t32"><data value='RetryConfig'>RetryConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t64">src/enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t64"><data value='RetryMetrics'>RetryMetrics</data></a></td>
                <td>22</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="8 22">36%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t109">src/enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t109"><data value='EnhancedCircuitBreaker'>EnhancedCircuitBreaker</data></a></td>
                <td>92</td>
                <td>77</td>
                <td>0</td>
                <td class="right" data-ratio="15 92">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t272">src/enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html#t272"><data value='EnhancedRetryHandler'>EnhancedRetryHandler</data></a></td>
                <td>136</td>
                <td>126</td>
                <td>0</td>
                <td class="right" data-ratio="10 136">7%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html">src/enhanced_retry_handler.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_enhanced_retry_handler_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>66</td>
                <td>0</td>
                <td>26</td>
                <td class="right" data-ratio="66 66">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t17">src/exchange_factory.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t17"><data value='ExchangeGateway'>ExchangeGateway</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>8</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t37">src/exchange_factory.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t37"><data value='CCXTExchangeGateway'>CCXTExchangeGateway</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t60">src/exchange_factory.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t60"><data value='MockExchangeGateway'>MockExchangeGateway</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t110">src/exchange_factory.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html#t110"><data value='ExchangeFactory'>ExchangeFactory</data></a></td>
                <td>61</td>
                <td>61</td>
                <td>0</td>
                <td class="right" data-ratio="0 61">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html">src/exchange_factory.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_exchange_factory_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>25</td>
                <td>0</td>
                <td>19</td>
                <td class="right" data-ratio="25 25">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t21">src/global_event_bus.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t21"><data value='EventType'>EventType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t59">src/global_event_bus.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t59"><data value='Event'>Event</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t92">src/global_event_bus.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t92"><data value='EventSubscription'>EventSubscription</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t115">src/global_event_bus.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html#t115"><data value='GlobalEventBus'>GlobalEventBus</data></a></td>
                <td>82</td>
                <td>82</td>
                <td>0</td>
                <td class="right" data-ratio="0 82">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html">src/global_event_bus.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_global_event_bus_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>70</td>
                <td>6</td>
                <td>36</td>
                <td class="right" data-ratio="64 70">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t20">src/graceful_shutdown.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t20"><data value='ShutdownComponent'>ShutdownComponent</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>7</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t38">src/graceful_shutdown.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t38"><data value='GracefulShutdownManager'>GracefulShutdownManager</data></a></td>
                <td>84</td>
                <td>84</td>
                <td>0</td>
                <td class="right" data-ratio="0 84">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t228">src/graceful_shutdown.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html#t228"><data value='ExampleShutdownComponent'>ExampleShutdownComponent</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html">src/graceful_shutdown.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_graceful_shutdown_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>60</td>
                <td>29</td>
                <td>7</td>
                <td class="right" data-ratio="31 60">52%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t42">src/health_server.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html#t42"><data value='HealthServer'>HealthServer</data></a></td>
                <td>154</td>
                <td>154</td>
                <td>0</td>
                <td class="right" data-ratio="0 154">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html">src/health_server.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_health_server_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>40</td>
                <td>15</td>
                <td>15</td>
                <td class="right" data-ratio="25 40">62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_high_leverage_risk_checker_py.html#t22">src/high_leverage_risk_checker.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_high_leverage_risk_checker_py.html#t22"><data value='HighLeverageRiskChecker'>HighLeverageRiskChecker</data></a></td>
                <td>68</td>
                <td>68</td>
                <td>0</td>
                <td class="right" data-ratio="0 68">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_high_leverage_risk_checker_py.html">src/high_leverage_risk_checker.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_high_leverage_risk_checker_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>49</td>
                <td>29</td>
                <td>2</td>
                <td class="right" data-ratio="20 49">41%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t23">src/integrated_trading_executor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t23"><data value='RiskCheckResult'>RiskCheckResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t31">src/integrated_trading_executor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t31"><data value='TradingSignal'>TradingSignal</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t48">src/integrated_trading_executor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t48"><data value='RiskCheckReport'>RiskCheckReport</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t62">src/integrated_trading_executor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html#t62"><data value='IntegratedTradingExecutor'>IntegratedTradingExecutor</data></a></td>
                <td>178</td>
                <td>178</td>
                <td>0</td>
                <td class="right" data-ratio="0 178">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html">src/integrated_trading_executor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_integrated_trading_executor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>83</td>
                <td>26</td>
                <td>2</td>
                <td class="right" data-ratio="57 83">69%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t25">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t25"><data value='CacheStrategy'>CacheStrategy</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t34">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t34"><data value='CacheEntry'>CacheEntry</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t71">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html#t71"><data value='IntelligentCacheManager'>IntelligentCacheManager</data></a></td>
                <td>208</td>
                <td>208</td>
                <td>0</td>
                <td class="right" data-ratio="0 208">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html">src/intelligent_cache_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_intelligent_cache_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>104</td>
                <td>35</td>
                <td>2</td>
                <td class="right" data-ratio="69 104">66%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_logging_config_py.html">src/logging_config.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_logging_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>40</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="39 40">98%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t25">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t25"><data value='TaskType'>TaskType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t34">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t34"><data value='TaskPriority'>TaskPriority</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t43">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t43"><data value='TaskMetrics'>TaskMetrics</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t70">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html#t70"><data value='MultiExecutorManager'>MultiExecutorManager</data></a></td>
                <td>146</td>
                <td>146</td>
                <td>0</td>
                <td class="right" data-ratio="0 146">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html">src/multi_executor_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_multi_executor_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>143</td>
                <td>73</td>
                <td>2</td>
                <td class="right" data-ratio="70 143">49%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t18">src/pair_trading_bot.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t18"><data value='TradingState'>TradingState</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t25">src/pair_trading_bot.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t25"><data value='SignalState'>SignalState</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t32">src/pair_trading_bot.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t32"><data value='TakeProfitTarget'>TakeProfitTarget</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t37">src/pair_trading_bot.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html#t37"><data value='PairTradingBot'>PairTradingBot</data></a></td>
                <td>201</td>
                <td>201</td>
                <td>0</td>
                <td class="right" data-ratio="0 201">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html">src/pair_trading_bot.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_pair_trading_bot_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>39</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="6 39">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_backtester_py.html#t23">src/portfolio_backtester.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_backtester_py.html#t23"><data value='PortfolioBacktester'>PortfolioBacktester</data></a></td>
                <td>185</td>
                <td>185</td>
                <td>0</td>
                <td class="right" data-ratio="0 185">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_backtester_py.html">src/portfolio_backtester.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_backtester_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>26</td>
                <td>17</td>
                <td>15</td>
                <td class="right" data-ratio="9 26">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t23">src/portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t23"><data value='StrategyAllocation'>StrategyAllocation</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t36">src/portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t36"><data value='CorrelationMatrix'>CorrelationMatrix</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t58">src/portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t58"><data value='PortfolioManager'>PortfolioManager</data></a></td>
                <td>144</td>
                <td>144</td>
                <td>0</td>
                <td class="right" data-ratio="0 144">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t445">src/portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html#t445"><data value='MockStrategy'>MockStrategy</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>4</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html">src/portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_portfolio_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>43</td>
                <td>0</td>
                <td>21</td>
                <td class="right" data-ratio="43 43">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t14">src/strategy_config_models.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t14"><data value='StrategyType'>StrategyType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t24">src/strategy_config_models.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t24"><data value='RiskLevel'>RiskLevel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t32">src/strategy_config_models.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t32"><data value='TimeFrame'>TimeFrame</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t43">src/strategy_config_models.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t43"><data value='BaseStrategyConfig'>BaseStrategyConfig</data></a></td>
                <td>21</td>
                <td>21</td>
                <td>0</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t116">src/strategy_config_models.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t116"><data value='Config'>BaseStrategyConfig.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t122">src/strategy_config_models.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t122"><data value='PairsTradingConfig'>PairsTradingConfig</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t154">src/strategy_config_models.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t154"><data value='MomentumConfig'>MomentumConfig</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t181">src/strategy_config_models.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t181"><data value='ArbitrageConfig'>ArbitrageConfig</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t207">src/strategy_config_models.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t207"><data value='StrategyConfigFactory'>StrategyConfigFactory</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t242">src/strategy_config_models.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html#t242"><data value='StrategyConfigValidator'>StrategyConfigValidator</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html">src/strategy_config_models.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_config_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>114</td>
                <td>5</td>
                <td>22</td>
                <td class="right" data-ratio="109 114">96%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t20">src/strategy_framework.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t20"><data value='StrategyType'>StrategyType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t30">src/strategy_framework.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t30"><data value='SignalType'>SignalType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t40">src/strategy_framework.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t40"><data value='SignalStrength'>SignalStrength</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t49">src/strategy_framework.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t49"><data value='TradingSignal'>TradingSignal</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t72">src/strategy_framework.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t72"><data value='StrategyPerformance'>StrategyPerformance</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t88">src/strategy_framework.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t88"><data value='BaseStrategy'>BaseStrategy</data></a></td>
                <td>45</td>
                <td>45</td>
                <td>34</td>
                <td class="right" data-ratio="0 45">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t276">src/strategy_framework.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html#t276"><data value='StrategyManager'>StrategyManager</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html">src/strategy_framework.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_framework_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>72</td>
                <td>0</td>
                <td>19</td>
                <td class="right" data-ratio="72 72">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html#t23">src/strategy_health_monitor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html#t23"><data value='StrategyHealthMonitor'>StrategyHealthMonitor</data></a></td>
                <td>162</td>
                <td>162</td>
                <td>0</td>
                <td class="right" data-ratio="0 162">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html">src/strategy_health_monitor.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_strategy_health_monitor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>36</td>
                <td>3</td>
                <td>11</td>
                <td class="right" data-ratio="33 36">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_exceptions_py.html#t11">src/trading_exceptions.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trading_exceptions_py.html#t11"><data value='TradingException'>TradingException</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_exceptions_py.html#t21">src/trading_exceptions.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trading_exceptions_py.html#t21"><data value='ConfigError'>ConfigError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_exceptions_py.html#t26">src/trading_exceptions.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trading_exceptions_py.html#t26"><data value='DataError'>DataError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_exceptions_py.html#t31">src/trading_exceptions.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trading_exceptions_py.html#t31"><data value='ExecutionError'>ExecutionError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_exceptions_py.html#t36">src/trading_exceptions.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trading_exceptions_py.html#t36"><data value='PartialFillError'>PartialFillError</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_exceptions_py.html#t47">src/trading_exceptions.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trading_exceptions_py.html#t47"><data value='NetworkError'>NetworkError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_exceptions_py.html#t52">src/trading_exceptions.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trading_exceptions_py.html#t52"><data value='InsufficientFundsError'>InsufficientFundsError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_exceptions_py.html#t57">src/trading_exceptions.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trading_exceptions_py.html#t57"><data value='RiskLimitExceededError'>RiskLimitExceededError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_exceptions_py.html">src/trading_exceptions.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trading_exceptions_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_safety_guard_py.html#t18">src/trading_safety_guard.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trading_safety_guard_py.html#t18"><data value='TradingMode'>TradingMode</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_safety_guard_py.html#t25">src/trading_safety_guard.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trading_safety_guard_py.html#t25"><data value='SafetyLevel'>SafetyLevel</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_safety_guard_py.html#t32">src/trading_safety_guard.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trading_safety_guard_py.html#t32"><data value='TradingSafetyGuard'>TradingSafetyGuard</data></a></td>
                <td>108</td>
                <td>108</td>
                <td>0</td>
                <td class="right" data-ratio="0 108">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_trading_safety_guard_py.html">src/trading_safety_guard.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_trading_safety_guard_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>34</td>
                <td>5</td>
                <td>20</td>
                <td class="right" data-ratio="29 34">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html#t20">src/unified_client_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html#t20"><data value='UnifiedClientManager'>UnifiedClientManager</data></a></td>
                <td>78</td>
                <td>78</td>
                <td>0</td>
                <td class="right" data-ratio="0 78">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html">src/unified_client_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_client_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>74</td>
                <td>39</td>
                <td>2</td>
                <td class="right" data-ratio="35 74">47%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t24">src/unified_portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t24"><data value='SystemOverview'>SystemOverview</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t34">src/unified_portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html#t34"><data value='UnifiedPortfolioManager'>UnifiedPortfolioManager</data></a></td>
                <td>229</td>
                <td>229</td>
                <td>0</td>
                <td class="right" data-ratio="0 229">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html">src/unified_portfolio_manager.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_unified_portfolio_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>44</td>
                <td>3</td>
                <td>22</td>
                <td class="right" data-ratio="41 44">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_145eef247bfb46b6_utils_py.html">src/utils.py</a></td>
                <td class="name left"><a href="z_145eef247bfb46b6_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>134</td>
                <td>113</td>
                <td>0</td>
                <td class="right" data-ratio="21 134">16%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t24">state_management/state_manager.py</a></td>
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t24"><data value='StateType'>StateType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t35">state_management/state_manager.py</a></td>
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t35"><data value='StateSnapshot'>StateSnapshot</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t68">state_management/state_manager.py</a></td>
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html#t68"><data value='StateManager'>StateManager</data></a></td>
                <td>212</td>
                <td>212</td>
                <td>0</td>
                <td class="right" data-ratio="0 212">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html">state_management/state_manager.py</a></td>
                <td class="name left"><a href="z_d58350e995d6b7cc_state_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>60</td>
                <td>3</td>
                <td>14</td>
                <td class="right" data-ratio="57 60">95%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_test_runner_py.html#t25">unified_test_runner.py</a></td>
                <td class="name left"><a href="unified_test_runner_py.html#t25"><data value='TestType'>TestType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_test_runner_py.html#t34">unified_test_runner.py</a></td>
                <td class="name left"><a href="unified_test_runner_py.html#t34"><data value='TestResult'>TestResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_test_runner_py.html#t43">unified_test_runner.py</a></td>
                <td class="name left"><a href="unified_test_runner_py.html#t43"><data value='TestSuiteResult'>TestSuiteResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_test_runner_py.html#t59">unified_test_runner.py</a></td>
                <td class="name left"><a href="unified_test_runner_py.html#t59"><data value='UnifiedTestRunner'>UnifiedTestRunner</data></a></td>
                <td>159</td>
                <td>159</td>
                <td>0</td>
                <td class="right" data-ratio="0 159">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="unified_test_runner_py.html">unified_test_runner.py</a></td>
                <td class="name left"><a href="unified_test_runner_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>62</td>
                <td>62</td>
                <td>3</td>
                <td class="right" data-ratio="0 62">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>8727</td>
                <td>6979</td>
                <td>561</td>
                <td class="right" data-ratio="1748 8727">20%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-07-06 12:16 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
