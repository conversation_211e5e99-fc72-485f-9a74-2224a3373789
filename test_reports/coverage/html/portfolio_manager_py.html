<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage for portfolio_manager.py: 22%</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_8e611ae1.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="pyfile">
<header>
    <div class="content">
        <h1>
            <span class="text">Coverage for </span><b>portfolio_manager.py</b>:
            <span class="pc_cov">22%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>r</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        &nbsp; toggle line displays
                    </p>
                    <p>
                        <kbd>j</kbd>
                        <kbd>k</kbd>
                        &nbsp; next/prev highlighted chunk
                    </p>
                    <p>
                        <kbd>0</kbd> &nbsp; (zero) top of page
                    </p>
                    <p>
                        <kbd>1</kbd> &nbsp; (one) first highlighted chunk
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>u</kbd> &nbsp; up to the index
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <h2>
            <span class="text">196 statements &nbsp;</span>
            <button type="button" class="run button_toggle_run" value="run" data-shortcut="r" title="Toggle lines run">43<span class="text"> run</span></button>
            <button type="button" class="mis show_mis button_toggle_mis" value="mis" data-shortcut="m" title="Toggle lines missing">153<span class="text"> missing</span></button>
            <button type="button" class="exc show_exc button_toggle_exc" value="exc" data-shortcut="x" title="Toggle lines excluded">25<span class="text"> excluded</span></button>
        </h2>
        <p class="text">
            <a id="prevFileLink" class="nav" href="portfolio_backtester_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a id="indexLink" class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a id="nextFileLink" class="nav" href="production_readiness_check_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-07-06 11:50 +0800
        </p>
        <aside class="hidden">
            <button type="button" class="button_next_chunk" data-shortcut="j"></button>
            <button type="button" class="button_prev_chunk" data-shortcut="k"></button>
            <button type="button" class="button_top_of_page" data-shortcut="0"></button>
            <button type="button" class="button_first_chunk" data-shortcut="1"></button>
            <button type="button" class="button_prev_file" data-shortcut="["></button>
            <button type="button" class="button_next_file" data-shortcut="]"></button>
            <button type="button" class="button_to_index" data-shortcut="u"></button>
            <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
        </aside>
    </div>
</header>
<main id="source">
    <p class="pln"><span class="n"><a id="t1" href="#t1">1</a></span><span class="t"><span class="com">#!/usr/bin/env python3</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t2" href="#t2">2</a></span><span class="t"><span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t3" href="#t3">3</a></span><span class="t"><span class="str">&#32068;&#21512;&#31649;&#29702;&#22120; - &#20803;&#31574;&#30053;&#32026;&#21029;&#30340;&#25237;&#36039;&#32068;&#21512;&#31649;&#29702;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t4" href="#t4">4</a></span><span class="t"><span class="str">Portfolio Manager - Meta-strategy level portfolio management</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t5" href="#t5">5</a></span><span class="t"><span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t6" href="#t6">6</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t7" href="#t7">7</a></span><span class="t"><span class="key">import</span> <span class="nam">numpy</span> <span class="key">as</span> <span class="nam">np</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t8" href="#t8">8</a></span><span class="t"><span class="key">import</span> <span class="nam">pandas</span> <span class="key">as</span> <span class="nam">pd</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t9" href="#t9">9</a></span><span class="t"><span class="key">from</span> <span class="nam">datetime</span> <span class="key">import</span> <span class="nam">datetime</span><span class="op">,</span> <span class="nam">timedelta</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t10" href="#t10">10</a></span><span class="t"><span class="key">from</span> <span class="nam">typing</span> <span class="key">import</span> <span class="nam">Dict</span><span class="op">,</span> <span class="nam">Any</span><span class="op">,</span> <span class="nam">List</span><span class="op">,</span> <span class="nam">Optional</span><span class="op">,</span> <span class="nam">Tuple</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t11" href="#t11">11</a></span><span class="t"><span class="key">from</span> <span class="nam">dataclasses</span> <span class="key">import</span> <span class="nam">dataclass</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t12" href="#t12">12</a></span><span class="t"><span class="key">from</span> <span class="nam">scipy</span><span class="op">.</span><span class="nam">stats</span> <span class="key">import</span> <span class="nam">pearsonr</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t13" href="#t13">13</a></span><span class="t"><span class="key">import</span> <span class="nam">asyncio</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t14" href="#t14">14</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t15" href="#t15">15</a></span><span class="t"><span class="key">from</span> <span class="nam">strategy_framework</span> <span class="key">import</span> <span class="nam">BaseStrategy</span><span class="op">,</span> <span class="nam">StrategyManager</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t16" href="#t16">16</a></span><span class="t"><span class="key">from</span> <span class="nam">global_event_bus</span> <span class="key">import</span> <span class="nam">get_global_event_bus</span><span class="op">,</span> <span class="nam">Event</span><span class="op">,</span> <span class="nam">EventType</span><span class="op">,</span> <span class="nam">publish_event</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t17" href="#t17">17</a></span><span class="t"><span class="key">from</span> <span class="nam">logging_config</span> <span class="key">import</span> <span class="nam">get_logger</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t18" href="#t18">18</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t19" href="#t19">19</a></span><span class="t"><span class="nam">logger</span> <span class="op">=</span> <span class="nam">get_logger</span><span class="op">(</span><span class="nam">__name__</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t20" href="#t20">20</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t21" href="#t21">21</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t22" href="#t22">22</a></span><span class="t"><span class="op">@</span><span class="nam">dataclass</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t23" href="#t23">23</a></span><span class="t"><span class="key">class</span> <span class="nam">StrategyAllocation</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t24" href="#t24">24</a></span><span class="t">    <span class="str">"""&#31574;&#30053;&#36039;&#37329;&#20998;&#37197;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t25" href="#t25">25</a></span><span class="t">    <span class="nam">strategy_id</span><span class="op">:</span> <span class="nam">str</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t26" href="#t26">26</a></span><span class="t">    <span class="nam">allocated_capital</span><span class="op">:</span> <span class="nam">float</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t27" href="#t27">27</a></span><span class="t">    <span class="nam">target_allocation</span><span class="op">:</span> <span class="nam">float</span>      <span class="com"># &#30446;&#27161;&#20998;&#37197;&#27604;&#20363; (0-1)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t28" href="#t28">28</a></span><span class="t">    <span class="nam">current_allocation</span><span class="op">:</span> <span class="nam">float</span>     <span class="com"># &#30070;&#21069;&#20998;&#37197;&#27604;&#20363; (0-1)</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t29" href="#t29">29</a></span><span class="t">    <span class="nam">risk_budget</span><span class="op">:</span> <span class="nam">float</span>           <span class="com"># &#39080;&#38570;&#38928;&#31639;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t30" href="#t30">30</a></span><span class="t">    <span class="nam">health_score</span><span class="op">:</span> <span class="nam">float</span>          <span class="com"># &#20581;&#24247;&#20998;&#25976;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t31" href="#t31">31</a></span><span class="t">    <span class="nam">correlation_penalty</span><span class="op">:</span> <span class="nam">float</span>   <span class="com"># &#30456;&#38364;&#24615;&#25074;&#32624;</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t32" href="#t32">32</a></span><span class="t">    <span class="nam">last_updated</span><span class="op">:</span> <span class="nam">datetime</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t33" href="#t33">33</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t34" href="#t34">34</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t35" href="#t35">35</a></span><span class="t"><span class="op">@</span><span class="nam">dataclass</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t36" href="#t36">36</a></span><span class="t"><span class="key">class</span> <span class="nam">CorrelationMatrix</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t37" href="#t37">37</a></span><span class="t">    <span class="str">"""&#30456;&#38364;&#24615;&#30697;&#38499;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t38" href="#t38">38</a></span><span class="t">    <span class="nam">matrix</span><span class="op">:</span> <span class="nam">pd</span><span class="op">.</span><span class="nam">DataFrame</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t39" href="#t39">39</a></span><span class="t">    <span class="nam">timestamp</span><span class="op">:</span> <span class="nam">datetime</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t40" href="#t40">40</a></span><span class="t">    <span class="nam">lookback_days</span><span class="op">:</span> <span class="nam">int</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t41" href="#t41">41</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t42" href="#t42">42</a></span><span class="t">    <span class="key">def</span> <span class="nam">get_correlation</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">strategy1</span><span class="op">:</span> <span class="nam">str</span><span class="op">,</span> <span class="nam">strategy2</span><span class="op">:</span> <span class="nam">str</span><span class="op">)</span> <span class="op">-></span> <span class="nam">float</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t43" href="#t43">43</a></span><span class="t">        <span class="str">"""&#29554;&#21462;&#20841;&#20491;&#31574;&#30053;&#30340;&#30456;&#38364;&#24615;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t44" href="#t44">44</a></span><span class="t">        <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t45" href="#t45">45</a></span><span class="t">            <span class="key">return</span> <span class="nam">self</span><span class="op">.</span><span class="nam">matrix</span><span class="op">.</span><span class="nam">loc</span><span class="op">[</span><span class="nam">strategy1</span><span class="op">,</span> <span class="nam">strategy2</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t46" href="#t46">46</a></span><span class="t">        <span class="key">except</span> <span class="op">(</span><span class="nam">KeyError</span><span class="op">,</span> <span class="nam">IndexError</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t47" href="#t47">47</a></span><span class="t">            <span class="key">return</span> <span class="num">0.0</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t48" href="#t48">48</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t49" href="#t49">49</a></span><span class="t">    <span class="key">def</span> <span class="nam">get_max_correlation</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">strategy_id</span><span class="op">:</span> <span class="nam">str</span><span class="op">)</span> <span class="op">-></span> <span class="nam">float</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t50" href="#t50">50</a></span><span class="t">        <span class="str">"""&#29554;&#21462;&#31574;&#30053;&#33287;&#20854;&#20182;&#31574;&#30053;&#30340;&#26368;&#22823;&#30456;&#38364;&#24615;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t51" href="#t51">51</a></span><span class="t">        <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t52" href="#t52">52</a></span><span class="t">            <span class="nam">correlations</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">matrix</span><span class="op">.</span><span class="nam">loc</span><span class="op">[</span><span class="nam">strategy_id</span><span class="op">]</span><span class="op">.</span><span class="nam">drop</span><span class="op">(</span><span class="nam">strategy_id</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t53" href="#t53">53</a></span><span class="t">            <span class="key">return</span> <span class="nam">correlations</span><span class="op">.</span><span class="nam">abs</span><span class="op">(</span><span class="op">)</span><span class="op">.</span><span class="nam">max</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t54" href="#t54">54</a></span><span class="t">        <span class="key">except</span> <span class="op">(</span><span class="nam">KeyError</span><span class="op">,</span> <span class="nam">IndexError</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t55" href="#t55">55</a></span><span class="t">            <span class="key">return</span> <span class="num">0.0</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t56" href="#t56">56</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t57" href="#t57">57</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t58" href="#t58">58</a></span><span class="t"><span class="key">class</span> <span class="nam">PortfolioManager</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t59" href="#t59">59</a></span><span class="t">    <span class="str">"""&#32068;&#21512;&#31649;&#29702;&#22120; - &#20803;&#31574;&#30053;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t60" href="#t60">60</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t61" href="#t61">61</a></span><span class="t">    <span class="key">def</span> <span class="nam">__init__</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t62" href="#t62">62</a></span><span class="t">                 <span class="nam">total_capital</span><span class="op">:</span> <span class="nam">float</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t63" href="#t63">63</a></span><span class="t">                 <span class="nam">rebalance_frequency</span><span class="op">:</span> <span class="nam">int</span> <span class="op">=</span> <span class="num">7</span><span class="op">,</span>  <span class="com"># &#22825;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t64" href="#t64">64</a></span><span class="t">                 <span class="nam">max_strategy_allocation</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="num">0.4</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t65" href="#t65">65</a></span><span class="t">                 <span class="nam">min_strategy_allocation</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="num">0.05</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t66" href="#t66">66</a></span><span class="t">                 <span class="nam">correlation_threshold</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="num">0.8</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t67" href="#t67">67</a></span><span class="t">        <span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t68" href="#t68">68</a></span><span class="t"><span class="str">        &#21021;&#22987;&#21270;&#32068;&#21512;&#31649;&#29702;&#22120;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t69" href="#t69">69</a></span><span class="t"><span class="str">        </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t70" href="#t70">70</a></span><span class="t"><span class="str">        Args:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t71" href="#t71">71</a></span><span class="t"><span class="str">            total_capital: &#32317;&#36039;&#37329;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t72" href="#t72">72</a></span><span class="t"><span class="str">            rebalance_frequency: &#37325;&#26032;&#24179;&#34913;&#38971;&#29575;&#65288;&#22825;&#65289;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t73" href="#t73">73</a></span><span class="t"><span class="str">            max_strategy_allocation: &#21934;&#20491;&#31574;&#30053;&#26368;&#22823;&#20998;&#37197;&#27604;&#20363;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t74" href="#t74">74</a></span><span class="t"><span class="str">            min_strategy_allocation: &#21934;&#20491;&#31574;&#30053;&#26368;&#23567;&#20998;&#37197;&#27604;&#20363;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t75" href="#t75">75</a></span><span class="t"><span class="str">            correlation_threshold: &#30456;&#38364;&#24615;&#35686;&#21578;&#38334;&#20540;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t76" href="#t76">76</a></span><span class="t"><span class="str">        """</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t77" href="#t77">77</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">total_capital</span> <span class="op">=</span> <span class="nam">total_capital</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t78" href="#t78">78</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">rebalance_frequency</span> <span class="op">=</span> <span class="nam">rebalance_frequency</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t79" href="#t79">79</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">max_strategy_allocation</span> <span class="op">=</span> <span class="nam">max_strategy_allocation</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t80" href="#t80">80</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">min_strategy_allocation</span> <span class="op">=</span> <span class="nam">min_strategy_allocation</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t81" href="#t81">81</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">correlation_threshold</span> <span class="op">=</span> <span class="nam">correlation_threshold</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t82" href="#t82">82</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t83" href="#t83">83</a></span><span class="t">        <span class="com"># &#29376;&#24907;&#36861;&#36452;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t84" href="#t84">84</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">strategy_allocations</span><span class="op">:</span> <span class="nam">Dict</span><span class="op">[</span><span class="nam">str</span><span class="op">,</span> <span class="nam">StrategyAllocation</span><span class="op">]</span> <span class="op">=</span> <span class="op">{</span><span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t85" href="#t85">85</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">performance_history</span><span class="op">:</span> <span class="nam">Dict</span><span class="op">[</span><span class="nam">str</span><span class="op">,</span> <span class="nam">List</span><span class="op">[</span><span class="nam">float</span><span class="op">]</span><span class="op">]</span> <span class="op">=</span> <span class="op">{</span><span class="op">}</span>  <span class="com"># {strategy_id: [daily_returns]}</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t86" href="#t86">86</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">correlation_matrix</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">CorrelationMatrix</span><span class="op">]</span> <span class="op">=</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t87" href="#t87">87</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">last_rebalance</span><span class="op">:</span> <span class="nam">Optional</span><span class="op">[</span><span class="nam">datetime</span><span class="op">]</span> <span class="op">=</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t88" href="#t88">88</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t89" href="#t89">89</a></span><span class="t">        <span class="com"># &#39080;&#38570;&#25511;&#21046;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t90" href="#t90">90</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">max_portfolio_drawdown</span> <span class="op">=</span> <span class="num">0.15</span>  <span class="com"># 15%</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t91" href="#t91">91</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">emergency_stop_triggered</span> <span class="op">=</span> <span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t92" href="#t92">92</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t93" href="#t93">93</a></span><span class="t">        <span class="com"># &#20107;&#20214;&#32317;&#32218;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t94" href="#t94">94</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">event_bus</span> <span class="op">=</span> <span class="nam">get_global_event_bus</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t95" href="#t95">95</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">_setup_event_subscriptions</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t96" href="#t96">96</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t97" href="#t97">97</a></span><span class="t">        <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#32068;&#21512;&#31649;&#29702;&#22120;&#21021;&#22987;&#21270;&#23436;&#25104;: &#32317;&#36039;&#37329; $</span><span class="op">{</span><span class="nam">total_capital</span><span class="op">:</span><span class="fst">,.2f</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t98" href="#t98">98</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t99" href="#t99">99</a></span><span class="t">    <span class="key">def</span> <span class="nam">_setup_event_subscriptions</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t100" href="#t100">100</a></span><span class="t">        <span class="str">"""&#35373;&#32622;&#20107;&#20214;&#35330;&#38321;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t101" href="#t101">101</a></span><span class="t">        <span class="com"># &#35330;&#38321;&#31574;&#30053;&#20581;&#24247;&#35722;&#21270;&#20107;&#20214;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t102" href="#t102">102</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">event_bus</span><span class="op">.</span><span class="nam">subscribe</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t103" href="#t103">103</a></span><span class="t">            <span class="op">[</span><span class="nam">EventType</span><span class="op">.</span><span class="nam">STRATEGY_HEALTH_CHANGED</span><span class="op">]</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t104" href="#t104">104</a></span><span class="t">            <span class="nam">self</span><span class="op">.</span><span class="nam">_on_strategy_health_changed</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t105" href="#t105">105</a></span><span class="t">        <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t106" href="#t106">106</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t107" href="#t107">107</a></span><span class="t">        <span class="com"># &#35330;&#38321;&#20132;&#26131;&#23436;&#25104;&#20107;&#20214;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t108" href="#t108">108</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">event_bus</span><span class="op">.</span><span class="nam">subscribe</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t109" href="#t109">109</a></span><span class="t">            <span class="op">[</span><span class="nam">EventType</span><span class="op">.</span><span class="nam">ORDER_FILLED</span><span class="op">]</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t110" href="#t110">110</a></span><span class="t">            <span class="nam">self</span><span class="op">.</span><span class="nam">_on_order_filled</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t111" href="#t111">111</a></span><span class="t">        <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t112" href="#t112">112</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t113" href="#t113">113</a></span><span class="t">        <span class="com"># &#35330;&#38321;&#39080;&#38570;&#20107;&#20214;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t114" href="#t114">114</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">event_bus</span><span class="op">.</span><span class="nam">subscribe</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t115" href="#t115">115</a></span><span class="t">            <span class="op">[</span><span class="nam">EventType</span><span class="op">.</span><span class="nam">RISK_LIMIT_EXCEEDED</span><span class="op">]</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t116" href="#t116">116</a></span><span class="t">            <span class="nam">self</span><span class="op">.</span><span class="nam">_on_risk_limit_exceeded</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t117" href="#t117">117</a></span><span class="t">        <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t118" href="#t118">118</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t119" href="#t119">119</a></span><span class="t">    <span class="key">def</span> <span class="nam">add_strategy</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">strategy</span><span class="op">:</span> <span class="nam">BaseStrategy</span><span class="op">,</span> <span class="nam">initial_allocation</span><span class="op">:</span> <span class="nam">float</span> <span class="op">=</span> <span class="key">None</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t120" href="#t120">120</a></span><span class="t">        <span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t121" href="#t121">121</a></span><span class="t"><span class="str">        &#28155;&#21152;&#31574;&#30053;&#21040;&#32068;&#21512;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t122" href="#t122">122</a></span><span class="t"><span class="str">        </span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t123" href="#t123">123</a></span><span class="t"><span class="str">        Args:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t124" href="#t124">124</a></span><span class="t"><span class="str">            strategy: &#31574;&#30053;&#23526;&#20363;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t125" href="#t125">125</a></span><span class="t"><span class="str">            initial_allocation: &#21021;&#22987;&#20998;&#37197;&#27604;&#20363;&#65292;&#22914;&#26524;&#28858;None&#21063;&#24179;&#22343;&#20998;&#37197;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t126" href="#t126">126</a></span><span class="t"><span class="str">        """</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t127" href="#t127">127</a></span><span class="t">        <span class="nam">strategy_id</span> <span class="op">=</span> <span class="nam">strategy</span><span class="op">.</span><span class="nam">strategy_id</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t128" href="#t128">128</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t129" href="#t129">129</a></span><span class="t">        <span class="key">if</span> <span class="nam">initial_allocation</span> <span class="key">is</span> <span class="key">None</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t130" href="#t130">130</a></span><span class="t">            <span class="com"># &#24179;&#22343;&#20998;&#37197;&#21097;&#39192;&#36039;&#37329;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t131" href="#t131">131</a></span><span class="t">            <span class="nam">current_total</span> <span class="op">=</span> <span class="nam">sum</span><span class="op">(</span><span class="nam">alloc</span><span class="op">.</span><span class="nam">target_allocation</span> <span class="key">for</span> <span class="nam">alloc</span> <span class="key">in</span> <span class="nam">self</span><span class="op">.</span><span class="nam">strategy_allocations</span><span class="op">.</span><span class="nam">values</span><span class="op">(</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t132" href="#t132">132</a></span><span class="t">            <span class="nam">remaining</span> <span class="op">=</span> <span class="num">1.0</span> <span class="op">-</span> <span class="nam">current_total</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t133" href="#t133">133</a></span><span class="t">            <span class="nam">num_new_strategies</span> <span class="op">=</span> <span class="num">1</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t134" href="#t134">134</a></span><span class="t">            <span class="nam">initial_allocation</span> <span class="op">=</span> <span class="nam">min</span><span class="op">(</span><span class="nam">remaining</span> <span class="op">/</span> <span class="nam">num_new_strategies</span><span class="op">,</span> <span class="nam">self</span><span class="op">.</span><span class="nam">max_strategy_allocation</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t135" href="#t135">135</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t136" href="#t136">136</a></span><span class="t">        <span class="com"># &#30906;&#20445;&#20998;&#37197;&#22312;&#21512;&#29702;&#31684;&#22285;&#20839;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t137" href="#t137">137</a></span><span class="t">        <span class="nam">initial_allocation</span> <span class="op">=</span> <span class="nam">max</span><span class="op">(</span><span class="nam">min</span><span class="op">(</span><span class="nam">initial_allocation</span><span class="op">,</span> <span class="nam">self</span><span class="op">.</span><span class="nam">max_strategy_allocation</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t138" href="#t138">138</a></span><span class="t">                               <span class="nam">self</span><span class="op">.</span><span class="nam">min_strategy_allocation</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t139" href="#t139">139</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t140" href="#t140">140</a></span><span class="t">        <span class="nam">allocation</span> <span class="op">=</span> <span class="nam">StrategyAllocation</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t141" href="#t141">141</a></span><span class="t">            <span class="nam">strategy_id</span><span class="op">=</span><span class="nam">strategy_id</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t142" href="#t142">142</a></span><span class="t">            <span class="nam">allocated_capital</span><span class="op">=</span><span class="nam">self</span><span class="op">.</span><span class="nam">total_capital</span> <span class="op">*</span> <span class="nam">initial_allocation</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t143" href="#t143">143</a></span><span class="t">            <span class="nam">target_allocation</span><span class="op">=</span><span class="nam">initial_allocation</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t144" href="#t144">144</a></span><span class="t">            <span class="nam">current_allocation</span><span class="op">=</span><span class="nam">initial_allocation</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t145" href="#t145">145</a></span><span class="t">            <span class="nam">risk_budget</span><span class="op">=</span><span class="nam">initial_allocation</span> <span class="op">*</span> <span class="num">0.02</span><span class="op">,</span>  <span class="com"># 2%&#39080;&#38570;&#38928;&#31639;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t146" href="#t146">146</a></span><span class="t">            <span class="nam">health_score</span><span class="op">=</span><span class="num">0.5</span><span class="op">,</span>  <span class="com"># &#21021;&#22987;&#20581;&#24247;&#20998;&#25976;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t147" href="#t147">147</a></span><span class="t">            <span class="nam">correlation_penalty</span><span class="op">=</span><span class="num">0.0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t148" href="#t148">148</a></span><span class="t">            <span class="nam">last_updated</span><span class="op">=</span><span class="nam">datetime</span><span class="op">.</span><span class="nam">now</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t149" href="#t149">149</a></span><span class="t">        <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t150" href="#t150">150</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t151" href="#t151">151</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">strategy_allocations</span><span class="op">[</span><span class="nam">strategy_id</span><span class="op">]</span> <span class="op">=</span> <span class="nam">allocation</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t152" href="#t152">152</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">performance_history</span><span class="op">[</span><span class="nam">strategy_id</span><span class="op">]</span> <span class="op">=</span> <span class="op">[</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t153" href="#t153">153</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t154" href="#t154">154</a></span><span class="t">        <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#31574;&#30053;&#24050;&#28155;&#21152;&#21040;&#32068;&#21512;: </span><span class="op">{</span><span class="nam">strategy_id</span><span class="op">}</span><span class="fst">, &#20998;&#37197;: </span><span class="op">{</span><span class="nam">initial_allocation</span><span class="op">:</span><span class="fst">.1%</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t155" href="#t155">155</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t156" href="#t156">156</a></span><span class="t">        <span class="com"># &#30332;&#24067;&#20107;&#20214;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t157" href="#t157">157</a></span><span class="t">        <span class="nam">publish_event</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t158" href="#t158">158</a></span><span class="t">            <span class="nam">EventType</span><span class="op">.</span><span class="nam">CAPITAL_ALLOCATION_CHANGED</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t159" href="#t159">159</a></span><span class="t">            <span class="str">"portfolio_manager"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t160" href="#t160">160</a></span><span class="t">            <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t161" href="#t161">161</a></span><span class="t">                <span class="str">"strategy_id"</span><span class="op">:</span> <span class="nam">strategy_id</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t162" href="#t162">162</a></span><span class="t">                <span class="str">"allocation"</span><span class="op">:</span> <span class="nam">initial_allocation</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t163" href="#t163">163</a></span><span class="t">                <span class="str">"allocated_capital"</span><span class="op">:</span> <span class="nam">allocation</span><span class="op">.</span><span class="nam">allocated_capital</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t164" href="#t164">164</a></span><span class="t">            <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t165" href="#t165">165</a></span><span class="t">        <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t166" href="#t166">166</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t167" href="#t167">167</a></span><span class="t">    <span class="key">def</span> <span class="nam">calculate_optimal_allocation</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span> <span class="op">-></span> <span class="nam">Dict</span><span class="op">[</span><span class="nam">str</span><span class="op">,</span> <span class="nam">float</span><span class="op">]</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t168" href="#t168">168</a></span><span class="t">        <span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t169" href="#t169">169</a></span><span class="t"><span class="str">        &#35336;&#31639;&#26368;&#20778;&#36039;&#37329;&#20998;&#37197;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t170" href="#t170">170</a></span><span class="t"><span class="str">        &#22522;&#26044;&#20581;&#24247;&#20998;&#25976;&#12289;&#30456;&#38364;&#24615;&#12289;&#21205;&#37327;&#21644;&#39080;&#38570;&#35519;&#25972;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t171" href="#t171">171</a></span><span class="t"><span class="str">        """</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t172" href="#t172">172</a></span><span class="t">        <span class="key">if</span> <span class="key">not</span> <span class="nam">self</span><span class="op">.</span><span class="nam">strategy_allocations</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t173" href="#t173">173</a></span><span class="t">            <span class="key">return</span> <span class="op">{</span><span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t174" href="#t174">174</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t175" href="#t175">175</a></span><span class="t">        <span class="com"># &#35336;&#31639;&#27599;&#20491;&#31574;&#30053;&#30340;&#32156;&#21512;&#20998;&#25976;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t176" href="#t176">176</a></span><span class="t">        <span class="nam">strategy_scores</span> <span class="op">=</span> <span class="op">{</span><span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t177" href="#t177">177</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t178" href="#t178">178</a></span><span class="t">        <span class="key">for</span> <span class="nam">strategy_id</span><span class="op">,</span> <span class="nam">allocation</span> <span class="key">in</span> <span class="nam">self</span><span class="op">.</span><span class="nam">strategy_allocations</span><span class="op">.</span><span class="nam">items</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t179" href="#t179">179</a></span><span class="t">            <span class="com"># 1. &#22522;&#30990;&#20581;&#24247;&#20998;&#25976; (40%&#27402;&#37325;)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t180" href="#t180">180</a></span><span class="t">            <span class="nam">health_score</span> <span class="op">=</span> <span class="nam">allocation</span><span class="op">.</span><span class="nam">health_score</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t181" href="#t181">181</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t182" href="#t182">182</a></span><span class="t">            <span class="com"># 2. &#21205;&#37327;&#20998;&#25976; (30%&#27402;&#37325;) - &#22522;&#26044;&#26368;&#36817;&#34920;&#29694;&#36264;&#21218;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t183" href="#t183">183</a></span><span class="t">            <span class="nam">momentum_score</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_calculate_momentum_score</span><span class="op">(</span><span class="nam">strategy_id</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t184" href="#t184">184</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t185" href="#t185">185</a></span><span class="t">            <span class="com"># 3. &#39080;&#38570;&#35519;&#25972;&#20998;&#25976; (20%&#27402;&#37325;) - &#22522;&#26044;&#27874;&#21205;&#29575;&#21644;&#22238;&#25764;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t186" href="#t186">186</a></span><span class="t">            <span class="nam">risk_adjusted_score</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_calculate_risk_adjusted_score</span><span class="op">(</span><span class="nam">strategy_id</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t187" href="#t187">187</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t188" href="#t188">188</a></span><span class="t">            <span class="com"># 4. &#30456;&#38364;&#24615;&#25074;&#32624; (10%&#27402;&#37325;)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t189" href="#t189">189</a></span><span class="t">            <span class="nam">correlation_penalty</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_calculate_correlation_penalty</span><span class="op">(</span><span class="nam">strategy_id</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t190" href="#t190">190</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t191" href="#t191">191</a></span><span class="t">            <span class="com"># &#32156;&#21512;&#20998;&#25976;&#35336;&#31639;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t192" href="#t192">192</a></span><span class="t">            <span class="nam">composite_score</span> <span class="op">=</span> <span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t193" href="#t193">193</a></span><span class="t">                <span class="nam">health_score</span> <span class="op">*</span> <span class="num">0.4</span> <span class="op">+</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t194" href="#t194">194</a></span><span class="t">                <span class="nam">momentum_score</span> <span class="op">*</span> <span class="num">0.3</span> <span class="op">+</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t195" href="#t195">195</a></span><span class="t">                <span class="nam">risk_adjusted_score</span> <span class="op">*</span> <span class="num">0.2</span> <span class="op">+</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t196" href="#t196">196</a></span><span class="t">                <span class="op">(</span><span class="num">1</span> <span class="op">-</span> <span class="nam">correlation_penalty</span><span class="op">)</span> <span class="op">*</span> <span class="num">0.1</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t197" href="#t197">197</a></span><span class="t">            <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t198" href="#t198">198</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t199" href="#t199">199</a></span><span class="t">            <span class="com"># &#25033;&#29992;&#24066;&#22580;&#29872;&#22659;&#35519;&#25972;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t200" href="#t200">200</a></span><span class="t">            <span class="nam">market_adjustment</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_get_market_environment_adjustment</span><span class="op">(</span><span class="nam">strategy_id</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t201" href="#t201">201</a></span><span class="t">            <span class="nam">final_score</span> <span class="op">=</span> <span class="nam">composite_score</span> <span class="op">*</span> <span class="nam">market_adjustment</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t202" href="#t202">202</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t203" href="#t203">203</a></span><span class="t">            <span class="nam">strategy_scores</span><span class="op">[</span><span class="nam">strategy_id</span><span class="op">]</span> <span class="op">=</span> <span class="nam">max</span><span class="op">(</span><span class="nam">final_score</span><span class="op">,</span> <span class="num">0.05</span><span class="op">)</span>  <span class="com"># &#26368;&#20302;5%</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t204" href="#t204">204</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t205" href="#t205">205</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#31574;&#30053; </span><span class="op">{</span><span class="nam">strategy_id</span><span class="op">}</span><span class="fst"> &#20998;&#25976;: &#20581;&#24247;=</span><span class="op">{</span><span class="nam">health_score</span><span class="op">:</span><span class="fst">.2f</span><span class="op">}</span><span class="fst">, </span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t206" href="#t206">206</a></span><span class="t">                        <span class="fst">f"</span><span class="fst">&#21205;&#37327;=</span><span class="op">{</span><span class="nam">momentum_score</span><span class="op">:</span><span class="fst">.2f</span><span class="op">}</span><span class="fst">, &#39080;&#38570;&#35519;&#25972;=</span><span class="op">{</span><span class="nam">risk_adjusted_score</span><span class="op">:</span><span class="fst">.2f</span><span class="op">}</span><span class="fst">, </span><span class="fst">"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t207" href="#t207">207</a></span><span class="t">                        <span class="fst">f"</span><span class="fst">&#30456;&#38364;&#24615;&#25074;&#32624;=</span><span class="op">{</span><span class="nam">correlation_penalty</span><span class="op">:</span><span class="fst">.2f</span><span class="op">}</span><span class="fst">, &#26368;&#32066;=</span><span class="op">{</span><span class="nam">final_score</span><span class="op">:</span><span class="fst">.2f</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t208" href="#t208">208</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t209" href="#t209">209</a></span><span class="t">        <span class="com"># &#27512;&#19968;&#21270;&#20006;&#25033;&#29992;&#20998;&#37197;&#38480;&#21046;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t210" href="#t210">210</a></span><span class="t">        <span class="key">return</span> <span class="nam">self</span><span class="op">.</span><span class="nam">_normalize_allocations</span><span class="op">(</span><span class="nam">strategy_scores</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t211" href="#t211">211</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t212" href="#t212">212</a></span><span class="t">    <span class="key">def</span> <span class="nam">_calculate_momentum_score</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">strategy_id</span><span class="op">:</span> <span class="nam">str</span><span class="op">)</span> <span class="op">-></span> <span class="nam">float</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t213" href="#t213">213</a></span><span class="t">        <span class="str">"""&#35336;&#31639;&#31574;&#30053;&#21205;&#37327;&#20998;&#25976;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t214" href="#t214">214</a></span><span class="t">        <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t215" href="#t215">215</a></span><span class="t">            <span class="key">if</span> <span class="nam">strategy_id</span> <span class="key">not</span> <span class="key">in</span> <span class="nam">self</span><span class="op">.</span><span class="nam">performance_history</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t216" href="#t216">216</a></span><span class="t">                <span class="key">return</span> <span class="num">0.5</span>  <span class="com"># &#20013;&#24615;&#20998;&#25976;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t217" href="#t217">217</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t218" href="#t218">218</a></span><span class="t">            <span class="nam">returns</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">performance_history</span><span class="op">[</span><span class="nam">strategy_id</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t219" href="#t219">219</a></span><span class="t">            <span class="key">if</span> <span class="nam">len</span><span class="op">(</span><span class="nam">returns</span><span class="op">)</span> <span class="op">&lt;</span> <span class="num">10</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t220" href="#t220">220</a></span><span class="t">                <span class="key">return</span> <span class="num">0.5</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t221" href="#t221">221</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t222" href="#t222">222</a></span><span class="t">            <span class="com"># &#35336;&#31639;&#26368;&#36817;10&#26399;&#30340;&#24179;&#22343;&#25910;&#30410;&#29575;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t223" href="#t223">223</a></span><span class="t">            <span class="nam">recent_returns</span> <span class="op">=</span> <span class="nam">returns</span><span class="op">[</span><span class="op">-</span><span class="num">10</span><span class="op">:</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t224" href="#t224">224</a></span><span class="t">            <span class="nam">avg_return</span> <span class="op">=</span> <span class="nam">np</span><span class="op">.</span><span class="nam">mean</span><span class="op">(</span><span class="nam">recent_returns</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t225" href="#t225">225</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t226" href="#t226">226</a></span><span class="t">            <span class="com"># &#35336;&#31639;&#36264;&#21218;&#24375;&#24230;&#65288;&#32218;&#24615;&#22238;&#27512;&#26012;&#29575;&#65289;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t227" href="#t227">227</a></span><span class="t">            <span class="nam">x</span> <span class="op">=</span> <span class="nam">np</span><span class="op">.</span><span class="nam">arange</span><span class="op">(</span><span class="nam">len</span><span class="op">(</span><span class="nam">recent_returns</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t228" href="#t228">228</a></span><span class="t">            <span class="nam">slope</span> <span class="op">=</span> <span class="nam">np</span><span class="op">.</span><span class="nam">polyfit</span><span class="op">(</span><span class="nam">x</span><span class="op">,</span> <span class="nam">recent_returns</span><span class="op">,</span> <span class="num">1</span><span class="op">)</span><span class="op">[</span><span class="num">0</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t229" href="#t229">229</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t230" href="#t230">230</a></span><span class="t">            <span class="com"># &#36681;&#25563;&#28858;0-1&#20998;&#25976;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t231" href="#t231">231</a></span><span class="t">            <span class="nam">momentum_score</span> <span class="op">=</span> <span class="num">0.5</span> <span class="op">+</span> <span class="nam">np</span><span class="op">.</span><span class="nam">tanh</span><span class="op">(</span><span class="nam">avg_return</span> <span class="op">*</span> <span class="num">10</span><span class="op">)</span> <span class="op">*</span> <span class="num">0.3</span> <span class="op">+</span> <span class="nam">np</span><span class="op">.</span><span class="nam">tanh</span><span class="op">(</span><span class="nam">slope</span> <span class="op">*</span> <span class="num">50</span><span class="op">)</span> <span class="op">*</span> <span class="num">0.2</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t232" href="#t232">232</a></span><span class="t">            <span class="key">return</span> <span class="nam">max</span><span class="op">(</span><span class="nam">min</span><span class="op">(</span><span class="nam">momentum_score</span><span class="op">,</span> <span class="num">1.0</span><span class="op">)</span><span class="op">,</span> <span class="num">0.0</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t233" href="#t233">233</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t234" href="#t234">234</a></span><span class="t">        <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t235" href="#t235">235</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#35336;&#31639;&#21205;&#37327;&#20998;&#25976;&#22833;&#25943; </span><span class="op">{</span><span class="nam">strategy_id</span><span class="op">}</span><span class="fst">: </span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t236" href="#t236">236</a></span><span class="t">            <span class="key">return</span> <span class="num">0.5</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t237" href="#t237">237</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t238" href="#t238">238</a></span><span class="t">    <span class="key">def</span> <span class="nam">_calculate_risk_adjusted_score</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">strategy_id</span><span class="op">:</span> <span class="nam">str</span><span class="op">)</span> <span class="op">-></span> <span class="nam">float</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t239" href="#t239">239</a></span><span class="t">        <span class="str">"""&#35336;&#31639;&#39080;&#38570;&#35519;&#25972;&#20998;&#25976;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t240" href="#t240">240</a></span><span class="t">        <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t241" href="#t241">241</a></span><span class="t">            <span class="key">if</span> <span class="nam">strategy_id</span> <span class="key">not</span> <span class="key">in</span> <span class="nam">self</span><span class="op">.</span><span class="nam">performance_history</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t242" href="#t242">242</a></span><span class="t">                <span class="key">return</span> <span class="num">0.5</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t243" href="#t243">243</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t244" href="#t244">244</a></span><span class="t">            <span class="nam">returns</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">performance_history</span><span class="op">[</span><span class="nam">strategy_id</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t245" href="#t245">245</a></span><span class="t">            <span class="key">if</span> <span class="nam">len</span><span class="op">(</span><span class="nam">returns</span><span class="op">)</span> <span class="op">&lt;</span> <span class="num">20</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t246" href="#t246">246</a></span><span class="t">                <span class="key">return</span> <span class="num">0.5</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t247" href="#t247">247</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t248" href="#t248">248</a></span><span class="t">            <span class="com"># &#35336;&#31639;&#22799;&#26222;&#27604;&#29575;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t249" href="#t249">249</a></span><span class="t">            <span class="nam">avg_return</span> <span class="op">=</span> <span class="nam">np</span><span class="op">.</span><span class="nam">mean</span><span class="op">(</span><span class="nam">returns</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t250" href="#t250">250</a></span><span class="t">            <span class="nam">volatility</span> <span class="op">=</span> <span class="nam">np</span><span class="op">.</span><span class="nam">std</span><span class="op">(</span><span class="nam">returns</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t251" href="#t251">251</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t252" href="#t252">252</a></span><span class="t">            <span class="key">if</span> <span class="nam">volatility</span> <span class="op">==</span> <span class="num">0</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t253" href="#t253">253</a></span><span class="t">                <span class="key">return</span> <span class="num">0.5</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t254" href="#t254">254</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t255" href="#t255">255</a></span><span class="t">            <span class="nam">sharpe_ratio</span> <span class="op">=</span> <span class="nam">avg_return</span> <span class="op">/</span> <span class="nam">volatility</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t256" href="#t256">256</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t257" href="#t257">257</a></span><span class="t">            <span class="com"># &#35336;&#31639;&#26368;&#22823;&#22238;&#25764;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t258" href="#t258">258</a></span><span class="t">            <span class="nam">cumulative</span> <span class="op">=</span> <span class="nam">np</span><span class="op">.</span><span class="nam">cumprod</span><span class="op">(</span><span class="num">1</span> <span class="op">+</span> <span class="nam">np</span><span class="op">.</span><span class="nam">array</span><span class="op">(</span><span class="nam">returns</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t259" href="#t259">259</a></span><span class="t">            <span class="nam">running_max</span> <span class="op">=</span> <span class="nam">np</span><span class="op">.</span><span class="nam">maximum</span><span class="op">.</span><span class="nam">accumulate</span><span class="op">(</span><span class="nam">cumulative</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t260" href="#t260">260</a></span><span class="t">            <span class="nam">drawdown</span> <span class="op">=</span> <span class="op">(</span><span class="nam">cumulative</span> <span class="op">-</span> <span class="nam">running_max</span><span class="op">)</span> <span class="op">/</span> <span class="nam">running_max</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t261" href="#t261">261</a></span><span class="t">            <span class="nam">max_drawdown</span> <span class="op">=</span> <span class="nam">abs</span><span class="op">(</span><span class="nam">np</span><span class="op">.</span><span class="nam">min</span><span class="op">(</span><span class="nam">drawdown</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t262" href="#t262">262</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t263" href="#t263">263</a></span><span class="t">            <span class="com"># &#39080;&#38570;&#35519;&#25972;&#20998;&#25976; = &#22799;&#26222;&#27604;&#29575;&#27402;&#37325; - &#22238;&#25764;&#25074;&#32624;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t264" href="#t264">264</a></span><span class="t">            <span class="nam">risk_score</span> <span class="op">=</span> <span class="num">0.5</span> <span class="op">+</span> <span class="nam">np</span><span class="op">.</span><span class="nam">tanh</span><span class="op">(</span><span class="nam">sharpe_ratio</span><span class="op">)</span> <span class="op">*</span> <span class="num">0.3</span> <span class="op">-</span> <span class="nam">max_drawdown</span> <span class="op">*</span> <span class="num">0.5</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t265" href="#t265">265</a></span><span class="t">            <span class="key">return</span> <span class="nam">max</span><span class="op">(</span><span class="nam">min</span><span class="op">(</span><span class="nam">risk_score</span><span class="op">,</span> <span class="num">1.0</span><span class="op">)</span><span class="op">,</span> <span class="num">0.0</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t266" href="#t266">266</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t267" href="#t267">267</a></span><span class="t">        <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t268" href="#t268">268</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#35336;&#31639;&#39080;&#38570;&#35519;&#25972;&#20998;&#25976;&#22833;&#25943; </span><span class="op">{</span><span class="nam">strategy_id</span><span class="op">}</span><span class="fst">: </span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t269" href="#t269">269</a></span><span class="t">            <span class="key">return</span> <span class="num">0.5</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t270" href="#t270">270</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t271" href="#t271">271</a></span><span class="t">    <span class="key">def</span> <span class="nam">_calculate_correlation_penalty</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">strategy_id</span><span class="op">:</span> <span class="nam">str</span><span class="op">)</span> <span class="op">-></span> <span class="nam">float</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t272" href="#t272">272</a></span><span class="t">        <span class="str">"""&#35336;&#31639;&#30456;&#38364;&#24615;&#25074;&#32624;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t273" href="#t273">273</a></span><span class="t">        <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t274" href="#t274">274</a></span><span class="t">            <span class="key">if</span> <span class="key">not</span> <span class="nam">self</span><span class="op">.</span><span class="nam">correlation_matrix</span> <span class="key">or</span> <span class="nam">self</span><span class="op">.</span><span class="nam">correlation_matrix</span><span class="op">.</span><span class="nam">matrix</span><span class="op">.</span><span class="nam">empty</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t275" href="#t275">275</a></span><span class="t">                <span class="key">return</span> <span class="num">0.0</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t276" href="#t276">276</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t277" href="#t277">277</a></span><span class="t">            <span class="nam">max_correlation</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">correlation_matrix</span><span class="op">.</span><span class="nam">get_max_correlation</span><span class="op">(</span><span class="nam">strategy_id</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t278" href="#t278">278</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t279" href="#t279">279</a></span><span class="t">            <span class="com"># &#30456;&#38364;&#24615;&#36229;&#36942;0.7&#38283;&#22987;&#25074;&#32624;&#65292;&#36229;&#36942;0.9&#37325;&#24230;&#25074;&#32624;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t280" href="#t280">280</a></span><span class="t">            <span class="key">if</span> <span class="nam">max_correlation</span> <span class="op">></span> <span class="num">0.9</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t281" href="#t281">281</a></span><span class="t">                <span class="key">return</span> <span class="num">0.8</span>  <span class="com"># &#37325;&#24230;&#25074;&#32624;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t282" href="#t282">282</a></span><span class="t">            <span class="key">elif</span> <span class="nam">max_correlation</span> <span class="op">></span> <span class="num">0.7</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t283" href="#t283">283</a></span><span class="t">                <span class="key">return</span> <span class="op">(</span><span class="nam">max_correlation</span> <span class="op">-</span> <span class="num">0.7</span><span class="op">)</span> <span class="op">/</span> <span class="num">0.2</span> <span class="op">*</span> <span class="num">0.5</span>  <span class="com"># &#32218;&#24615;&#25074;&#32624;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t284" href="#t284">284</a></span><span class="t">            <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t285" href="#t285">285</a></span><span class="t">                <span class="key">return</span> <span class="num">0.0</span>  <span class="com"># &#28961;&#25074;&#32624;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t286" href="#t286">286</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t287" href="#t287">287</a></span><span class="t">        <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t288" href="#t288">288</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#35336;&#31639;&#30456;&#38364;&#24615;&#25074;&#32624;&#22833;&#25943; </span><span class="op">{</span><span class="nam">strategy_id</span><span class="op">}</span><span class="fst">: </span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t289" href="#t289">289</a></span><span class="t">            <span class="key">return</span> <span class="num">0.0</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t290" href="#t290">290</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t291" href="#t291">291</a></span><span class="t">    <span class="key">def</span> <span class="nam">_get_market_environment_adjustment</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">strategy_id</span><span class="op">:</span> <span class="nam">str</span><span class="op">)</span> <span class="op">-></span> <span class="nam">float</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t292" href="#t292">292</a></span><span class="t">        <span class="str">"""&#26681;&#25818;&#24066;&#22580;&#29872;&#22659;&#35519;&#25972;&#31574;&#30053;&#27402;&#37325;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t293" href="#t293">293</a></span><span class="t">        <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t294" href="#t294">294</a></span><span class="t">            <span class="com"># &#31777;&#21270;&#30340;&#24066;&#22580;&#29872;&#22659;&#21028;&#26039;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t295" href="#t295">295</a></span><span class="t">            <span class="com"># &#23526;&#38555;&#23526;&#29694;&#20013;&#21487;&#20197;&#22522;&#26044;VIX&#12289;&#24066;&#22580;&#36264;&#21218;&#31561;&#25351;&#27161;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t296" href="#t296">296</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t297" href="#t297">297</a></span><span class="t">            <span class="nam">allocation</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">strategy_allocations</span><span class="op">[</span><span class="nam">strategy_id</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t298" href="#t298">298</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t299" href="#t299">299</a></span><span class="t">            <span class="com"># &#26681;&#25818;&#31574;&#30053;&#39006;&#22411;&#21644;&#30070;&#21069;&#24066;&#22580;&#29872;&#22659;&#35519;&#25972;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t300" href="#t300">300</a></span><span class="t">            <span class="com"># &#36889;&#35041;&#20351;&#29992;&#20581;&#24247;&#20998;&#25976;&#20316;&#28858;&#24066;&#22580;&#36969;&#25033;&#24615;&#30340;&#20195;&#29702;&#25351;&#27161;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t301" href="#t301">301</a></span><span class="t">            <span class="key">if</span> <span class="nam">allocation</span><span class="op">.</span><span class="nam">health_score</span> <span class="op">></span> <span class="num">0.7</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t302" href="#t302">302</a></span><span class="t">                <span class="key">return</span> <span class="num">1.2</span>  <span class="com"># &#34920;&#29694;&#22909;&#30340;&#31574;&#30053;&#22312;&#22909;&#24066;&#22580;&#20013;&#21152;&#27402;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t303" href="#t303">303</a></span><span class="t">            <span class="key">elif</span> <span class="nam">allocation</span><span class="op">.</span><span class="nam">health_score</span> <span class="op">&lt;</span> <span class="num">0.3</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t304" href="#t304">304</a></span><span class="t">                <span class="key">return</span> <span class="num">0.8</span>  <span class="com"># &#34920;&#29694;&#24046;&#30340;&#31574;&#30053;&#38477;&#27402;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t305" href="#t305">305</a></span><span class="t">            <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t306" href="#t306">306</a></span><span class="t">                <span class="key">return</span> <span class="num">1.0</span>  <span class="com"># &#20013;&#24615;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t307" href="#t307">307</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t308" href="#t308">308</a></span><span class="t">        <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t309" href="#t309">309</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">error</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#24066;&#22580;&#29872;&#22659;&#35519;&#25972;&#22833;&#25943; </span><span class="op">{</span><span class="nam">strategy_id</span><span class="op">}</span><span class="fst">: </span><span class="op">{</span><span class="nam">e</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t310" href="#t310">310</a></span><span class="t">            <span class="key">return</span> <span class="num">1.0</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t311" href="#t311">311</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t312" href="#t312">312</a></span><span class="t">    <span class="key">def</span> <span class="nam">_normalize_allocations</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">strategy_scores</span><span class="op">:</span> <span class="nam">Dict</span><span class="op">[</span><span class="nam">str</span><span class="op">,</span> <span class="nam">float</span><span class="op">]</span><span class="op">)</span> <span class="op">-></span> <span class="nam">Dict</span><span class="op">[</span><span class="nam">str</span><span class="op">,</span> <span class="nam">float</span><span class="op">]</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t313" href="#t313">313</a></span><span class="t">        <span class="str">"""&#27512;&#19968;&#21270;&#20998;&#37197;&#20006;&#25033;&#29992;&#38480;&#21046;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t314" href="#t314">314</a></span><span class="t">        <span class="nam">total_score</span> <span class="op">=</span> <span class="nam">sum</span><span class="op">(</span><span class="nam">strategy_scores</span><span class="op">.</span><span class="nam">values</span><span class="op">(</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t315" href="#t315">315</a></span><span class="t">        <span class="key">if</span> <span class="nam">total_score</span> <span class="op">==</span> <span class="num">0</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t316" href="#t316">316</a></span><span class="t">            <span class="nam">equal_allocation</span> <span class="op">=</span> <span class="num">1.0</span> <span class="op">/</span> <span class="nam">len</span><span class="op">(</span><span class="nam">strategy_scores</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t317" href="#t317">317</a></span><span class="t">            <span class="key">return</span> <span class="op">{</span><span class="nam">sid</span><span class="op">:</span> <span class="nam">equal_allocation</span> <span class="key">for</span> <span class="nam">sid</span> <span class="key">in</span> <span class="nam">strategy_scores</span><span class="op">.</span><span class="nam">keys</span><span class="op">(</span><span class="op">)</span><span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t318" href="#t318">318</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t319" href="#t319">319</a></span><span class="t">        <span class="com"># &#35336;&#31639;&#21407;&#22987;&#20998;&#37197;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t320" href="#t320">320</a></span><span class="t">        <span class="nam">raw_allocations</span> <span class="op">=</span> <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t321" href="#t321">321</a></span><span class="t">            <span class="nam">sid</span><span class="op">:</span> <span class="nam">score</span> <span class="op">/</span> <span class="nam">total_score</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t322" href="#t322">322</a></span><span class="t">            <span class="key">for</span> <span class="nam">sid</span><span class="op">,</span> <span class="nam">score</span> <span class="key">in</span> <span class="nam">strategy_scores</span><span class="op">.</span><span class="nam">items</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t323" href="#t323">323</a></span><span class="t">        <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t324" href="#t324">324</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t325" href="#t325">325</a></span><span class="t">        <span class="com"># &#25033;&#29992;&#20998;&#37197;&#38480;&#21046;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t326" href="#t326">326</a></span><span class="t">        <span class="nam">target_allocations</span> <span class="op">=</span> <span class="op">{</span><span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t327" href="#t327">327</a></span><span class="t">        <span class="key">for</span> <span class="nam">strategy_id</span><span class="op">,</span> <span class="nam">raw_allocation</span> <span class="key">in</span> <span class="nam">raw_allocations</span><span class="op">.</span><span class="nam">items</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t328" href="#t328">328</a></span><span class="t">            <span class="nam">target_allocation</span> <span class="op">=</span> <span class="nam">max</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t329" href="#t329">329</a></span><span class="t">                <span class="nam">min</span><span class="op">(</span><span class="nam">raw_allocation</span><span class="op">,</span> <span class="nam">self</span><span class="op">.</span><span class="nam">max_strategy_allocation</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t330" href="#t330">330</a></span><span class="t">                <span class="nam">self</span><span class="op">.</span><span class="nam">min_strategy_allocation</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t331" href="#t331">331</a></span><span class="t">            <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t332" href="#t332">332</a></span><span class="t">            <span class="nam">target_allocations</span><span class="op">[</span><span class="nam">strategy_id</span><span class="op">]</span> <span class="op">=</span> <span class="nam">target_allocation</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t333" href="#t333">333</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t334" href="#t334">334</a></span><span class="t">        <span class="com"># &#37325;&#26032;&#27512;&#19968;&#21270;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t335" href="#t335">335</a></span><span class="t">        <span class="nam">total_allocation</span> <span class="op">=</span> <span class="nam">sum</span><span class="op">(</span><span class="nam">target_allocations</span><span class="op">.</span><span class="nam">values</span><span class="op">(</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t336" href="#t336">336</a></span><span class="t">        <span class="key">if</span> <span class="nam">total_allocation</span> <span class="op">></span> <span class="num">0</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t337" href="#t337">337</a></span><span class="t">            <span class="key">for</span> <span class="nam">strategy_id</span> <span class="key">in</span> <span class="nam">target_allocations</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t338" href="#t338">338</a></span><span class="t">                <span class="nam">target_allocations</span><span class="op">[</span><span class="nam">strategy_id</span><span class="op">]</span> <span class="op">/=</span> <span class="nam">total_allocation</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t339" href="#t339">339</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t340" href="#t340">340</a></span><span class="t">        <span class="key">return</span> <span class="nam">target_allocations</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t341" href="#t341">341</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t342" href="#t342">342</a></span><span class="t">    <span class="key">def</span> <span class="nam">rebalance_portfolio</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span> <span class="op">-></span> <span class="nam">Dict</span><span class="op">[</span><span class="nam">str</span><span class="op">,</span> <span class="nam">Any</span><span class="op">]</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t343" href="#t343">343</a></span><span class="t">        <span class="str">"""&#37325;&#26032;&#24179;&#34913;&#25237;&#36039;&#32068;&#21512;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t344" href="#t344">344</a></span><span class="t">        <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"&#38283;&#22987;&#25237;&#36039;&#32068;&#21512;&#37325;&#26032;&#24179;&#34913;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t345" href="#t345">345</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t346" href="#t346">346</a></span><span class="t">        <span class="com"># &#35336;&#31639;&#26368;&#20778;&#20998;&#37197;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t347" href="#t347">347</a></span><span class="t">        <span class="nam">optimal_allocations</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">calculate_optimal_allocation</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t348" href="#t348">348</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t349" href="#t349">349</a></span><span class="t">        <span class="nam">rebalance_actions</span> <span class="op">=</span> <span class="op">[</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t350" href="#t350">350</a></span><span class="t">        <span class="nam">total_change</span> <span class="op">=</span> <span class="num">0.0</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t351" href="#t351">351</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t352" href="#t352">352</a></span><span class="t">        <span class="key">for</span> <span class="nam">strategy_id</span><span class="op">,</span> <span class="nam">target_allocation</span> <span class="key">in</span> <span class="nam">optimal_allocations</span><span class="op">.</span><span class="nam">items</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t353" href="#t353">353</a></span><span class="t">            <span class="key">if</span> <span class="nam">strategy_id</span> <span class="key">in</span> <span class="nam">self</span><span class="op">.</span><span class="nam">strategy_allocations</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t354" href="#t354">354</a></span><span class="t">                <span class="nam">current_allocation</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">strategy_allocations</span><span class="op">[</span><span class="nam">strategy_id</span><span class="op">]</span><span class="op">.</span><span class="nam">current_allocation</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t355" href="#t355">355</a></span><span class="t">                <span class="nam">change</span> <span class="op">=</span> <span class="nam">target_allocation</span> <span class="op">-</span> <span class="nam">current_allocation</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t356" href="#t356">356</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t357" href="#t357">357</a></span><span class="t">                <span class="key">if</span> <span class="nam">abs</span><span class="op">(</span><span class="nam">change</span><span class="op">)</span> <span class="op">></span> <span class="num">0.02</span><span class="op">:</span>  <span class="com"># &#21482;&#26377;&#35722;&#21270;&#36229;&#36942;2%&#25165;&#22519;&#34892;</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t358" href="#t358">358</a></span><span class="t">                    <span class="com"># &#26356;&#26032;&#20998;&#37197;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t359" href="#t359">359</a></span><span class="t">                    <span class="nam">self</span><span class="op">.</span><span class="nam">strategy_allocations</span><span class="op">[</span><span class="nam">strategy_id</span><span class="op">]</span><span class="op">.</span><span class="nam">target_allocation</span> <span class="op">=</span> <span class="nam">target_allocation</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t360" href="#t360">360</a></span><span class="t">                    <span class="nam">self</span><span class="op">.</span><span class="nam">strategy_allocations</span><span class="op">[</span><span class="nam">strategy_id</span><span class="op">]</span><span class="op">.</span><span class="nam">current_allocation</span> <span class="op">=</span> <span class="nam">target_allocation</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t361" href="#t361">361</a></span><span class="t">                    <span class="nam">self</span><span class="op">.</span><span class="nam">strategy_allocations</span><span class="op">[</span><span class="nam">strategy_id</span><span class="op">]</span><span class="op">.</span><span class="nam">allocated_capital</span> <span class="op">=</span> <span class="nam">self</span><span class="op">.</span><span class="nam">total_capital</span> <span class="op">*</span> <span class="nam">target_allocation</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t362" href="#t362">362</a></span><span class="t">                    <span class="nam">self</span><span class="op">.</span><span class="nam">strategy_allocations</span><span class="op">[</span><span class="nam">strategy_id</span><span class="op">]</span><span class="op">.</span><span class="nam">last_updated</span> <span class="op">=</span> <span class="nam">datetime</span><span class="op">.</span><span class="nam">now</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t363" href="#t363">363</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t364" href="#t364">364</a></span><span class="t">                    <span class="nam">rebalance_actions</span><span class="op">.</span><span class="nam">append</span><span class="op">(</span><span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t365" href="#t365">365</a></span><span class="t">                        <span class="str">'strategy_id'</span><span class="op">:</span> <span class="nam">strategy_id</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t366" href="#t366">366</a></span><span class="t">                        <span class="str">'old_allocation'</span><span class="op">:</span> <span class="nam">current_allocation</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t367" href="#t367">367</a></span><span class="t">                        <span class="str">'new_allocation'</span><span class="op">:</span> <span class="nam">target_allocation</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t368" href="#t368">368</a></span><span class="t">                        <span class="str">'change'</span><span class="op">:</span> <span class="nam">change</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t369" href="#t369">369</a></span><span class="t">                        <span class="str">'new_capital'</span><span class="op">:</span> <span class="nam">self</span><span class="op">.</span><span class="nam">total_capital</span> <span class="op">*</span> <span class="nam">target_allocation</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t370" href="#t370">370</a></span><span class="t">                    <span class="op">}</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t371" href="#t371">371</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t372" href="#t372">372</a></span><span class="t">                    <span class="nam">total_change</span> <span class="op">+=</span> <span class="nam">abs</span><span class="op">(</span><span class="nam">change</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t373" href="#t373">373</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t374" href="#t374">374</a></span><span class="t">        <span class="nam">self</span><span class="op">.</span><span class="nam">last_rebalance</span> <span class="op">=</span> <span class="nam">datetime</span><span class="op">.</span><span class="nam">now</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t375" href="#t375">375</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t376" href="#t376">376</a></span><span class="t">        <span class="com"># &#30332;&#24067;&#37325;&#26032;&#24179;&#34913;&#20107;&#20214;</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t377" href="#t377">377</a></span><span class="t">        <span class="key">if</span> <span class="nam">rebalance_actions</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t378" href="#t378">378</a></span><span class="t">            <span class="nam">publish_event</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t379" href="#t379">379</a></span><span class="t">                <span class="nam">EventType</span><span class="op">.</span><span class="nam">PORTFOLIO_REBALANCE</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t380" href="#t380">380</a></span><span class="t">                <span class="str">"portfolio_manager"</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t381" href="#t381">381</a></span><span class="t">                <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t382" href="#t382">382</a></span><span class="t">                    <span class="str">"actions"</span><span class="op">:</span> <span class="nam">rebalance_actions</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t383" href="#t383">383</a></span><span class="t">                    <span class="str">"total_change"</span><span class="op">:</span> <span class="nam">total_change</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t384" href="#t384">384</a></span><span class="t">                    <span class="str">"timestamp"</span><span class="op">:</span> <span class="nam">self</span><span class="op">.</span><span class="nam">last_rebalance</span><span class="op">.</span><span class="nam">isoformat</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t385" href="#t385">385</a></span><span class="t">                <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t386" href="#t386">386</a></span><span class="t">            <span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t387" href="#t387">387</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t388" href="#t388">388</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#25237;&#36039;&#32068;&#21512;&#37325;&#26032;&#24179;&#34913;&#23436;&#25104;: </span><span class="op">{</span><span class="nam">len</span><span class="op">(</span><span class="nam">rebalance_actions</span><span class="op">)</span><span class="op">}</span><span class="fst"> &#20491;&#31574;&#30053;&#35519;&#25972;</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t389" href="#t389">389</a></span><span class="t">        <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t390" href="#t390">390</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">info</span><span class="op">(</span><span class="str">"&#25237;&#36039;&#32068;&#21512;&#28961;&#38656;&#37325;&#26032;&#24179;&#34913;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t391" href="#t391">391</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t392" href="#t392">392</a></span><span class="t">        <span class="key">return</span> <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t393" href="#t393">393</a></span><span class="t">            <span class="str">'rebalanced'</span><span class="op">:</span> <span class="nam">len</span><span class="op">(</span><span class="nam">rebalance_actions</span><span class="op">)</span> <span class="op">></span> <span class="num">0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t394" href="#t394">394</a></span><span class="t">            <span class="str">'actions'</span><span class="op">:</span> <span class="nam">rebalance_actions</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t395" href="#t395">395</a></span><span class="t">            <span class="str">'total_change'</span><span class="op">:</span> <span class="nam">total_change</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t396" href="#t396">396</a></span><span class="t">        <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t397" href="#t397">397</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t398" href="#t398">398</a></span><span class="t">    <span class="key">def</span> <span class="nam">_on_strategy_health_changed</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">event</span><span class="op">:</span> <span class="nam">Event</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t399" href="#t399">399</a></span><span class="t">        <span class="str">"""&#34389;&#29702;&#31574;&#30053;&#20581;&#24247;&#35722;&#21270;&#20107;&#20214;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t400" href="#t400">400</a></span><span class="t">        <span class="nam">strategy_id</span> <span class="op">=</span> <span class="nam">event</span><span class="op">.</span><span class="nam">data</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">'strategy_id'</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t401" href="#t401">401</a></span><span class="t">        <span class="nam">health_score</span> <span class="op">=</span> <span class="nam">event</span><span class="op">.</span><span class="nam">data</span><span class="op">.</span><span class="nam">get</span><span class="op">(</span><span class="str">'health_score'</span><span class="op">,</span> <span class="num">0.5</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t402" href="#t402">402</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t403" href="#t403">403</a></span><span class="t">        <span class="key">if</span> <span class="nam">strategy_id</span> <span class="key">in</span> <span class="nam">self</span><span class="op">.</span><span class="nam">strategy_allocations</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t404" href="#t404">404</a></span><span class="t">            <span class="nam">self</span><span class="op">.</span><span class="nam">strategy_allocations</span><span class="op">[</span><span class="nam">strategy_id</span><span class="op">]</span><span class="op">.</span><span class="nam">health_score</span> <span class="op">=</span> <span class="nam">health_score</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t405" href="#t405">405</a></span><span class="t">            <span class="nam">logger</span><span class="op">.</span><span class="nam">debug</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#31574;&#30053;&#20581;&#24247;&#20998;&#25976;&#26356;&#26032;: </span><span class="op">{</span><span class="nam">strategy_id</span><span class="op">}</span><span class="fst"> = </span><span class="op">{</span><span class="nam">health_score</span><span class="op">:</span><span class="fst">.2f</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t406" href="#t406">406</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t407" href="#t407">407</a></span><span class="t">    <span class="key">def</span> <span class="nam">_on_order_filled</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">event</span><span class="op">:</span> <span class="nam">Event</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t408" href="#t408">408</a></span><span class="t">        <span class="str">"""&#34389;&#29702;&#35330;&#21934;&#25104;&#20132;&#20107;&#20214;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t409" href="#t409">409</a></span><span class="t">        <span class="key">pass</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t410" href="#t410">410</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t411" href="#t411">411</a></span><span class="t">    <span class="key">def</span> <span class="nam">_on_risk_limit_exceeded</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">event</span><span class="op">:</span> <span class="nam">Event</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t412" href="#t412">412</a></span><span class="t">        <span class="str">"""&#34389;&#29702;&#39080;&#38570;&#38480;&#21046;&#36229;&#20986;&#20107;&#20214;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t413" href="#t413">413</a></span><span class="t">        <span class="nam">logger</span><span class="op">.</span><span class="nam">warning</span><span class="op">(</span><span class="str">"&#27298;&#28204;&#21040;&#39080;&#38570;&#38480;&#21046;&#36229;&#20986;&#65292;&#32771;&#24942;&#32202;&#24613;&#37325;&#26032;&#24179;&#34913;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t414" href="#t414">414</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t415" href="#t415">415</a></span><span class="t">    <span class="key">def</span> <span class="nam">get_portfolio_status</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span> <span class="op">-></span> <span class="nam">Dict</span><span class="op">[</span><span class="nam">str</span><span class="op">,</span> <span class="nam">Any</span><span class="op">]</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t416" href="#t416">416</a></span><span class="t">        <span class="str">"""&#29554;&#21462;&#25237;&#36039;&#32068;&#21512;&#29376;&#24907;"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t417" href="#t417">417</a></span><span class="t">        <span class="nam">total_allocated</span> <span class="op">=</span> <span class="nam">sum</span><span class="op">(</span><span class="nam">alloc</span><span class="op">.</span><span class="nam">allocated_capital</span> <span class="key">for</span> <span class="nam">alloc</span> <span class="key">in</span> <span class="nam">self</span><span class="op">.</span><span class="nam">strategy_allocations</span><span class="op">.</span><span class="nam">values</span><span class="op">(</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t418" href="#t418">418</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t419" href="#t419">419</a></span><span class="t">        <span class="key">return</span> <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t420" href="#t420">420</a></span><span class="t">            <span class="str">'total_capital'</span><span class="op">:</span> <span class="nam">self</span><span class="op">.</span><span class="nam">total_capital</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t421" href="#t421">421</a></span><span class="t">            <span class="str">'total_allocated'</span><span class="op">:</span> <span class="nam">total_allocated</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t422" href="#t422">422</a></span><span class="t">            <span class="str">'allocation_ratio'</span><span class="op">:</span> <span class="nam">total_allocated</span> <span class="op">/</span> <span class="nam">self</span><span class="op">.</span><span class="nam">total_capital</span> <span class="key">if</span> <span class="nam">self</span><span class="op">.</span><span class="nam">total_capital</span> <span class="op">></span> <span class="num">0</span> <span class="key">else</span> <span class="num">0</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t423" href="#t423">423</a></span><span class="t">            <span class="str">'num_strategies'</span><span class="op">:</span> <span class="nam">len</span><span class="op">(</span><span class="nam">self</span><span class="op">.</span><span class="nam">strategy_allocations</span><span class="op">)</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t424" href="#t424">424</a></span><span class="t">            <span class="str">'last_rebalance'</span><span class="op">:</span> <span class="nam">self</span><span class="op">.</span><span class="nam">last_rebalance</span><span class="op">.</span><span class="nam">isoformat</span><span class="op">(</span><span class="op">)</span> <span class="key">if</span> <span class="nam">self</span><span class="op">.</span><span class="nam">last_rebalance</span> <span class="key">else</span> <span class="key">None</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t425" href="#t425">425</a></span><span class="t">            <span class="str">'emergency_stop'</span><span class="op">:</span> <span class="nam">self</span><span class="op">.</span><span class="nam">emergency_stop_triggered</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t426" href="#t426">426</a></span><span class="t">            <span class="str">'strategy_allocations'</span><span class="op">:</span> <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t427" href="#t427">427</a></span><span class="t">                <span class="nam">sid</span><span class="op">:</span> <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t428" href="#t428">428</a></span><span class="t">                    <span class="str">'allocated_capital'</span><span class="op">:</span> <span class="nam">alloc</span><span class="op">.</span><span class="nam">allocated_capital</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t429" href="#t429">429</a></span><span class="t">                    <span class="str">'target_allocation'</span><span class="op">:</span> <span class="nam">alloc</span><span class="op">.</span><span class="nam">target_allocation</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t430" href="#t430">430</a></span><span class="t">                    <span class="str">'health_score'</span><span class="op">:</span> <span class="nam">alloc</span><span class="op">.</span><span class="nam">health_score</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t431" href="#t431">431</a></span><span class="t">                    <span class="str">'correlation_penalty'</span><span class="op">:</span> <span class="nam">alloc</span><span class="op">.</span><span class="nam">correlation_penalty</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t432" href="#t432">432</a></span><span class="t">                <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t433" href="#t433">433</a></span><span class="t">                <span class="key">for</span> <span class="nam">sid</span><span class="op">,</span> <span class="nam">alloc</span> <span class="key">in</span> <span class="nam">self</span><span class="op">.</span><span class="nam">strategy_allocations</span><span class="op">.</span><span class="nam">items</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t434" href="#t434">434</a></span><span class="t">            <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t435" href="#t435">435</a></span><span class="t">        <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t436" href="#t436">436</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t437" href="#t437">437</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t438" href="#t438">438</a></span><span class="t"><span class="key">if</span> <span class="nam">__name__</span> <span class="op">==</span> <span class="str">"__main__"</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t439" href="#t439">439</a></span><span class="t">    <span class="com"># &#28204;&#35430;&#32068;&#21512;&#31649;&#29702;&#22120;</span>&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t440" href="#t440">440</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="str">"&#129514; &#32068;&#21512;&#31649;&#29702;&#22120;&#28204;&#35430;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t441" href="#t441">441</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t442" href="#t442">442</a></span><span class="t">    <span class="com"># &#21109;&#24314;&#27169;&#25836;&#31574;&#30053;</span>&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t443" href="#t443">443</a></span><span class="t">    <span class="key">from</span> <span class="nam">strategy_framework</span> <span class="key">import</span> <span class="nam">BaseStrategy</span><span class="op">,</span> <span class="nam">StrategyType</span><span class="op">,</span> <span class="nam">TradingSignal</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t444" href="#t444">444</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t445" href="#t445">445</a></span><span class="t">    <span class="key">class</span> <span class="nam">MockStrategy</span><span class="op">(</span><span class="nam">BaseStrategy</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t446" href="#t446">446</a></span><span class="t">        <span class="key">def</span> <span class="nam">get_strategy_type</span><span class="op">(</span><span class="nam">self</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t447" href="#t447">447</a></span><span class="t">            <span class="key">return</span> <span class="nam">StrategyType</span><span class="op">.</span><span class="nam">PAIRS_TRADING</span>&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t448" href="#t448">448</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t449" href="#t449">449</a></span><span class="t">        <span class="key">def</span> <span class="nam">analyze_market</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">market_data</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t450" href="#t450">450</a></span><span class="t">            <span class="key">return</span> <span class="op">[</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t451" href="#t451">451</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t452" href="#t452">452</a></span><span class="t">        <span class="key">def</span> <span class="nam">calculate_position_size</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">signal</span><span class="op">,</span> <span class="nam">capital</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t453" href="#t453">453</a></span><span class="t">            <span class="key">return</span> <span class="op">{</span><span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t454" href="#t454">454</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t455" href="#t455">455</a></span><span class="t">        <span class="key">def</span> <span class="nam">validate_signal</span><span class="op">(</span><span class="nam">self</span><span class="op">,</span> <span class="nam">signal</span><span class="op">,</span> <span class="nam">positions</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t456" href="#t456">456</a></span><span class="t">            <span class="key">return</span> <span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t457" href="#t457">457</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t458" href="#t458">458</a></span><span class="t">    <span class="com"># &#21109;&#24314;&#32068;&#21512;&#31649;&#29702;&#22120;</span>&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t459" href="#t459">459</a></span><span class="t">    <span class="nam">portfolio_manager</span> <span class="op">=</span> <span class="nam">PortfolioManager</span><span class="op">(</span><span class="num">100000</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t460" href="#t460">460</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t461" href="#t461">461</a></span><span class="t">    <span class="com"># &#28155;&#21152;&#31574;&#30053;</span>&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t462" href="#t462">462</a></span><span class="t">    <span class="nam">strategy1</span> <span class="op">=</span> <span class="nam">MockStrategy</span><span class="op">(</span><span class="str">"test_strategy_1"</span><span class="op">,</span> <span class="op">{</span><span class="op">}</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t463" href="#t463">463</a></span><span class="t">    <span class="nam">strategy2</span> <span class="op">=</span> <span class="nam">MockStrategy</span><span class="op">(</span><span class="str">"test_strategy_2"</span><span class="op">,</span> <span class="op">{</span><span class="op">}</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t464" href="#t464">464</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t465" href="#t465">465</a></span><span class="t">    <span class="nam">portfolio_manager</span><span class="op">.</span><span class="nam">add_strategy</span><span class="op">(</span><span class="nam">strategy1</span><span class="op">,</span> <span class="num">0.4</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t466" href="#t466">466</a></span><span class="t">    <span class="nam">portfolio_manager</span><span class="op">.</span><span class="nam">add_strategy</span><span class="op">(</span><span class="nam">strategy2</span><span class="op">,</span> <span class="num">0.6</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t467" href="#t467">467</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t468" href="#t468">468</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="str">"&#9989; &#32068;&#21512;&#31649;&#29702;&#22120;&#21109;&#24314;&#25104;&#21151;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t469" href="#t469">469</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#9989; &#25237;&#36039;&#32068;&#21512;&#29376;&#24907;: </span><span class="op">{</span><span class="nam">portfolio_manager</span><span class="op">.</span><span class="nam">get_portfolio_status</span><span class="op">(</span><span class="op">)</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t470" href="#t470">470</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t471" href="#t471">471</a></span><span class="t">    <span class="com"># &#28204;&#35430;&#37325;&#26032;&#24179;&#34913;</span>&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t472" href="#t472">472</a></span><span class="t">    <span class="nam">result</span> <span class="op">=</span> <span class="nam">portfolio_manager</span><span class="op">.</span><span class="nam">rebalance_portfolio</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t473" href="#t473">473</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="fst">f"</span><span class="fst">&#9989; &#37325;&#26032;&#24179;&#34913;&#32080;&#26524;: </span><span class="op">{</span><span class="nam">result</span><span class="op">}</span><span class="fst">"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t474" href="#t474">474</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t475" href="#t475">475</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="str">"&#9989; &#32068;&#21512;&#31649;&#29702;&#22120;&#28204;&#35430;&#23436;&#25104;"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="portfolio_backtester_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a class="nav" href="production_readiness_check_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.0">coverage.py v7.8.0</a>,
            created at 2025-07-06 11:50 +0800
        </p>
    </div>
</footer>
</body>
</html>
