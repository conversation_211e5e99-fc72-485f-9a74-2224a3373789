<?xml version="1.0" encoding="utf-8"?><testsuites><testsuite name="pytest" errors="4" failures="0" skipped="0" tests="4" time="3.945" timestamp="2025-07-06T12:05:11.824453+08:00" hostname="mARTindeMacBook-Pro.local"><testcase classname="" name="tests.test_adaptive_system" time="0.000"><error message="collection failure">ImportError while importing test module '/Users/<USER>/Documents/Python/Pair_trading_v2/tests/test_adaptive_system.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/__init__.py:88: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
tests/test_adaptive_system.py:17: in &lt;module&gt;
    from adaptive_pair_selector import AdaptivePairSelector
E   ModuleNotFoundError: No module named 'adaptive_pair_selector'</error></testcase><testcase classname="" name="tests.test_complete_system" time="0.000"><error message="collection failure">ImportError while importing test module '/Users/<USER>/Documents/Python/Pair_trading_v2/tests/test_complete_system.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/__init__.py:88: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
tests/test_complete_system.py:22: in &lt;module&gt;
    from research_environment.research_toolkit import get_research_toolkit
research_environment/research_toolkit.py:18: in &lt;module&gt;
    from portfolio_backtester import PortfolioBacktester
src/portfolio_backtester.py:16: in &lt;module&gt;
    from adaptive_pair_selector import AdaptivePairSelector
E   ModuleNotFoundError: No module named 'adaptive_pair_selector'</error></testcase><testcase classname="" name="tests.test_pair_trading_bot" time="0.000"><error message="collection failure">ImportError while importing test module '/Users/<USER>/Documents/Python/Pair_trading_v2/tests/test_pair_trading_bot.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/__init__.py:88: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
tests/test_pair_trading_bot.py:16: in &lt;module&gt;
    from pair_trading_bot import PairTradingBot, TradingState, SignalState
src/pair_trading_bot.py:7: in &lt;module&gt;
    from trading_executor import TradingExecutor
E   ModuleNotFoundError: No module named 'trading_executor'</error></testcase><testcase classname="" name="tests.test_refactored_system" time="0.000"><error message="collection failure">ImportError while importing test module '/Users/<USER>/Documents/Python/Pair_trading_v2/tests/test_refactored_system.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/__init__.py:88: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
tests/test_refactored_system.py:14: in &lt;module&gt;
    from safe_trading_executor import SafeTradingExecutor
E   ModuleNotFoundError: No module named 'safe_trading_executor'</error></testcase></testsuite></testsuites>