#!/usr/bin/env python3
"""
事件驅動系統 - 實現低延遲的事件驅動架構
Event-Driven System - Low-latency event-driven architecture
"""

import asyncio
import json
import threading
import time
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from queue import Empty, Queue
from typing import Any, Callable, Dict, List, Optional

import redis

from logging_config import get_logger

logger = get_logger(__name__)


class EventType(Enum):
    """事件類型枚舉"""

    MARKET_DATA = "market_data"
    TRADE_SIGNAL = "trade_signal"
    ORDER_EXECUTED = "order_executed"
    POSITION_UPDATED = "position_updated"
    RISK_ALERT = "risk_alert"
    SYSTEM_STATUS = "system_status"
    STRATEGY_HEALTH = "strategy_health"
    PORTFOLIO_REBALANCE = "portfolio_rebalance"


@dataclass
class Event:
    """事件數據結構"""

    event_type: EventType
    timestamp: datetime
    source: str
    data: Dict[str, Any]
    priority: int = 5  # 1-10, 1最高優先級
    correlation_id: Optional[str] = None


class EventHandler:
    """事件處理器基類"""

    def __init__(self, name: str):
        self.name = name
        self.processed_count = 0
        self.error_count = 0
        self.last_processed = None

    async def handle(self, event: Event) -> bool:
        """處理事件，返回是否成功"""
        try:
            start_time = time.time()
            result = await self._process_event(event)

            self.processed_count += 1
            self.last_processed = datetime.now()

            processing_time = (time.time() - start_time) * 1000
            logger.debug(
                f"事件處理器 {self.name} 處理事件 {event.event_type.value} " f"耗時 {processing_time:.2f}ms"
            )

            return result

        except Exception as e:
            self.error_count += 1
            logger.error(f"事件處理器 {self.name} 處理事件失敗: {e}")
            return False

    async def _process_event(self, event: Event) -> bool:
        """子類需要實現的事件處理邏輯"""
        raise NotImplementedError


class EventBus:
    """事件總線 - 核心事件分發系統"""

    def __init__(self, use_redis: bool = False, redis_url: str = "redis://localhost:6379"):
        self.handlers: Dict[EventType, List[EventHandler]] = {}
        self.event_queue = asyncio.Queue(maxsize=10000)
        self.priority_queue = Queue(maxsize=1000)

        # Redis 支持（用於分佈式部署）
        self.use_redis = use_redis
        self.redis_client = None
        if use_redis:
            try:
                self.redis_client = redis.from_url(redis_url)
                self.redis_client.ping()
                logger.info("Redis 事件總線已連接")
            except Exception as e:
                logger.warning(f"Redis 連接失敗，使用本地事件總線: {e}")
                self.use_redis = False

        # 統計信息
        self.total_events = 0
        self.processed_events = 0
        self.failed_events = 0
        self.start_time = datetime.now()

        # 控制標誌
        self.running = False
        self.workers = []

        logger.info("EventBus 初始化完成")

    def subscribe(self, event_type: EventType, handler: EventHandler):
        """訂閱事件類型"""
        if event_type not in self.handlers:
            self.handlers[event_type] = []

        self.handlers[event_type].append(handler)
        logger.info(f"事件處理器 {handler.name} 已訂閱 {event_type.value}")

    def unsubscribe(self, event_type: EventType, handler: EventHandler):
        """取消訂閱"""
        if event_type in self.handlers:
            if handler in self.handlers[event_type]:
                self.handlers[event_type].remove(handler)
                logger.info(f"事件處理器 {handler.name} 已取消訂閱 {event_type.value}")

    async def publish(self, event: Event):
        """發布事件"""
        try:
            self.total_events += 1

            # 高優先級事件直接處理
            if event.priority <= 2:
                await self._process_event_immediately(event)
            else:
                # 普通事件加入隊列
                if self.use_redis:
                    await self._publish_to_redis(event)
                else:
                    await self.event_queue.put(event)

            logger.debug(f"事件已發布: {event.event_type.value} (優先級: {event.priority})")

        except Exception as e:
            logger.error(f"發布事件失敗: {e}")

    async def _publish_to_redis(self, event: Event):
        """發布事件到 Redis"""
        try:
            event_data = {
                "event_type": event.event_type.value,
                "timestamp": event.timestamp.isoformat(),
                "source": event.source,
                "data": event.data,
                "priority": event.priority,
                "correlation_id": event.correlation_id,
            }

            channel = f"trading_events:{event.event_type.value}"
            self.redis_client.publish(channel, json.dumps(event_data))

        except Exception as e:
            logger.error(f"Redis 事件發布失敗: {e}")
            # 降級到本地隊列
            await self.event_queue.put(event)

    async def _process_event_immediately(self, event: Event):
        """立即處理高優先級事件"""
        handlers = self.handlers.get(event.event_type, [])

        if not handlers:
            return

        # 並行處理所有處理器
        tasks = []
        for handler in handlers:
            task = asyncio.create_task(handler.handle(event))
            tasks.append(task)

        results = await asyncio.gather(*tasks, return_exceptions=True)

        success_count = sum(1 for r in results if r is True)
        self.processed_events += success_count
        self.failed_events += len(results) - success_count

    async def start(self, num_workers: int = 4):
        """啟動事件總線"""
        if self.running:
            return

        self.running = True
        logger.info(f"啟動事件總線，工作線程數: {num_workers}")

        # 啟動事件處理工作線程
        for i in range(num_workers):
            worker = asyncio.create_task(self._event_worker(f"worker-{i}"))
            self.workers.append(worker)

        # 如果使用 Redis，啟動訂閱者
        if self.use_redis:
            subscriber = asyncio.create_task(self._redis_subscriber())
            self.workers.append(subscriber)

    async def stop(self):
        """停止事件總線"""
        if not self.running:
            return

        self.running = False
        logger.info("正在停止事件總線...")

        # 取消所有工作線程
        for worker in self.workers:
            worker.cancel()

        # 等待工作線程結束
        await asyncio.gather(*self.workers, return_exceptions=True)
        self.workers.clear()

        logger.info("事件總線已停止")

    async def _event_worker(self, worker_name: str):
        """事件處理工作線程"""
        logger.info(f"事件工作線程 {worker_name} 已啟動")

        while self.running:
            try:
                # 等待事件，設置超時避免阻塞
                event = await asyncio.wait_for(self.event_queue.get(), timeout=1.0)

                # 處理事件
                await self._process_event(event)

                # 標記任務完成
                self.event_queue.task_done()

            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.error(f"事件工作線程 {worker_name} 錯誤: {e}")

        logger.info(f"事件工作線程 {worker_name} 已停止")

    async def _process_event(self, event: Event):
        """處理單個事件"""
        handlers = self.handlers.get(event.event_type, [])

        if not handlers:
            logger.debug(f"沒有處理器訂閱事件類型: {event.event_type.value}")
            return

        # 並行處理所有處理器
        tasks = []
        for handler in handlers:
            task = asyncio.create_task(handler.handle(event))
            tasks.append(task)

        try:
            results = await asyncio.gather(*tasks, return_exceptions=True)

            success_count = sum(1 for r in results if r is True)
            self.processed_events += success_count
            self.failed_events += len(results) - success_count

        except Exception as e:
            logger.error(f"處理事件時發生錯誤: {e}")
            self.failed_events += len(handlers)

    async def _redis_subscriber(self):
        """Redis 事件訂閱者"""
        logger.info("Redis 事件訂閱者已啟動")

        try:
            pubsub = self.redis_client.pubsub()

            # 訂閱所有事件類型
            for event_type in EventType:
                channel = f"trading_events:{event_type.value}"
                pubsub.subscribe(channel)

            while self.running:
                try:
                    message = pubsub.get_message(timeout=1.0)
                    if message and message["type"] == "message":
                        await self._handle_redis_message(message)

                except Exception as e:
                    logger.error(f"Redis 訂閱者錯誤: {e}")

        except Exception as e:
            logger.error(f"Redis 訂閱者啟動失敗: {e}")
        finally:
            logger.info("Redis 事件訂閱者已停止")

    async def _handle_redis_message(self, message):
        """處理 Redis 消息"""
        try:
            event_data = json.loads(message["data"])

            event = Event(
                event_type=EventType(event_data["event_type"]),
                timestamp=datetime.fromisoformat(event_data["timestamp"]),
                source=event_data["source"],
                data=event_data["data"],
                priority=event_data["priority"],
                correlation_id=event_data.get("correlation_id"),
            )

            await self._process_event(event)

        except Exception as e:
            logger.error(f"處理 Redis 消息失敗: {e}")

    def get_statistics(self) -> Dict:
        """獲取事件總線統計信息"""
        uptime = (datetime.now() - self.start_time).total_seconds()

        return {
            "total_events": self.total_events,
            "processed_events": self.processed_events,
            "failed_events": self.failed_events,
            "success_rate": self.processed_events / max(self.total_events, 1),
            "events_per_second": self.total_events / max(uptime, 1),
            "queue_size": self.event_queue.qsize(),
            "active_workers": len(self.workers),
            "uptime_seconds": uptime,
            "handler_stats": self._get_handler_stats(),
        }

    def _get_handler_stats(self) -> Dict:
        """獲取處理器統計信息"""
        stats = {}

        for event_type, handlers in self.handlers.items():
            stats[event_type.value] = []
            for handler in handlers:
                stats[event_type.value].append(
                    {
                        "name": handler.name,
                        "processed_count": handler.processed_count,
                        "error_count": handler.error_count,
                        "last_processed": handler.last_processed.isoformat()
                        if handler.last_processed
                        else None,
                    }
                )

        return stats


# 全局事件總線實例
_event_bus = None


def get_event_bus() -> EventBus:
    """獲取全局事件總線實例"""
    global _event_bus
    if _event_bus is None:
        _event_bus = EventBus()
    return _event_bus


# 便利函數
async def publish_event(
    event_type: EventType,
    source: str,
    data: Dict[str, Any],
    priority: int = 5,
    correlation_id: Optional[str] = None,
):
    """發布事件的便利函數"""
    event = Event(
        event_type=event_type,
        timestamp=datetime.now(),
        source=source,
        data=data,
        priority=priority,
        correlation_id=correlation_id,
    )

    event_bus = get_event_bus()
    await event_bus.publish(event)


if __name__ == "__main__":
    # 測試事件總線
    import asyncio

    class TestHandler(EventHandler):
        pass

        async def _process_event(self, event: Event) -> bool:
            print(f"處理事件: {event.event_type.value} from {event.source}")
            await asyncio.sleep(0.1)  # 模擬處理時間
            return True

    async def test_event_bus():
        # 創建事件總線
        bus = EventBus()

        # 創建處理器
        handler = TestHandler("test_handler")
        bus.subscribe(EventType.MARKET_DATA, handler)

        # 啟動事件總線
        await bus.start(num_workers=2)

        # 發布測試事件
        for i in range(5):
            await publish_event(
                EventType.MARKET_DATA, "test_source", {"price": 50000 + i, "volume": 1000}
            )

        # 等待處理完成
        await asyncio.sleep(2)

        # 獲取統計信息
        stats = bus.get_statistics()
        print(f"統計信息: {stats}")

        # 停止事件總線
        await bus.stop()

    # 運行測試
    asyncio.run(test_event_bus())
