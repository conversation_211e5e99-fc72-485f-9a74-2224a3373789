#!/usr/bin/env python3
"""
配置調試測試
"""

import os
from pathlib import Path
from config_validation import ComprehensiveConfig

def test_env_loading():
    """測試環境變量載入"""
    print("=== 環境變量調試 ===")
    
    # 檢查.env文件是否存在
    env_file = Path(".env")
    print(f".env文件存在: {env_file.exists()}")
    
    if env_file.exists():
        print(f".env文件內容:")
        with open(env_file, 'r') as f:
            for i, line in enumerate(f, 1):
                if line.strip() and not line.startswith('#'):
                    print(f"  {i}: {line.strip()}")
    
    # 檢查環境變量
    print(f"\n當前環境變量:")
    trading_vars = [k for k in os.environ.keys() if k.startswith('TRADING_')]
    for var in trading_vars:
        print(f"  {var} = {os.environ[var]}")
    
    if not trading_vars:
        print("  沒有找到TRADING_開頭的環境變量")
    
    # 手動載入.env文件
    if env_file.exists():
        print(f"\n手動載入.env文件...")
        with open(env_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key] = value
                    if key.startswith('TRADING_'):
                        print(f"  設置 {key} = {value}")
    
    # 再次檢查環境變量
    print(f"\n載入後的TRADING_環境變量:")
    trading_vars = [k for k in os.environ.keys() if k.startswith('TRADING_')]
    for var in trading_vars:
        print(f"  {var} = {os.environ[var]}")
    
    # 測試Pydantic配置
    print(f"\n測試Pydantic配置載入...")
    try:
        config = ComprehensiveConfig()
        print(f"API Key: {config.trading.api_key}")
        print(f"Secret: {config.trading.secret}")
        print(f"Exchange: {config.trading.exchange}")
        print(f"Sandbox: {config.trading.sandbox}")
        
        assert True  # 配置載入成功
    except Exception as e:
        print(f"配置載入失敗: {e}")
        assert False, f"配置載入失敗: {e}"

if __name__ == "__main__":
    test_env_loading()
