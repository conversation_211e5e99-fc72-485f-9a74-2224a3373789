# 敏感信息和配置文件
.env
.env.local
.env.production
.env.staging
*.key
*.pem
*.p12
*.pfx

# 數據庫文件
*.db
*.sqlite
*.sqlite3
data/*.db
test_platform.db

# 日誌文件
*.log
logs/
logs/**/*
*.log.*

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.tmp
*.temp
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# 項目特定文件
# 測試結果
test_results/
records/
pair_selection_results/

# 數據文件
data/states/
data/*.csv
data/*.json
!data/sample_data.csv

# 備份文件
*.bak
*.backup
*.old

# 臨時文件
tmp/
temp/
*.tmp

# 配置文件（保留示例）
config_local.json
config_production.json
!config.json
!.env.example

# 部署相關
.docker/
docker-compose.override.yml
kubernetes/secrets/

# 監控和指標
prometheus_data/
grafana_data/

# 機器學習模型
models/
*.pkl
*.joblib
*.h5
*.onnx

# 研究和實驗
experiments/
notebooks/checkpoints/
research_data/

# 性能分析
*.prof
*.pstats

# 安全掃描結果
security_reports/
vulnerability_reports/
security_report.json

# Pre-commit
.pre-commit-cache/

# Test reports
test_reports/
