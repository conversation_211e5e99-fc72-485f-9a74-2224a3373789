# 跨平台使用指南 / Cross-Platform Usage Guide

本指南幫助您在不同操作系統上順利運行配對交易機器人。

This guide helps you run the pair trading bot smoothly across different operating systems.

## 🖥️ 支持的平台 / Supported Platforms

- ✅ **Windows 10/11** (簡體中文/English)
- ✅ **macOS 10.14+** (繁體中文/English)  
- ✅ **Linux Ubuntu 18.04+** (UTF-8)

## 🚀 快速開始 / Quick Start

### Windows 用戶

1. **自動安裝**
```cmd
# 下載項目後，雙擊運行
setup.py

# 或在命令提示符中運行
python setup.py
```

2. **啟動程序**
```cmd
# 雙擊運行啟動腳本
start.bat

# 或在命令提示符中運行
start.bat
```

### macOS 用戶

1. **自動安裝**
```bash
# 在終端中運行
python3 setup.py
```

2. **啟動程序**
```bash
# 在終端中運行
./start.sh

# 或者
bash start.sh
```

### Linux 用戶

1. **自動安裝**
```bash
# 在終端中運行
python3 setup.py
```

2. **啟動程序**
```bash
# 在終端中運行
./start.sh

# 或者
bash start.sh
```

## 🔧 環境配置 / Environment Setup

### 1. Python 環境

**Windows:**
- 從 [python.org](https://python.org) 下載 Python 3.8+
- 安裝時勾選 "Add Python to PATH"

**macOS:**
```bash
# 使用 Homebrew 安裝
brew install python3

# 或使用官方安裝包
# 從 python.org 下載
```

**Linux (Ubuntu/Debian):**
```bash
sudo apt update
sudo apt install python3 python3-pip
```

### 2. API 密鑰配置

**所有平台通用步驟:**

1. 複製環境變量模板：
```bash
# Windows
copy .env.template .env

# macOS/Linux
cp .env.template .env
```

2. 編輯 `.env` 文件：
```bash
# Windows
notepad .env

# macOS
open -e .env

# Linux
nano .env
```

3. 填入您的 API 密鑰：
```env
EXCHANGE_API_KEY=your_actual_api_key_here
EXCHANGE_SECRET=your_actual_secret_here
```

## 📁 目錄結構 / Directory Structure

```
Pair_trading_v2/
├── main.py                    # 主程序入口
├── config.json               # 配置文件
├── .env                      # 環境變量 (需要創建)
├── .env.template             # 環境變量模板
├── setup.py                  # 安裝腳本
├── start.bat                 # Windows 啟動腳本
├── start.sh                  # macOS/Linux 啟動腳本
├── requirements.txt          # Python 依賴
├── data/                     # 數據目錄
├── logs/                     # 日誌目錄
├── records/                  # 交易記錄
└── backtest_results/         # 回測結果
```

## 🛠️ 常見問題 / Troubleshooting

### Windows 常見問題

**問題 1: "python 不是內部或外部命令"**
```cmd
# 解決方案：重新安裝 Python 並勾選 "Add to PATH"
# 或手動添加 Python 到系統 PATH
```

**問題 2: 中文亂碼**
```cmd
# 在命令提示符中設置 UTF-8 編碼
chcp 65001
```

**問題 3: 權限問題**
```cmd
# 以管理員身份運行命令提示符
# 右鍵點擊 "命令提示符" -> "以管理員身份運行"
```

### macOS 常見問題

**問題 1: "permission denied"**
```bash
# 給腳本添加執行權限
chmod +x start.sh
```

**問題 2: Python 版本問題**
```bash
# 檢查 Python 版本
python3 --version

# 如果版本過低，使用 Homebrew 更新
brew upgrade python3
```

**問題 3: SSL 證書問題**
```bash
# 更新證書
/Applications/Python\ 3.x/Install\ Certificates.command
```

### Linux 常見問題

**問題 1: 缺少依賴**
```bash
# Ubuntu/Debian
sudo apt install python3-dev python3-pip build-essential

# CentOS/RHEL
sudo yum install python3-devel python3-pip gcc
```

**問題 2: 權限問題**
```bash
# 給腳本添加執行權限
chmod +x start.sh

# 如果需要 sudo 權限安裝包
sudo pip3 install -r requirements.txt
```

## 🔒 安全建議 / Security Recommendations

### API 密鑰安全

1. **使用測試環境**
```env
EXCHANGE_SANDBOX=true
```

2. **限制 API 權限**
- 只開啟必要的交易權限
- 禁用提現權限
- 設置 IP 白名單

3. **定期輪換密鑰**
- 建議每月更換一次 API 密鑰
- 監控 API 使用情況

### 文件權限

**Windows:**
```cmd
# 設置 .env 文件為只讀
attrib +R .env
```

**macOS/Linux:**
```bash
# 設置 .env 文件權限為僅所有者可讀寫
chmod 600 .env
```

## 📊 性能優化 / Performance Optimization

### Windows 優化

1. **關閉 Windows Defender 實時保護** (針對項目目錄)
2. **使用 SSD 存儲** 提高 I/O 性能
3. **增加虛擬內存** 如果 RAM 不足

### macOS 優化

1. **關閉不必要的後台應用**
2. **使用終端而非 IDE** 運行腳本以減少資源占用
3. **定期清理系統緩存**

### Linux 優化

1. **調整系統 swappiness**
```bash
echo 'vm.swappiness=10' | sudo tee -a /etc/sysctl.conf
```

2. **使用 screen 或 tmux** 保持會話
```bash
# 安裝 screen
sudo apt install screen

# 創建新會話
screen -S trading_bot

# 運行機器人
./start.sh

# 分離會話: Ctrl+A, D
# 重新連接: screen -r trading_bot
```

## 🌐 多語言支持 / Multi-language Support

### 中文支持

**Windows 簡體中文:**
- 系統自動檢測編碼
- 支持 GBK/UTF-8 自動轉換

**macOS 繁體中文:**
- 默認使用 UTF-8 編碼
- 支持繁體中文顯示

**Linux UTF-8:**
- 確保系統 locale 設置為 UTF-8
```bash
export LANG=zh_CN.UTF-8
export LC_ALL=zh_CN.UTF-8
```

### 英文支持

所有平台都完全支持英文界面和日誌輸出。

## 📞 技術支持 / Technical Support

### 日誌文件位置

- **Windows**: `logs\pair_trading_bot.log`
- **macOS/Linux**: `logs/pair_trading_bot.log`

### 系統信息收集

運行診斷命令：
```bash
python main.py check-system
```

### 常用調試命令

```bash
# 檢查配置
python main.py config --validate

# 運行測試
python main.py test

# 生成示例數據
python main.py generate-data

# 查看幫助
python main.py --help
```

---

## 📝 更新日誌 / Changelog

- **v2.0**: 添加跨平台支持
- **v2.1**: 增強中文編碼處理
- **v2.2**: 添加自動安裝腳本
- **v2.3**: 優化 API 密鑰管理

---

**免責聲明**: 本軟體僅供教育和研究目的。請在充分了解風險的情況下使用。

**Disclaimer**: This software is for educational and research purposes only. Please use with full understanding of the risks involved.
