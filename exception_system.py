#!/usr/bin/env python3
"""
統一異常體系 - 企業級異常管理
Unified Exception System - Enterprise-grade Exception Management
"""

import traceback
from abc import ABC
from datetime import datetime
from enum import Enum
from typing import Dict, Any, Optional, List
import json

from logging_config import get_logger

logger = get_logger(__name__)


class ErrorSeverity(Enum):
    """錯誤嚴重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """錯誤分類"""
    # 數據相關
    DATA_UNAVAILABLE = "data_unavailable"
    DATA_INVALID = "data_invalid"
    DATA_STALE = "data_stale"
    
    # 交易相關
    ORDER_EXECUTION = "order_execution"
    INSUFFICIENT_FUNDS = "insufficient_funds"
    MARKET_CLOSED = "market_closed"
    POSITION_LIMIT = "position_limit"
    
    # 網絡相關
    NETWORK_ERROR = "network_error"
    API_RATE_LIMIT = "api_rate_limit"
    API_AUTHENTICATION = "api_authentication"
    
    # 系統相關
    CONFIGURATION_ERROR = "configuration_error"
    RESOURCE_EXHAUSTED = "resource_exhausted"
    SERVICE_UNAVAILABLE = "service_unavailable"
    
    # 策略相關
    STRATEGY_ERROR = "strategy_error"
    SIGNAL_GENERATION = "signal_generation"
    RISK_VIOLATION = "risk_violation"


class TradingException(Exception, ABC):
    """交易系統基礎異常類"""
    
    def __init__(self, 
                 message: str,
                 category: ErrorCategory,
                 severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                 context: Optional[Dict[str, Any]] = None,
                 cause: Optional[Exception] = None):
        super().__init__(message)
        
        self.message = message
        self.category = category
        self.severity = severity
        self.context = context or {}
        self.cause = cause
        self.timestamp = datetime.now()
        self.error_id = f"{category.value}_{int(self.timestamp.timestamp())}"
        
        # 記錄異常
        self._log_exception()
    
    def _log_exception(self):
        """記錄異常"""
        log_level = {
            ErrorSeverity.LOW: logger.info,
            ErrorSeverity.MEDIUM: logger.warning,
            ErrorSeverity.HIGH: logger.error,
            ErrorSeverity.CRITICAL: logger.critical
        }.get(self.severity, logger.error)
        
        log_level(f"[{self.error_id}] {self.category.value}: {self.message}")
        
        if self.context:
            logger.debug(f"異常上下文: {json.dumps(self.context, default=str)}")
        
        if self.cause:
            logger.debug(f"原始異常: {self.cause}")
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典"""
        return {
            'error_id': self.error_id,
            'message': self.message,
            'category': self.category.value,
            'severity': self.severity.value,
            'timestamp': self.timestamp.isoformat(),
            'context': self.context,
            'cause': str(self.cause) if self.cause else None,
            'traceback': traceback.format_exc() if self.cause else None
        }
    
    def is_retryable(self) -> bool:
        """判斷是否可重試"""
        # 默認實現：網絡錯誤和服務不可用錯誤可重試
        retryable_categories = {
            ErrorCategory.NETWORK_ERROR,
            ErrorCategory.SERVICE_UNAVAILABLE,
            ErrorCategory.DATA_UNAVAILABLE
        }
        return self.category in retryable_categories
    
    def should_trigger_circuit_breaker(self) -> bool:
        """判斷是否應該觸發熔斷器"""
        # 只有暫時性錯誤才觸發熔斷器
        return self.is_retryable()


# 數據相關異常
class DataUnavailableError(TradingException):
    """數據不可用異常"""
    
    def __init__(self, symbol: str, data_type: str, **kwargs):
        message = f"數據不可用: {symbol} - {data_type}"
        context = {'symbol': symbol, 'data_type': data_type}
        super().__init__(
            message=message,
            category=ErrorCategory.DATA_UNAVAILABLE,
            severity=ErrorSeverity.MEDIUM,
            context=context,
            **kwargs
        )


class DataInvalidError(TradingException):
    """數據無效異常"""
    
    def __init__(self, reason: str, data_sample: Any = None, **kwargs):
        message = f"數據無效: {reason}"
        context = {'reason': reason, 'data_sample': str(data_sample)[:100]}
        super().__init__(
            message=message,
            category=ErrorCategory.DATA_INVALID,
            severity=ErrorSeverity.HIGH,
            context=context,
            **kwargs
        )


class DataStaleError(TradingException):
    """數據過期異常"""
    
    def __init__(self, symbol: str, last_update: datetime, **kwargs):
        age_seconds = (datetime.now() - last_update).total_seconds()
        message = f"數據過期: {symbol} (最後更新: {age_seconds:.0f}秒前)"
        context = {'symbol': symbol, 'last_update': last_update.isoformat(), 'age_seconds': age_seconds}
        super().__init__(
            message=message,
            category=ErrorCategory.DATA_STALE,
            severity=ErrorSeverity.MEDIUM,
            context=context,
            **kwargs
        )


# 交易相關異常
class OrderExecutionError(TradingException):
    """訂單執行異常"""
    
    def __init__(self, order_id: str, reason: str, **kwargs):
        message = f"訂單執行失敗: {order_id} - {reason}"
        context = {'order_id': order_id, 'reason': reason}
        super().__init__(
            message=message,
            category=ErrorCategory.ORDER_EXECUTION,
            severity=ErrorSeverity.HIGH,
            context=context,
            **kwargs
        )
    
    def is_retryable(self) -> bool:
        """訂單執行錯誤通常不可重試"""
        return False


class InsufficientFundsError(TradingException):
    """資金不足異常"""
    
    def __init__(self, required: float, available: float, currency: str = "USDT", **kwargs):
        message = f"資金不足: 需要 {required} {currency}, 可用 {available} {currency}"
        context = {'required': required, 'available': available, 'currency': currency}
        super().__init__(
            message=message,
            category=ErrorCategory.INSUFFICIENT_FUNDS,
            severity=ErrorSeverity.HIGH,
            context=context,
            **kwargs
        )
    
    def is_retryable(self) -> bool:
        """資金不足錯誤不可重試"""
        return False


class PositionLimitExceededError(TradingException):
    """倉位限制超出異常"""
    
    def __init__(self, symbol: str, current_position: float, limit: float, **kwargs):
        message = f"倉位限制超出: {symbol} 當前 {current_position}, 限制 {limit}"
        context = {'symbol': symbol, 'current_position': current_position, 'limit': limit}
        super().__init__(
            message=message,
            category=ErrorCategory.POSITION_LIMIT,
            severity=ErrorSeverity.HIGH,
            context=context,
            **kwargs
        )
    
    def is_retryable(self) -> bool:
        """倉位限制錯誤不可重試"""
        return False


# 網絡相關異常
class NetworkError(TradingException):
    """網絡錯誤異常"""
    
    def __init__(self, endpoint: str, status_code: Optional[int] = None, **kwargs):
        message = f"網絡錯誤: {endpoint}"
        if status_code:
            message += f" (HTTP {status_code})"
        
        context = {'endpoint': endpoint, 'status_code': status_code}
        super().__init__(
            message=message,
            category=ErrorCategory.NETWORK_ERROR,
            severity=ErrorSeverity.MEDIUM,
            context=context,
            **kwargs
        )


class APIRateLimitError(TradingException):
    """API限流異常"""
    
    def __init__(self, endpoint: str, retry_after: Optional[int] = None, **kwargs):
        message = f"API限流: {endpoint}"
        if retry_after:
            message += f" (重試間隔: {retry_after}秒)"
        
        context = {'endpoint': endpoint, 'retry_after': retry_after}
        super().__init__(
            message=message,
            category=ErrorCategory.API_RATE_LIMIT,
            severity=ErrorSeverity.MEDIUM,
            context=context,
            **kwargs
        )


class APIAuthenticationError(TradingException):
    """API認證異常"""
    
    def __init__(self, exchange: str, **kwargs):
        message = f"API認證失敗: {exchange}"
        context = {'exchange': exchange}
        super().__init__(
            message=message,
            category=ErrorCategory.API_AUTHENTICATION,
            severity=ErrorSeverity.CRITICAL,
            context=context,
            **kwargs
        )
    
    def is_retryable(self) -> bool:
        """認證錯誤不可重試"""
        return False


# 系統相關異常
class ConfigurationError(TradingException):
    """配置錯誤異常"""
    
    def __init__(self, config_key: str, reason: str, **kwargs):
        message = f"配置錯誤: {config_key} - {reason}"
        context = {'config_key': config_key, 'reason': reason}
        super().__init__(
            message=message,
            category=ErrorCategory.CONFIGURATION_ERROR,
            severity=ErrorSeverity.CRITICAL,
            context=context,
            **kwargs
        )
    
    def is_retryable(self) -> bool:
        """配置錯誤不可重試"""
        return False


class ResourceExhaustedError(TradingException):
    """資源耗盡異常"""
    
    def __init__(self, resource_type: str, current_usage: float, limit: float, **kwargs):
        message = f"資源耗盡: {resource_type} 使用率 {current_usage:.1%}, 限制 {limit:.1%}"
        context = {'resource_type': resource_type, 'current_usage': current_usage, 'limit': limit}
        super().__init__(
            message=message,
            category=ErrorCategory.RESOURCE_EXHAUSTED,
            severity=ErrorSeverity.HIGH,
            context=context,
            **kwargs
        )


# 策略相關異常
class StrategyError(TradingException):
    """策略錯誤異常"""
    
    def __init__(self, strategy_name: str, reason: str, **kwargs):
        message = f"策略錯誤: {strategy_name} - {reason}"
        context = {'strategy_name': strategy_name, 'reason': reason}
        super().__init__(
            message=message,
            category=ErrorCategory.STRATEGY_ERROR,
            severity=ErrorSeverity.HIGH,
            context=context,
            **kwargs
        )


class RiskViolationError(TradingException):
    """風險違規異常"""
    
    def __init__(self, risk_type: str, current_value: float, limit: float, **kwargs):
        message = f"風險違規: {risk_type} 當前值 {current_value}, 限制 {limit}"
        context = {'risk_type': risk_type, 'current_value': current_value, 'limit': limit}
        super().__init__(
            message=message,
            category=ErrorCategory.RISK_VIOLATION,
            severity=ErrorSeverity.CRITICAL,
            context=context,
            **kwargs
        )
    
    def is_retryable(self) -> bool:
        """風險違規錯誤不可重試"""
        return False


class ExceptionManager:
    """異常管理器"""
    
    def __init__(self):
        self.exception_history: List[Dict[str, Any]] = []
        self.max_history_size = 1000
    
    def handle_exception(self, exception: Exception) -> TradingException:
        """處理異常，轉換為統一格式"""
        if isinstance(exception, TradingException):
            trading_exception = exception
        else:
            # 將普通異常轉換為TradingException
            trading_exception = self._convert_exception(exception)
        
        # 記錄到歷史
        self._record_exception(trading_exception)
        
        return trading_exception
    
    def _convert_exception(self, exception: Exception) -> TradingException:
        """將普通異常轉換為TradingException"""
        exception_type = type(exception).__name__
        message = str(exception)
        
        # 根據異常類型和消息內容推斷分類
        if 'connection' in message.lower() or 'network' in message.lower():
            category = ErrorCategory.NETWORK_ERROR
        elif 'timeout' in message.lower():
            category = ErrorCategory.NETWORK_ERROR
        elif 'authentication' in message.lower() or 'unauthorized' in message.lower():
            category = ErrorCategory.API_AUTHENTICATION
        elif 'config' in message.lower():
            category = ErrorCategory.CONFIGURATION_ERROR
        else:
            category = ErrorCategory.SERVICE_UNAVAILABLE
        
        return TradingException(
            message=f"{exception_type}: {message}",
            category=category,
            severity=ErrorSeverity.MEDIUM,
            cause=exception
        )
    
    def _record_exception(self, exception: TradingException):
        """記錄異常到歷史"""
        self.exception_history.append(exception.to_dict())
        
        # 限制歷史大小
        if len(self.exception_history) > self.max_history_size:
            self.exception_history = self.exception_history[-self.max_history_size:]
    
    def get_exception_stats(self) -> Dict[str, Any]:
        """獲取異常統計"""
        if not self.exception_history:
            return {'total_exceptions': 0}
        
        # 按分類統計
        category_counts = {}
        severity_counts = {}
        
        for exc_data in self.exception_history:
            category = exc_data['category']
            severity = exc_data['severity']
            
            category_counts[category] = category_counts.get(category, 0) + 1
            severity_counts[severity] = severity_counts.get(severity, 0) + 1
        
        return {
            'total_exceptions': len(self.exception_history),
            'category_counts': category_counts,
            'severity_counts': severity_counts,
            'recent_exceptions': self.exception_history[-10:]  # 最近10個異常
        }


# 全局異常管理器
_exception_manager = None

def get_exception_manager() -> ExceptionManager:
    """獲取全局異常管理器"""
    global _exception_manager
    if _exception_manager is None:
        _exception_manager = ExceptionManager()
    return _exception_manager


def handle_exception(exception: Exception) -> TradingException:
    """處理異常的便利函數"""
    return get_exception_manager().handle_exception(exception)


if __name__ == "__main__":
    # 測試異常系統
    try:
        # 測試數據異常
        raise DataUnavailableError("BTC/USDT", "kline_data")
    except TradingException as e:
        print(f"捕獲異常: {e.to_dict()}")
    
    # 測試異常管理器
    manager = get_exception_manager()
    stats = manager.get_exception_stats()
    print(f"異常統計: {json.dumps(stats, indent=2, default=str)}")
