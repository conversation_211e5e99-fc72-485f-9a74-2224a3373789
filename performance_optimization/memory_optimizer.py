#!/usr/bin/env python3
"""
內存優化器 - 極致的內存管理和分析
Memory Optimizer - Ultimate memory management and analysis
"""

import gc
import sys
import psutil
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import threading
import time
from functools import wraps
import tracemalloc

from logging_config import get_logger

logger = get_logger(__name__)


@dataclass
class MemorySnapshot:
    """內存快照"""
    timestamp: datetime
    rss_mb: float
    vms_mb: float
    percent: float
    available_mb: float
    peak_mb: float
    gc_counts: Tuple[int, int, int]


class MemoryProfiler:
    """內存分析器 - 精準定位內存使用"""
    
    def __init__(self):
        self.snapshots: List[MemorySnapshot] = []
        self.peak_memory = 0.0
        self.baseline_memory = 0.0
        self.tracemalloc_enabled = False
        
        # 啟動內存追蹤
        self._start_tracemalloc()
        
        logger.info("MemoryProfiler 初始化完成")
    
    def _start_tracemalloc(self):
        """啟動內存追蹤"""
        try:
            tracemalloc.start()
            self.tracemalloc_enabled = True
            logger.info("內存追蹤已啟動")
        except Exception as e:
            logger.warning(f"內存追蹤啟動失敗: {e}")
    
    def take_snapshot(self, label: str = "") -> MemorySnapshot:
        """拍攝內存快照"""
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            memory_percent = process.memory_percent()
            
            # 系統內存信息
            system_memory = psutil.virtual_memory()
            
            # GC統計
            gc_counts = gc.get_count()
            
            snapshot = MemorySnapshot(
                timestamp=datetime.now(),
                rss_mb=memory_info.rss / 1024 / 1024,
                vms_mb=memory_info.vms / 1024 / 1024,
                percent=memory_percent,
                available_mb=system_memory.available / 1024 / 1024,
                peak_mb=self.peak_memory,
                gc_counts=gc_counts
            )
            
            # 更新峰值
            if snapshot.rss_mb > self.peak_memory:
                self.peak_memory = snapshot.rss_mb
                snapshot.peak_mb = self.peak_memory
            
            self.snapshots.append(snapshot)
            
            if label:
                logger.debug(f"內存快照 [{label}]: {snapshot.rss_mb:.1f}MB")
            
            return snapshot
            
        except Exception as e:
            logger.error(f"內存快照失敗: {e}")
            return None
    
    def get_memory_growth(self) -> float:
        """獲取內存增長"""
        if len(self.snapshots) < 2:
            return 0.0
        
        return self.snapshots[-1].rss_mb - self.snapshots[0].rss_mb
    
    def get_top_memory_consumers(self, limit: int = 10) -> List[Dict]:
        """獲取內存消耗最大的對象"""
        try:
            if not self.tracemalloc_enabled:
                return []
            
            snapshot = tracemalloc.take_snapshot()
            top_stats = snapshot.statistics('lineno')
            
            consumers = []
            for stat in top_stats[:limit]:
                consumers.append({
                    'filename': stat.traceback.format()[-1],
                    'size_mb': stat.size / 1024 / 1024,
                    'count': stat.count
                })
            
            return consumers
            
        except Exception as e:
            logger.error(f"獲取內存消耗者失敗: {e}")
            return []
    
    def profile_function(self, func):
        """函數內存分析裝飾器"""
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 執行前快照
            before = self.take_snapshot(f"{func.__name__}_before")
            
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                # 執行後快照
                after = self.take_snapshot(f"{func.__name__}_after")
                
                if before and after:
                    memory_delta = after.rss_mb - before.rss_mb
                    if abs(memory_delta) > 1.0:  # 只記錄顯著的內存變化
                        logger.info(f"函數 {func.__name__} 內存變化: {memory_delta:+.1f}MB")
        
        return wrapper


class DataFrameOptimizer:
    """DataFrame內存優化器"""
    
    @staticmethod
    def optimize_dtypes(df: pd.DataFrame, aggressive: bool = False) -> pd.DataFrame:
        """優化DataFrame數據類型"""
        try:
            original_memory = df.memory_usage(deep=True).sum() / 1024 / 1024
            
            optimized_df = df.copy()
            
            for col in optimized_df.columns:
                col_type = optimized_df[col].dtype
                
                # 數值類型優化
                if pd.api.types.is_numeric_dtype(col_type):
                    optimized_df[col] = DataFrameOptimizer._optimize_numeric_column(
                        optimized_df[col], aggressive
                    )
                
                # 字符串類型優化
                elif pd.api.types.is_object_dtype(col_type):
                    optimized_df[col] = DataFrameOptimizer._optimize_object_column(
                        optimized_df[col]
                    )
            
            optimized_memory = optimized_df.memory_usage(deep=True).sum() / 1024 / 1024
            reduction = (1 - optimized_memory / original_memory) * 100
            
            logger.info(f"DataFrame內存優化: {original_memory:.1f}MB → {optimized_memory:.1f}MB "
                       f"(減少 {reduction:.1f}%)")
            
            return optimized_df
            
        except Exception as e:
            logger.error(f"DataFrame優化失敗: {e}")
            return df
    
    @staticmethod
    def _optimize_numeric_column(series: pd.Series, aggressive: bool = False) -> pd.Series:
        """優化數值列"""
        try:
            if pd.api.types.is_integer_dtype(series):
                # 整數優化
                min_val, max_val = series.min(), series.max()
                
                if min_val >= np.iinfo(np.int8).min and max_val <= np.iinfo(np.int8).max:
                    return series.astype(np.int8)
                elif min_val >= np.iinfo(np.int16).min and max_val <= np.iinfo(np.int16).max:
                    return series.astype(np.int16)
                elif min_val >= np.iinfo(np.int32).min and max_val <= np.iinfo(np.int32).max:
                    return series.astype(np.int32)
            
            elif pd.api.types.is_float_dtype(series):
                # 浮點數優化
                if aggressive or series.dtype == np.float64:
                    # 檢查是否可以安全轉換為float32
                    if DataFrameOptimizer._can_convert_to_float32(series):
                        return series.astype(np.float32)
            
            return series
            
        except Exception as e:
            logger.warning(f"數值列優化失敗: {e}")
            return series
    
    @staticmethod
    def _can_convert_to_float32(series: pd.Series) -> bool:
        """檢查是否可以安全轉換為float32"""
        try:
            # 檢查範圍
            min_val, max_val = series.min(), series.max()
            float32_info = np.finfo(np.float32)
            
            if (min_val >= float32_info.min and max_val <= float32_info.max):
                # 檢查精度損失
                converted = series.astype(np.float32)
                relative_error = np.abs((series - converted) / series).max()
                return relative_error < 1e-6  # 允許的相對誤差
            
            return False
            
        except:
            return False
    
    @staticmethod
    def _optimize_object_column(series: pd.Series) -> pd.Series:
        """優化對象列"""
        try:
            # 檢查是否適合category類型
            unique_ratio = series.nunique() / len(series)
            
            if unique_ratio < 0.5:  # 如果唯一值比例小於50%
                return series.astype('category')
            
            return series
            
        except Exception as e:
            logger.warning(f"對象列優化失敗: {e}")
            return series


class ChunkedDataProcessor:
    """分塊數據處理器"""
    
    def __init__(self, chunk_size: int = 10000):
        self.chunk_size = chunk_size
        self.profiler = MemoryProfiler()
    
    def process_large_dataset(self, file_path: str, 
                            processor_func: callable,
                            **kwargs) -> List[Any]:
        """分塊處理大型數據集"""
        try:
            results = []
            chunk_count = 0
            
            logger.info(f"開始分塊處理: {file_path}")
            
            # 分塊讀取
            for chunk in pd.read_csv(file_path, chunksize=self.chunk_size, **kwargs):
                chunk_count += 1
                
                # 優化chunk內存
                chunk = DataFrameOptimizer.optimize_dtypes(chunk)
                
                # 處理chunk
                result = processor_func(chunk)
                results.append(result)
                
                # 內存監控
                if chunk_count % 10 == 0:
                    snapshot = self.profiler.take_snapshot(f"chunk_{chunk_count}")
                    if snapshot and snapshot.rss_mb > 1000:  # 超過1GB時警告
                        logger.warning(f"內存使用過高: {snapshot.rss_mb:.1f}MB")
                        gc.collect()
                
                # 清理chunk
                del chunk
            
            logger.info(f"分塊處理完成: {chunk_count} 個chunk")
            return results
            
        except Exception as e:
            logger.error(f"分塊處理失敗: {e}")
            return []


class MemoryMonitor:
    """內存監控器 - 持續監控內存使用"""
    
    def __init__(self, threshold_mb: float = 1000, check_interval: int = 30):
        self.threshold_mb = threshold_mb
        self.check_interval = check_interval
        self.running = False
        self.monitor_thread = None
        self.profiler = MemoryProfiler()
        
        logger.info(f"MemoryMonitor 初始化: 閾值={threshold_mb}MB")
    
    def start(self):
        """啟動內存監控"""
        if self.running:
            return
        
        self.running = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        logger.info("內存監控已啟動")
    
    def stop(self):
        """停止內存監控"""
        self.running = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        
        logger.info("內存監控已停止")
    
    def _monitor_loop(self):
        """監控循環"""
        while self.running:
            try:
                snapshot = self.profiler.take_snapshot("monitor")
                
                if snapshot and snapshot.rss_mb > self.threshold_mb:
                    logger.warning(f"內存使用超過閾值: {snapshot.rss_mb:.1f}MB > {self.threshold_mb}MB")
                    
                    # 獲取內存消耗者
                    consumers = self.profiler.get_top_memory_consumers(5)
                    if consumers:
                        logger.info("內存消耗最大的對象:")
                        for i, consumer in enumerate(consumers, 1):
                            logger.info(f"  {i}. {consumer['filename']}: {consumer['size_mb']:.1f}MB")
                    
                    # 強制垃圾回收
                    collected = gc.collect()
                    logger.info(f"垃圾回收: 清理了 {collected} 個對象")
                
                time.sleep(self.check_interval)
                
            except Exception as e:
                logger.error(f"內存監控錯誤: {e}")
                time.sleep(5)
    
    def get_memory_report(self) -> Dict:
        """獲取內存報告"""
        try:
            current_snapshot = self.profiler.take_snapshot("report")
            memory_growth = self.profiler.get_memory_growth()
            top_consumers = self.profiler.get_top_memory_consumers(10)
            
            return {
                'current_memory_mb': current_snapshot.rss_mb if current_snapshot else 0,
                'peak_memory_mb': self.profiler.peak_memory,
                'memory_growth_mb': memory_growth,
                'memory_percent': current_snapshot.percent if current_snapshot else 0,
                'available_memory_mb': current_snapshot.available_mb if current_snapshot else 0,
                'gc_counts': current_snapshot.gc_counts if current_snapshot else (0, 0, 0),
                'top_consumers': top_consumers,
                'snapshots_count': len(self.profiler.snapshots)
            }
            
        except Exception as e:
            logger.error(f"生成內存報告失敗: {e}")
            return {}


# 全局內存監控器
_memory_monitor = None

def get_memory_monitor() -> MemoryMonitor:
    """獲取全局內存監控器"""
    global _memory_monitor
    if _memory_monitor is None:
        _memory_monitor = MemoryMonitor()
    return _memory_monitor


# 便利裝飾器
def memory_profile(func):
    """內存分析裝飾器"""
    profiler = MemoryProfiler()
    return profiler.profile_function(func)


if __name__ == "__main__":
    # 測試內存優化器
    import time
    
    # 創建測試數據
    print("創建測試數據...")
    df = pd.DataFrame({
        'int_col': np.random.randint(0, 100, 100000),
        'float_col': np.random.random(100000) * 1000,
        'category_col': np.random.choice(['A', 'B', 'C', 'D'], 100000),
        'string_col': [f"item_{i%1000}" for i in range(100000)]
    })
    
    print(f"原始內存使用: {df.memory_usage(deep=True).sum() / 1024 / 1024:.1f}MB")
    
    # 優化DataFrame
    optimized_df = DataFrameOptimizer.optimize_dtypes(df, aggressive=True)
    print(f"優化後內存使用: {optimized_df.memory_usage(deep=True).sum() / 1024 / 1024:.1f}MB")
    
    # 測試內存監控
    monitor = get_memory_monitor()
    monitor.start()
    
    time.sleep(2)
    
    report = monitor.get_memory_report()
    print(f"內存報告: {report}")
    
    monitor.stop()
    print("內存優化器測試完成！")
