#!/usr/bin/env python3
"""
向量化計算優化 - 提升數據處理性能
Vectorized Calculations Optimization - Improve data processing performance
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from numba import jit, njit
import warnings
warnings.filterwarnings('ignore')

from logging_config import get_logger

logger = get_logger(__name__)


class VectorizedCalculator:
    """向量化計算器 - 提供高性能的數值計算和內存優化"""

    def __init__(self):
        self.cache = {}
        self.cache_size_limit = 500  # 減少緩存大小
        self.memory_threshold_mb = 1000  # 內存閾值 1GB

        logger.info("VectorizedCalculator 初始化完成（內存優化版本）")
    
    @staticmethod
    @njit
    def calculate_log_spread_numba(base_prices: np.ndarray, quote_prices: np.ndarray) -> np.ndarray:
        """使用Numba加速的對數價差計算"""
        return np.log(base_prices) - np.log(quote_prices)
    
    @staticmethod
    @njit
    def calculate_rolling_mean_numba(data: np.ndarray, window: int) -> np.ndarray:
        """使用Numba加速的滾動平均計算"""
        n = len(data)
        result = np.full(n, np.nan)
        
        for i in range(window - 1, n):
            start_idx = i - window + 1
            result[i] = np.mean(data[start_idx:i + 1])
        
        return result
    
    @staticmethod
    @njit
    def calculate_rolling_std_numba(data: np.ndarray, window: int) -> np.ndarray:
        """使用Numba加速的滾動標準差計算"""
        n = len(data)
        result = np.full(n, np.nan)
        
        for i in range(window - 1, n):
            start_idx = i - window + 1
            window_data = data[start_idx:i + 1]
            result[i] = np.std(window_data)
        
        return result
    
    @staticmethod
    @njit
    def calculate_zscore_numba(data: np.ndarray, mean: np.ndarray, std: np.ndarray) -> np.ndarray:
        """使用Numba加速的Z-score計算"""
        result = np.full_like(data, 0.0)
        
        for i in range(len(data)):
            if not np.isnan(mean[i]) and not np.isnan(std[i]) and std[i] > 0:
                result[i] = (data[i] - mean[i]) / std[i]
        
        return result
    
    @staticmethod
    @njit
    def calculate_half_life_numba(spread: np.ndarray) -> float:
        """使用Numba加速的半衰期計算"""
        n = len(spread)
        if n < 2:
            return np.nan
        
        # 計算滯後項和差分項
        spread_lag = spread[:-1]
        spread_diff = spread[1:] - spread[:-1]
        
        # 簡化的線性回歸
        n_obs = len(spread_lag)
        sum_x = np.sum(spread_lag)
        sum_y = np.sum(spread_diff)
        sum_xx = np.sum(spread_lag * spread_lag)
        sum_xy = np.sum(spread_lag * spread_diff)
        
        # 計算斜率
        denominator = n_obs * sum_xx - sum_x * sum_x
        if abs(denominator) < 1e-10:
            return np.nan
        
        slope = (n_obs * sum_xy - sum_x * sum_y) / denominator
        
        if slope >= 0:
            return np.nan
        
        return -np.log(2) / slope
    
    @staticmethod
    @njit
    def calculate_correlation_numba(x: np.ndarray, y: np.ndarray) -> float:
        """使用Numba加速的相關性計算"""
        n = len(x)
        if n != len(y) or n < 2:
            return np.nan
        
        # 計算均值
        mean_x = np.mean(x)
        mean_y = np.mean(y)
        
        # 計算協方差和方差
        cov_xy = 0.0
        var_x = 0.0
        var_y = 0.0
        
        for i in range(n):
            dx = x[i] - mean_x
            dy = y[i] - mean_y
            cov_xy += dx * dy
            var_x += dx * dx
            var_y += dy * dy
        
        # 計算相關係數
        denominator = np.sqrt(var_x * var_y)
        if denominator == 0:
            return np.nan
        
        return cov_xy / denominator
    
    @staticmethod
    @njit
    def calculate_momentum_numba(prices: np.ndarray, periods: np.ndarray) -> np.ndarray:
        """使用Numba加速的動量計算"""
        n = len(prices)
        max_period = int(np.max(periods))
        result = np.full((len(periods), n), np.nan)
        
        for p_idx, period in enumerate(periods):
            period = int(period)
            for i in range(period, n):
                if prices[i] != 0 and prices[i - period] != 0:
                    result[p_idx, i] = (prices[i] / prices[i - period]) - 1
        
        return result
    
    def calculate_spread_statistics_vectorized(self, base_prices: np.ndarray,
                                             quote_prices: np.ndarray,
                                             window: int = 20) -> Dict:
        """向量化價差統計計算 - 內存優化版本"""
        try:
            # 檢查內存使用
            self._check_memory_usage()

            # 優化數據類型
            base_prices = self._optimize_dtype(base_prices)
            quote_prices = self._optimize_dtype(quote_prices)

            # 使用Numba加速計算對數價差
            log_spread = self.calculate_log_spread_numba(base_prices, quote_prices)

            # 向量化滾動統計
            rolling_mean = self.calculate_rolling_mean_numba(log_spread, window)
            rolling_std = self.calculate_rolling_std_numba(log_spread, window)

            # 計算Z-score
            zscore = self.calculate_zscore_numba(log_spread, rolling_mean, rolling_std)

            # 計算半衰期
            half_life = self.calculate_half_life_numba(log_spread)

            # 計算相關性
            log_base = np.log(base_prices)
            log_quote = np.log(quote_prices)
            correlation = self.calculate_correlation_numba(log_base, log_quote)

            # 基本統計
            current_spread = log_spread[-1] if len(log_spread) > 0 else np.nan
            current_zscore = zscore[-1] if len(zscore) > 0 else np.nan
            spread_mean = np.nanmean(log_spread)
            spread_std = np.nanstd(log_spread)

            # 內存優化：只返回必要的數據
            result = {
                'current_spread': current_spread,
                'current_zscore': current_zscore,
                'spread_mean': spread_mean,
                'spread_std': spread_std,
                'half_life': half_life,
                'correlation': correlation
            }

            # 可選：返回完整數據（如果內存充足）
            if self._has_sufficient_memory():
                result.update({
                    'log_spread': log_spread,
                    'rolling_mean': rolling_mean,
                    'rolling_std': rolling_std,
                    'zscore': zscore
                })

            # 清理臨時變量
            del log_base, log_quote

            return result

        except Exception as e:
            logger.error(f"向量化價差統計計算失敗: {e}")
            return {}
    
    def calculate_technical_indicators_vectorized(self, prices: np.ndarray,
                                                 high: Optional[np.ndarray] = None,
                                                 low: Optional[np.ndarray] = None,
                                                 volume: Optional[np.ndarray] = None) -> Dict:
        """向量化技術指標計算"""
        try:
            indicators = {}
            
            # RSI計算
            indicators['rsi_14'] = self._calculate_rsi_vectorized(prices, 14)
            indicators['rsi_21'] = self._calculate_rsi_vectorized(prices, 21)
            
            # 移動平均
            for period in [5, 10, 20, 50]:
                indicators[f'sma_{period}'] = self._calculate_sma_vectorized(prices, period)
                indicators[f'ema_{period}'] = self._calculate_ema_vectorized(prices, period)
            
            # 布林帶
            for period in [20, 50]:
                bb_data = self._calculate_bollinger_bands_vectorized(prices, period)
                indicators[f'bb_upper_{period}'] = bb_data['upper']
                indicators[f'bb_middle_{period}'] = bb_data['middle']
                indicators[f'bb_lower_{period}'] = bb_data['lower']
                indicators[f'bb_width_{period}'] = bb_data['width']
                indicators[f'bb_position_{period}'] = bb_data['position']
            
            # 動量指標
            momentum_periods = np.array([5, 10, 20])
            momentum_data = self.calculate_momentum_numba(prices, momentum_periods)
            for i, period in enumerate(momentum_periods):
                indicators[f'momentum_{period}'] = momentum_data[i]
            
            # 如果有高低價數據，計算ATR
            if high is not None and low is not None:
                indicators['atr_14'] = self._calculate_atr_vectorized(high, low, prices, 14)
            
            # 如果有成交量數據，計算成交量指標
            if volume is not None:
                indicators['volume_sma_20'] = self._calculate_sma_vectorized(volume, 20)
                indicators['volume_ratio'] = volume / indicators['volume_sma_20']
            
            return indicators
            
        except Exception as e:
            logger.error(f"向量化技術指標計算失敗: {e}")
            return {}
    
    @staticmethod
    @njit
    def _calculate_rsi_vectorized(prices: np.ndarray, period: int) -> np.ndarray:
        """向量化RSI計算"""
        n = len(prices)
        rsi = np.full(n, 50.0)
        
        if n < period + 1:
            return rsi
        
        # 計算價格變化
        deltas = np.diff(prices)
        
        # 分離上漲和下跌
        gains = np.where(deltas > 0, deltas, 0.0)
        losses = np.where(deltas < 0, -deltas, 0.0)
        
        # 計算初始平均
        avg_gain = np.mean(gains[:period])
        avg_loss = np.mean(losses[:period])
        
        # 計算RSI
        for i in range(period, n - 1):
            avg_gain = (avg_gain * (period - 1) + gains[i]) / period
            avg_loss = (avg_loss * (period - 1) + losses[i]) / period
            
            if avg_loss == 0:
                rsi[i + 1] = 100.0
            else:
                rs = avg_gain / avg_loss
                rsi[i + 1] = 100.0 - (100.0 / (1.0 + rs))
        
        return rsi
    
    @staticmethod
    @njit
    def _calculate_sma_vectorized(prices: np.ndarray, period: int) -> np.ndarray:
        """向量化SMA計算"""
        n = len(prices)
        sma = np.full(n, np.nan)
        
        for i in range(period - 1, n):
            sma[i] = np.mean(prices[i - period + 1:i + 1])
        
        return sma
    
    @staticmethod
    @njit
    def _calculate_ema_vectorized(prices: np.ndarray, period: int) -> np.ndarray:
        """向量化EMA計算"""
        n = len(prices)
        ema = np.full(n, np.nan)
        
        if n < period:
            return ema
        
        # 計算平滑因子
        alpha = 2.0 / (period + 1)
        
        # 初始值使用SMA
        ema[period - 1] = np.mean(prices[:period])
        
        # 計算EMA
        for i in range(period, n):
            ema[i] = alpha * prices[i] + (1 - alpha) * ema[i - 1]
        
        return ema
    
    def _calculate_bollinger_bands_vectorized(self, prices: np.ndarray, 
                                            period: int, std_dev: float = 2.0) -> Dict:
        """向量化布林帶計算"""
        try:
            # 計算中軌（移動平均）
            middle = self._calculate_sma_vectorized(prices, period)
            
            # 計算標準差
            rolling_std = self.calculate_rolling_std_numba(prices, period)
            
            # 計算上下軌
            upper = middle + std_dev * rolling_std
            lower = middle - std_dev * rolling_std
            
            # 計算帶寬和位置
            width = (upper - lower) / middle
            position = (prices - lower) / (upper - lower)
            
            return {
                'upper': upper,
                'middle': middle,
                'lower': lower,
                'width': width,
                'position': position
            }
            
        except Exception as e:
            logger.error(f"布林帶計算失敗: {e}")
            return {}
    
    @staticmethod
    @njit
    def _calculate_atr_vectorized(high: np.ndarray, low: np.ndarray, 
                                close: np.ndarray, period: int) -> np.ndarray:
        """向量化ATR計算"""
        n = len(high)
        atr = np.full(n, np.nan)
        
        if n < period + 1:
            return atr
        
        # 計算真實範圍
        tr = np.full(n - 1, np.nan)
        for i in range(1, n):
            tr1 = high[i] - low[i]
            tr2 = abs(high[i] - close[i - 1])
            tr3 = abs(low[i] - close[i - 1])
            tr[i - 1] = max(tr1, tr2, tr3)
        
        # 計算ATR
        for i in range(period - 1, len(tr)):
            atr[i + 1] = np.mean(tr[i - period + 1:i + 1])
        
        return atr
    
    def batch_calculate_features(self, price_data: pd.DataFrame, 
                               pairs: List[List[str]]) -> Dict:
        """批量計算多個配對的特徵"""
        try:
            batch_results = {}
            
            for pair in pairs:
                pair_key = f"{pair[0]}-{pair[1]}"
                
                # 提取價格數據
                base_prices = price_data[f'{pair[0]}_price'].values
                quote_prices = price_data[f'{pair[1]}_price'].values
                
                # 計算價差統計
                spread_stats = self.calculate_spread_statistics_vectorized(
                    base_prices, quote_prices
                )
                
                # 計算技術指標
                base_indicators = self.calculate_technical_indicators_vectorized(base_prices)
                quote_indicators = self.calculate_technical_indicators_vectorized(quote_prices)
                
                batch_results[pair_key] = {
                    'spread_statistics': spread_stats,
                    'base_indicators': base_indicators,
                    'quote_indicators': quote_indicators
                }
            
            return batch_results
            
        except Exception as e:
            logger.error(f"批量特徵計算失敗: {e}")
            return {}
    
    def _optimize_dtype(self, data: np.ndarray) -> np.ndarray:
        """優化數據類型以節省內存"""
        try:
            # 檢查數據範圍，選擇合適的數據類型
            if data.dtype == np.float64:
                # 檢查是否可以使用float32
                if np.all(np.isfinite(data)):
                    data_min, data_max = np.min(data), np.max(data)
                    if (data_min >= np.finfo(np.float32).min and
                        data_max <= np.finfo(np.float32).max):
                        return data.astype(np.float32)

            return data

        except Exception as e:
            logger.warning(f"數據類型優化失敗: {e}")
            return data

    def _check_memory_usage(self):
        """檢查內存使用情況"""
        try:
            import psutil
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024

            if memory_mb > self.memory_threshold_mb:
                logger.warning(f"內存使用過高: {memory_mb:.1f}MB，開始清理")
                self._cleanup_memory()

        except ImportError:
            # 如果沒有psutil，跳過內存檢查
            pass
        except Exception as e:
            logger.warning(f"內存檢查失敗: {e}")

    def _has_sufficient_memory(self) -> bool:
        """檢查是否有足夠內存"""
        try:
            import psutil
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024
            return memory_mb < self.memory_threshold_mb * 0.8
        except:
            return True  # 如果無法檢查，假設有足夠內存

    def _cleanup_memory(self):
        """清理內存"""
        try:
            # 清理緩存
            if len(self.cache) > self.cache_size_limit // 2:
                # 保留最近使用的一半緩存
                cache_items = list(self.cache.items())
                self.cache = dict(cache_items[-self.cache_size_limit // 2:])

            # 強制垃圾回收
            import gc
            gc.collect()

            logger.info("內存清理完成")

        except Exception as e:
            logger.error(f"內存清理失敗: {e}")

    def clear_cache(self):
        """清理緩存"""
        self.cache.clear()
        logger.info("計算緩存已清理")


# 全局向量化計算器實例
_vectorized_calculator = None

def get_vectorized_calculator() -> VectorizedCalculator:
    """獲取全局向量化計算器實例"""
    global _vectorized_calculator
    if _vectorized_calculator is None:
        _vectorized_calculator = VectorizedCalculator()
    return _vectorized_calculator


if __name__ == "__main__":
    # 測試向量化計算器
    import time
    
    # 生成測試數據
    np.random.seed(42)
    n_points = 10000
    base_prices = 50000 + np.cumsum(np.random.normal(0, 100, n_points))
    quote_prices = 3000 + np.cumsum(np.random.normal(0, 10, n_points))
    
    calculator = VectorizedCalculator()
    
    # 測試性能
    start_time = time.time()
    
    # 計算價差統計
    spread_stats = calculator.calculate_spread_statistics_vectorized(
        base_prices, quote_prices, window=20
    )
    
    # 計算技術指標
    indicators = calculator.calculate_technical_indicators_vectorized(base_prices)
    
    end_time = time.time()
    
    print(f"向量化計算耗時: {(end_time - start_time) * 1000:.2f} ms")
    print(f"當前價差: {spread_stats.get('current_spread', 0):.6f}")
    print(f"當前Z-score: {spread_stats.get('current_zscore', 0):.4f}")
    print(f"半衰期: {spread_stats.get('half_life', 0):.2f}")
    print(f"相關性: {spread_stats.get('correlation', 0):.4f}")
    print(f"RSI-14: {indicators.get('rsi_14', [0])[-1]:.2f}")
    
    print("向量化計算器測試完成！")
