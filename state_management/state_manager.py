#!/usr/bin/env python3
"""
狀態管理器 - 確保交易機器人狀態的可靠持久化和恢復
State Manager - Ensure reliable persistence and recovery of trading bot state
"""

import hashlib
import json
import pickle
import threading
import time
from dataclasses import asdict, dataclass
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional

from database_manager import get_database_manager
from logging_config import get_logger

logger = get_logger(__name__)


class StateType(Enum):
    """狀態類型枚舉"""

    POSITION = "position"
    ORDER = "order"
    STRATEGY = "strategy"
    PORTFOLIO = "portfolio"
    SYSTEM = "system"
    CONFIGURATION = "configuration"


@dataclass
class StateSnapshot:
    """狀態快照"""

    state_id: str
    state_type: StateType
    timestamp: datetime
    data: Dict[str, Any]
    checksum: str
    version: int = 1

    def to_dict(self) -> Dict:
        """轉換為字典"""
        return {
            "state_id": self.state_id,
            "state_type": self.state_type.value,
            "timestamp": self.timestamp.isoformat(),
            "data": self.data,
            "checksum": self.checksum,
            "version": self.version,
        }

    @classmethod
    def from_dict(cls, data: Dict) -> "StateSnapshot":
        """從字典創建"""
        return cls(
            state_id=data["state_id"],
            state_type=StateType(data["state_type"]),
            timestamp=datetime.fromisoformat(data["timestamp"]),
            data=data["data"],
            checksum=data["checksum"],
            version=data.get("version", 1),
        )


class StateManager:
    """狀態管理器 - 負責狀態的持久化、恢復和一致性保證"""

    def __init__(
        self,
        persistence_interval: int = 30,
        max_snapshots: int = 100,
        enable_compression: bool = True,
        backup_enabled: bool = True,
    ):
        self.persistence_interval = persistence_interval
        self.max_snapshots = max_snapshots
        self.enable_compression = enable_compression
        self.backup_enabled = backup_enabled

        # 內存狀態存儲
        self.current_states: Dict[str, StateSnapshot] = {}
        self.state_history: Dict[str, List[StateSnapshot]] = {}

        # 持久化配置
        self.state_dir = Path("data/states")
        self.backup_dir = Path("data/states/backups")
        self.state_dir.mkdir(parents=True, exist_ok=True)
        self.backup_dir.mkdir(parents=True, exist_ok=True)

        # 數據庫管理器
        self.db_manager = get_database_manager()

        # 線程安全
        self._lock = threading.RLock()
        self._persistence_thread = None
        self._running = False

        # 狀態變更回調
        self.change_callbacks: Dict[StateType, List[Callable]] = {}

        # 統計信息
        self.stats = {
            "total_saves": 0,
            "total_loads": 0,
            "total_recoveries": 0,
            "last_persistence": None,
            "corruption_detected": 0,
        }

        logger.info("StateManager 初始化完成")

    def start(self):
        """啟動狀態管理器"""
        if self._running:
            return

        self._running = True

        # 啟動持久化線程
        self._persistence_thread = threading.Thread(target=self._persistence_worker, daemon=True)
        self._persistence_thread.start()

        # 恢復狀態
        self._recover_states()

        logger.info("StateManager 已啟動")

    def stop(self):
        """停止狀態管理器"""
        if not self._running:
            return

        self._running = False

        # 最後一次持久化
        self._persist_all_states()

        # 等待持久化線程結束
        if self._persistence_thread:
            self._persistence_thread.join(timeout=5)

        logger.info("StateManager 已停止")

    def save_state(
        self,
        state_id: str,
        state_type: StateType,
        data: Dict[str, Any],
        force_persist: bool = False,
    ) -> bool:
        """保存狀態"""
        try:
            with self._lock:
                # 計算校驗和
                checksum = self._calculate_checksum(data)

                # 創建狀態快照
                snapshot = StateSnapshot(
                    state_id=state_id,
                    state_type=state_type,
                    timestamp=datetime.now(),
                    data=data.copy(),
                    checksum=checksum,
                )

                # 檢查是否有變化
                if state_id in self.current_states:
                    old_snapshot = self.current_states[state_id]
                    if old_snapshot.checksum == checksum:
                        # 數據沒有變化，不需要保存
                        return True

                # 保存到內存
                self.current_states[state_id] = snapshot

                # 添加到歷史記錄
                if state_id not in self.state_history:
                    self.state_history[state_id] = []

                self.state_history[state_id].append(snapshot)

                # 限制歷史記錄數量
                if len(self.state_history[state_id]) > self.max_snapshots:
                    self.state_history[state_id] = self.state_history[state_id][
                        -self.max_snapshots :
                    ]

                # 觸發變更回調
                self._trigger_change_callbacks(state_type, snapshot)

                # 強制持久化
                if force_persist:
                    self._persist_state(snapshot)

                self.stats["total_saves"] += 1

                logger.debug(f"狀態已保存: {state_id} ({state_type.value})")
                return True

        except Exception as e:
            logger.error(f"保存狀態失敗: {state_id}, {e}")
            return False

    def load_state(self, state_id: str) -> Optional[StateSnapshot]:
        """載入狀態"""
        try:
            with self._lock:
                # 首先從內存中查找
                if state_id in self.current_states:
                    self.stats["total_loads"] += 1
                    return self.current_states[state_id]

                # 從持久化存儲中載入
                snapshot = self._load_state_from_disk(state_id)
                if snapshot:
                    # 驗證校驗和
                    if self._verify_checksum(snapshot):
                        self.current_states[state_id] = snapshot
                        self.stats["total_loads"] += 1
                        return snapshot
                    else:
                        logger.error(f"狀態校驗和驗證失敗: {state_id}")
                        self.stats["corruption_detected"] += 1

                return None

        except Exception as e:
            logger.error(f"載入狀態失敗: {state_id}, {e}")
            return None

    def get_state_data(self, state_id: str) -> Optional[Dict[str, Any]]:
        """獲取狀態數據"""
        snapshot = self.load_state(state_id)
        return snapshot.data if snapshot else None

    def delete_state(self, state_id: str) -> bool:
        """刪除狀態"""
        try:
            with self._lock:
                # 從內存中刪除
                if state_id in self.current_states:
                    del self.current_states[state_id]

                if state_id in self.state_history:
                    del self.state_history[state_id]

                # 從磁盤中刪除
                state_file = self.state_dir / f"{state_id}.json"
                if state_file.exists():
                    state_file.unlink()

                logger.info(f"狀態已刪除: {state_id}")
                return True

        except Exception as e:
            logger.error(f"刪除狀態失敗: {state_id}, {e}")
            return False

    def list_states(self, state_type: Optional[StateType] = None) -> List[str]:
        """列出所有狀態ID"""
        with self._lock:
            if state_type is None:
                return list(self.current_states.keys())
            else:
                return [
                    state_id
                    for state_id, snapshot in self.current_states.items()
                    if snapshot.state_type == state_type
                ]

    def get_state_history(self, state_id: str, limit: int = 10) -> List[StateSnapshot]:
        """獲取狀態歷史"""
        with self._lock:
            if state_id not in self.state_history:
                return []

            history = self.state_history[state_id]
            return history[-limit:] if limit > 0 else history

    def rollback_state(self, state_id: str, steps: int = 1) -> bool:
        """回滾狀態到之前的版本"""
        try:
            with self._lock:
                if state_id not in self.state_history:
                    logger.error(f"沒有找到狀態歷史: {state_id}")
                    return False

                history = self.state_history[state_id]
                if len(history) <= steps:
                    logger.error(f"歷史記錄不足，無法回滾 {steps} 步")
                    return False

                # 回滾到指定版本
                target_snapshot = history[-(steps + 1)]
                self.current_states[state_id] = target_snapshot

                # 移除後續的歷史記錄
                self.state_history[state_id] = history[:-(steps)]

                logger.info(f"狀態已回滾: {state_id}, 回滾 {steps} 步")
                return True

        except Exception as e:
            logger.error(f"回滾狀態失敗: {state_id}, {e}")
            return False

    def register_change_callback(self, state_type: StateType, callback: Callable):
        """註冊狀態變更回調"""
        if state_type not in self.change_callbacks:
            self.change_callbacks[state_type] = []

        self.change_callbacks[state_type].append(callback)
        logger.info(f"已註冊狀態變更回調: {state_type.value}")

    def _trigger_change_callbacks(self, state_type: StateType, snapshot: StateSnapshot):
        """觸發狀態變更回調"""
        try:
            callbacks = self.change_callbacks.get(state_type, [])
            for callback in callbacks:
                try:
                    callback(snapshot)
                except Exception as e:
                    logger.error(f"狀態變更回調執行失敗: {e}")
        except Exception as e:
            logger.error(f"觸發狀態變更回調失敗: {e}")

    def _calculate_checksum(self, data: Dict[str, Any]) -> str:
        """計算數據校驗和"""
        try:
            # 將數據序列化為JSON字符串
            json_str = json.dumps(data, sort_keys=True, ensure_ascii=False)

            # 計算MD5校驗和
            return hashlib.md5(json_str.encode("utf-8")).hexdigest()

        except Exception as e:
            logger.error(f"計算校驗和失敗: {e}")
            return ""

    def _verify_checksum(self, snapshot: StateSnapshot) -> bool:
        """驗證校驗和"""
        try:
            calculated_checksum = self._calculate_checksum(snapshot.data)
            return calculated_checksum == snapshot.checksum
        except Exception as e:
            logger.error(f"驗證校驗和失敗: {e}")
            return False

    def _persistence_worker(self):
        """持久化工作線程"""
        logger.info("狀態持久化線程已啟動")

        while self._running:
            try:
                # 持久化所有狀態
                self._persist_all_states()

                # 等待下一次持久化
                time.sleep(self.persistence_interval)

            except Exception as e:
                logger.error(f"持久化工作線程錯誤: {e}")
                time.sleep(5)  # 錯誤後短暫等待

        logger.info("狀態持久化線程已停止")

    def _persist_all_states(self):
        """持久化所有狀態"""
        try:
            with self._lock:
                for snapshot in self.current_states.values():
                    self._persist_state(snapshot)

                self.stats["last_persistence"] = datetime.now()

        except Exception as e:
            logger.error(f"持久化所有狀態失敗: {e}")

    def _persist_state(self, snapshot: StateSnapshot):
        """持久化單個狀態"""
        try:
            # 保存到文件
            state_file = self.state_dir / f"{snapshot.state_id}.json"

            with open(state_file, "w", encoding="utf-8") as f:
                json.dump(snapshot.to_dict(), f, indent=2, ensure_ascii=False)

            # 創建備份
            if self.backup_enabled:
                self._create_backup(snapshot)

            # 保存到數據庫
            self._persist_to_database(snapshot)

        except Exception as e:
            logger.error(f"持久化狀態失敗: {snapshot.state_id}, {e}")

    def _create_backup(self, snapshot: StateSnapshot):
        """創建狀態備份"""
        try:
            timestamp = snapshot.timestamp.strftime("%Y%m%d_%H%M%S")
            backup_file = self.backup_dir / f"{snapshot.state_id}_{timestamp}.json"

            with open(backup_file, "w", encoding="utf-8") as f:
                json.dump(snapshot.to_dict(), f, indent=2, ensure_ascii=False)

            # 清理舊備份
            self._cleanup_old_backups(snapshot.state_id)

        except Exception as e:
            logger.error(f"創建備份失敗: {snapshot.state_id}, {e}")

    def _cleanup_old_backups(self, state_id: str, keep_count: int = 10):
        """清理舊備份文件"""
        try:
            pattern = f"{state_id}_*.json"
            backup_files = list(self.backup_dir.glob(pattern))

            if len(backup_files) > keep_count:
                # 按修改時間排序
                backup_files.sort(key=lambda f: f.stat().st_mtime)

                # 刪除最舊的文件
                for old_file in backup_files[:-keep_count]:
                    old_file.unlink()

        except Exception as e:
            logger.error(f"清理舊備份失敗: {state_id}, {e}")

    def _persist_to_database(self, snapshot: StateSnapshot):
        """持久化到數據庫"""
        try:
            # 這裡可以將狀態保存到數據庫
            # 暫時跳過，因為我們主要使用文件存儲
            pass
        except Exception as e:
            logger.error(f"數據庫持久化失敗: {snapshot.state_id}, {e}")

    def _load_state_from_disk(self, state_id: str) -> Optional[StateSnapshot]:
        """從磁盤載入狀態"""
        try:
            state_file = self.state_dir / f"{state_id}.json"

            if not state_file.exists():
                return None

            with open(state_file, "r", encoding="utf-8") as f:
                data = json.load(f)

            return StateSnapshot.from_dict(data)

        except Exception as e:
            logger.error(f"從磁盤載入狀態失敗: {state_id}, {e}")
            return None

    def _recover_states(self):
        """恢復所有狀態"""
        try:
            logger.info("開始恢復狀態...")

            # 從磁盤恢復
            recovered_count = 0
            for state_file in self.state_dir.glob("*.json"):
                if state_file.name.startswith("."):
                    continue

                state_id = state_file.stem
                snapshot = self._load_state_from_disk(state_id)

                if snapshot and self._verify_checksum(snapshot):
                    self.current_states[state_id] = snapshot
                    recovered_count += 1
                else:
                    logger.warning(f"狀態恢復失敗或校驗和錯誤: {state_id}")

            self.stats["total_recoveries"] = recovered_count
            logger.info(f"狀態恢復完成，恢復了 {recovered_count} 個狀態")

        except Exception as e:
            logger.error(f"狀態恢復失敗: {e}")

    def get_statistics(self) -> Dict:
        """獲取統計信息"""
        with self._lock:
            return {
                "current_states_count": len(self.current_states),
                "total_history_entries": sum(
                    len(history) for history in self.state_history.values()
                ),
                "stats": self.stats.copy(),
                "persistence_interval": self.persistence_interval,
                "max_snapshots": self.max_snapshots,
                "running": self._running,
            }


# 全局狀態管理器實例
_state_manager = None


def get_state_manager() -> StateManager:
    """獲取全局狀態管理器實例"""
    global _state_manager
    if _state_manager is None:
        _state_manager = StateManager()
    return _state_manager


if __name__ == "__main__":
    # 測試狀態管理器
    import time

    # 創建狀態管理器
    state_manager = StateManager(persistence_interval=5)
    state_manager.start()

    # 測試保存狀態
    test_data = {
        "symbol": "BTCUSDT",
        "position": 1000,
        "entry_price": 50000,
        "timestamp": datetime.now().isoformat(),
    }

    state_manager.save_state("test_position", StateType.POSITION, test_data)
    print("狀態已保存")

    # 測試載入狀態
    loaded_data = state_manager.get_state_data("test_position")
    print(f"載入的狀態: {loaded_data}")

    # 等待持久化
    time.sleep(6)

    # 獲取統計信息
    stats = state_manager.get_statistics()
    print(f"統計信息: {stats}")

    # 停止狀態管理器
    state_manager.stop()
    print("狀態管理器已停止")
