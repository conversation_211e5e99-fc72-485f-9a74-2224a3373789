#!/usr/bin/env python3
"""
批量修復測試文件中的函數定義語法錯誤
修復模式: def function() pass params): → def function(params):
"""

import re
import sys


def fix_function_definitions(file_path):
    """修復函數定義中的語法錯誤"""
    with open(file_path, "r", encoding="utf-8") as f:
        lines = f.readlines()

    fixed_lines = []
    i = 0
    fixes_made = 0

    while i < len(lines):
        line = lines[i]

        # 檢查是否是有問題的函數定義模式
        # def function_name() 後面跟著 pass 和參數
        if (
            re.match(r"\s*def\s+\w+\(\)\s*$", line.strip())
            and i + 1 < len(lines)
            and "pass" in lines[i + 1]
            and i + 2 < len(lines)
            and lines[i + 2].strip().endswith("):")
        ):
            # 提取函數名
            func_match = re.match(r"(\s*)def\s+(\w+)\(\)\s*$", line)
            if func_match:
                indent = func_match.group(1)
                func_name = func_match.group(2)

                # 提取參數（跳過 pass 行）
                params_line = lines[i + 2].strip()
                if params_line.endswith("):"):
                    params = params_line[:-2].strip()  # 移除 ):

                    # 創建修復後的函數定義
                    fixed_line = f"{indent}def {func_name}(\n{indent}    {params}\n{indent}):\n"
                    fixed_lines.append(fixed_line)

                    # 跳過原來的3行（def行、pass行、參數行）
                    i += 3
                    fixes_made += 1
                    continue

        # 如果不是需要修復的模式，保持原樣
        fixed_lines.append(line)
        i += 1

    if fixes_made > 0:
        with open(file_path, "w", encoding="utf-8") as f:
            f.writelines(fixed_lines)
        print(f"✅ 修復了 {file_path} 中的 {fixes_made} 個函數定義錯誤")
        return True
    else:
        print(f"ℹ️  {file_path} 沒有發現需要修復的函數定義錯誤")
        return False


if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("用法: python fix_test_functions.py <file_path>")
        sys.exit(1)

    file_path = sys.argv[1]
    try:
        fixed = fix_function_definitions(file_path)
        if fixed:
            print(f"🎉 成功修復 {file_path}")
        else:
            print(f"📝 {file_path} 無需修復")
    except Exception as e:
        print(f"❌ 修復 {file_path} 時出錯: {e}")
        sys.exit(1)
