{"description": "智能槓桿配置 - 50倍槓桿但限制實際風險敞口到5-10倍", "version": "3.0", "last_updated": "2025-06-30", "strategy": {"timeframe": "5m", "lookback_period": 60, "entry_threshold_high": 2.0, "entry_threshold_low": -2.0, "confirmation_threshold_high": 1.5, "confirmation_threshold_low": -1.5, "exit_threshold": 0.1, "cooldown_period": 10, "stop_loss_pct": 0.01, "take_profit_target": "zero_crossing"}, "exchange": {"name": "gate", "sandbox": true}, "trading_mode": "futures", "futures_type": "linear", "leverage": 50, "margin_mode": "cross", "capital_management": {"total_capital": 10000, "risk_level": "moderate", "max_utilization_rate": 0.15, "effective_leverage_target": 7.5, "max_single_position_pct": 5.0, "risk_buffer_pct": 3.0, "enable_smart_sizing": true}, "trading_pairs": [["BTC/USDT:USDT", "ETH/USDT:USDT"], ["ETH/USDT:USDT", "AVAX/USDT:USDT"]], "position_sizing": {"use_smart_sizing": true, "base_position_size_usd": 500, "confidence_scaling": true, "correlation_adjustment": true, "volatility_adjustment": true}, "risk_management": {"max_daily_trades": 10, "max_concurrent_pairs": 2, "max_drawdown_pct": 0.02, "stop_loss_enabled": true, "take_profit_enabled": true, "funding_rate_threshold": 0.01, "margin_ratio_threshold": 0.85, "correlation_threshold": 0.8, "volatility_threshold": 0.05}, "smart_controls": {"auto_position_sizing": true, "dynamic_risk_adjustment": true, "correlation_monitoring": true, "volatility_filtering": true, "margin_utilization_control": true, "emergency_position_reduction": true}, "risk_levels": {"conservative": {"effective_leverage": 5.0, "max_utilization_rate": 0.1, "max_single_position_pct": 3.0}, "moderate": {"effective_leverage": 7.5, "max_utilization_rate": 0.15, "max_single_position_pct": 5.0}, "aggressive": {"effective_leverage": 10.0, "max_utilization_rate": 0.2, "max_single_position_pct": 8.0}}, "alerts": {"telegram": {"enabled": true, "authorized_users": []}, "margin_utilization_alerts": true, "position_size_alerts": true, "correlation_alerts": true, "volatility_alerts": true, "effective_leverage_alerts": true}, "monitoring": {"health_check_interval": 30, "position_check_interval": 10, "margin_check_interval": 5, "correlation_check_interval": 60, "volatility_check_interval": 30}, "emergency_procedures": {"auto_emergency_close": true, "emergency_conditions": ["margin_utilization > 0.18", "effective_leverage > 12", "correlation_breakdown > 0.9", "volatility_spike > 0.08"], "position_reduction_threshold": 0.15, "emergency_contact_enabled": true}}