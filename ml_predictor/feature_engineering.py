#!/usr/bin/env python3
"""
特徵工程模組 - 為機器學習模型生成特徵
Feature Engineering Module - Generate features for ML models
"""

from typing import List, Optional

import numpy as np
import pandas as pd
from scipy import stats
from sklearn.preprocessing import RobustScaler
from statsmodels.tsa.stattools import adfuller

# import talib  # 可選依賴，如果沒有安裝會使用替代實現
try:
    import talib

    TALIB_AVAILABLE = True
except ImportError:
    TALIB_AVAILABLE = False

from logging_config import get_logger

logger = get_logger(__name__)


class FeatureEngineer:
    """特徵工程器"""

    def __init__(self):
        self.scaler = RobustScaler()
        self.feature_names = []

        logger.info("FeatureEngineer 初始化完成")

    def generate_features(
        self, price_data: pd.DataFrame, pair: List[str], lookback_window: int = 100
    ) -> pd.DataFrame:
        """生成完整的特徵集"""
        try:
            if len(price_data) < lookback_window:
                logger.warning(f"數據長度不足: {len(price_data)} < {lookback_window}")
                return pd.DataFrame()

            features_df = pd.DataFrame(index=price_data.index)

            # 基礎價格特徵
            base_features = self._generate_price_features(price_data)
            features_df = pd.concat([features_df, base_features], axis=1)

            # 價差特徵
            spread_features = self._generate_spread_features(price_data)
            features_df = pd.concat([features_df, spread_features], axis=1)

            # 技術指標特徵
            technical_features = self._generate_technical_features(price_data)
            features_df = pd.concat([features_df, technical_features], axis=1)

            # 統計特徵
            statistical_features = self._generate_statistical_features(price_data, lookback_window)
            features_df = pd.concat([features_df, statistical_features], axis=1)

            # 市場微觀結構特徵
            microstructure_features = self._generate_microstructure_features(price_data)
            features_df = pd.concat([features_df, microstructure_features], axis=1)

            # 時間特徵
            time_features = self._generate_time_features(price_data)
            features_df = pd.concat([features_df, time_features], axis=1)

            # 清理和標準化
            features_df = self._clean_features(features_df)

            # 記錄特徵名稱
            self.feature_names = features_df.columns.tolist()

            logger.info(f"生成特徵完成: {len(self.feature_names)} 個特徵")
            return features_df

        except Exception as e:
            logger.error(f"生成特徵失敗: {e}")
            return pd.DataFrame()

    def _generate_price_features(self, price_data: pd.DataFrame) -> pd.DataFrame:
        """生成價格相關特徵"""
        try:
            features = pd.DataFrame(index=price_data.index)

            base_price = price_data["base_price"]
            quote_price = price_data["quote_price"]

            # 對數價格
            features["log_base_price"] = np.log(base_price)
            features["log_quote_price"] = np.log(quote_price)

            # 價格比率
            features["price_ratio"] = base_price / quote_price
            features["log_price_ratio"] = np.log(features["price_ratio"])

            # 價格變化率
            for period in [1, 5, 10, 20]:
                features[f"base_return_{period}"] = base_price.pct_change(period)
                features[f"quote_return_{period}"] = quote_price.pct_change(period)
                features[f"ratio_return_{period}"] = features["price_ratio"].pct_change(period)

            # 價格動量
            for period in [5, 10, 20]:
                features[f"base_momentum_{period}"] = base_price / base_price.shift(period) - 1
                features[f"quote_momentum_{period}"] = quote_price / quote_price.shift(period) - 1

            return features

        except Exception as e:
            logger.error(f"生成價格特徵失敗: {e}")
            return pd.DataFrame()

    def _generate_spread_features(self, price_data: pd.DataFrame) -> pd.DataFrame:
        """生成價差相關特徵"""
        try:
            features = pd.DataFrame(index=price_data.index)

            base_price = price_data["base_price"]
            quote_price = price_data["quote_price"]

            # 對數價差
            log_spread = np.log(base_price) - np.log(quote_price)
            features["log_spread"] = log_spread

            # 價差統計特徵
            for window in [10, 20, 50]:
                rolling_mean = log_spread.rolling(window).mean()
                rolling_std = log_spread.rolling(window).std()

                features[f"spread_zscore_{window}"] = (log_spread - rolling_mean) / rolling_std
                features[f"spread_mean_{window}"] = rolling_mean
                features[f"spread_std_{window}"] = rolling_std

                # 價差相對位置
                rolling_min = log_spread.rolling(window).min()
                rolling_max = log_spread.rolling(window).max()
                features[f"spread_percentile_{window}"] = (log_spread - rolling_min) / (
                    rolling_max - rolling_min
                )

            # 價差變化率
            for period in [1, 5, 10]:
                features[f"spread_change_{period}"] = log_spread.diff(period)
                features[f"spread_pct_change_{period}"] = log_spread.pct_change(period)

            # 價差趨勢
            for window in [10, 20]:
                features[f"spread_trend_{window}"] = log_spread.rolling(window).apply(
                    lambda x: stats.linregress(range(len(x)), x)[0] if len(x) == window else np.nan
                )

            return features

        except Exception as e:
            logger.error(f"生成價差特徵失敗: {e}")
            return pd.DataFrame()

    def _generate_technical_features(self, price_data: pd.DataFrame) -> pd.DataFrame:
        """生成技術指標特徵"""
        try:
            features = pd.DataFrame(index=price_data.index)

            base_price = price_data["base_price"]
            quote_price = price_data["quote_price"]

            if TALIB_AVAILABLE:
                # 使用 TA-Lib
                base_price_values = base_price.values
                quote_price_values = quote_price.values

                # RSI
                for period in [14, 21]:
                    features[f"base_rsi_{period}"] = talib.RSI(base_price_values, timeperiod=period)
                    features[f"quote_rsi_{period}"] = talib.RSI(
                        quote_price_values, timeperiod=period
                    )
            else:
                # 使用替代實現
                # RSI
                for period in [14, 21]:
                    features[f"base_rsi_{period}"] = self._calculate_rsi(base_price, period)
                    features[f"quote_rsi_{period}"] = self._calculate_rsi(quote_price, period)

            # 移動平均
            for period in [10, 20, 50]:
                features[f"base_sma_{period}"] = self._calculate_sma(base_price, period)
                features[f"quote_sma_{period}"] = self._calculate_sma(quote_price, period)
                features[f"base_ema_{period}"] = self._calculate_ema(base_price, period)
                features[f"quote_ema_{period}"] = self._calculate_ema(quote_price, period)

            # 布林帶（簡化版）
            for period in [20, 50]:
                base_sma = self._calculate_sma(base_price, period)
                base_std = base_price.rolling(period).std()
                quote_sma = self._calculate_sma(quote_price, period)
                quote_std = quote_price.rolling(period).std()

                features[f"base_bb_upper_{period}"] = base_sma + 2 * base_std
                features[f"base_bb_lower_{period}"] = base_sma - 2 * base_std
                features[f"base_bb_width_{period}"] = (4 * base_std) / base_sma
                features[f"base_bb_position_{period}"] = (
                    base_price - (base_sma - 2 * base_std)
                ) / (4 * base_std)

                features[f"quote_bb_upper_{period}"] = quote_sma + 2 * quote_std
                features[f"quote_bb_lower_{period}"] = quote_sma - 2 * quote_std
                features[f"quote_bb_width_{period}"] = (4 * quote_std) / quote_sma
                features[f"quote_bb_position_{period}"] = (
                    quote_price - (quote_sma - 2 * quote_std)
                ) / (4 * quote_std)

            return features

        except Exception as e:
            logger.error(f"生成技術指標特徵失敗: {e}")
            return pd.DataFrame()

    def _generate_statistical_features(
        self, price_data: pd.DataFrame, lookback_window: int
    ) -> pd.DataFrame:
        """生成統計特徵"""
        try:
            features = pd.DataFrame(index=price_data.index)

            base_price = price_data["base_price"]
            quote_price = price_data["quote_price"]
            log_spread = np.log(base_price) - np.log(quote_price)

            # 滾動統計
            for window in [20, 50]:
                # 相關性
                features[f"correlation_{window}"] = base_price.rolling(window).corr(quote_price)

                # 價差的統計特性
                features[f"spread_skew_{window}"] = log_spread.rolling(window).skew()
                features[f"spread_kurt_{window}"] = log_spread.rolling(window).kurt()

                # 波動率
                base_returns = base_price.pct_change()
                quote_returns = quote_price.pct_change()

                features[f"base_volatility_{window}"] = base_returns.rolling(window).std()
                features[f"quote_volatility_{window}"] = quote_returns.rolling(window).std()
                features[f"volatility_ratio_{window}"] = (
                    features[f"base_volatility_{window}"] / features[f"quote_volatility_{window}"]
                )

            # 半衰期估計
            features["half_life"] = self._calculate_rolling_half_life(log_spread, window=50)

            # ADF 檢定 p-value (滾動)
            features["adf_pvalue"] = self._calculate_rolling_adf(log_spread, window=100)

            return features

        except Exception as e:
            logger.error(f"生成統計特徵失敗: {e}")
            return pd.DataFrame()

    def _generate_microstructure_features(self, price_data: pd.DataFrame) -> pd.DataFrame:
        """生成市場微觀結構特徵"""
        try:
            features = pd.DataFrame(index=price_data.index)

            # 如果有成交量數據
            if "base_volume" in price_data.columns:
                base_volume = price_data["base_volume"]
                features["base_volume"] = base_volume

                # 成交量移動平均
                for period in [10, 20]:
                    features[f"base_volume_ma_{period}"] = base_volume.rolling(period).mean()
                    features[f"base_volume_ratio_{period}"] = (
                        base_volume / features[f"base_volume_ma_{period}"]
                    )

                # 價量關係
                base_returns = price_data["base_price"].pct_change()
                features["base_price_volume_corr"] = base_returns.rolling(20).corr(base_volume)

            if "quote_volume" in price_data.columns:
                quote_volume = price_data["quote_volume"]
                features["quote_volume"] = quote_volume

                for period in [10, 20]:
                    features[f"quote_volume_ma_{period}"] = quote_volume.rolling(period).mean()
                    features[f"quote_volume_ratio_{period}"] = (
                        quote_volume / features[f"quote_volume_ma_{period}"]
                    )

                quote_returns = price_data["quote_price"].pct_change()
                features["quote_price_volume_corr"] = quote_returns.rolling(20).corr(quote_volume)

            return features

        except Exception as e:
            logger.error(f"生成微觀結構特徵失敗: {e}")
            return pd.DataFrame()

    def _generate_time_features(self, price_data: pd.DataFrame) -> pd.DataFrame:
        """生成時間特徵"""
        try:
            features = pd.DataFrame(index=price_data.index)

            # 假設索引是時間戳
            if hasattr(price_data.index, "hour"):
                features["hour"] = price_data.index.hour
                features["day_of_week"] = price_data.index.dayofweek
                features["month"] = price_data.index.month

                # 時間段特徵
                features["is_asian_session"] = (
                    (features["hour"] >= 0) & (features["hour"] < 8)
                ).astype(int)
                features["is_european_session"] = (
                    (features["hour"] >= 8) & (features["hour"] < 16)
                ).astype(int)
                features["is_american_session"] = (
                    (features["hour"] >= 16) & (features["hour"] < 24)
                ).astype(int)

                # 週末特徵
                features["is_weekend"] = (features["day_of_week"] >= 5).astype(int)

            return features

        except Exception as e:
            logger.error(f"生成時間特徵失敗: {e}")
            return pd.DataFrame()

    def _calculate_rolling_half_life(self, series: pd.Series, window: int) -> pd.Series:
        """計算滾動半衰期"""
        try:

            def half_life(x):
                if len(x) < 10:
                    return np.nan
                try:
                    x_lag = x.shift(1).dropna()
                    x_diff = x.diff().dropna()
                    if len(x_lag) != len(x_diff):
                        min_len = min(len(x_lag), len(x_diff))
                        x_lag = x_lag.iloc[:min_len]
                        x_diff = x_diff.iloc[:min_len]

                    slope, _, _, _, _ = stats.linregress(x_lag, x_diff)
                    return -np.log(2) / slope if slope < 0 else np.nan
                except:
                    return np.nan

            return series.rolling(window).apply(half_life)

        except Exception as e:
            logger.error(f"計算半衰期失敗: {e}")
            return pd.Series(index=series.index)

    def _calculate_rolling_adf(self, series: pd.Series, window: int) -> pd.Series:
        """計算滾動 ADF 檢定 p-value"""
        try:

            def adf_pvalue(x):
                if len(x) < 20:
                    return np.nan
                try:
                    result = adfuller(x.dropna())
                    return result[1]  # p-value
                except:
                    return np.nan

            return series.rolling(window).apply(adf_pvalue)

        except Exception as e:
            logger.error(f"計算 ADF 檢定失敗: {e}")
            return pd.Series(index=series.index)

    def _clean_features(self, features_df: pd.DataFrame) -> pd.DataFrame:
        """清理特徵數據"""
        try:
            # 移除無限值
            features_df = features_df.replace([np.inf, -np.inf], np.nan)

            # 移除全為 NaN 的列
            features_df = features_df.dropna(axis=1, how="all")

            # 移除方差為 0 的列
            numeric_cols = features_df.select_dtypes(include=[np.number]).columns
            for col in numeric_cols:
                if features_df[col].var() == 0:
                    features_df = features_df.drop(col, axis=1)

            # 前向填充 NaN 值
            features_df = features_df.fillna(method="ffill")

            # 剩餘 NaN 值用 0 填充
            features_df = features_df.fillna(0)

            return features_df

        except Exception as e:
            logger.error(f"清理特徵失敗: {e}")
            return features_df

    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """計算RSI（替代TA-Lib實現）"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi.fillna(50)
        except:
            return pd.Series(50, index=prices.index)

    def _calculate_sma(self, prices: pd.Series, period: int) -> pd.Series:
        """計算簡單移動平均"""
        return prices.rolling(window=period).mean()

    def _calculate_ema(self, prices: pd.Series, period: int) -> pd.Series:
        """計算指數移動平均"""
        return prices.ewm(span=period).mean()

    def get_feature_importance(
        self, model, feature_names: Optional[List[str]] = None
    ) -> pd.DataFrame:
        """獲取特徵重要性"""
        try:
            if feature_names is None:
                feature_names = self.feature_names

            if hasattr(model, "feature_importances_"):
                importance = model.feature_importances_
            elif hasattr(model, "coef_"):
                importance = np.abs(model.coef_).flatten()
            else:
                logger.warning("模型不支持特徵重要性分析")
                return pd.DataFrame()

            importance_df = pd.DataFrame(
                {"feature": feature_names, "importance": importance}
            ).sort_values("importance", ascending=False)

            return importance_df

        except Exception as e:
            logger.error(f"獲取特徵重要性失敗: {e}")
            return pd.DataFrame()


if __name__ == "__main__":
    # 測試特徵工程
    print("測試特徵工程...")

    # 生成模擬數據
    dates = pd.date_range("2024-01-01", periods=200, freq="H")
    np.random.seed(42)

    price_data = pd.DataFrame(
        {
            "base_price": 50000 + np.cumsum(np.random.normal(0, 100, 200)),
            "quote_price": 3000 + np.cumsum(np.random.normal(0, 10, 200)),
            "base_volume": np.random.exponential(1000, 200),
            "quote_volume": np.random.exponential(100, 200),
        },
        index=dates,
    )

    # 創建特徵工程器
    feature_engineer = FeatureEngineer()

    # 生成特徵
    features = feature_engineer.generate_features(price_data, ["BTCUSDT", "ETHUSDT"])

    print(f"生成特徵數量: {len(features.columns)}")
    print(f"特徵名稱: {features.columns.tolist()[:10]}...")  # 顯示前10個
    print(f"特徵數據形狀: {features.shape}")

    print("特徵工程測試完成！")
