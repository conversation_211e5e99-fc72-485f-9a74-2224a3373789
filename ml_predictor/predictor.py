#!/usr/bin/env python3
"""
機器學習預測器 - 集成多種ML模型進行預測
ML Predictor - Integrated ML models for prediction
"""

import pickle
import warnings
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple

import numpy as np
import pandas as pd

warnings.filterwarnings("ignore")

from factor_factory.alpha_factors import get_alpha_factory
from logging_config import get_logger
from ml_predictor.feature_engineering import FeatureEngineer

logger = get_logger(__name__)


class MLPredictor:
    """機器學習預測器"""

    def __init__(self, model_path: Optional[str] = None):
        self.alpha_factory = get_alpha_factory()
        self.feature_engineer = FeatureEngineer()

        # 模型存儲
        self.models = {}
        self.model_weights = {}
        self.feature_importance = {}

        # 預測配置
        self.prediction_horizon = 1  # 預測未來1期
        self.confidence_threshold = 0.6
        self.ensemble_method = "weighted_average"

        # 載入模型
        if model_path and Path(model_path).exists():
            self.load_models(model_path)
        else:
            self._initialize_default_models()

        logger.info("MLPredictor 初始化完成")

    def _initialize_default_models(self):
        """初始化默認模型"""
        try:
            from sklearn.ensemble import GradientBoostingRegressor, RandomForestRegressor
            from sklearn.linear_model import Ridge

            # 初始化多個模型
            self.models = {
                "random_forest": RandomForestRegressor(
                    n_estimators=100, max_depth=10, random_state=42
                ),
                "gradient_boosting": GradientBoostingRegressor(
                    n_estimators=100, max_depth=6, random_state=42
                ),
                "ridge_regression": Ridge(alpha=1.0, random_state=42),
            }

            # 默認權重
            self.model_weights = {
                "random_forest": 0.4,
                "gradient_boosting": 0.4,
                "ridge_regression": 0.2,
            }

            logger.info("默認ML模型初始化完成")

        except ImportError as e:
            logger.error(f"ML庫導入失敗: {e}")
            self.models = {}

    def prepare_training_data(
        self, price_data: pd.DataFrame, pair: List[str]
    ) -> Tuple[pd.DataFrame, pd.Series]:
        """準備訓練數據"""
        try:
            # 生成特徵
            features = self.feature_engineer.generate_features(price_data, pair)

            if features.empty:
                logger.error("特徵生成失敗")
                return pd.DataFrame(), pd.Series()

            # 生成Alpha因子
            alpha_factors = self.alpha_factory.calculate_all_factors(price_data)

            # 合併特徵
            if not alpha_factors.empty:
                # 對齊索引
                common_index = features.index.intersection(alpha_factors.index)
                features = features.loc[common_index]
                alpha_factors = alpha_factors.loc[common_index]

                # 合併
                all_features = pd.concat([features, alpha_factors], axis=1)
            else:
                all_features = features

            # 生成目標變量（未來收益）
            log_spread = np.log(price_data["base_price"]) - np.log(price_data["quote_price"])
            future_returns = log_spread.shift(-self.prediction_horizon) - log_spread

            # 對齊數據
            common_index = all_features.index.intersection(future_returns.index)
            X = all_features.loc[common_index].fillna(0)
            y = future_returns.loc[common_index].fillna(0)

            # 移除最後幾行（沒有未來數據）
            X = X.iloc[: -self.prediction_horizon]
            y = y.iloc[: -self.prediction_horizon]

            logger.info(f"訓練數據準備完成: {X.shape[0]} 樣本, {X.shape[1]} 特徵")
            return X, y

        except Exception as e:
            logger.error(f"準備訓練數據失敗: {e}")
            return pd.DataFrame(), pd.Series()

    def train_models(self, X: pd.DataFrame, y: pd.Series) -> Dict:
        """訓練所有模型"""
        try:
            if X.empty or y.empty:
                logger.error("訓練數據為空")
                return {}

            training_results = {}

            for model_name, model in self.models.items():
                try:
                    # 訓練模型
                    model.fit(X, y)

                    # 計算訓練指標
                    train_score = model.score(X, y)

                    # 特徵重要性
                    if hasattr(model, "feature_importances_"):
                        importance = model.feature_importances_
                    elif hasattr(model, "coef_"):
                        importance = np.abs(model.coef_)
                    else:
                        importance = np.ones(X.shape[1]) / X.shape[1]

                    self.feature_importance[model_name] = dict(zip(X.columns, importance))

                    training_results[model_name] = {
                        "train_score": train_score,
                        "feature_count": X.shape[1],
                        "sample_count": X.shape[0],
                    }

                    logger.info(f"模型 {model_name} 訓練完成, R²: {train_score:.4f}")

                except Exception as e:
                    logger.error(f"訓練模型 {model_name} 失敗: {e}")
                    training_results[model_name] = {"error": str(e)}

            return training_results

        except Exception as e:
            logger.error(f"訓練模型失敗: {e}")
            return {}

    def predict(self, price_data: pd.DataFrame, pair: List[str]) -> Dict:
        """進行預測"""
        try:
            if not self.models:
                logger.warning("沒有可用的模型")
                return {"prediction": 0, "confidence": 0, "method": "no_model"}

            # 準備特徵
            features = self.feature_engineer.generate_features(price_data, pair)
            alpha_factors = self.alpha_factory.calculate_all_factors(price_data)

            if features.empty:
                logger.error("特徵生成失敗")
                return {"prediction": 0, "confidence": 0, "method": "feature_error"}

            # 合併特徵
            if not alpha_factors.empty:
                common_index = features.index.intersection(alpha_factors.index)
                features = features.loc[common_index]
                alpha_factors = alpha_factors.loc[common_index]
                all_features = pd.concat([features, alpha_factors], axis=1)
            else:
                all_features = features

            # 獲取最新特徵
            latest_features = all_features.iloc[-1:].fillna(0)

            # 各模型預測
            predictions = {}
            confidences = {}

            for model_name, model in self.models.items():
                try:
                    # 確保特徵維度匹配
                    if hasattr(model, "n_features_in_"):
                        expected_features = model.n_features_in_
                        if latest_features.shape[1] != expected_features:
                            logger.warning(f"模型 {model_name} 特徵維度不匹配")
                            continue

                    pred = model.predict(latest_features)[0]
                    predictions[model_name] = pred

                    # 計算置信度（基於特徵重要性）
                    if model_name in self.feature_importance:
                        importance_sum = sum(self.feature_importance[model_name].values())
                        confidence = min(importance_sum, 1.0)
                    else:
                        confidence = 0.5

                    confidences[model_name] = confidence

                except Exception as e:
                    logger.error(f"模型 {model_name} 預測失敗: {e}")

            if not predictions:
                return {"prediction": 0, "confidence": 0, "method": "prediction_error"}

            # 集成預測
            ensemble_prediction = self._ensemble_predictions(predictions, confidences)

            return {
                "prediction": ensemble_prediction["value"],
                "confidence": ensemble_prediction["confidence"],
                "method": "ml_ensemble",
                "individual_predictions": predictions,
                "model_confidences": confidences,
            }

        except Exception as e:
            logger.error(f"預測失敗: {e}")
            return {"prediction": 0, "confidence": 0, "method": "error"}

    def _ensemble_predictions(self, predictions: Dict, confidences: Dict) -> Dict:
        """集成多個模型的預測"""
        try:
            if self.ensemble_method == "weighted_average":
                # 加權平均
                total_weight = 0
                weighted_sum = 0

                for model_name, pred in predictions.items():
                    weight = self.model_weights.get(model_name, 1.0)
                    confidence = confidences.get(model_name, 0.5)

                    # 權重 × 置信度
                    effective_weight = weight * confidence
                    weighted_sum += pred * effective_weight
                    total_weight += effective_weight

                if total_weight > 0:
                    ensemble_pred = weighted_sum / total_weight
                    ensemble_confidence = total_weight / len(predictions)
                else:
                    ensemble_pred = 0
                    ensemble_confidence = 0

            elif self.ensemble_method == "median":
                # 中位數
                pred_values = list(predictions.values())
                ensemble_pred = np.median(pred_values)
                ensemble_confidence = np.mean(list(confidences.values()))

            else:
                # 簡單平均
                ensemble_pred = np.mean(list(predictions.values()))
                ensemble_confidence = np.mean(list(confidences.values()))

            return {"value": ensemble_pred, "confidence": ensemble_confidence}

        except Exception as e:
            logger.error(f"集成預測失敗: {e}")
            return {"value": 0, "confidence": 0}

    def save_models(self, save_path: str):
        """保存模型"""
        try:
            save_dir = Path(save_path)
            save_dir.mkdir(parents=True, exist_ok=True)

            # 保存每個模型
            for model_name, model in self.models.items():
                model_file = save_dir / f"{model_name}.pkl"
                with open(model_file, "wb") as f:
                    pickle.dump(model, f)

            # 保存元數據
            metadata = {
                "model_weights": self.model_weights,
                "feature_importance": self.feature_importance,
                "prediction_horizon": self.prediction_horizon,
                "confidence_threshold": self.confidence_threshold,
                "ensemble_method": self.ensemble_method,
                "saved_at": datetime.now().isoformat(),
            }

            metadata_file = save_dir / "metadata.json"
            with open(metadata_file, "w") as f:
                import json

                json.dump(metadata, f, indent=2)

            logger.info(f"模型已保存到: {save_path}")

        except Exception as e:
            logger.error(f"保存模型失敗: {e}")

    def load_models(self, load_path: str) -> bool:
        """載入模型"""
        try:
            load_dir = Path(load_path)

            if not load_dir.exists():
                logger.error(f"模型路徑不存在: {load_path}")
                return False

            # 載入元數據
            metadata_file = load_dir / "metadata.json"
            if metadata_file.exists():
                with open(metadata_file, "r") as f:
                    import json

                    metadata = json.load(f)

                self.model_weights = metadata.get("model_weights", {})
                self.feature_importance = metadata.get("feature_importance", {})
                self.prediction_horizon = metadata.get("prediction_horizon", 1)
                self.confidence_threshold = metadata.get("confidence_threshold", 0.6)
                self.ensemble_method = metadata.get("ensemble_method", "weighted_average")

            # 載入模型
            self.models = {}
            for model_file in load_dir.glob("*.pkl"):
                model_name = model_file.stem

                with open(model_file, "rb") as f:
                    model = pickle.load(f)
                    self.models[model_name] = model

            logger.info(f"載入了 {len(self.models)} 個模型")
            return True

        except Exception as e:
            logger.error(f"載入模型失敗: {e}")
            return False

    def evaluate_models(self, X_test: pd.DataFrame, y_test: pd.Series) -> Dict:
        """評估模型性能"""
        try:
            from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score

            evaluation_results = {}

            for model_name, model in self.models.items():
                try:
                    # 預測
                    y_pred = model.predict(X_test)

                    # 計算指標
                    mse = mean_squared_error(y_test, y_pred)
                    mae = mean_absolute_error(y_test, y_pred)
                    r2 = r2_score(y_test, y_pred)

                    # 方向準確率
                    direction_accuracy = np.mean(np.sign(y_test) == np.sign(y_pred))

                    evaluation_results[model_name] = {
                        "mse": mse,
                        "mae": mae,
                        "r2_score": r2,
                        "direction_accuracy": direction_accuracy,
                        "rmse": np.sqrt(mse),
                    }

                except Exception as e:
                    logger.error(f"評估模型 {model_name} 失敗: {e}")
                    evaluation_results[model_name] = {"error": str(e)}

            return evaluation_results

        except Exception as e:
            logger.error(f"模型評估失敗: {e}")
            return {}

    def get_model_summary(self) -> Dict:
        """獲取模型總結"""
        try:
            summary = {
                "model_count": len(self.models),
                "model_names": list(self.models.keys()),
                "model_weights": self.model_weights,
                "prediction_horizon": self.prediction_horizon,
                "confidence_threshold": self.confidence_threshold,
                "ensemble_method": self.ensemble_method,
            }

            # 特徵重要性總結
            if self.feature_importance:
                all_features = set()
                for importance_dict in self.feature_importance.values():
                    all_features.update(importance_dict.keys())

                # 計算平均重要性
                avg_importance = {}
                for feature in all_features:
                    importances = []
                    for importance_dict in self.feature_importance.values():
                        if feature in importance_dict:
                            importances.append(importance_dict[feature])

                    if importances:
                        avg_importance[feature] = np.mean(importances)

                # 排序
                sorted_importance = sorted(avg_importance.items(), key=lambda x: x[1], reverse=True)

                summary["top_features"] = sorted_importance[:10]

            return summary

        except Exception as e:
            logger.error(f"獲取模型總結失敗: {e}")
            return {}


# 全局ML預測器實例
_ml_predictor = None


def get_ml_predictor() -> MLPredictor:
    """獲取全局ML預測器實例"""
    global _ml_predictor
    if _ml_predictor is None:
        _ml_predictor = MLPredictor()
    return _ml_predictor


if __name__ == "__main__":
    # 測試ML預測器
    print("測試ML預測器...")

    # 生成模擬數據
    dates = pd.date_range("2024-01-01", periods=200, freq="H")
    np.random.seed(42)

    price_data = pd.DataFrame(
        {
            "base_price": 50000 + np.cumsum(np.random.normal(0, 100, 200)),
            "quote_price": 3000 + np.cumsum(np.random.normal(0, 10, 200)),
            "base_volume": np.random.exponential(1000, 200),
            "quote_volume": np.random.exponential(100, 200),
        },
        index=dates,
    )

    # 創建預測器
    predictor = MLPredictor()

    # 準備訓練數據
    X, y = predictor.prepare_training_data(price_data, ["BTCUSDT", "ETHUSDT"])

    if not X.empty and not y.empty:
        print(f"訓練數據: {X.shape[0]} 樣本, {X.shape[1]} 特徵")

        # 訓練模型
        training_results = predictor.train_models(X, y)
        print(f"訓練結果: {training_results}")

        # 進行預測
        prediction = predictor.predict(price_data, ["BTCUSDT", "ETHUSDT"])
        print(f"預測結果: {prediction}")

        # 獲取模型總結
        summary = predictor.get_model_summary()
        print(f"模型總結: {summary}")
    else:
        print("訓練數據準備失敗")

    print("ML預測器測試完成！")
