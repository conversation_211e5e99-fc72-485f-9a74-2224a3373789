#!/usr/bin/env python3
"""
數據目錄管理器 - 統一管理所有數據文件和目錄
Data Directory Manager - Unified management of all data files and directories
"""

import os
import shutil
import json
from pathlib import Path
from typing import Dict, List, Optional, Union
from datetime import datetime, timed<PERSON><PERSON>
from dataclasses import dataclass
from enum import Enum

from logging_config import get_logger

logger = get_logger(__name__)


class DataType(str, Enum):
    """數據類型"""
    DATABASE = "databases"
    LOGS = "logs"
    BACKUPS = "backups"
    CACHE = "cache"
    EXPORTS = "exports"
    TEMP = "temp"
    CONFIGS = "configs"
    REPORTS = "reports"


@dataclass
class DirectoryInfo:
    """目錄信息"""
    path: Path
    size_mb: float
    file_count: int
    last_modified: datetime
    data_type: DataType


class DataDirectoryManager:
    """數據目錄管理器"""
    
    def __init__(self, base_data_dir: str = "data"):
        self.base_data_dir = Path(base_data_dir)
        
        # 標準數據目錄結構
        self.directories = {
            DataType.DATABASE: self.base_data_dir / "databases",
            DataType.LOGS: self.base_data_dir / "logs", 
            DataType.BACKUPS: self.base_data_dir / "backups",
            DataType.CACHE: self.base_data_dir / "cache",
            DataType.EXPORTS: self.base_data_dir / "exports",
            DataType.TEMP: self.base_data_dir / "temp",
            DataType.CONFIGS: self.base_data_dir / "configs",
            DataType.REPORTS: self.base_data_dir / "reports"
        }
        
        # 文件類型映射
        self.file_type_mapping = {
            '.db': DataType.DATABASE,
            '.sqlite': DataType.DATABASE,
            '.sqlite3': DataType.DATABASE,
            '.log': DataType.LOGS,
            '.json': DataType.CONFIGS,
            '.yaml': DataType.CONFIGS,
            '.yml': DataType.CONFIGS,
            '.csv': DataType.EXPORTS,
            '.xlsx': DataType.EXPORTS,
            '.html': DataType.REPORTS,
            '.pdf': DataType.REPORTS,
            '.tmp': DataType.TEMP,
            '.cache': DataType.CACHE
        }
        
        # 初始化目錄結構
        self._initialize_directories()
        
        logger.info(f"數據目錄管理器初始化完成，基礎目錄: {self.base_data_dir}")
    
    def _initialize_directories(self):
        """初始化目錄結構"""
        for data_type, directory in self.directories.items():
            directory.mkdir(parents=True, exist_ok=True)
            logger.debug(f"創建目錄: {directory}")
        
        # 創建.gitkeep文件保持目錄結構
        for directory in self.directories.values():
            gitkeep_file = directory / ".gitkeep"
            if not gitkeep_file.exists():
                gitkeep_file.touch()
    
    def get_directory(self, data_type: DataType) -> Path:
        """獲取指定類型的數據目錄"""
        return self.directories[data_type]
    
    def organize_files(self, source_directory: str = ".") -> Dict[DataType, List[str]]:
        """
        整理文件到對應目錄
        
        Args:
            source_directory: 源目錄路徑
            
        Returns:
            移動的文件列表，按數據類型分組
        """
        source_path = Path(source_directory)
        moved_files = {data_type: [] for data_type in DataType}
        
        logger.info(f"開始整理文件，源目錄: {source_path}")
        
        # 遍歷源目錄中的文件
        for file_path in source_path.rglob("*"):
            if file_path.is_file() and not self._is_in_data_directory(file_path):
                data_type = self._determine_data_type(file_path)
                
                if data_type:
                    try:
                        new_path = self._move_file_to_data_directory(file_path, data_type)
                        moved_files[data_type].append(str(new_path))
                        logger.debug(f"移動文件: {file_path} -> {new_path}")
                    except Exception as e:
                        logger.error(f"移動文件失敗 {file_path}: {e}")
        
        # 記錄整理結果
        total_moved = sum(len(files) for files in moved_files.values())
        logger.info(f"文件整理完成，共移動 {total_moved} 個文件")
        
        return moved_files
    
    def _is_in_data_directory(self, file_path: Path) -> bool:
        """檢查文件是否已在數據目錄中"""
        try:
            file_path.relative_to(self.base_data_dir)
            return True
        except ValueError:
            return False
    
    def _determine_data_type(self, file_path: Path) -> Optional[DataType]:
        """根據文件擴展名確定數據類型"""
        suffix = file_path.suffix.lower()
        
        # 檢查文件擴展名映射
        if suffix in self.file_type_mapping:
            return self.file_type_mapping[suffix]
        
        # 檢查文件名模式
        filename = file_path.name.lower()
        
        if 'backup' in filename or 'bak' in filename:
            return DataType.BACKUPS
        elif 'test' in filename and suffix in ['.db', '.sqlite']:
            return DataType.DATABASE
        elif 'report' in filename:
            return DataType.REPORTS
        elif 'config' in filename:
            return DataType.CONFIGS
        
        return None
    
    def _move_file_to_data_directory(self, file_path: Path, data_type: DataType) -> Path:
        """移動文件到對應的數據目錄"""
        target_directory = self.directories[data_type]
        target_path = target_directory / file_path.name
        
        # 處理文件名衝突
        counter = 1
        original_target = target_path
        while target_path.exists():
            stem = original_target.stem
            suffix = original_target.suffix
            target_path = target_directory / f"{stem}_{counter}{suffix}"
            counter += 1
        
        # 移動文件
        shutil.move(str(file_path), str(target_path))
        return target_path
    
    def get_directory_info(self, data_type: DataType) -> DirectoryInfo:
        """獲取目錄信息"""
        directory = self.directories[data_type]
        
        if not directory.exists():
            return DirectoryInfo(
                path=directory,
                size_mb=0.0,
                file_count=0,
                last_modified=datetime.now(),
                data_type=data_type
            )
        
        # 計算目錄大小和文件數量
        total_size = 0
        file_count = 0
        last_modified = datetime.min
        
        for file_path in directory.rglob("*"):
            if file_path.is_file():
                file_count += 1
                file_size = file_path.stat().st_size
                total_size += file_size
                
                file_modified = datetime.fromtimestamp(file_path.stat().st_mtime)
                if file_modified > last_modified:
                    last_modified = file_modified
        
        return DirectoryInfo(
            path=directory,
            size_mb=total_size / (1024 * 1024),
            file_count=file_count,
            last_modified=last_modified if last_modified != datetime.min else datetime.now(),
            data_type=data_type
        )
    
    def get_all_directory_info(self) -> Dict[DataType, DirectoryInfo]:
        """獲取所有目錄信息"""
        return {data_type: self.get_directory_info(data_type) for data_type in DataType}
    
    def cleanup_temp_files(self, max_age_hours: int = 24):
        """清理臨時文件"""
        temp_dir = self.directories[DataType.TEMP]
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        
        cleaned_files = []
        
        for file_path in temp_dir.rglob("*"):
            if file_path.is_file():
                file_modified = datetime.fromtimestamp(file_path.stat().st_mtime)
                if file_modified < cutoff_time:
                    try:
                        file_path.unlink()
                        cleaned_files.append(str(file_path))
                        logger.debug(f"清理臨時文件: {file_path}")
                    except Exception as e:
                        logger.error(f"清理臨時文件失敗 {file_path}: {e}")
        
        logger.info(f"清理完成，刪除 {len(cleaned_files)} 個臨時文件")
        return cleaned_files
    
    def cleanup_old_logs(self, max_age_days: int = 30):
        """清理舊日誌文件"""
        logs_dir = self.directories[DataType.LOGS]
        cutoff_time = datetime.now() - timedelta(days=max_age_days)
        
        cleaned_files = []
        
        for file_path in logs_dir.rglob("*.log*"):
            if file_path.is_file():
                file_modified = datetime.fromtimestamp(file_path.stat().st_mtime)
                if file_modified < cutoff_time:
                    try:
                        file_path.unlink()
                        cleaned_files.append(str(file_path))
                        logger.debug(f"清理舊日誌: {file_path}")
                    except Exception as e:
                        logger.error(f"清理舊日誌失敗 {file_path}: {e}")
        
        logger.info(f"日誌清理完成，刪除 {len(cleaned_files)} 個舊日誌文件")
        return cleaned_files
    
    def create_backup(self, data_type: DataType, backup_name: Optional[str] = None) -> Path:
        """創建數據備份"""
        source_dir = self.directories[data_type]
        backup_dir = self.directories[DataType.BACKUPS]
        
        if not backup_name:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"{data_type.value}_backup_{timestamp}"
        
        backup_path = backup_dir / backup_name
        
        try:
            shutil.copytree(source_dir, backup_path)
            logger.info(f"創建備份成功: {backup_path}")
            return backup_path
        except Exception as e:
            logger.error(f"創建備份失敗: {e}")
            raise
    
    def restore_backup(self, backup_path: Union[str, Path], data_type: DataType):
        """恢復數據備份"""
        backup_path = Path(backup_path)
        target_dir = self.directories[data_type]
        
        if not backup_path.exists():
            raise FileNotFoundError(f"備份文件不存在: {backup_path}")
        
        try:
            # 備份當前數據
            current_backup = self.create_backup(data_type, f"{data_type.value}_before_restore")
            logger.info(f"當前數據已備份到: {current_backup}")
            
            # 清空目標目錄
            if target_dir.exists():
                shutil.rmtree(target_dir)
            
            # 恢復備份
            shutil.copytree(backup_path, target_dir)
            logger.info(f"恢復備份成功: {backup_path} -> {target_dir}")
            
        except Exception as e:
            logger.error(f"恢復備份失敗: {e}")
            raise
    
    def generate_directory_report(self) -> Dict:
        """生成目錄報告"""
        report = {
            "timestamp": datetime.now().isoformat(),
            "base_directory": str(self.base_data_dir),
            "directories": {},
            "summary": {
                "total_size_mb": 0.0,
                "total_files": 0
            }
        }
        
        total_size = 0.0
        total_files = 0
        
        for data_type, info in self.get_all_directory_info().items():
            report["directories"][data_type.value] = {
                "path": str(info.path),
                "size_mb": round(info.size_mb, 2),
                "file_count": info.file_count,
                "last_modified": info.last_modified.isoformat()
            }
            
            total_size += info.size_mb
            total_files += info.file_count
        
        report["summary"]["total_size_mb"] = round(total_size, 2)
        report["summary"]["total_files"] = total_files
        
        # 保存報告
        report_file = self.directories[DataType.REPORTS] / "directory_report.json"
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            logger.info(f"目錄報告已保存: {report_file}")
        except Exception as e:
            logger.error(f"保存目錄報告失敗: {e}")
        
        return report
    
    def get_file_path(self, data_type: DataType, filename: str) -> Path:
        """獲取指定數據類型目錄下的文件路徑"""
        return self.directories[data_type] / filename
    
    def ensure_file_in_correct_directory(self, file_path: Union[str, Path]) -> Path:
        """確保文件在正確的目錄中"""
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        # 如果文件已在數據目錄中，直接返回
        if self._is_in_data_directory(file_path):
            return file_path
        
        # 確定數據類型並移動文件
        data_type = self._determine_data_type(file_path)
        if data_type:
            return self._move_file_to_data_directory(file_path, data_type)
        else:
            logger.warning(f"無法確定文件類型，保持原位置: {file_path}")
            return file_path


# 全局數據目錄管理器實例
_data_manager: Optional[DataDirectoryManager] = None


def get_data_directory_manager(base_data_dir: str = "data") -> DataDirectoryManager:
    """獲取數據目錄管理器實例"""
    global _data_manager
    if _data_manager is None:
        _data_manager = DataDirectoryManager(base_data_dir)
    return _data_manager


def get_data_path(data_type: DataType, filename: str = "") -> Path:
    """獲取數據路徑的便利函數"""
    manager = get_data_directory_manager()
    if filename:
        return manager.get_file_path(data_type, filename)
    else:
        return manager.get_directory(data_type)


async def main():
    """測試數據目錄管理器"""
    print("🧪 測試數據目錄管理器")
    
    try:
        manager = get_data_directory_manager()
        
        # 整理文件
        print("\n整理文件...")
        moved_files = manager.organize_files()
        for data_type, files in moved_files.items():
            if files:
                print(f"  {data_type.value}: {len(files)} 個文件")
        
        # 生成目錄報告
        print("\n生成目錄報告...")
        report = manager.generate_directory_report()
        print(f"  總大小: {report['summary']['total_size_mb']} MB")
        print(f"  總文件數: {report['summary']['total_files']}")
        
        # 清理臨時文件
        print("\n清理臨時文件...")
        cleaned = manager.cleanup_temp_files()
        print(f"  清理了 {len(cleaned)} 個臨時文件")
        
    except Exception as e:
        print(f"  ❌ 測試失敗: {e}")
    
    print("✅ 數據目錄管理器測試完成")


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
