#!/usr/bin/env python3
"""
Alpha 因子工廠 - 工業化生產 Alpha 因子
Alpha Factor Factory - Industrial production of Alpha factors
"""

import warnings
from abc import ABC, abstractmethod
from typing import Dict, List, Optional

import numpy as np
import pandas as pd
from scipy import stats
from scipy.stats import rankdata
from statsmodels.tsa.stattools import adfuller

warnings.filterwarnings("ignore")

from logging_config import get_logger

logger = get_logger(__name__)


class AlphaFactor(ABC):
    """Alpha 因子基類"""

    def __init__(self, name: str, description: str, lookback_window: int = 20):
        self.name = name
        self.description = description
        self.lookback_window = lookback_window
        self.factor_values = None
        self.factor_returns = None
        self.ic_series = None  # Information Coefficient

    @abstractmethod
    def calculate(self, data: pd.DataFrame) -> pd.Series:
        """計算因子值"""
        pass

    def evaluate(self, data: pd.DataFrame, forward_returns: pd.Series) -> Dict:
        """評估因子有效性"""
        try:
            factor_values = self.calculate(data)

            if factor_values is None or len(factor_values) == 0:
                return {"ic_mean": 0, "ic_std": 0, "ic_ir": 0, "valid": False}

            # 計算 Information Coefficient (IC)
            ic_series = []
            for i in range(len(factor_values) - 1):
                if i + 1 < len(forward_returns):
                    factor_rank = rankdata(
                        factor_values.iloc[max(0, i - self.lookback_window) : i + 1]
                    )
                    return_rank = rankdata(
                        forward_returns.iloc[max(0, i - self.lookback_window) : i + 1]
                    )

                    if len(factor_rank) > 5 and len(return_rank) > 5:
                        ic, _ = stats.spearmanr(factor_rank, return_rank)
                        if not np.isnan(ic):
                            ic_series.append(ic)

            if len(ic_series) == 0:
                return {"ic_mean": 0, "ic_std": 0, "ic_ir": 0, "valid": False}

            ic_series = pd.Series(ic_series)
            ic_mean = ic_series.mean()
            ic_std = ic_series.std()
            ic_ir = ic_mean / ic_std if ic_std > 0 else 0

            # 因子有效性判斷
            valid = abs(ic_mean) > 0.02 and abs(ic_ir) > 0.5

            return {
                "ic_mean": ic_mean,
                "ic_std": ic_std,
                "ic_ir": ic_ir,
                "ic_series": ic_series,
                "valid": valid,
                "factor_values": factor_values,
            }

        except Exception as e:
            logger.error(f"評估因子 {self.name} 失敗: {e}")
            return {"ic_mean": 0, "ic_std": 0, "ic_ir": 0, "valid": False}


class SpreadMomentumFactor(AlphaFactor):
    """價差動量因子"""

    def __init__(self, lookback_window: int = 20):
        super().__init__(
            name="spread_momentum", description="價差的短期動量信號", lookback_window=lookback_window
        )

    def calculate(self, data: pd.DataFrame) -> pd.Series:
        try:
            if "base_price" not in data.columns or "quote_price" not in data.columns:
                return pd.Series()

            log_spread = np.log(data["base_price"]) - np.log(data["quote_price"])

            # 計算動量：當前價差相對於移動平均的偏離
            ma = log_spread.rolling(self.lookback_window).mean()
            momentum = (log_spread - ma) / log_spread.rolling(self.lookback_window).std()

            return momentum.fillna(0)

        except Exception as e:
            logger.error(f"計算價差動量因子失敗: {e}")
            return pd.Series()


class VolatilityRegimeFactor(AlphaFactor):
    """波動率狀態因子"""

    def __init__(self, lookback_window: int = 20):
        super().__init__(
            name="volatility_regime", description="基於波動率狀態的均值回歸信號", lookback_window=lookback_window
        )

    def calculate(self, data: pd.DataFrame) -> pd.Series:
        try:
            if "base_price" not in data.columns or "quote_price" not in data.columns:
                return pd.Series()

            log_spread = np.log(data["base_price"]) - np.log(data["quote_price"])

            # 計算滾動波動率
            returns = log_spread.diff()
            rolling_vol = returns.rolling(self.lookback_window).std()

            # 波動率狀態：當前波動率相對於歷史分位數
            vol_percentile = rolling_vol.rolling(self.lookback_window * 2).rank(pct=True)

            # 在高波動率狀態下，均值回歸信號更強
            spread_zscore = (
                log_spread - log_spread.rolling(self.lookback_window).mean()
            ) / rolling_vol

            # 波動率調整的信號
            factor = spread_zscore * vol_percentile

            return factor.fillna(0)

        except Exception as e:
            logger.error(f"計算波動率狀態因子失敗: {e}")
            return pd.Series()


class CointegrationStrengthFactor(AlphaFactor):
    """共整合強度因子"""

    def __init__(self, lookback_window: int = 50):
        super().__init__(
            name="cointegration_strength", description="基於共整合強度的信號", lookback_window=lookback_window
        )

    def calculate(self, data: pd.DataFrame) -> pd.Series:
        try:
            if "base_price" not in data.columns or "quote_price" not in data.columns:
                return pd.Series()

            log_base = np.log(data["base_price"])
            log_quote = np.log(data["quote_price"])

            # 滾動計算共整合強度
            factor_values = []

            for i in range(len(data)):
                if i < self.lookback_window:
                    factor_values.append(0)
                    continue

                # 取滾動窗口數據
                window_base = log_base.iloc[i - self.lookback_window : i]
                window_quote = log_quote.iloc[i - self.lookback_window : i]

                try:
                    # 計算協整合係數
                    slope, intercept, r_value, p_value, std_err = stats.linregress(
                        window_base, window_quote
                    )

                    # 計算殘差
                    residuals = window_quote - (slope * window_base + intercept)

                    # ADF 檢定
                    adf_stat, adf_pvalue, _, _, _, _ = adfuller(residuals, maxlag=1)

                    # 共整合強度：結合 R² 和 ADF p-value
                    cointegration_strength = r_value**2 * (1 - adf_pvalue)

                    # 當前價差相對於均值的偏離
                    current_residual = log_quote.iloc[i] - (slope * log_base.iloc[i] + intercept)
                    residual_zscore = (current_residual - residuals.mean()) / residuals.std()

                    # 因子值：共整合強度 × 價差偏離度
                    factor_value = cointegration_strength * (-residual_zscore)  # 負號表示均值回歸

                    factor_values.append(factor_value)

                except:
                    factor_values.append(0)

            return pd.Series(factor_values, index=data.index).fillna(0)

        except Exception as e:
            logger.error(f"計算共整合強度因子失敗: {e}")
            return pd.Series()


class VolumeProfileFactor(AlphaFactor):
    """成交量分佈因子"""

    def __init__(self, lookback_window: int = 20):
        super().__init__(
            name="volume_profile", description="基於成交量分佈的價格回歸信號", lookback_window=lookback_window
        )

    def calculate(self, data: pd.DataFrame) -> pd.Series:
        try:
            if "base_volume" not in data.columns or "quote_volume" not in data.columns:
                return pd.Series()

            base_volume = data["base_volume"]
            quote_volume = data["quote_volume"]
            log_spread = np.log(data["base_price"]) - np.log(data["quote_price"])

            # 成交量比率
            volume_ratio = base_volume / (quote_volume + 1e-8)

            # 成交量加權價差
            vwap_spread = (log_spread * (base_volume + quote_volume)).rolling(
                self.lookback_window
            ).sum() / (base_volume + quote_volume).rolling(self.lookback_window).sum()

            # 當前價差相對於成交量加權價差的偏離
            spread_deviation = log_spread - vwap_spread

            # 成交量異常檢測
            volume_ma = (base_volume + quote_volume).rolling(self.lookback_window).mean()
            volume_anomaly = (base_volume + quote_volume) / volume_ma

            # 因子值：在成交量異常時，價差偏離的信號更強
            factor = spread_deviation * np.log(volume_anomaly + 1)

            return factor.fillna(0)

        except Exception as e:
            logger.error(f"計算成交量分佈因子失敗: {e}")
            return pd.Series()


class MarketRegimeFactor(AlphaFactor):
    """市場狀態因子"""

    def __init__(self, lookback_window: int = 30):
        super().__init__(
            name="market_regime", description="基於市場狀態的自適應信號", lookback_window=lookback_window
        )

    def calculate(self, data: pd.DataFrame) -> pd.Series:
        try:
            if "base_price" not in data.columns or "quote_price" not in data.columns:
                return pd.Series()

            base_returns = data["base_price"].pct_change()
            quote_returns = data["quote_price"].pct_change()
            log_spread = np.log(data["base_price"]) - np.log(data["quote_price"])

            # 市場狀態指標
            # 1. 相關性狀態
            rolling_corr = base_returns.rolling(self.lookback_window).corr(quote_returns)

            # 2. 波動率狀態
            base_vol = base_returns.rolling(self.lookback_window).std()
            quote_vol = quote_returns.rolling(self.lookback_window).std()
            vol_ratio = base_vol / quote_vol

            # 3. 趨勢狀態
            base_trend = base_returns.rolling(self.lookback_window).mean() / base_vol
            quote_trend = quote_returns.rolling(self.lookback_window).mean() / quote_vol

            # 市場狀態評分
            regime_score = (
                rolling_corr * 0.4 + (1 / vol_ratio) * 0.3 + (base_trend - quote_trend) * 0.3
            )

            # 價差信號
            spread_zscore = (
                log_spread - log_spread.rolling(self.lookback_window).mean()
            ) / log_spread.rolling(self.lookback_window).std()

            # 狀態調整的信號
            factor = spread_zscore * regime_score

            return factor.fillna(0)

        except Exception as e:
            logger.error(f"計算市場狀態因子失敗: {e}")
            return pd.Series()


class AlphaFactoryManager:
    """Alpha 因子工廠管理器"""

    def __init__(self):
        self.factors = {}
        self.factor_evaluations = {}

        # 註冊所有因子
        self._register_factors()

        logger.info("AlphaFactoryManager 初始化完成")

    def _register_factors(self):
        """註冊所有因子"""
        factor_classes = [
            SpreadMomentumFactor,
            VolatilityRegimeFactor,
            CointegrationStrengthFactor,
            VolumeProfileFactor,
            MarketRegimeFactor,
        ]

        for factor_class in factor_classes:
            factor = factor_class()
            self.factors[factor.name] = factor

        logger.info(f"註冊了 {len(self.factors)} 個 Alpha 因子")

    def calculate_all_factors(self, data: pd.DataFrame) -> pd.DataFrame:
        """計算所有因子值"""
        try:
            factor_matrix = pd.DataFrame(index=data.index)

            for name, factor in self.factors.items():
                try:
                    factor_values = factor.calculate(data)
                    if not factor_values.empty:
                        factor_matrix[name] = factor_values
                    else:
                        factor_matrix[name] = 0

                except Exception as e:
                    logger.error(f"計算因子 {name} 失敗: {e}")
                    factor_matrix[name] = 0

            # 標準化因子值
            factor_matrix = self._standardize_factors(factor_matrix)

            return factor_matrix

        except Exception as e:
            logger.error(f"計算所有因子失敗: {e}")
            return pd.DataFrame()

    def _standardize_factors(self, factor_matrix: pd.DataFrame) -> pd.DataFrame:
        """標準化因子值"""
        try:
            # Z-score 標準化
            standardized = factor_matrix.copy()

            for col in factor_matrix.columns:
                mean = factor_matrix[col].rolling(50, min_periods=10).mean()
                std = factor_matrix[col].rolling(50, min_periods=10).std()
                standardized[col] = (factor_matrix[col] - mean) / (std + 1e-8)

            # 限制極值
            standardized = standardized.clip(-3, 3)

            return standardized.fillna(0)

        except Exception as e:
            logger.error(f"標準化因子失敗: {e}")
            return factor_matrix

    def evaluate_factors(self, data: pd.DataFrame, forward_returns: pd.Series) -> Dict:
        """評估所有因子的有效性"""
        try:
            evaluations = {}

            for name, factor in self.factors.items():
                evaluation = factor.evaluate(data, forward_returns)
                evaluations[name] = evaluation

                logger.info(
                    f"因子 {name}: IC={evaluation['ic_mean']:.4f}, "
                    f"IR={evaluation['ic_ir']:.4f}, 有效={evaluation['valid']}"
                )

            self.factor_evaluations = evaluations
            return evaluations

        except Exception as e:
            logger.error(f"評估因子失敗: {e}")
            return {}

    def get_top_factors(self, n: int = 5) -> List[str]:
        """獲取表現最好的 N 個因子"""
        try:
            if not self.factor_evaluations:
                return list(self.factors.keys())[:n]

            # 按 IC IR 排序
            sorted_factors = sorted(
                self.factor_evaluations.items(),
                key=lambda x: abs(x[1].get("ic_ir", 0)),
                reverse=True,
            )

            # 只返回有效的因子
            top_factors = []
            for name, evaluation in sorted_factors:
                if evaluation.get("valid", False) and len(top_factors) < n:
                    top_factors.append(name)

            # 如果有效因子不足，補充其他因子
            if len(top_factors) < n:
                for name in self.factors.keys():
                    if name not in top_factors and len(top_factors) < n:
                        top_factors.append(name)

            return top_factors

        except Exception as e:
            logger.error(f"獲取頂級因子失敗: {e}")
            return list(self.factors.keys())[:n]

    def create_composite_factor(
        self, factor_names: List[str], weights: Optional[List[float]] = None
    ) -> pd.Series:
        """創建複合因子"""
        try:
            if not hasattr(self, "_last_factor_matrix"):
                logger.error("需要先計算因子矩陣")
                return pd.Series()

            factor_matrix = self._last_factor_matrix

            if weights is None:
                weights = [1.0 / len(factor_names)] * len(factor_names)

            composite_factor = pd.Series(0, index=factor_matrix.index)

            for name, weight in zip(factor_names, weights):
                if name in factor_matrix.columns:
                    composite_factor += weight * factor_matrix[name]

            return composite_factor

        except Exception as e:
            logger.error(f"創建複合因子失敗: {e}")
            return pd.Series()

    def get_factor_summary(self) -> pd.DataFrame:
        """獲取因子總結報告"""
        try:
            if not self.factor_evaluations:
                return pd.DataFrame()

            summary_data = []
            for name, evaluation in self.factor_evaluations.items():
                summary_data.append(
                    {
                        "factor_name": name,
                        "description": self.factors[name].description,
                        "ic_mean": evaluation.get("ic_mean", 0),
                        "ic_std": evaluation.get("ic_std", 0),
                        "ic_ir": evaluation.get("ic_ir", 0),
                        "valid": evaluation.get("valid", False),
                    }
                )

            summary_df = pd.DataFrame(summary_data)
            summary_df = summary_df.sort_values("ic_ir", key=abs, ascending=False)

            return summary_df

        except Exception as e:
            logger.error(f"獲取因子總結失敗: {e}")
            return pd.DataFrame()


# 全局因子工廠實例
_alpha_factory = None


def get_alpha_factory() -> AlphaFactoryManager:
    """獲取全局 Alpha 因子工廠實例"""
    global _alpha_factory
    if _alpha_factory is None:
        _alpha_factory = AlphaFactoryManager()
    return _alpha_factory


if __name__ == "__main__":
    # 測試 Alpha 因子工廠
    print("測試 Alpha 因子工廠...")

    # 生成模擬數據
    dates = pd.date_range("2024-01-01", periods=200, freq="H")
    np.random.seed(42)

    data = pd.DataFrame(
        {
            "base_price": 50000 + np.cumsum(np.random.normal(0, 100, 200)),
            "quote_price": 3000 + np.cumsum(np.random.normal(0, 10, 200)),
            "base_volume": np.random.exponential(1000, 200),
            "quote_volume": np.random.exponential(100, 200),
        },
        index=dates,
    )

    # 創建因子工廠
    factory = AlphaFactoryManager()

    # 計算所有因子
    factor_matrix = factory.calculate_all_factors(data)
    print(f"因子矩陣形狀: {factor_matrix.shape}")

    # 模擬未來收益
    future_returns = pd.Series(np.random.normal(0, 0.01, len(data)), index=data.index)

    # 評估因子
    evaluations = factory.evaluate_factors(data, future_returns)

    # 獲取因子總結
    summary = factory.get_factor_summary()
    print("\n因子總結:")
    print(summary)

    print("Alpha 因子工廠測試完成！")
