#!/usr/bin/env python3
"""
增強的事件總線深度集成 - 確保所有關鍵通信通過事件總線
Enhanced Event Bus Deep Integration - Ensure all critical communications go through event bus
"""

import asyncio
from datetime import datetime
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

from global_event_bus import get_global_event_bus, Event, EventType, publish_event
from logging_config import get_logger

logger = get_logger(__name__)


@dataclass
class SystemMetrics:
    """系統指標"""
    timestamp: datetime
    total_strategies: int
    active_strategies: int
    total_trades: int
    total_pnl: float
    portfolio_value: float
    risk_exposure: float
    health_scores: Dict[str, float]


class EnhancedEventIntegrator:
    """增強的事件集成器"""
    
    def __init__(self):
        self.event_bus = get_global_event_bus()
        self.metrics_history: List[SystemMetrics] = []
        self.last_health_scores: Dict[str, float] = {}
        
        # 設置事件監聽
        self._setup_comprehensive_event_listeners()
        
        logger.info("增強事件集成器初始化完成")
    
    def _setup_comprehensive_event_listeners(self):
        """設置全面的事件監聽器"""
        # 監聽所有關鍵事件
        self.event_bus.subscribe(
            [
                EventType.MARKET_DATA_UPDATE,
                EventType.SIGNAL_GENERATED,
                EventType.ORDER_PLACED,
                EventType.ORDER_FILLED,
                EventType.POSITION_OPENED,
                EventType.POSITION_CLOSED,
                EventType.STRATEGY_HEALTH_CHANGED,
                EventType.PORTFOLIO_REBALANCE,
                EventType.RISK_LIMIT_EXCEEDED,
                EventType.CORRELATION_ALERT,
                EventType.HEALTH_CHECK
            ],
            self._on_comprehensive_event
        )
    
    def _on_comprehensive_event(self, event: Event):
        """處理全面的事件"""
        try:
            # 記錄事件
            logger.debug(f"事件處理: {event.event_type.value} from {event.source}")
            
            # 根據事件類型進行特定處理
            if event.event_type == EventType.MARKET_DATA_UPDATE:
                self._handle_market_data_update(event)
            elif event.event_type == EventType.SIGNAL_GENERATED:
                self._handle_signal_generated(event)
            elif event.event_type == EventType.ORDER_FILLED:
                self._handle_order_filled(event)
            elif event.event_type == EventType.STRATEGY_HEALTH_CHANGED:
                self._handle_health_changed(event)
            elif event.event_type == EventType.PORTFOLIO_REBALANCE:
                self._handle_portfolio_rebalance(event)
            elif event.event_type == EventType.RISK_LIMIT_EXCEEDED:
                self._handle_risk_alert(event)
            elif event.event_type == EventType.CORRELATION_ALERT:
                self._handle_correlation_alert(event)
            elif event.event_type == EventType.HEALTH_CHECK:
                self._handle_health_check(event)
                
        except Exception as e:
            logger.error(f"事件處理失敗: {e}")
    
    def _handle_market_data_update(self, event: Event):
        """處理市場數據更新事件"""
        symbol = event.data.get('symbol', 'unknown')
        logger.debug(f"市場數據更新: {symbol}")
        
        # 可以在這裡添加市場數據分析邏輯
        # 例如：檢測異常波動、計算技術指標等
    
    def _handle_signal_generated(self, event: Event):
        """處理信號生成事件"""
        signals_count = event.data.get('signals_count', 0)
        logger.info(f"信號生成: {signals_count} 個信號 from {event.source}")
        
        # 發布信號統計事件
        publish_event(
            EventType.HEALTH_CHECK,
            "enhanced_event_integrator",
            {
                "metric_type": "signal_generation",
                "signals_count": signals_count,
                "source": event.source,
                "timestamp": datetime.now().isoformat()
            }
        )
    
    def _handle_order_filled(self, event: Event):
        """處理訂單成交事件"""
        strategy_id = event.data.get('strategy_id', 'unknown')
        pnl = event.data.get('pnl', 0.0)
        
        logger.info(f"訂單成交: {strategy_id}, PnL: ${pnl:.2f}")
        
        # 發布交易統計事件
        publish_event(
            EventType.HEALTH_CHECK,
            "enhanced_event_integrator",
            {
                "metric_type": "trade_execution",
                "strategy_id": strategy_id,
                "pnl": pnl,
                "timestamp": datetime.now().isoformat()
            }
        )
    
    def _handle_health_changed(self, event: Event):
        """處理健康分數變化事件"""
        strategy_id = event.data.get('strategy_id', 'unknown')
        new_health = event.data.get('new_health', event.data.get('health_score', 0.5))
        old_health = self.last_health_scores.get(strategy_id, 0.5)
        
        # 更新健康分數記錄
        self.last_health_scores[strategy_id] = new_health
        
        # 檢查健康分數變化
        health_change = new_health - old_health
        if abs(health_change) > 0.1:
            severity = "critical" if new_health < 0.3 else "warning" if new_health < 0.5 else "info"
            
            logger.info(f"策略健康變化: {strategy_id} {old_health:.2f} → {new_health:.2f} ({health_change:+.2f})")
            
            # 如果健康分數過低，發布風險警報
            if new_health < 0.3:
                publish_event(
                    EventType.RISK_LIMIT_EXCEEDED,
                    "enhanced_event_integrator",
                    {
                        "risk_type": "low_strategy_health",
                        "strategy_id": strategy_id,
                        "health_score": new_health,
                        "severity": severity,
                        "timestamp": datetime.now().isoformat()
                    }
                )
    
    def _handle_portfolio_rebalance(self, event: Event):
        """處理組合重新平衡事件"""
        actions = event.data.get('actions', [])
        total_change = event.data.get('total_change', 0.0)
        
        logger.info(f"組合重新平衡: {len(actions)} 個調整, 總變化: {total_change:.2%}")
        
        # 發布重新平衡統計
        publish_event(
            EventType.HEALTH_CHECK,
            "enhanced_event_integrator",
            {
                "metric_type": "portfolio_rebalance",
                "adjustments_count": len(actions),
                "total_change": total_change,
                "timestamp": datetime.now().isoformat()
            }
        )
    
    def _handle_risk_alert(self, event: Event):
        """處理風險警報事件"""
        risk_type = event.data.get('risk_type', 'unknown')
        severity = event.data.get('severity', 'medium')
        
        logger.warning(f"風險警報: {risk_type} (嚴重程度: {severity})")
        
        # 可以在這裡實施自動風險控制措施
        if severity == "critical":
            self._trigger_emergency_procedures(event)
    
    def _handle_correlation_alert(self, event: Event):
        """處理相關性警報事件"""
        strategy1 = event.data.get('strategy1', '')
        strategy2 = event.data.get('strategy2', '')
        correlation = event.data.get('correlation', 0.0)
        
        logger.warning(f"相關性警報: {strategy1} ↔ {strategy2} = {correlation:.3f}")
        
        # 發布相關性統計
        publish_event(
            EventType.HEALTH_CHECK,
            "enhanced_event_integrator",
            {
                "metric_type": "correlation_alert",
                "strategy_pair": f"{strategy1}_{strategy2}",
                "correlation": correlation,
                "timestamp": datetime.now().isoformat()
            }
        )
    
    def _handle_health_check(self, event: Event):
        """處理健康檢查事件"""
        metric_type = event.data.get('metric_type', 'general')
        logger.debug(f"健康檢查: {metric_type}")
        
        # 可以在這裡收集和分析系統指標
    
    def _trigger_emergency_procedures(self, event: Event):
        """觸發緊急程序"""
        logger.critical("觸發緊急風險控制程序")
        
        # 發布緊急停止事件
        publish_event(
            EventType.EMERGENCY_STOP,
            "enhanced_event_integrator",
            {
                "reason": "critical_risk_detected",
                "original_event": event.data,
                "timestamp": datetime.now().isoformat()
            }
        )
    
    def publish_system_metrics(self, metrics: SystemMetrics):
        """發布系統指標"""
        self.metrics_history.append(metrics)
        
        # 保持歷史記錄大小
        if len(self.metrics_history) > 1000:
            self.metrics_history = self.metrics_history[-1000:]
        
        # 發布指標事件
        publish_event(
            EventType.HEALTH_CHECK,
            "enhanced_event_integrator",
            {
                "metric_type": "system_metrics",
                "total_strategies": metrics.total_strategies,
                "active_strategies": metrics.active_strategies,
                "total_trades": metrics.total_trades,
                "total_pnl": metrics.total_pnl,
                "portfolio_value": metrics.portfolio_value,
                "risk_exposure": metrics.risk_exposure,
                "timestamp": metrics.timestamp.isoformat()
            }
        )
    
    def get_event_statistics(self) -> Dict[str, Any]:
        """獲取事件統計"""
        event_bus_stats = self.event_bus.get_stats()
        
        return {
            "event_bus_stats": event_bus_stats,
            "metrics_history_size": len(self.metrics_history),
            "tracked_strategies": len(self.last_health_scores),
            "last_health_scores": self.last_health_scores.copy()
        }


# 全局事件集成器實例
_enhanced_event_integrator = None


def get_enhanced_event_integrator() -> EnhancedEventIntegrator:
    """獲取增強事件集成器實例"""
    global _enhanced_event_integrator
    if _enhanced_event_integrator is None:
        _enhanced_event_integrator = EnhancedEventIntegrator()
    return _enhanced_event_integrator


def publish_market_data_update(symbol: str, price: float, volume: float = 0.0):
    """便利函數：發布市場數據更新"""
    publish_event(
        EventType.MARKET_DATA_UPDATE,
        "market_data_provider",
        {
            "symbol": symbol,
            "price": price,
            "volume": volume,
            "timestamp": datetime.now().isoformat()
        }
    )


def publish_strategy_signal(strategy_id: str, signals: List[Dict[str, Any]]):
    """便利函數：發布策略信號"""
    publish_event(
        EventType.SIGNAL_GENERATED,
        strategy_id,
        {
            "signals_count": len(signals),
            "signals": signals[:5],  # 只發布前5個信號的詳情
            "timestamp": datetime.now().isoformat()
        }
    )


def publish_trade_execution(strategy_id: str, symbol: str, side: str, 
                          amount: float, price: float, pnl: float = 0.0):
    """便利函數：發布交易執行"""
    publish_event(
        EventType.ORDER_FILLED,
        "trading_executor",
        {
            "strategy_id": strategy_id,
            "symbol": symbol,
            "side": side,
            "amount": amount,
            "price": price,
            "pnl": pnl,
            "timestamp": datetime.now().isoformat()
        }
    )


if __name__ == "__main__":
    # 測試增強事件集成
    print("🧪 增強事件集成測試")
    
    async def test_enhanced_integration():
        integrator = get_enhanced_event_integrator()
        
        # 啟動事件總線
        integrator.event_bus.start()
        
        # 測試各種事件發布
        publish_market_data_update("BTC/USDT:USDT", 50000.0, 1.5)
        publish_strategy_signal("test_strategy", [{"signal": "buy", "strength": 0.8}])
        publish_trade_execution("test_strategy", "BTC/USDT:USDT", "buy", 0.001, 50000.0, 100.0)
        
        # 等待事件處理
        await asyncio.sleep(0.5)
        
        # 獲取統計
        stats = integrator.get_event_statistics()
        print(f"✅ 事件統計: {stats}")
        
        integrator.event_bus.stop()
    
    asyncio.run(test_enhanced_integration())
    print("✅ 增強事件集成測試完成")
