#!/usr/bin/env python3
"""
緊急語法錯誤修復工具
Emergency Syntax Error Fixer
"""

import re
from pathlib import Path
from typing import List


class EmergencySyntaxFixer:
    """緊急語法錯誤修復器"""

    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.fixes_applied = 0

    def fix_critical_syntax_errors(self):
        """修復關鍵語法錯誤"""

        # 定義需要修復的文件和對應的修復規則
        critical_fixes = {
            "main.py": [
                # 修復描述字符串中的語法問題
                ('description="配對交易機器人 - 企業級量化交易平台",', 'description="配對交易機器人 - 企業級量化交易平台",')
            ],
            "advanced_code_fixer.py": [
                # 修復條件語句語法
                (
                    'if "for _ in range(" in line and \'f"\' in lines[i + 1 : i + 5]:',
                    'if "for _ in range(" in line and \'f"\' in str(lines[i + 1 : i + 5]):',
                )
            ],
            "final_code_quality_fixer.py": [
                # 修復空的any()調用
                ("if any(", "if any([")
            ],
            "fix_loop_variables.py": [
                # 修復條件語句語法
                ('if "for _ in range(" in line:', 'if "for _ in range(" in line:')
            ],
            "fix_code_quality.py": [
                # 修復空的條件語句
                ("if (", "if True and (")
            ],
        }

        for file_path, fixes in critical_fixes.items():
            full_path = self.project_root / file_path
            if full_path.exists():
                try:
                    with open(full_path, "r", encoding="utf-8") as f:
                        content = f.read()

                    new_content = content
                    for old_text, new_text in fixes:
                        if old_text in new_content:
                            new_content = new_content.replace(old_text, new_text)

                    if new_content != content:
                        with open(full_path, "w", encoding="utf-8") as f:
                            f.write(new_content)
                        self.fixes_applied += 1
                        print(f"修復關鍵語法錯誤: {file_path}")

                except Exception as e:
                    print(f"修復關鍵語法錯誤失敗 {file_path}: {e}")

    def fix_incomplete_function_definitions(self):
        """修復不完整的函數定義"""
        python_files = list(self.project_root.glob("**/*.py"))

        for file_path in python_files:
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    content = f.read()

                lines = content.split("\n")
                new_lines = []
                changes_made = False

                for i, line in enumerate(lines):
                    # 檢查不完整的函數定義
                    if re.match(r"^\s*def\s+\w+\(\)\s*$", line.strip()):
                        # 如果下一行不是函數體，添加pass
                        if i + 1 < len(lines):
                            next_line = lines[i + 1].strip()
                            if not next_line or not (
                                next_line.startswith('"""')
                                or next_line.startswith("pass")
                                or next_line.startswith("return")
                            ):
                                new_lines.append(line)
                                new_lines.append(
                                    line[: len(line) - len(line.lstrip())] + "    pass"
                                )
                                changes_made = True
                                continue

                    # 檢查不完整的類定義
                    if re.match(r"^\s*class\s+\w+.*:\s*$", line.strip()):
                        # 如果下一行不是類體，添加pass
                        if i + 1 < len(lines):
                            next_line = lines[i + 1].strip()
                            if not next_line or not (
                                next_line.startswith('"""')
                                or next_line.startswith("pass")
                                or next_line.startswith("def")
                            ):
                                new_lines.append(line)
                                new_lines.append(
                                    line[: len(line) - len(line.lstrip())] + "    pass"
                                )
                                changes_made = True
                                continue

                    new_lines.append(line)

                if changes_made:
                    new_content = "\n".join(new_lines)
                    with open(file_path, "w", encoding="utf-8") as f:
                        f.write(new_content)
                    self.fixes_applied += 1
                    print(f"修復不完整定義: {file_path}")

            except Exception as e:
                print(f"修復不完整定義失敗 {file_path}: {e}")

    def fix_incomplete_imports(self):
        """修復不完整的導入語句"""
        python_files = list(self.project_root.glob("**/*.py"))

        for file_path in python_files:
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    content = f.read()

                new_content = content
                changes_made = False

                # 修復不完整的from import語句
                patterns = [
                    (r"from\s+(\w+)\s+import\s+\(\)\s*$", r"from \1 import *"),
                    (r"from\s+([.\w]+)\s+import\s+\(\)\s*$", r"# from \1 import  # 修復不完整導入"),
                ]

                for pattern, replacement in patterns:
                    if re.search(pattern, new_content, re.MULTILINE):
                        new_content = re.sub(pattern, replacement, new_content, flags=re.MULTILINE)
                        changes_made = True

                if changes_made:
                    with open(file_path, "w", encoding="utf-8") as f:
                        f.write(new_content)
                    self.fixes_applied += 1
                    print(f"修復不完整導入: {file_path}")

            except Exception as e:
                print(f"修復不完整導入失敗 {file_path}: {e}")

    def fix_incomplete_expressions(self):
        """修復不完整的表達式"""
        python_files = list(self.project_root.glob("**/*.py"))

        for file_path in python_files:
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    content = f.read()

                lines = content.split("\n")
                new_lines = []
                changes_made = False

                for line in lines:
                    # 修復不完整的條件語句
                    if re.match(r"^\s*if\s+\(\s*\)\s*:", line):
                        new_lines.append(line.replace("if ():", "if True:"))
                        changes_made = True
                    # 修復不完整的函數調用
                    elif re.match(r"^\s*\w+\(\)\s*$", line.strip()) and not line.strip().endswith(
                        ":"
                    ):
                        new_lines.append(line + "  # 修復不完整調用")
                        changes_made = True
                    else:
                        new_lines.append(line)

                if changes_made:
                    new_content = "\n".join(new_lines)
                    with open(file_path, "w", encoding="utf-8") as f:
                        f.write(new_content)
                    self.fixes_applied += 1
                    print(f"修復不完整表達式: {file_path}")

            except Exception as e:
                print(f"修復不完整表達式失敗 {file_path}: {e}")

    def run_emergency_fixes(self):
        """運行緊急修復"""
        print("🚨 開始緊急語法錯誤修復...")

        # 按順序執行修復
        self.fix_critical_syntax_errors()
        self.fix_incomplete_function_definitions()
        self.fix_incomplete_imports()
        self.fix_incomplete_expressions()

        print(f"✅ 緊急語法修復完成，共應用 {self.fixes_applied} 個修復")


def main():
    """主函數"""
    project_root = Path(__file__).parent
    fixer = EmergencySyntaxFixer(str(project_root))
    fixer.run_emergency_fixes()


if __name__ == "__main__":
    main()  # 修復不完整調用
