#!/usr/bin/env python3
"""
簡單路由測試 - 驗證監控端點是否正確註冊
Simple Route Test - Verify monitoring endpoints are correctly registered
"""

import sys
import time
import requests
import threading
from datetime import datetime

# 添加項目根目錄到路徑
sys.path.append('.')

from health_server import HealthServer
from logging_config import setup_logging, get_logger

# 設置日誌
setup_logging()
logger = get_logger(__name__)


def test_health_server_routes():
    """測試健康服務器路由"""
    print("🔧 測試健康服務器路由...")
    
    # 啟動健康檢查服務器
    health_server = HealthServer(port=8083)
    
    # 在後台線程中啟動服務器
    server_thread = threading.Thread(target=health_server.start, daemon=True)
    server_thread.start()
    
    # 等待服務器啟動
    time.sleep(3)
    
    # 測試所有端點
    endpoints = [
        '/health',
        '/status', 
        '/metrics',
        '/memory',
        '/performance',
        '/portfolio',
        '/dashboard'
    ]
    
    results = {}
    
    for endpoint in endpoints:
        try:
            print(f"測試端點: {endpoint}")
            response = requests.get(f"http://localhost:8083{endpoint}", timeout=10)
            
            results[endpoint] = {
                'status_code': response.status_code,
                'success': response.status_code == 200,
                'content_length': len(response.text) if response.text else 0
            }
            
            print(f"  狀態碼: {response.status_code}")
            print(f"  內容長度: {results[endpoint]['content_length']}")
            
            if response.status_code == 200:
                print(f"  ✅ {endpoint} 正常")
            else:
                print(f"  ❌ {endpoint} 失敗")
                
        except Exception as e:
            results[endpoint] = {
                'status_code': 0,
                'success': False,
                'error': str(e)
            }
            print(f"  ❌ {endpoint} 異常: {e}")
    
    # 停止服務器
    try:
        health_server.stop()
    except:
        pass
    
    # 總結結果
    print("\n" + "=" * 50)
    print("路由測試結果總結:")
    print("=" * 50)
    
    working_endpoints = [ep for ep, result in results.items() if result['success']]
    failing_endpoints = [ep for ep, result in results.items() if not result['success']]
    
    print(f"正常端點 ({len(working_endpoints)}):")
    for ep in working_endpoints:
        print(f"  ✅ {ep}")
    
    print(f"\n失敗端點 ({len(failing_endpoints)}):")
    for ep in failing_endpoints:
        error = results[ep].get('error', f"HTTP {results[ep]['status_code']}")
        print(f"  ❌ {ep}: {error}")
    
    success_rate = len(working_endpoints) / len(endpoints) * 100
    print(f"\n成功率: {success_rate:.1f}%")
    
    if success_rate >= 70:
        print("🎉 路由測試基本通過！")
        assert True, "路由測試通過"
    else:
        print("⚠️ 路由測試需要改進")
        assert success_rate >= 50, f"路由測試成功率過低: {success_rate}%"

    # 驗證至少有一些端點工作正常
    assert len(working_endpoints) > 0, "至少應該有一個端點正常工作"


if __name__ == "__main__":
    try:
        results = test_health_server_routes()
        
        # 保存結果
        import json
        from pathlib import Path
        
        results_dir = Path("test_results")
        results_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = results_dir / f"route_test_{timestamp}.json"
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'test_type': 'route_test',
                'results': results
            }, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n📁 測試結果已保存: {results_file}")
        
    except Exception as e:
        logger.error(f"路由測試失敗: {e}")
        import traceback
        traceback.print_exc()
