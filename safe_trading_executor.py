#!/usr/bin/env python3
"""
安全交易執行器 - 解決單邊風險問題
Safe Trading Executor - Solves unilateral risk issues
"""

import time
from datetime import datetime
from typing import Dict, Any, Optional, Tuple
from dataclasses import dataclass

from trading_exceptions import PartialFillError, ExecutionError, NetworkError
from logging_config import get_logger

logger = get_logger(__name__)


@dataclass
class OrderResult:
    """訂單結果"""
    success: bool
    order_id: Optional[str] = None
    symbol: str = ""
    side: str = ""
    amount: float = 0.0
    price: float = 0.0
    error: Optional[str] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


class SafeTradingExecutor:
    """安全交易執行器 - 防止單邊風險"""
    
    def __init__(self, exchange, position_size_usd: float = 1000):
        """
        初始化安全交易執行器
        
        Args:
            exchange: CCXT交易所實例
            position_size_usd: 倉位大小（美元）
        """
        self.exchange = exchange
        self.position_size_usd = position_size_usd
        self.compensation_timeout = 30  # 補償操作超時時間（秒）
        self.max_compensation_retries = 3  # 最大補償重試次數
        
        logger.info(f"安全交易執行器初始化完成，倉位大小: ${position_size_usd}")
    
    def place_market_order_safe(self, symbol: str, side: str, amount: float) -> OrderResult:
        """
        安全的市價單下單
        
        Args:
            symbol: 交易對
            side: 方向 ('buy' 或 'sell')
            amount: 數量
            
        Returns:
            OrderResult: 訂單結果
        """
        try:
            logger.info(f"執行市價單: {symbol} {side} {amount}")
            
            # 執行訂單
            order = self.exchange.create_market_order(symbol, side, amount)
            
            result = OrderResult(
                success=True,
                order_id=order.get('id'),
                symbol=symbol,
                side=side,
                amount=amount,
                price=order.get('price', 0.0)
            )
            
            logger.info(f"訂單成功: {symbol} {side} {amount}, ID: {result.order_id}")
            return result
            
        except Exception as e:
            logger.error(f"訂單失敗: {symbol} {side} {amount}, 錯誤: {e}")
            
            return OrderResult(
                success=False,
                symbol=symbol,
                side=side,
                amount=amount,
                error=str(e)
            )
    
    def compensate_failed_order(self, successful_order: OrderResult, 
                              max_retries: int = None) -> OrderResult:
        """
        補償失敗的訂單 - 平掉已成功的單邊倉位
        
        Args:
            successful_order: 已成功的訂單
            max_retries: 最大重試次數
            
        Returns:
            OrderResult: 補償訂單結果
        """
        if max_retries is None:
            max_retries = self.max_compensation_retries
        
        # 計算反向操作
        compensation_side = 'sell' if successful_order.side == 'buy' else 'buy'
        
        logger.critical(f"開始補償操作: {successful_order.symbol} {compensation_side} {successful_order.amount}")
        
        for attempt in range(max_retries):
            try:
                # 執行補償訂單
                compensation_order = self.exchange.create_market_order(
                    successful_order.symbol,
                    compensation_side,
                    successful_order.amount
                )
                
                result = OrderResult(
                    success=True,
                    order_id=compensation_order.get('id'),
                    symbol=successful_order.symbol,
                    side=compensation_side,
                    amount=successful_order.amount,
                    price=compensation_order.get('price', 0.0)
                )
                
                logger.critical(f"補償成功: {successful_order.symbol} {compensation_side} {successful_order.amount}")
                return result
                
            except Exception as e:
                logger.error(f"補償嘗試 {attempt + 1}/{max_retries} 失敗: {e}")
                
                if attempt < max_retries - 1:
                    # 等待後重試
                    wait_time = (attempt + 1) * 2  # 指數退避
                    logger.warning(f"等待 {wait_time} 秒後重試補償...")
                    time.sleep(wait_time)
                else:
                    # 最後一次嘗試失敗
                    logger.critical(f"補償完全失敗！存在單邊風險: {successful_order.symbol} {successful_order.side} {successful_order.amount}")
                    
                    return OrderResult(
                        success=False,
                        symbol=successful_order.symbol,
                        side=compensation_side,
                        amount=successful_order.amount,
                        error=f"補償失敗: {e}"
                    )
        
        # 不應該到達這裡
        return OrderResult(success=False, error="未知補償錯誤")
    
    def execute_pair_trade_atomic(self, base_symbol: str, quote_symbol: str,
                                base_side: str, quote_side: str,
                                base_amount: float, quote_amount: float) -> Dict[str, Any]:
        """
        原子性配對交易執行 - 要麼全部成功，要麼全部失敗
        
        Args:
            base_symbol: 基礎交易對
            quote_symbol: 報價交易對
            base_side: 基礎交易對方向
            quote_side: 報價交易對方向
            base_amount: 基礎交易對數量
            quote_amount: 報價交易對數量
            
        Returns:
            Dict: 執行結果
        """
        logger.info(f"開始原子性配對交易: {base_symbol} {base_side} {base_amount}, {quote_symbol} {quote_side} {quote_amount}")
        
        base_order = None
        quote_order = None
        compensation_order = None
        
        try:
            # 第一步：執行第一個訂單
            base_order = self.place_market_order_safe(base_symbol, base_side, base_amount)
            
            if not base_order.success:
                # 第一個訂單就失敗，直接返回
                logger.error(f"第一個訂單失敗: {base_order.error}")
                return {
                    'success': False,
                    'error': f"第一個訂單失敗: {base_order.error}",
                    'base_order': base_order,
                    'quote_order': None,
                    'compensation_order': None,
                    'timestamp': datetime.now().isoformat()
                }
            
            logger.info(f"第一個訂單成功: {base_symbol} {base_side} {base_amount}")
            
            # 第二步：執行第二個訂單
            quote_order = self.place_market_order_safe(quote_symbol, quote_side, quote_amount)
            
            if not quote_order.success:
                # 第二個訂單失敗，必須補償第一個
                logger.critical(f"第二個訂單失敗，開始補償: {quote_order.error}")
                
                compensation_order = self.compensate_failed_order(base_order)
                
                if compensation_order.success:
                    logger.critical("補償成功，配對交易完全失敗但無單邊風險")
                    error_msg = f"配對交易失敗但已補償: 第二個訂單失敗 - {quote_order.error}"
                else:
                    logger.critical("補償失敗！存在嚴重的單邊風險！")
                    error_msg = f"嚴重錯誤：配對交易失敗且補償失敗，存在單邊風險！第二個訂單錯誤: {quote_order.error}, 補償錯誤: {compensation_order.error}"
                
                # 拋出特殊異常
                raise PartialFillError(
                    error_msg,
                    successful_order=base_order.__dict__,
                    failed_order_info=quote_order.__dict__,
                    context={
                        'compensation_order': compensation_order.__dict__ if compensation_order else None,
                        'compensation_success': compensation_order.success if compensation_order else False
                    }
                )
            
            # 兩個訂單都成功
            logger.info(f"配對交易完全成功: {base_symbol} {base_side}, {quote_symbol} {quote_side}")
            
            return {
                'success': True,
                'base_order': base_order,
                'quote_order': quote_order,
                'compensation_order': None,
                'timestamp': datetime.now().isoformat()
            }
            
        except PartialFillError:
            # 重新拋出已處理的部分成交異常
            raise
            
        except Exception as e:
            # 其他未預期的異常
            logger.critical(f"配對交易執行過程中發生未預期異常: {e}")
            
            # 如果第一個訂單成功但出現其他異常，也需要補償
            if base_order and base_order.success:
                logger.critical("檢測到第一個訂單成功但出現異常，執行緊急補償")
                try:
                    compensation_order = self.compensate_failed_order(base_order)
                except Exception as comp_error:
                    logger.critical(f"緊急補償也失敗: {comp_error}")
            
            raise ExecutionError(f"配對交易執行異常: {e}")
    
    def enter_long_base_short_quote_safe(self, base_symbol: str, quote_symbol: str,
                                       base_amount: float, quote_amount: float) -> Dict[str, Any]:
        """安全的做多base做空quote"""
        return self.execute_pair_trade_atomic(
            base_symbol, quote_symbol,
            'buy', 'sell',
            base_amount, quote_amount
        )
    
    def enter_short_base_long_quote_safe(self, base_symbol: str, quote_symbol: str,
                                       base_amount: float, quote_amount: float) -> Dict[str, Any]:
        """安全的做空base做多quote"""
        return self.execute_pair_trade_atomic(
            base_symbol, quote_symbol,
            'sell', 'buy',
            base_amount, quote_amount
        )


if __name__ == "__main__":
    # 測試代碼
    print("🧪 安全交易執行器測試")
    print("注意：這只是結構測試，需要真實的交易所實例才能完整測試")
    
    # 模擬測試
    class MockExchange:
        def create_market_order(self, symbol, side, amount):
            return {'id': 'test_order_123', 'price': 50000}
    
    executor = SafeTradingExecutor(MockExchange(), 1000)
    print(f"✅ 安全交易執行器創建成功")
