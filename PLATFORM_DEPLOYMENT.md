# 量化交易平台部署指南 / Quantitative Trading Platform Deployment Guide

本指南將幫助您部署一個完整的量化交易平台，包括投資組合管理、機器學習預測、實時監控和容器化部署。

This guide helps you deploy a complete quantitative trading platform with portfolio management, ML prediction, real-time monitoring, and containerized deployment.

## 🏗️ 平台架構 / Platform Architecture

### 核心組件 / Core Components

```
┌─────────────────────────────────────────────────────────────┐
│                    量化交易平台 v2.0                          │
│                Quantitative Trading Platform v2.0           │
└─────────────────────────────────────────────────────────────┘
                                │
                ┌───────────────┼───────────────┐
                │               │               │
        ┌───────▼──────┐ ┌─────▼─────┐ ┌──────▼──────┐
        │ 投資組合管理器  │ │ 機器學習引擎 │ │  監控儀表板   │
        │Portfolio Mgr  │ │ ML Engine  │ │ Dashboard   │
        └───────┬──────┘ └─────┬─────┘ └──────┬──────┘
                │               │               │
        ┌───────▼──────┐ ┌─────▼─────┐ ┌──────▼──────┐
        │ 配對交易機器人  │ │ 特徵工程器  │ │ 警報管理器   │
        │Pair Bot 1-N  │ │Feature Eng │ │Alert Mgr   │
        └───────┬──────┘ └─────┬─────┘ └──────┬──────┘
                │               │               │
        ┌───────▼──────┐ ┌─────▼─────┐ ┌──────▼──────┐
        │ 數據庫管理器   │ │ 安全管理器  │ │ 健康檢查器   │
        │Database Mgr  │ │Security   │ │Health Check │
        └──────────────┘ └───────────┘ └─────────────┘
```

### 技術棧 / Technology Stack

- **後端**: Python 3.9+, SQLite/PostgreSQL, Redis
- **前端**: HTML5, JavaScript, Chart.js
- **機器學習**: scikit-learn, XGBoost, TensorFlow
- **監控**: Prometheus, Grafana
- **容器化**: Docker, Docker Compose
- **Web服務**: Flask, Nginx
- **消息隊列**: Redis Pub/Sub

## 🚀 快速開始 / Quick Start

### 1. 環境準備

```bash
# 克隆項目
git clone <repository-url>
cd Pair_trading_v2

# 設置執行權限
chmod +x start.sh

# 快速啟動（推薦）
./start.sh portfolio 5m
```

### 2. 訪問儀表板

- **主儀表板**: http://localhost:8080/dashboard
- **健康檢查**: http://localhost:8080/health
- **API文檔**: http://localhost:8080/status

## 📦 容器化部署 / Containerized Deployment

### 使用 Docker Compose（推薦）

```bash
# 構建並啟動所有服務
docker-compose up -d

# 查看服務狀態
docker-compose ps

# 查看日誌
docker-compose logs -f trading-platform

# 停止服務
docker-compose down
```

### 服務端口映射

| 服務 | 端口 | 描述 |
|------|------|------|
| 交易平台 | 8080 | 主應用和儀表板 |
| PostgreSQL | 5432 | 數據庫 |
| Redis | 6379 | 緩存和消息隊列 |
| Grafana | 3000 | 監控儀表板 |
| Prometheus | 9090 | 指標收集 |
| Nginx | 80/443 | 反向代理 |

## 🗄️ 數據庫配置 / Database Configuration

### SQLite（默認，適合開發）

```json
{
  "database": {
    "type": "sqlite",
    "path": "data/trading_platform.db"
  }
}
```

### PostgreSQL（推薦生產環境）

```json
{
  "database": {
    "type": "postgresql",
    "host": "postgres",
    "port": 5432,
    "database": "trading_platform",
    "username": "trading_user",
    "password": "trading_password"
  }
}
```

### 數據庫遷移

```bash
# 初始化數據庫
python3 -c "from database_manager import DatabaseManager; DatabaseManager()"

# 數據遷移（如果需要）
python3 database_migration.py
```

## 🤖 機器學習配置 / Machine Learning Configuration

### 特徵工程

```python
# 生成特徵
from ml_predictor.feature_engineering import FeatureEngineer

feature_engineer = FeatureEngineer()
features = feature_engineer.generate_features(price_data, pair)
```

### 模型訓練

```bash
# 訓練預測模型
python3 ml_predictor/train_model.py --data data/historical_data.csv

# 評估模型
python3 ml_predictor/evaluate_model.py --model ml_models/latest_model.pkl
```

### 模型部署

```json
{
  "ml_prediction": {
    "enabled": true,
    "model_path": "ml_models/latest_model.pkl",
    "feature_importance_threshold": 0.01,
    "prediction_confidence_threshold": 0.7
  }
}
```

## 📊 監控與警報 / Monitoring & Alerting

### Grafana 儀表板

1. 訪問 http://localhost:3000
2. 登錄 (admin/admin123)
3. 導入預配置儀表板

### 警報配置

```json
{
  "alerts": {
    "telegram": {
      "enabled": true,
      "bot_token": "YOUR_BOT_TOKEN",
      "chat_id": "YOUR_CHAT_ID"
    },
    "discord": {
      "enabled": true,
      "webhook_url": "YOUR_WEBHOOK_URL"
    },
    "email": {
      "enabled": false,
      "smtp_server": "smtp.gmail.com",
      "smtp_port": 587
    }
  }
}
```

### 健康檢查

```bash
# 檢查系統健康狀態
curl http://localhost:8080/health

# 獲取詳細狀態
curl http://localhost:8080/status

# 獲取投資組合狀態
curl http://localhost:8080/portfolio
```

## 🔒 安全配置 / Security Configuration

### API 認證

```bash
# 生成 API 密鑰
python3 -c "
from security_manager import get_security_manager
sm = get_security_manager()
key = sm.generate_api_key('production', ['read', 'write', 'control'])
print(f'API Key: {key}')
"
```

### 環境變量

```bash
# .env 文件
MASTER_API_KEY=your_master_api_key_here
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_AUTHORIZED_USERS=123456789,987654321
DISCORD_WEBHOOK_URL=your_discord_webhook_url
```

### 防火牆配置

```bash
# 僅允許必要端口
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw allow 8080  # 應用端口
sudo ufw enable
```

## 📈 投資組合配置 / Portfolio Configuration

### 基本配置

```json
{
  "portfolio": {
    "max_concurrent_pairs": 5,
    "total_capital": 50000,
    "auto_scaling": true,
    "min_pair_score": 75.0,
    "rebalance_interval_hours": 24
  }
}
```

### 風險管理

```json
{
  "risk_management": {
    "max_portfolio_drawdown": 0.15,
    "max_daily_loss": 0.05,
    "max_pair_allocation": 0.3,
    "correlation_limit": 0.7,
    "emergency_stop_loss": 0.20,
    "position_sizing": {
      "method": "equal_weight",
      "max_leverage": 1.0
    }
  }
}
```

## 🔄 回測與優化 / Backtesting & Optimization

### 投資組合回測

```bash
# 運行投資組合回測
python3 portfolio_backtester.py --start-date 2024-01-01 --end-date 2024-06-01

# 比較不同配置
python3 main.py backtest --config config_v1.json
python3 main.py backtest --config config_v2.json
```

### 參數優化

```python
# 網格搜索優化
from optimization import ParameterOptimizer

optimizer = ParameterOptimizer()
best_params = optimizer.grid_search(
    param_grid={
        'entry_threshold_high': [2.0, 2.5, 3.0],
        'stop_loss_pct': [0.02, 0.03, 0.05]
    }
)
```

## 🚨 故障排除 / Troubleshooting

### 常見問題

1. **端口衝突**
   ```bash
   # 檢查端口使用
   netstat -tlnp | grep 8080
   
   # 修改配置中的端口
   vim config.json
   ```

2. **數據庫連接失敗**
   ```bash
   # 檢查數據庫狀態
   docker-compose ps postgres
   
   # 重啟數據庫
   docker-compose restart postgres
   ```

3. **API 密鑰錯誤**
   ```bash
   # 重新生成密鑰
   python3 -c "from security_manager import get_security_manager; print(get_security_manager().generate_api_key('new', ['read']))"
   ```

### 日誌分析

```bash
# 查看應用日誌
tail -f logs/pair_trading_bot.log

# 查看錯誤日誌
grep ERROR logs/pair_trading_bot.log

# 查看警報日誌
ls -la logs/alerts/
```

## 📋 維護清單 / Maintenance Checklist

### 每日檢查

- [ ] 檢查系統健康狀態
- [ ] 查看投資組合表現
- [ ] 檢查警報日誌
- [ ] 驗證數據更新

### 每週維護

- [ ] 清理舊日誌文件
- [ ] 備份數據庫
- [ ] 更新配對評分
- [ ] 檢查系統資源使用

### 每月維護

- [ ] 重新訓練ML模型
- [ ] 優化策略參數
- [ ] 更新依賴包
- [ ] 安全審計

## 🔧 高級配置 / Advanced Configuration

### 負載均衡

```yaml
# nginx.conf
upstream trading_backend {
    server trading-platform-1:8080;
    server trading-platform-2:8080;
}
```

### 數據備份

```bash
# 自動備份腳本
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
docker exec trading-postgres pg_dump -U trading_user trading_platform > backup_$DATE.sql
```

### 性能優化

```json
{
  "performance": {
    "cache_enabled": true,
    "cache_ttl": 300,
    "async_processing": true,
    "batch_size": 100,
    "worker_threads": 4
  }
}
```

## 📞 支持與聯繫 / Support & Contact

- **文檔**: 查看項目 README.md
- **問題報告**: 使用 GitHub Issues
- **功能請求**: 提交 Pull Request

---

**免責聲明**: 本平台僅供教育和研究目的。實際交易存在風險，請謹慎使用並充分測試。
