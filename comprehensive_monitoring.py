#!/usr/bin/env python3
"""
全面監控與可觀測性系統 - 企業級監控解決方案
Comprehensive Monitoring & Observability - Enterprise-grade monitoring solution
"""

import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, asdict
try:
    from prometheus_client import Counter, Histogram, Gauge, CollectorRegistry, generate_latest
    PROMETHEUS_AVAILABLE = True
except ImportError:
    PROMETHEUS_AVAILABLE = False
    # 創建模擬類
    class Counter:
        def __init__(self, *args, **kwargs): pass
        def inc(self, *args, **kwargs): pass
        def labels(self, **kwargs): return self

    class Gauge:
        def __init__(self, *args, **kwargs): pass
        def set(self, *args, **kwargs): pass
        def labels(self, **kwargs): return self

    class Histogram:
        def __init__(self, *args, **kwargs): pass
        def observe(self, *args, **kwargs): pass
        def labels(self, **kwargs): return self

    class CollectorRegistry:
        def __init__(self): pass

    def generate_latest(registry): return b"# Prometheus not available"

try:
    import structlog
    STRUCTLOG_AVAILABLE = True
except ImportError:
    STRUCTLOG_AVAILABLE = False
    # 創建模擬structlog
    class MockStructlog:
        def configure(self, **kwargs): pass
        def get_logger(self): return self
        def info(self, *args, **kwargs): pass
        def warning(self, *args, **kwargs): pass
        def error(self, *args, **kwargs): pass

    structlog = MockStructlog()

from logging_config import get_logger

# 配置結構化日誌
if STRUCTLOG_AVAILABLE:
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )

logger = get_logger(__name__)
struct_logger = structlog.get_logger()


@dataclass
class MetricDefinition:
    """指標定義"""
    name: str
    metric_type: str  # counter, gauge, histogram
    description: str
    labels: List[str]
    buckets: Optional[List[float]] = None


class PrometheusMetricsManager:
    """Prometheus指標管理器"""
    
    def __init__(self):
        self.registry = CollectorRegistry()
        self.metrics: Dict[str, Any] = {}
        
        # 定義核心業務指標
        self.metric_definitions = [
            # 交易相關指標
            MetricDefinition("trades_total", "counter", "總交易次數", ["strategy", "symbol", "side"]),
            MetricDefinition("trade_pnl", "histogram", "交易盈虧分佈", ["strategy"], [0.01, 0.05, 0.1, 0.2, 0.5, 1.0, 2.0, 5.0]),
            MetricDefinition("trade_execution_time", "histogram", "交易執行時間", ["strategy"], [0.1, 0.5, 1.0, 2.0, 5.0, 10.0]),
            
            # 策略相關指標
            MetricDefinition("strategy_health_score", "gauge", "策略健康分數", ["strategy"]),
            MetricDefinition("strategy_sharpe_ratio", "gauge", "策略夏普比率", ["strategy"]),
            MetricDefinition("strategy_max_drawdown", "gauge", "策略最大回撤", ["strategy"]),
            MetricDefinition("strategy_win_rate", "gauge", "策略勝率", ["strategy"]),
            MetricDefinition("strategy_allocated_capital", "gauge", "策略分配資金", ["strategy"]),
            
            # 投資組合指標
            MetricDefinition("portfolio_total_value", "gauge", "投資組合總價值", []),
            MetricDefinition("portfolio_pnl", "gauge", "投資組合盈虧", []),
            MetricDefinition("portfolio_volatility", "gauge", "投資組合波動率", []),
            MetricDefinition("portfolio_var_95", "gauge", "投資組合VaR(95%)", []),
            MetricDefinition("portfolio_active_strategies", "gauge", "活躍策略數量", []),
            
            # 系統性能指標
            MetricDefinition("system_cpu_usage", "gauge", "系統CPU使用率", []),
            MetricDefinition("system_memory_usage", "gauge", "系統內存使用率", []),
            MetricDefinition("database_connections_active", "gauge", "活躍數據庫連接數", []),
            MetricDefinition("event_bus_events_processed", "counter", "事件總線處理事件數", ["event_type"]),
            MetricDefinition("api_requests_total", "counter", "API請求總數", ["endpoint", "method", "status"]),
            MetricDefinition("api_request_duration", "histogram", "API請求耗時", ["endpoint"], [0.1, 0.5, 1.0, 2.0, 5.0]),
            
            # 風險管理指標
            MetricDefinition("risk_limit_breaches", "counter", "風險限制突破次數", ["risk_type", "strategy"]),
            MetricDefinition("correlation_alerts", "counter", "相關性警報次數", ["strategy_pair"]),
            MetricDefinition("emergency_stops", "counter", "緊急停止次數", ["reason"]),
        ]
        
        # 初始化指標
        self._initialize_metrics()
        
        logger.info(f"Prometheus指標管理器初始化完成: {len(self.metrics)} 個指標")
    
    def _initialize_metrics(self):
        """初始化指標"""
        for metric_def in self.metric_definitions:
            if metric_def.metric_type == "counter":
                self.metrics[metric_def.name] = Counter(
                    metric_def.name,
                    metric_def.description,
                    metric_def.labels,
                    registry=self.registry
                )
            elif metric_def.metric_type == "gauge":
                self.metrics[metric_def.name] = Gauge(
                    metric_def.name,
                    metric_def.description,
                    metric_def.labels,
                    registry=self.registry
                )
            elif metric_def.metric_type == "histogram":
                self.metrics[metric_def.name] = Histogram(
                    metric_def.name,
                    metric_def.description,
                    metric_def.labels,
                    buckets=metric_def.buckets,
                    registry=self.registry
                )
    
    def increment_counter(self, metric_name: str, labels: Dict[str, str] = None, value: float = 1):
        """增加計數器"""
        try:
            metric = self.metrics.get(metric_name)
            if metric and hasattr(metric, 'inc'):
                if labels:
                    metric.labels(**labels).inc(value)
                else:
                    metric.inc(value)
        except Exception as e:
            logger.error(f"增加計數器失敗 {metric_name}: {e}")
    
    def set_gauge(self, metric_name: str, value: float, labels: Dict[str, str] = None):
        """設置儀表值"""
        try:
            metric = self.metrics.get(metric_name)
            if metric and hasattr(metric, 'set'):
                if labels:
                    metric.labels(**labels).set(value)
                else:
                    metric.set(value)
        except Exception as e:
            logger.error(f"設置儀表值失敗 {metric_name}: {e}")
    
    def observe_histogram(self, metric_name: str, value: float, labels: Dict[str, str] = None):
        """觀察直方圖值"""
        try:
            metric = self.metrics.get(metric_name)
            if metric and hasattr(metric, 'observe'):
                if labels:
                    metric.labels(**labels).observe(value)
                else:
                    metric.observe(value)
        except Exception as e:
            logger.error(f"觀察直方圖值失敗 {metric_name}: {e}")
    
    def get_metrics_text(self) -> str:
        """獲取Prometheus格式的指標文本"""
        return generate_latest(self.registry).decode('utf-8')


class StructuredLogger:
    """結構化日誌記錄器"""
    
    def __init__(self):
        self.logger = structlog.get_logger()
    
    def log_trade_execution(self, strategy_id: str, symbol: str, side: str, 
                          amount: float, price: float, pnl: float, 
                          execution_time: float):
        """記錄交易執行"""
        self.logger.info(
            "trade_executed",
            strategy_id=strategy_id,
            symbol=symbol,
            side=side,
            amount=amount,
            price=price,
            pnl=pnl,
            execution_time=execution_time,
            event_type="trade_execution"
        )
    
    def log_strategy_health_change(self, strategy_id: str, old_health: float, 
                                 new_health: float, metrics: Dict[str, Any]):
        """記錄策略健康變化"""
        self.logger.info(
            "strategy_health_changed",
            strategy_id=strategy_id,
            old_health=old_health,
            new_health=new_health,
            health_change=new_health - old_health,
            metrics=metrics,
            event_type="strategy_health"
        )
    
    def log_portfolio_rebalance(self, changes: List[Dict[str, Any]], 
                              total_change: float, reason: str):
        """記錄投資組合重新平衡"""
        self.logger.info(
            "portfolio_rebalanced",
            changes=changes,
            total_change=total_change,
            reason=reason,
            num_changes=len(changes),
            event_type="portfolio_rebalance"
        )
    
    def log_risk_alert(self, risk_type: str, severity: str, 
                      strategy_id: str, details: Dict[str, Any]):
        """記錄風險警報"""
        self.logger.warning(
            "risk_alert",
            risk_type=risk_type,
            severity=severity,
            strategy_id=strategy_id,
            details=details,
            event_type="risk_alert"
        )
    
    def log_system_performance(self, cpu_usage: float, memory_usage: float, 
                             db_connections: int, event_bus_stats: Dict[str, Any]):
        """記錄系統性能"""
        self.logger.info(
            "system_performance",
            cpu_usage=cpu_usage,
            memory_usage=memory_usage,
            db_connections=db_connections,
            event_bus_stats=event_bus_stats,
            event_type="system_performance"
        )


class ComprehensiveMonitor:
    """全面監控系統"""
    
    def __init__(self, portfolio_system):
        self.portfolio_system = portfolio_system
        self.metrics_manager = PrometheusMetricsManager()
        self.structured_logger = StructuredLogger()
        
        # 監控配置
        self.monitoring_interval = 30  # 30秒監控間隔
        self.is_monitoring = False
        self.monitoring_task = None
        
        # 性能基線
        self.performance_baseline = {
            'cpu_threshold': 80.0,
            'memory_threshold': 85.0,
            'db_connection_threshold': 50,
            'api_response_threshold': 5.0
        }
        
        logger.info("全面監控系統初始化完成")
    
    async def start_monitoring(self):
        """啟動監控"""
        if self.is_monitoring:
            logger.warning("監控系統已在運行")
            return
        
        self.is_monitoring = True
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        logger.info("🔍 全面監控系統已啟動")
    
    async def stop_monitoring(self):
        """停止監控"""
        self.is_monitoring = False
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        logger.info("全面監控系統已停止")
    
    async def _monitoring_loop(self):
        """監控循環"""
        while self.is_monitoring:
            try:
                # 收集系統指標
                await self._collect_system_metrics()
                
                # 收集業務指標
                await self._collect_business_metrics()
                
                # 檢查警報條件
                await self._check_alert_conditions()
                
                # 等待下一個監控週期
                await asyncio.sleep(self.monitoring_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"監控循環異常: {e}")
                await asyncio.sleep(5)  # 短暫等待後重試
    
    async def _collect_system_metrics(self):
        """收集系統指標"""
        try:
            import psutil
            
            # CPU使用率
            cpu_usage = psutil.cpu_percent(interval=1)
            self.metrics_manager.set_gauge("system_cpu_usage", cpu_usage)
            
            # 內存使用率
            memory = psutil.virtual_memory()
            memory_usage = memory.percent
            self.metrics_manager.set_gauge("system_memory_usage", memory_usage)
            
            # 數據庫連接數
            try:
                from comprehensive_optimization import enhanced_resource_manager
                db_stats = enhanced_resource_manager.get_connection_stats()
                active_connections = db_stats['stats']['current_active']
                self.metrics_manager.set_gauge("database_connections_active", active_connections)
            except Exception as e:
                logger.debug(f"獲取數據庫連接統計失敗: {e}")
                active_connections = 0
            
            # 記錄系統性能
            event_bus_stats = {}
            if hasattr(self.portfolio_system, 'global_event_bus'):
                event_bus_stats = self.portfolio_system.global_event_bus.get_stats()
            
            self.structured_logger.log_system_performance(
                cpu_usage, memory_usage, active_connections, event_bus_stats
            )
            
        except Exception as e:
            logger.error(f"收集系統指標失敗: {e}")
    
    async def _collect_business_metrics(self):
        """收集業務指標"""
        try:
            # 投資組合指標
            if hasattr(self.portfolio_system, 'portfolio_manager'):
                portfolio_manager = self.portfolio_system.portfolio_manager
                
                # 總價值
                total_value = portfolio_manager.total_capital
                self.metrics_manager.set_gauge("portfolio_total_value", total_value)
                
                # 活躍策略數
                active_strategies = len([
                    s for s in portfolio_manager.strategy_allocations.values()
                    if s.current_allocation > 0
                ])
                self.metrics_manager.set_gauge("portfolio_active_strategies", active_strategies)
                
                # 策略指標
                for strategy_id, allocation in portfolio_manager.strategy_allocations.items():
                    # 分配資金
                    self.metrics_manager.set_gauge(
                        "strategy_allocated_capital",
                        allocation.allocated_capital,
                        {"strategy": strategy_id}
                    )
                    
                    # 健康分數（如果可用）
                    if hasattr(allocation, 'health_score'):
                        self.metrics_manager.set_gauge(
                            "strategy_health_score",
                            allocation.health_score,
                            {"strategy": strategy_id}
                        )
            
            # 事件總線指標
            if hasattr(self.portfolio_system, 'global_event_bus'):
                event_stats = self.portfolio_system.global_event_bus.get_stats()
                
                # 處理的事件數
                self.metrics_manager.increment_counter(
                    "event_bus_events_processed",
                    {"event_type": "total"},
                    event_stats.get('events_processed', 0)
                )
            
        except Exception as e:
            logger.error(f"收集業務指標失敗: {e}")
    
    async def _check_alert_conditions(self):
        """檢查警報條件"""
        try:
            # 檢查CPU使用率
            cpu_usage = psutil.cpu_percent()
            if cpu_usage > self.performance_baseline['cpu_threshold']:
                self.structured_logger.log_risk_alert(
                    "high_cpu_usage",
                    "warning",
                    "system",
                    {"cpu_usage": cpu_usage, "threshold": self.performance_baseline['cpu_threshold']}
                )
                self.metrics_manager.increment_counter(
                    "risk_limit_breaches",
                    {"risk_type": "cpu_usage", "strategy": "system"}
                )
            
            # 檢查內存使用率
            memory_usage = psutil.virtual_memory().percent
            if memory_usage > self.performance_baseline['memory_threshold']:
                self.structured_logger.log_risk_alert(
                    "high_memory_usage",
                    "warning",
                    "system",
                    {"memory_usage": memory_usage, "threshold": self.performance_baseline['memory_threshold']}
                )
                self.metrics_manager.increment_counter(
                    "risk_limit_breaches",
                    {"risk_type": "memory_usage", "strategy": "system"}
                )
            
            # 檢查策略健康分數
            if hasattr(self.portfolio_system, 'portfolio_manager'):
                for strategy_id, allocation in self.portfolio_system.portfolio_manager.strategy_allocations.items():
                    if hasattr(allocation, 'health_score') and allocation.health_score < 0.3:
                        self.structured_logger.log_risk_alert(
                            "low_strategy_health",
                            "critical",
                            strategy_id,
                            {"health_score": allocation.health_score, "threshold": 0.3}
                        )
                        self.metrics_manager.increment_counter(
                            "risk_limit_breaches",
                            {"risk_type": "strategy_health", "strategy": strategy_id}
                        )
            
        except Exception as e:
            logger.error(f"檢查警報條件失敗: {e}")
    
    def record_trade_execution(self, strategy_id: str, symbol: str, side: str,
                             amount: float, price: float, pnl: float,
                             execution_time: float):
        """記錄交易執行"""
        # 更新Prometheus指標
        self.metrics_manager.increment_counter(
            "trades_total",
            {"strategy": strategy_id, "symbol": symbol, "side": side}
        )
        
        self.metrics_manager.observe_histogram(
            "trade_pnl",
            pnl,
            {"strategy": strategy_id}
        )
        
        self.metrics_manager.observe_histogram(
            "trade_execution_time",
            execution_time,
            {"strategy": strategy_id}
        )
        
        # 記錄結構化日誌
        self.structured_logger.log_trade_execution(
            strategy_id, symbol, side, amount, price, pnl, execution_time
        )
    
    def record_strategy_health_change(self, strategy_id: str, old_health: float,
                                    new_health: float, metrics: Dict[str, Any]):
        """記錄策略健康變化"""
        # 更新Prometheus指標
        self.metrics_manager.set_gauge(
            "strategy_health_score",
            new_health,
            {"strategy": strategy_id}
        )
        
        if 'sharpe_ratio' in metrics:
            self.metrics_manager.set_gauge(
                "strategy_sharpe_ratio",
                metrics['sharpe_ratio'],
                {"strategy": strategy_id}
            )
        
        if 'max_drawdown' in metrics:
            self.metrics_manager.set_gauge(
                "strategy_max_drawdown",
                metrics['max_drawdown'],
                {"strategy": strategy_id}
            )
        
        if 'win_rate' in metrics:
            self.metrics_manager.set_gauge(
                "strategy_win_rate",
                metrics['win_rate'],
                {"strategy": strategy_id}
            )
        
        # 記錄結構化日誌
        self.structured_logger.log_strategy_health_change(
            strategy_id, old_health, new_health, metrics
        )
    
    def get_metrics_endpoint(self) -> str:
        """獲取Prometheus指標端點數據"""
        return self.metrics_manager.get_metrics_text()
    
    def get_monitoring_status(self) -> Dict[str, Any]:
        """獲取監控狀態"""
        return {
            'is_monitoring': self.is_monitoring,
            'monitoring_interval': self.monitoring_interval,
            'metrics_count': len(self.metrics_manager.metrics),
            'performance_baseline': self.performance_baseline,
            'last_check': datetime.now().isoformat()
        }


# 全局監控實例
comprehensive_monitor = None


def get_comprehensive_monitor(portfolio_system) -> ComprehensiveMonitor:
    """獲取全面監控實例"""
    global comprehensive_monitor
    if comprehensive_monitor is None:
        comprehensive_monitor = ComprehensiveMonitor(portfolio_system)
    return comprehensive_monitor


async def main():
    """測試監控系統"""
    print("🧪 全面監控系統測試")
    
    # 創建測試監控器
    class MockPortfolioSystem:
        pass
    
    mock_system = MockPortfolioSystem()
    monitor = ComprehensiveMonitor(mock_system)
    
    # 測試指標記錄
    monitor.record_trade_execution(
        "test_strategy", "BTC/USDT:USDT", "buy", 0.001, 50000, 100, 0.5
    )
    
    # 獲取指標
    metrics_text = monitor.get_metrics_endpoint()
    print(f"  📊 指標數據長度: {len(metrics_text)} 字符")
    
    # 獲取監控狀態
    status = monitor.get_monitoring_status()
    print(f"  📈 監控狀態: {status}")
    
    print("✅ 全面監控系統測試完成")


if __name__ == "__main__":
    asyncio.run(main())
