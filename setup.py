#!/usr/bin/env python3
"""
配對交易機器人安裝腳本
Cross-platform Setup Script for Pair Trading Bot
"""

import platform
import subprocess
import sys
from pathlib import Path


def print_header():
    """打印安裝標題"""
    print("=" * 60)
    print("配對交易機器人安裝程序")
    print("Pair Trading Bot Setup")
    print("=" * 60)
    print()  # 修復不完整調用


def check_python_version():
    """檢查 Python 版本"""
    print("檢查 Python 版本...")

    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python 版本過低: {version.major}.{version.minor}")
        print("需要 Python 3.8 或更高版本")
        return False

    print(f"✅ Python 版本: {version.major}.{version.minor}.{version.micro}")
    return True


def check_system_info():
    """檢查系統信息"""
    print("\n檢查系統信息...")

    system = platform.system()
    version = platform.version()
    machine = platform.machine()

    print(f"操作系統: {system}")
    print(f"版本: {version}")
    print(f"架構: {machine}")

    # 檢查系統兼容性
    supported_systems = ["Windows", "Darwin", "Linux"]
    if system not in supported_systems:
        print(f"⚠️  未測試的操作系統: {system}")
        print("程序可能無法正常運行")
    else:
        print(f"✅ 支持的操作系統: {system}")

    return True


def install_dependencies():
    """安裝依賴包"""
    print("\n安裝 Python 依賴包...")

    try:
        # 檢查 pip 是否可用
        subprocess.run([sys.executable, "-m", "pip", "--version"], check=True, capture_output=True)
        print("✅ pip 可用")
    except subprocess.CalledProcessError:
        print("❌ pip 不可用，請先安裝 pip")
        return False

    # 安裝依賴
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("❌ requirements.txt 文件不存在")
        return False

    try:
        print("正在安裝依賴包...")
        subprocess.run(
            [sys.executable, "-m", "pip", "install", "-r", str(requirements_file)],
            check=True,
            capture_output=True,
            text=True,
        )

        print("✅ 依賴包安裝成功")
        return True

    except subprocess.CalledProcessError as e:
        print(f"❌ 依賴包安裝失敗: {e}")
        print("錯誤輸出:", e.stderr)
        return False


def create_directories():
    """創建必要的目錄"""
    print("\n創建必要目錄...")

    directories = [
        "data",
        "logs",
        "records",
        "backtest_results",
        "pair_selection_results",
        "improved_backtest_results",
    ]

    for dir_name in directories:
        dir_path = Path(dir_name)
        try:
            dir_path.mkdir(exist_ok=True)
            print(f"✅ {dir_name}/ 目錄已創建")
        except Exception as e:
            print(f"❌ 創建 {dir_name}/ 目錄失敗: {e}")
            return False

    return True


def setup_config_files():
    """設置配置文件"""
    print("\n設置配置文件...")

    # 檢查 config.json
    config_file = Path("config.json")
    if config_file.exists():
        print("✅ config.json 已存在")
    else:
        print("❌ config.json 不存在，請確保配置文件存在")
        return False

    # 檢查 .env 模板
    env_template = Path(".env.template")
    env_file = Path(".env")

    if env_template.exists():
        print("✅ .env.template 存在")

        if not env_file.exists():
            try:
                # 複製模板為 .env
                import shutil

                shutil.copy(env_template, env_file)
                print("✅ 已創建 .env 文件（從模板複製）")
                print("⚠️  請編輯 .env 文件並填入您的 API 密鑰")
            except Exception as e:
                print(f"❌ 創建 .env 文件失敗: {e}")
                return False
        else:
            print("✅ .env 文件已存在")
    else:
        print("⚠️  .env.template 不存在")

    return True


def test_installation():
    """測試安裝"""
    print("\n測試安裝...")

    try:
        # 測試導入主要模組
        print("測試模組導入...")

        test_modules = [
            "pandas",
            "numpy",
            "ccxt",
            "scipy",
            "statsmodels",
            "matplotlib",
            "python-dotenv",
        ]

        for module in test_modules:
            try:
                if module == "python-dotenv":
                    __import__("dotenv")
                else:
                    __import__(module)
                print(f"✅ {module}")
            except ImportError:
                print(f"❌ {module} 導入失敗")
                return False

        # 測試配置載入
        print("\n測試配置載入...")
        try:
            from config_manager import ConfigManager

            config_manager = ConfigManager()
            config_manager.load_config()
            print("✅ 配置載入成功")
        except Exception as e:
            print(f"❌ 配置載入失敗: {e}")
            return False

        # 測試系統兼容性
        print("\n測試系統兼容性...")
        try:
            from main import check_system_compatibility

            if check_system_compatibility():
                print("✅ 系統兼容性測試通過")
            else:
                print("⚠️  系統兼容性測試有警告")
        except Exception as e:
            print(f"❌ 系統兼容性測試失敗: {e}")
            return False

        return True

    except Exception as e:
        print(f"❌ 安裝測試失敗: {e}")
        return False


def print_next_steps():
    """打印後續步驟"""
    print("\n" + "=" * 60)
    print("安裝完成！後續步驟:")
    print("Installation Complete! Next Steps:")
    print("=" * 60)

    _ = [
        "1. 編輯 .env 文件，填入您的交易所 API 密鑰",
        "   Edit .env file and add your exchange API keys",
        "",
        "2. 檢查系統兼容性:",
        "   Check system compatibility:",
        "   python main.py check-system",
        "",
        "3. 生成示例數據進行測試:",
        "   Generate sample data for testing:",
        "   python main.py generate-data",
        "",
        "4. 運行回測測試:",
        "   Run backtest:",
        "   python main.py backtest --data data/sample_data.csv",
        "",
        "5. 運行配對篩選:",
        "   Run pair selection:",
        "   python main.py select",
        "",
        "6. 查看更多命令:",
        "   View more commands:",
        "   python main.py --help",
    ]

    for step in steps:
        print(step)

    print("\n" + "=" * 60)
    print("⚠️  重要提醒 / Important Notes:")
    print("- 請勿在生產環境中使用默認配置")
    print("- 建議先在模擬環境中測試")
    print("- 定期備份配置和交易記錄")
    print("=" * 60)


def main():
    """主安裝函數"""
    print_header()  # 修復不完整調用

    # 檢查步驟
    checks = [
        ("Python 版本", check_python_version),
        ("系統信息", check_system_info),
        ("安裝依賴", install_dependencies),
        ("創建目錄", create_directories),
        ("配置文件", setup_config_files),
        ("測試安裝", test_installation),
    ]

    for step_name, step_func in checks:
        print(f"\n{'='*20} {step_name} {'='*20}")

        try:
            if not step_func():
                print(f"\n❌ {step_name} 失敗，安裝中止")
                sys.exit(1)
        except KeyboardInterrupt:
            print("\n\n用戶中斷安裝")
            sys.exit(1)
        except Exception as e:
            print(f"\n❌ {step_name} 過程中發生錯誤: {e}")
            sys.exit(1)

    print_next_steps()  # 修復不完整調用


if __name__ == "__main__":
    main()  # 修復不完整調用
