#!/usr/bin/env python3
"""
關鍵語法錯誤修復工具
Critical Syntax Error Fixer
"""

import re
from pathlib import Path
from typing import List, Dict


class CriticalSyntaxFixer:
    """關鍵語法錯誤修復器"""

    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.fixes_applied = 0

    def find_python_files(self) -> List[Path]:
        """查找所有Python文件"""
        python_files = []

        # 掃描src目錄
        src_dir = self.project_root / "src"
        if src_dir.exists():
            python_files.extend(src_dir.glob("*.py"))

        # 掃描tests目錄
        tests_dir = self.project_root / "tests"
        if tests_dir.exists():
            python_files.extend(tests_dir.glob("*.py"))

        # 掃描根目錄的Python文件
        python_files.extend(self.project_root.glob("*.py"))

        return python_files

    def add_logger_import(self, file_path: Path) -> bool:
        """添加logger導入"""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            # 檢查是否使用了logger但沒有導入
            if (
                "logger." in content
                and "import logging" not in content
                and "from logging import" not in content
            ):
                lines = content.split("\n")

                # 找到導入區域的結束位置
                import_end_line = 0
                for i, line in enumerate(lines):
                    if line.strip().startswith("import ") or line.strip().startswith("from "):
                        import_end_line = i + 1
                    elif (
                        line.strip()
                        and not line.strip().startswith("#")
                        and not line.strip().startswith('"""')
                        and not line.strip().startswith("'''")
                    ):
                        if not (
                            line.strip().startswith("import ") or line.strip().startswith("from ")
                        ):
                            break

                # 插入logger導入和配置
                logger_setup = [
                    "import logging",
                    "",
                    "# 配置logger",
                    "logger = logging.getLogger(__name__)",
                ]

                for line in reversed(logger_setup):
                    lines.insert(import_end_line, line)

                new_content = "\n".join(lines)

                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(new_content)

                self.fixes_applied += 1
                return True

        except Exception as e:
            print(f"添加logger導入失敗 {file_path}: {e}")

        return False

    def fix_syntax_errors(self, file_path: Path) -> bool:
        """修復語法錯誤"""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            new_content = content
            changes_made = False

            # 修復常見的語法錯誤
            fixes = [
                # 修復未閉合的括號
                (r'raise AssertionError\(\), "([^"]+)"', r'raise AssertionError("\1")'),
                # 修復字典語法錯誤
                (r"}\s*$", "}"),
                # 修復函數調用中的語法錯誤
                (r"return_value = ([^,\n]+),\s*$", r"return_value = \1"),
                # 修復PRAGMA語句
                (
                    r'conn\.execute\("PRAGMA synchronous=NORMAL"\)\[0\]',
                    r'conn.execute("PRAGMA synchronous=NORMAL")',
                ),
                # 修復f-string語法
                (r'f"([^"]*)"(?!\s*\.)', r'f"\1"'),
            ]

            for pattern, replacement in fixes:
                if re.search(pattern, new_content):
                    new_content = re.sub(pattern, replacement, new_content, flags=re.MULTILINE)
                    changes_made = True

            # 修復特定的語法問題
            lines = new_content.split("\n")
            for i, line in enumerate(lines):
                # 修復未閉合的括號
                if "(" in line and ")" not in line and not line.strip().endswith("\\"):
                    # 檢查下一行是否有閉合括號
                    if i + 1 < len(lines) and ")" in lines[i + 1]:
                        continue
                    # 嘗試在行末添加閉合括號
                    if line.count("(") > line.count(")"):
                        missing_parens = line.count("(") - line.count(")")
                        lines[i] = line + ")" * missing_parens
                        changes_made = True

                # 修復字典語法錯誤
                if line.strip().endswith("{") and i + 1 < len(lines):
                    next_line = lines[i + 1].strip()
                    if next_line == "}":
                        # 空字典
                        lines[i] = line.rstrip() + "}"
                        lines[i + 1] = ""
                        changes_made = True

            if changes_made:
                new_content = "\n".join(lines)
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(new_content)
                self.fixes_applied += 1
                return True

        except Exception as e:
            print(f"修復語法錯誤失敗 {file_path}: {e}")

        return False

    def fix_specific_files(self):
        """修復特定文件的已知問題"""
        specific_fixes = {
            "src/comprehensive_optimization.py": [
                (
                    'conn.execute("PRAGMA synchronous=NORMAL")',
                    'conn.execute("PRAGMA synchronous=NORMAL")',
                )
            ],
            "src/demo_multi_strategy_system.py": [('print("  ✅ 資金管理器創建完成")', 'print("資金管理器創建完成")')],
            "src/enhanced_portfolio_demo.py": [
                (
                    "health_score = 0.5 + np.tanh(daily_return * 100) * 0.3",
                    "health_score = 0.5 + np.tanh(daily_return * 100) * 0.3",
                )
            ],
            "src/enhanced_retry_handler.py": [
                (
                    "permanent_exceptions: tuple = (ValueError,",
                    "permanent_exceptions: tuple = (ValueError,)",
                )
            ],
            "src/event_driven_core.py": [("return [alert_event]", "return [alert_event]")],
            "src/multi_executor_manager.py": [
                (
                    'self.executors[TaskType.IO_BOUND] = concurrent.futures.ThreadPoolExecutor(max_workers=self.io_workers, thread_name_prefix="IOWorker")',
                    'self.executors[TaskType.IO_BOUND] = concurrent.futures.ThreadPoolExecutor(\n            max_workers=self.io_workers, thread_name_prefix="IOWorker")',
                )
            ],
            "src/smart_capital_management.py": [
                ("metrics = manager.get_risk_metrics()", "metrics = manager.get_risk_metrics()")
            ],
            "src/state_persistence_manager.py": [
                (
                    "elif event.event_type == EventType.STRATEGY_HEALTH_CHANGED:",
                    "elif event.event_type == EventType.STRATEGY_HEALTH_CHANGED:",
                )
            ],
            "src/unified_portfolio_manager.py": [
                ("old_health = allocation.health_score", "old_health = allocation.health_score")
            ],
            "tests/test_final_optimizations.py": [
                ('raise AssertionError("應該拋出驗證異常")', 'raise AssertionError("應該拋出驗證異常")')
            ],
            "tests/test_integrated_trading_executor.py": [
                (
                    "mock_mitigation.return_value = mitigated_signal",
                    "mock_mitigation.return_value = mitigated_signal",
                )
            ],
            "tests/test_optimized_robustness.py": [("}", "}")],
            "unified_test_runner.py": [
                ("duration = time.time() - start_time", "duration = time.time() - start_time")
            ],
            "tests/test_config_debug.py": [("invalid syntax", "# fixed syntax")],
        }

        for file_path, fixes in specific_fixes.items():
            full_path = self.project_root / file_path
            if full_path.exists():
                try:
                    with open(full_path, "r", encoding="utf-8") as f:
                        content = f.read()

                    new_content = content
                    for old_text, new_text in fixes:
                        if old_text in new_content:
                            new_content = new_content.replace(old_text, new_text)

                    if new_content != content:
                        with open(full_path, "w", encoding="utf-8") as f:
                            f.write(new_content)
                        self.fixes_applied += 1
                        print(f"修復特定文件: {file_path}")

                except Exception as e:
                    print(f"修復特定文件失敗 {file_path}: {e}")

    def run_fixes(self):
        """運行所有修復"""
        print("🔧 開始關鍵語法錯誤修復...")

        # 首先修復特定文件的已知問題
        self.fix_specific_files()

        python_files = self.find_python_files()
        print(f"找到 {len(python_files)} 個Python文件")

        for file_path in python_files:
            print(f"處理: {file_path}")

            # 應用各種修復
            self.add_logger_import(file_path)
            self.fix_syntax_errors(file_path)

        print(f"✅ 關鍵語法修復完成，共應用 {self.fixes_applied} 個修復")


def main():
    """主函數"""
    project_root = Path(__file__).parent
    fixer = CriticalSyntaxFixer(str(project_root))
    fixer.run_fixes()


if __name__ == "__main__":
    main()  # 修復不完整調用
