@echo off
REM Windows 啟動腳本 - 配對交易機器人
REM Windows Startup Script - Pair Trading Bot

echo ========================================
echo 配對交易機器人 - Windows 啟動腳本
echo Pair Trading Bot - Windows Startup
echo ========================================
echo.

REM 檢查 Python 是否安裝
python --version >nul 2>&1
if errorlevel 1 (
    echo 錯誤: 未找到 Python，請先安裝 Python 3.8+
    echo Error: Python not found, please install Python 3.8+
    pause
    exit /b 1
)

REM 檢查配置文件
if not exist "config.json" (
    echo 錯誤: 未找到 config.json 配置文件
    echo Error: config.json not found
    pause
    exit /b 1
)

REM 檢查 .env 文件
if not exist ".env" (
    echo 警告: 未找到 .env 文件，將使用默認配置
    echo Warning: .env file not found, using default config
    if exist ".env.template" (
        echo 提示: 請複製 .env.template 為 .env 並填入 API 密鑰
        echo Hint: Copy .env.template to .env and fill in API keys
    )
    echo.
)

REM 顯示菜單
:menu
echo 請選擇操作 / Please select an option:
echo.
echo 1. 檢查系統兼容性 / Check system compatibility
echo 2. 生成示例數據 / Generate sample data
echo 3. 運行回測 / Run backtest
echo 4. 運行配對篩選 / Run pair selection
echo 5. 啟動實時交易 (1分鐘) / Start live trading (1m)
echo 6. 啟動實時交易 (5分鐘) / Start live trading (5m)
echo 7. 檢查機器人狀態 / Check bot status
echo 8. 運行測試 / Run tests
echo 9. 退出 / Exit
echo.

set /p choice="請輸入選項 (1-9) / Enter choice (1-9): "

if "%choice%"=="1" goto check_system
if "%choice%"=="2" goto generate_data
if "%choice%"=="3" goto run_backtest
if "%choice%"=="4" goto run_pair_selection
if "%choice%"=="5" goto live_trading_1m
if "%choice%"=="6" goto live_trading_5m
if "%choice%"=="7" goto check_status
if "%choice%"=="8" goto run_tests
if "%choice%"=="9" goto exit
echo 無效選項，請重新選擇 / Invalid option, please try again
echo.
goto menu

:check_system
echo 檢查系統兼容性...
python main.py check-system
pause
goto menu

:generate_data
echo 生成示例數據...
python main.py generate-data
pause
goto menu

:run_backtest
echo 運行回測...
if exist "data\sample_data.csv" (
    python main.py backtest --data data\sample_data.csv
) else (
    echo 未找到示例數據，請先生成示例數據
    echo Sample data not found, please generate sample data first
)
pause
goto menu

:run_pair_selection
echo 運行配對篩選...
python main.py select
pause
goto menu

:live_trading_1m
echo 啟動實時交易 (1分鐘級別)...
echo 按 Ctrl+C 停止交易
echo Press Ctrl+C to stop trading
python main.py live --timeframe 1m
pause
goto menu

:live_trading_5m
echo 啟動實時交易 (5分鐘級別)...
echo 按 Ctrl+C 停止交易
echo Press Ctrl+C to stop trading
python main.py live --timeframe 5m
pause
goto menu

:check_status
echo 檢查機器人狀態...
python main.py status
pause
goto menu

:run_tests
echo 運行測試...
python main.py test
pause
goto menu

:exit
echo 退出程序...
echo Exiting...
exit /b 0
