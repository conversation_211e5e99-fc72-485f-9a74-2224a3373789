#!/usr/bin/env python3
"""
完整系統集成測試 - 測試所有模組的集成
Complete System Integration Test - Test all module integrations
"""

import sys
import time
import numpy as np
import pandas as pd
from datetime import datetime
from typing import Dict

# 添加項目根目錄到路徑
sys.path.append('.')

# 導入所有主要模組
from database_manager import get_database_manager
from factor_factory.alpha_factors import get_alpha_factory
from ml_predictor.predictor import get_ml_predictor
from risk_management.advanced_risk_manager import get_advanced_risk_manager
from research_environment.research_toolkit import get_research_toolkit
from health_server import HealthServer
from logging_config import setup_logging, get_logger

# 設置日誌
setup_logging()
logger = get_logger(__name__)


class CompleteSystemTester:
    """完整系統測試器"""
    
    def __init__(self):
        self.test_results = {}
        self.start_time = datetime.now()
        
        logger.info("CompleteSystemTester 初始化完成")
    
    def test_database_integration(self) -> bool:
        """測試數據庫集成"""
        try:
            print("\n=== 測試數據庫集成 ===")
            
            db_manager = get_database_manager()
            
            # 測試交易記錄
            trade_data = {
                'pair_key': 'BTCUSDT-ETHUSDT',
                'base_symbol': 'BTCUSDT',
                'quote_symbol': 'ETHUSDT',
                'trade_type': 'pair_trading',
                'pnl': 150.75,
                'exit_reason': 'take_profit',
                'metadata': {'test': True}
            }
            
            trade_id = db_manager.record_trade(trade_data)
            print(f"   ✓ 記錄交易: ID={trade_id}")
            
            # 測試獲取交易
            trades = db_manager.get_trades(limit=5)
            print(f"   ✓ 獲取交易記錄: {len(trades)} 筆")
            
            # 測試投資組合快照
            portfolio_data = {
                'total_capital': 10000,
                'total_pnl': 250.5,
                'active_pairs': 2,
                'metadata': {'test_snapshot': True}
            }
            
            snapshot_id = db_manager.record_portfolio_snapshot(portfolio_data)
            print(f"   ✓ 記錄投資組合快照: ID={snapshot_id}")
            
            # 測試數據庫統計
            stats = db_manager.get_database_stats()
            print(f"   ✓ 數據庫統計: {stats}")
            
            return True
            
        except Exception as e:
            logger.error(f"數據庫集成測試失敗: {e}")
            return False
    
    def test_alpha_factory_integration(self) -> bool:
        """測試Alpha因子工廠集成"""
        try:
            print("\n=== 測試Alpha因子工廠集成 ===")
            
            # 生成測試數據
            dates = pd.date_range('2024-01-01', periods=100, freq='h')
            np.random.seed(42)
            
            data = pd.DataFrame({
                'base_price': 50000 + np.cumsum(np.random.normal(0, 100, 100)),
                'quote_price': 3000 + np.cumsum(np.random.normal(0, 10, 100)),
                'base_volume': np.random.exponential(1000, 100),
                'quote_volume': np.random.exponential(100, 100)
            }, index=dates)
            
            alpha_factory = get_alpha_factory()
            
            # 測試因子計算
            factor_matrix = alpha_factory.calculate_all_factors(data)
            print(f"   ✓ 計算因子矩陣: {factor_matrix.shape}")
            
            # 測試因子評估
            future_returns = pd.Series(np.random.normal(0, 0.01, len(data)), index=data.index)
            evaluations = alpha_factory.evaluate_factors(data, future_returns)
            print(f"   ✓ 因子評估: {len(evaluations)} 個因子")
            
            # 測試頂級因子
            top_factors = alpha_factory.get_top_factors(3)
            print(f"   ✓ 頂級因子: {top_factors}")
            
            # 測試因子總結
            summary = alpha_factory.get_factor_summary()
            print(f"   ✓ 因子總結: {len(summary)} 行")
            
            return True
            
        except Exception as e:
            logger.error(f"Alpha因子工廠集成測試失敗: {e}")
            return False
    
    def test_ml_predictor_integration(self) -> bool:
        """測試ML預測器集成"""
        try:
            print("\n=== 測試ML預測器集成 ===")
            
            # 生成測試數據
            dates = pd.date_range('2024-01-01', periods=150, freq='h')
            np.random.seed(42)
            
            data = pd.DataFrame({
                'base_price': 50000 + np.cumsum(np.random.normal(0, 100, 150)),
                'quote_price': 3000 + np.cumsum(np.random.normal(0, 10, 150)),
                'base_volume': np.random.exponential(1000, 150),
                'quote_volume': np.random.exponential(100, 150)
            }, index=dates)
            
            ml_predictor = get_ml_predictor()
            
            # 測試訓練數據準備
            X, y = ml_predictor.prepare_training_data(data, ['BTCUSDT', 'ETHUSDT'])
            print(f"   ✓ 準備訓練數據: {X.shape[0]} 樣本, {X.shape[1]} 特徵")
            
            if not X.empty and not y.empty:
                # 測試模型訓練
                training_results = ml_predictor.train_models(X, y)
                print(f"   ✓ 模型訓練: {len(training_results)} 個模型")
                
                # 測試預測
                prediction = ml_predictor.predict(data, ['BTCUSDT', 'ETHUSDT'])
                print(f"   ✓ 預測結果: {prediction['prediction']:.6f}")
                
                # 測試模型總結
                summary = ml_predictor.get_model_summary()
                print(f"   ✓ 模型總結: {summary['model_count']} 個模型")
            
            return True
            
        except Exception as e:
            logger.error(f"ML預測器集成測試失敗: {e}")
            return False
    
    def test_risk_manager_integration(self) -> bool:
        """測試風險管理器集成"""
        try:
            print("\n=== 測試風險管理器集成 ===")
            
            risk_manager = get_advanced_risk_manager()
            
            # 生成測試收益數據
            np.random.seed(42)
            returns = pd.Series(np.random.normal(-0.001, 0.02, 100))
            
            # 測試VaR計算
            var_result = risk_manager.calculate_var(returns, 'historical')
            print(f"   ✓ VaR計算: {var_result['var']:.6f}")
            
            # 測試CVaR計算
            cvar_result = risk_manager.calculate_cvar(returns, 'historical')
            print(f"   ✓ CVaR計算: {cvar_result['cvar']:.6f}")
            
            # 測試壓力測試
            portfolio_data = {
                'total_pnl': 500,
                'total_capital': 10000,
                'positions': {
                    'BTCUSDT-ETHUSDT': {
                        'value': 5000,
                        'correlation': 0.8,
                        'weight': 0.5
                    }
                }
            }
            
            stress_result = risk_manager.stress_test(portfolio_data, 'covid_crash_2020')
            print(f"   ✓ 壓力測試: 損失 ${stress_result.get('total_stress_loss', 0):.2f}")
            
            # 測試風險歸因
            attribution = risk_manager.risk_attribution(portfolio_data)
            print(f"   ✓ 風險歸因: {len(attribution)} 個風險因子")
            
            return True
            
        except Exception as e:
            logger.error(f"風險管理器集成測試失敗: {e}")
            return False
    
    def test_research_toolkit_integration(self) -> bool:
        """測試研究工具包集成"""
        try:
            print("\n=== 測試研究工具包集成 ===")
            
            toolkit = get_research_toolkit()
            
            # 測試數據載入
            pair = ['BTCUSDT', 'ETHUSDT']
            start_date = '2024-01-01'
            end_date = '2024-02-01'
            
            data = toolkit.load_research_data(pair, start_date, end_date)
            print(f"   ✓ 載入研究數據: {data.shape}")
            
            # 測試配對分析
            pair_analysis = toolkit.analyze_pair_relationship(data, pair)
            print(f"   ✓ 配對分析: 相關性 {pair_analysis.get('correlation', 0):.4f}")
            
            # 測試因子研究
            factor_research = toolkit.factor_research(data, pair)
            print(f"   ✓ 因子研究: {len(factor_research)} 個結果")
            
            # 測試策略優化
            param_grid = {
                'lookback_window': [10, 20],
                'entry_threshold': [2.0, 2.5],
                'exit_threshold': [0.5, 0.7]
            }
            
            optimization = toolkit.strategy_optimization(data, pair, param_grid)
            print(f"   ✓ 策略優化: {len(optimization.get('optimization_results', []))} 個結果")
            
            return True
            
        except Exception as e:
            logger.error(f"研究工具包集成測試失敗: {e}")
            return False
    
    def test_web_interface_integration(self) -> bool:
        """測試Web界面集成"""
        try:
            print("\n=== 測試Web界面集成 ===")
            
            # 啟動健康檢查服務器
            health_server = HealthServer(port=8081)
            health_server.start()
            time.sleep(2)  # 等待服務器啟動
            
            # 測試API端點
            import requests
            
            endpoints = [
                '/health',
                '/status',
                '/portfolio',
                '/dashboard'
            ]
            
            for endpoint in endpoints:
                try:
                    response = requests.get(f'http://localhost:8081{endpoint}', timeout=5)
                    status = "✓" if response.status_code == 200 else "✗"
                    print(f"   {status} {endpoint}: {response.status_code}")
                except requests.exceptions.RequestException:
                    print(f"   ✗ {endpoint}: 請求失敗")
            
            # 停止服務器
            health_server.stop()
            
            return True
            
        except Exception as e:
            logger.error(f"Web界面集成測試失敗: {e}")
            return False
    
    def test_end_to_end_workflow(self) -> bool:
        """測試端到端工作流程"""
        try:
            print("\n=== 測試端到端工作流程 ===")
            
            # 1. 數據準備
            dates = pd.date_range('2024-01-01', periods=200, freq='h')
            np.random.seed(42)
            
            data = pd.DataFrame({
                'base_price': 50000 + np.cumsum(np.random.normal(0, 100, 200)),
                'quote_price': 3000 + np.cumsum(np.random.normal(0, 10, 200)),
                'base_volume': np.random.exponential(1000, 200),
                'quote_volume': np.random.exponential(100, 200)
            }, index=dates)
            
            print("   ✓ 步驟1: 數據準備完成")
            
            # 2. 因子計算
            alpha_factory = get_alpha_factory()
            factor_matrix = alpha_factory.calculate_all_factors(data)
            print(f"   ✓ 步驟2: 計算 {factor_matrix.shape[1]} 個因子")
            
            # 3. ML預測
            ml_predictor = get_ml_predictor()
            X, y = ml_predictor.prepare_training_data(data, ['BTCUSDT', 'ETHUSDT'])
            
            if not X.empty:
                training_results = ml_predictor.train_models(X, y)
                prediction = ml_predictor.predict(data, ['BTCUSDT', 'ETHUSDT'])
                print(f"   ✓ 步驟3: ML預測 {prediction['prediction']:.6f}")
            
            # 4. 風險評估
            risk_manager = get_advanced_risk_manager()
            returns = pd.Series(np.random.normal(0, 0.02, 100))
            var_result = risk_manager.calculate_var(returns)
            print(f"   ✓ 步驟4: 風險評估 VaR={var_result['var']:.6f}")
            
            # 5. 數據存儲
            db_manager = get_database_manager()
            trade_data = {
                'pair_key': 'BTCUSDT-ETHUSDT',
                'pnl': prediction.get('prediction', 0) * 1000,
                'trade_type': 'ml_enhanced',
                'metadata': {'end_to_end_test': True}
            }
            trade_id = db_manager.record_trade(trade_data)
            print(f"   ✓ 步驟5: 數據存儲 ID={trade_id}")
            
            print("   🎉 端到端工作流程測試成功！")
            return True
            
        except Exception as e:
            logger.error(f"端到端工作流程測試失敗: {e}")
            return False
    
    def run_complete_system_test(self) -> Dict:
        """運行完整系統測試"""
        print("🚀 開始完整系統集成測試...")
        print("=" * 80)
        
        # 執行所有測試
        tests = [
            ("數據庫集成", self.test_database_integration),
            ("Alpha因子工廠集成", self.test_alpha_factory_integration),
            ("ML預測器集成", self.test_ml_predictor_integration),
            ("風險管理器集成", self.test_risk_manager_integration),
            ("研究工具包集成", self.test_research_toolkit_integration),
            ("Web界面集成", self.test_web_interface_integration),
            ("端到端工作流程", self.test_end_to_end_workflow)
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            try:
                result = test_func()
                self.test_results[test_name] = result
                
                if result:
                    print(f"✅ {test_name} 測試通過")
                    passed_tests += 1
                else:
                    print(f"❌ {test_name} 測試失敗")
                    
            except Exception as e:
                print(f"❌ {test_name} 測試異常: {e}")
                self.test_results[test_name] = False
        
        # 生成測試報告
        print("\n" + "=" * 80)
        print("📊 完整系統測試結果總結")
        print("=" * 80)
        
        success_rate = (passed_tests / total_tests) * 100
        test_duration = (datetime.now() - self.start_time).total_seconds()
        
        print(f"通過測試: {passed_tests}/{total_tests}")
        print(f"成功率: {success_rate:.1f}%")
        print(f"測試時間: {test_duration:.1f} 秒")
        
        print("\n詳細結果:")
        for test_name, result in self.test_results.items():
            status = "✅ 通過" if result else "❌ 失敗"
            print(f"  {test_name}: {status}")
        
        if success_rate >= 85:
            print("\n🎉 完整系統集成測試通過！")
            print("🚀 系統已準備好進行生產部署！")
            print("\n🌟 恭喜！您現在擁有一個完整的量化交易平台：")
            print("   • 多配對投資組合管理")
            print("   • Alpha因子工廠")
            print("   • 機器學習預測")
            print("   • 高級風險管理")
            print("   • 量化研究環境")
            print("   • Web監控儀表板")
            print("   • 容器化部署")
        else:
            print("\n⚠️ 系統集成測試存在問題")
            print("請檢查失敗的測試項目並修復問題。")
        
        return {
            'success_rate': success_rate,
            'passed_tests': passed_tests,
            'total_tests': total_tests,
            'test_duration': test_duration,
            'detailed_results': self.test_results
        }


def main():
    """主函數"""
    try:
        tester = CompleteSystemTester()
        results = tester.run_complete_system_test()
        
        # 保存測試結果
        import json
        from pathlib import Path
        
        results_dir = Path("test_results")
        results_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = results_dir / f"complete_system_test_{timestamp}.json"
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'test_type': 'complete_system_integration',
                'results': results
            }, f, indent=2, ensure_ascii=False)
        
        print(f"\n📁 測試結果已保存: {results_file}")
        
        return 0 if results['success_rate'] >= 85 else 1
        
    except Exception as e:
        logger.error(f"完整系統測試執行失敗: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
