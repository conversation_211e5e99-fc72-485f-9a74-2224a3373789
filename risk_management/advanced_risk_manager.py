#!/usr/bin/env python3
"""
高級風險管理器 - VaR, CVaR, 壓力測試, 風險歸因
Advanced Risk Manager - VaR, CVaR, Stress Testing, Risk Attribution
"""

import warnings
from datetime import datetime, timedelta
from typing import Dict, List

import numpy as np
import pandas as pd
from scipy import stats

warnings.filterwarnings("ignore")

from database_manager import get_database_manager
from logging_config import get_logger

logger = get_logger(__name__)


class AdvancedRiskManager:
    """高級風險管理器"""

    def __init__(self, confidence_level: float = 0.95):
        self.confidence_level = confidence_level
        self.db_manager = get_database_manager()

        # 風險模型參數
        self.var_window = 252  # VaR 計算窗口
        self.stress_scenarios = self._load_stress_scenarios()

        # 風險歸因緩存
        self.risk_attribution_cache = {}

        logger.info("AdvancedRiskManager 初始化完成")

    def _load_stress_scenarios(self) -> Dict:
        """載入壓力測試情景"""
        return {
            "covid_crash_2020": {
                "description": "2020年3月新冠疫情崩盤",
                "market_shock": -0.35,  # 市場下跌35%
                "volatility_spike": 3.0,  # 波動率增加3倍
                "correlation_increase": 0.3,  # 相關性增加
                "liquidity_impact": 0.02,  # 流動性成本增加2%
            },
            "flash_crash_2010": {
                "description": "2010年5月閃電崩盤",
                "market_shock": -0.09,
                "volatility_spike": 5.0,
                "correlation_increase": 0.5,
                "liquidity_impact": 0.05,
            },
            "crypto_winter_2022": {
                "description": "2022年加密貨幣寒冬",
                "market_shock": -0.70,
                "volatility_spike": 2.5,
                "correlation_increase": 0.4,
                "liquidity_impact": 0.03,
            },
            "interest_rate_shock": {
                "description": "利率急劇上升",
                "market_shock": -0.15,
                "volatility_spike": 1.5,
                "correlation_increase": 0.2,
                "liquidity_impact": 0.01,
            },
        }

    def calculate_var(self, returns: pd.Series, method: str = "historical") -> Dict:
        """計算 Value at Risk"""
        try:
            if len(returns) < 30:
                logger.warning("數據不足，無法計算 VaR")
                return {"var": 0, "method": method, "confidence": self.confidence_level}

            alpha = 1 - self.confidence_level

            if method == "historical":
                var = np.percentile(returns, alpha * 100)

            elif method == "parametric":
                mean = returns.mean()
                std = returns.std()
                var = mean + stats.norm.ppf(alpha) * std

            elif method == "monte_carlo":
                # 蒙特卡羅模擬
                n_simulations = 10000
                mean = returns.mean()
                std = returns.std()

                # 假設正態分佈
                simulated_returns = np.random.normal(mean, std, n_simulations)
                var = np.percentile(simulated_returns, alpha * 100)

            else:
                raise ValueError(f"未知的 VaR 計算方法: {method}")

            return {
                "var": var,
                "var_dollar": var,  # 如果需要轉換為美元金額
                "method": method,
                "confidence": self.confidence_level,
                "observations": len(returns),
            }

        except Exception as e:
            logger.error(f"計算 VaR 失敗: {e}")
            return {"var": 0, "method": method, "confidence": self.confidence_level}

    def calculate_cvar(self, returns: pd.Series, method: str = "historical") -> Dict:
        """計算 Conditional Value at Risk (Expected Shortfall)"""
        try:
            var_result = self.calculate_var(returns, method)
            var_value = var_result["var"]

            if var_value == 0:
                return {"cvar": 0, "var": 0, "method": method}

            # 計算超過 VaR 的條件期望損失
            tail_losses = returns[returns <= var_value]

            if len(tail_losses) == 0:
                cvar = var_value
            else:
                cvar = tail_losses.mean()

            return {
                "cvar": cvar,
                "var": var_value,
                "tail_observations": len(tail_losses),
                "method": method,
                "confidence": self.confidence_level,
            }

        except Exception as e:
            logger.error(f"計算 CVaR 失敗: {e}")
            return {"cvar": 0, "var": 0, "method": method}

    def stress_test(self, portfolio_data: Dict, scenario: str = "covid_crash_2020") -> Dict:
        """壓力測試"""
        try:
            if scenario not in self.stress_scenarios:
                logger.error(f"未知的壓力測試情景: {scenario}")
                return {}

            scenario_params = self.stress_scenarios[scenario]

            # 獲取投資組合當前狀態
            current_positions = portfolio_data.get("positions", {})
            current_pnl = portfolio_data.get("total_pnl", 0)

            # 模擬壓力情景下的損失
            stress_results = {}
            total_stress_loss = 0

            for pair_key, position_data in current_positions.items():
                # 基礎市場衝擊
                market_loss = position_data.get("value", 0) * scenario_params["market_shock"]

                # 波動率衝擊（影響止損觸發概率）
                volatility_loss = (
                    position_data.get("value", 0) * 0.01 * scenario_params["volatility_spike"]
                )

                # 流動性衝擊（滑價成本）
                liquidity_loss = position_data.get("value", 0) * scenario_params["liquidity_impact"]

                # 相關性衝擊（配對交易特有）
                correlation_loss = self._calculate_correlation_impact(
                    position_data, scenario_params["correlation_increase"]
                )

                pair_total_loss = market_loss + volatility_loss + liquidity_loss + correlation_loss
                total_stress_loss += pair_total_loss

                stress_results[pair_key] = {
                    "market_loss": market_loss,
                    "volatility_loss": volatility_loss,
                    "liquidity_loss": liquidity_loss,
                    "correlation_loss": correlation_loss,
                    "total_loss": pair_total_loss,
                }

            # 計算壓力測試後的投資組合狀態
            stressed_pnl = current_pnl + total_stress_loss
            stressed_return = total_stress_loss / portfolio_data.get("total_capital", 1)

            return {
                "scenario": scenario,
                "description": scenario_params["description"],
                "total_stress_loss": total_stress_loss,
                "stressed_pnl": stressed_pnl,
                "stressed_return": stressed_return,
                "pair_breakdown": stress_results,
                "scenario_params": scenario_params,
            }

        except Exception as e:
            logger.error(f"壓力測試失敗: {e}")
            return {}

    def _calculate_correlation_impact(
        self, position_data: Dict, correlation_increase: float
    ) -> float:
        """計算相關性衝擊對配對交易的影響"""
        try:
            # 配對交易的風險主要來自相關性破裂
            position_value = position_data.get("value", 0)
            current_correlation = position_data.get("correlation", 0.8)

            # 相關性增加會降低配對交易的有效性
            # 這裡簡化為線性關係
            correlation_risk = position_value * correlation_increase * 0.1

            return correlation_risk

        except Exception as e:
            logger.error(f"計算相關性衝擊失敗: {e}")
            return 0

    def risk_attribution(self, portfolio_data: Dict, time_period: int = 30) -> Dict:
        """風險歸因分析"""
        try:
            # 獲取歷史數據
            end_date = datetime.now()
            start_date = end_date - timedelta(days=time_period)

            attribution_results = {}

            # 獲取投資組合歷史表現
            portfolio_history = self.db_manager.get_portfolio_history(days=time_period)

            if portfolio_history.empty:
                logger.warning("沒有足夠的歷史數據進行風險歸因")
                return {}

            # 計算投資組合收益
            portfolio_returns = portfolio_history["total_pnl"].pct_change().dropna()

            if len(portfolio_returns) < 10:
                logger.warning("歷史數據不足，無法進行風險歸因")
                return {}

            # 1. 市場風險歸因 (Beta 風險)
            market_attribution = self._attribute_market_risk(portfolio_returns)
            attribution_results["market_risk"] = market_attribution

            # 2. 特異風險歸因 (Alpha 風險)
            idiosyncratic_attribution = self._attribute_idiosyncratic_risk(portfolio_data)
            attribution_results["idiosyncratic_risk"] = idiosyncratic_attribution

            # 3. 配對特定風險歸因
            pair_attribution = self._attribute_pair_specific_risk(portfolio_data)
            attribution_results["pair_specific_risk"] = pair_attribution

            # 4. 流動性風險歸因
            liquidity_attribution = self._attribute_liquidity_risk(portfolio_data)
            attribution_results["liquidity_risk"] = liquidity_attribution

            # 5. 模型風險歸因
            model_attribution = self._attribute_model_risk(portfolio_data)
            attribution_results["model_risk"] = model_attribution

            return attribution_results

        except Exception as e:
            logger.error(f"風險歸因分析失敗: {e}")
            return {}

    def _attribute_market_risk(self, portfolio_returns: pd.Series) -> Dict:
        """市場風險歸因"""
        try:
            # 這裡簡化為使用投資組合自身的波動率作為市場風險代理
            market_volatility = portfolio_returns.std() * np.sqrt(252)  # 年化波動率
            market_var = self.calculate_var(portfolio_returns)["var"]

            return {
                "market_volatility": market_volatility,
                "market_var": market_var,
                "contribution_to_total_risk": 0.3,  # 簡化假設
            }

        except Exception as e:
            logger.error(f"市場風險歸因失敗: {e}")
            return {}

    def _attribute_idiosyncratic_risk(self, portfolio_data: Dict) -> Dict:
        """特異風險歸因"""
        try:
            positions = portfolio_data.get("positions", {})

            idiosyncratic_risk = 0
            pair_contributions = {}

            for pair_key, position_data in positions.items():
                # 獲取配對的歷史表現
                pair_trades = self.db_manager.get_trades(pair_key=pair_key, limit=50)

                if pair_trades:
                    pair_returns = [trade["pnl"] for trade in pair_trades]
                    pair_volatility = np.std(pair_returns) if len(pair_returns) > 1 else 0

                    # 配對的特異風險貢獻
                    position_weight = position_data.get("weight", 0)
                    pair_contribution = pair_volatility * position_weight

                    pair_contributions[pair_key] = pair_contribution
                    idiosyncratic_risk += pair_contribution**2

            idiosyncratic_risk = np.sqrt(idiosyncratic_risk)

            return {
                "total_idiosyncratic_risk": idiosyncratic_risk,
                "pair_contributions": pair_contributions,
            }

        except Exception as e:
            logger.error(f"特異風險歸因失敗: {e}")
            return {}

    def _attribute_pair_specific_risk(self, portfolio_data: Dict) -> Dict:
        """配對特定風險歸因"""
        try:
            positions = portfolio_data.get("positions", {})

            risk_factors = {"cointegration_risk": 0, "correlation_risk": 0, "spread_risk": 0}

            for pair_key, position_data in positions.items():
                # 共整合風險
                cointegration_pvalue = position_data.get("cointegration_pvalue", 0.5)
                if cointegration_pvalue > 0.05:  # 共整合關係減弱
                    risk_factors["cointegration_risk"] += position_data.get("value", 0) * 0.02

                # 相關性風險
                correlation = position_data.get("correlation", 0.8)
                if correlation < 0.6:  # 相關性過低
                    risk_factors["correlation_risk"] += position_data.get("value", 0) * 0.01

                # 價差風險
                current_zscore = abs(position_data.get("current_zscore", 0))
                if current_zscore > 3:  # 價差過度偏離
                    risk_factors["spread_risk"] += position_data.get("value", 0) * 0.015

            return risk_factors

        except Exception as e:
            logger.error(f"配對特定風險歸因失敗: {e}")
            return {}

    def _attribute_liquidity_risk(self, portfolio_data: Dict) -> Dict:
        """流動性風險歸因"""
        try:
            positions = portfolio_data.get("positions", {})

            liquidity_risk = 0

            for pair_key, position_data in positions.items():
                # 基於成交量的流動性評估
                avg_volume = position_data.get("avg_volume", 1000000)
                position_size = position_data.get("value", 0)

                # 流動性風險 = 倉位大小 / 平均成交量
                liquidity_ratio = position_size / avg_volume

                # 流動性成本估算
                if liquidity_ratio > 0.01:  # 倉位超過日均成交量的1%
                    liquidity_cost = position_size * 0.002 * liquidity_ratio
                    liquidity_risk += liquidity_cost

            return {
                "total_liquidity_risk": liquidity_risk,
                "liquidity_cost_estimate": liquidity_risk,
            }

        except Exception as e:
            logger.error(f"流動性風險歸因失敗: {e}")
            return {}

    def _attribute_model_risk(self, portfolio_data: Dict) -> Dict:
        """模型風險歸因"""
        try:
            # 模型風險來源
            model_risks = {
                "parameter_uncertainty": 0,
                "model_specification": 0,
                "estimation_error": 0,
            }

            positions = portfolio_data.get("positions", {})

            for pair_key, position_data in positions.items():
                position_value = position_data.get("value", 0)

                # 參數不確定性
                # 基於歷史交易數量評估參數穩定性
                trade_count = position_data.get("trade_count", 0)
                if trade_count < 20:  # 交易樣本不足
                    model_risks["parameter_uncertainty"] += position_value * 0.01

                # 模型設定風險
                # 基於模型假設的違背程度
                model_risks["model_specification"] += position_value * 0.005

                # 估計誤差
                # 基於預測準確度
                prediction_accuracy = position_data.get("prediction_accuracy", 0.5)
                estimation_error = (1 - prediction_accuracy) * position_value * 0.01
                model_risks["estimation_error"] += estimation_error

            return model_risks

        except Exception as e:
            logger.error(f"模型風險歸因失敗: {e}")
            return {}

    def generate_risk_report(self, portfolio_data: Dict) -> Dict:
        """生成綜合風險報告"""
        try:
            # 獲取投資組合收益序列
            portfolio_history = self.db_manager.get_portfolio_history(days=60)

            if portfolio_history.empty:
                logger.warning("沒有歷史數據，無法生成風險報告")
                return {}

            portfolio_returns = portfolio_history["total_pnl"].pct_change().dropna()

            # 計算各種風險指標
            var_results = {}
            cvar_results = {}

            for method in ["historical", "parametric", "monte_carlo"]:
                var_results[method] = self.calculate_var(portfolio_returns, method)
                cvar_results[method] = self.calculate_cvar(portfolio_returns, method)

            # 壓力測試
            stress_results = {}
            for scenario in self.stress_scenarios.keys():
                stress_results[scenario] = self.stress_test(portfolio_data, scenario)

            # 風險歸因
            attribution = self.risk_attribution(portfolio_data)

            # 綜合風險評分
            risk_score = self._calculate_risk_score(var_results, stress_results, attribution)

            return {
                "timestamp": datetime.now().isoformat(),
                "var_analysis": var_results,
                "cvar_analysis": cvar_results,
                "stress_testing": stress_results,
                "risk_attribution": attribution,
                "risk_score": risk_score,
                "recommendations": self._generate_risk_recommendations(risk_score, attribution),
            }

        except Exception as e:
            logger.error(f"生成風險報告失敗: {e}")
            return {}

    def _calculate_risk_score(
        self, var_results: Dict, stress_results: Dict, attribution: Dict
    ) -> Dict:
        """計算綜合風險評分"""
        try:
            # VaR 評分 (0-100)
            historical_var = abs(var_results.get("historical", {}).get("var", 0))
            var_score = min(historical_var * 1000, 100)  # 標準化到0-100

            # 壓力測試評分
            max_stress_loss = 0
            for scenario, result in stress_results.items():
                stress_return = abs(result.get("stressed_return", 0))
                max_stress_loss = max(max_stress_loss, stress_return)

            stress_score = min(max_stress_loss * 100, 100)

            # 風險歸因評分
            attribution_score = 50  # 默認中等風險
            if attribution:
                # 基於各種風險因子的綜合評估
                market_risk = attribution.get("market_risk", {}).get("market_volatility", 0)
                attribution_score = min(market_risk * 100, 100)

            # 綜合評分
            overall_score = var_score * 0.4 + stress_score * 0.4 + attribution_score * 0.2

            return {
                "overall_score": overall_score,
                "var_score": var_score,
                "stress_score": stress_score,
                "attribution_score": attribution_score,
                "risk_level": self._get_risk_level(overall_score),
            }

        except Exception as e:
            logger.error(f"計算風險評分失敗: {e}")
            return {"overall_score": 50, "risk_level": "medium"}

    def _get_risk_level(self, score: float) -> str:
        """根據評分獲取風險等級"""
        if score < 20:
            return "low"
        elif score < 40:
            return "medium-low"
        elif score < 60:
            return "medium"
        elif score < 80:
            return "medium-high"
        else:
            return "high"

    def _generate_risk_recommendations(self, risk_score: Dict, attribution: Dict) -> List[str]:
        """生成風險管理建議"""
        recommendations = []

        overall_score = risk_score.get("overall_score", 50)
        risk_level = risk_score.get("risk_level", "medium")

        if risk_level in ["high", "medium-high"]:
            recommendations.append("建議減少倉位規模以降低整體風險敞口")
            recommendations.append("考慮增加對沖策略以降低市場風險")

        if risk_score.get("stress_score", 0) > 70:
            recommendations.append("在壓力情景下損失較大，建議優化止損策略")
            recommendations.append("考慮增加流動性較好的交易對")

        # 基於風險歸因的建議
        if attribution:
            pair_risk = attribution.get("pair_specific_risk", {})
            if pair_risk.get("cointegration_risk", 0) > 0:
                recommendations.append("檢測到共整合風險，建議重新評估配對關係")

            if pair_risk.get("correlation_risk", 0) > 0:
                recommendations.append("相關性風險較高，建議調整配對選擇標準")

        if not recommendations:
            recommendations.append("當前風險水平可接受，繼續監控")

        return recommendations


# 全局高級風險管理器實例
_advanced_risk_manager = None


def get_advanced_risk_manager() -> AdvancedRiskManager:
    """獲取全局高級風險管理器實例"""
    global _advanced_risk_manager
    if _advanced_risk_manager is None:
        _advanced_risk_manager = AdvancedRiskManager()
    return _advanced_risk_manager


if __name__ == "__main__":
    # 測試高級風險管理器
    print("測試高級風險管理器...")

    # 生成模擬數據
    np.random.seed(42)
    returns = pd.Series(np.random.normal(-0.001, 0.02, 100))

    risk_manager = AdvancedRiskManager()

    # 測試 VaR 計算
    var_result = risk_manager.calculate_var(returns, "historical")
    print(f"Historical VaR: {var_result['var']:.4f}")

    # 測試 CVaR 計算
    cvar_result = risk_manager.calculate_cvar(returns, "historical")
    print(f"Historical CVaR: {cvar_result['cvar']:.4f}")

    # 測試壓力測試
    portfolio_data = {
        "total_pnl": 1000,
        "total_capital": 10000,
        "positions": {"BTCUSDT-ETHUSDT": {"value": 5000, "correlation": 0.8, "weight": 0.5}},
    }

    stress_result = risk_manager.stress_test(portfolio_data, "covid_crash_2020")
    print(f"壓力測試損失: {stress_result.get('total_stress_loss', 0):.2f}")

    print("高級風險管理器測試完成！")
