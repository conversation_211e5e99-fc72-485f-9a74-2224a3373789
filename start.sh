#!/bin/bash

# 配對交易平台啟動腳本
# Pair Trading Platform Startup Script

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日誌函數
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 顯示歡迎信息
show_welcome() {
    echo "=================================================="
    echo "    配對交易平台 v2.0"
    echo "    Pair Trading Platform v2.0"
    echo "=================================================="
    echo ""
}

# 檢查依賴
check_dependencies() {
    log_info "檢查系統依賴..."

    # 檢查 Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python 3 未安裝"
        exit 1
    fi

    # 檢查 pip
    if ! command -v pip3 &> /dev/null; then
        log_error "pip3 未安裝"
        exit 1
    fi

    log_info "依賴檢查完成"
}

# 安裝 Python 依賴
install_python_deps() {
    log_info "安裝 Python 依賴..."

    if [ -f "requirements.txt" ]; then
        pip3 install -r requirements.txt
        log_info "Python 依賴安裝完成"
    else
        log_error "requirements.txt 文件不存在"
        exit 1
    fi
}

# 創建必要目錄
create_directories() {
    log_info "創建必要目錄..."

    directories=(
        "data"
        "logs"
        "records"
        "backtest_results"
        "pair_selection_results"
        "test_results"
        "ml_models"
    )

    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
        fi
    done

    log_info "目錄創建完成"
}

# 檢查配置文件
check_config() {
    log_info "檢查配置文件..."

    # 檢查主配置文件
    if [ ! -f "config.json" ]; then
        log_error "config.json 文件不存在"
        exit 1
    fi

    # 檢查環境變量文件
    if [ ! -f ".env" ]; then
        log_warn ".env 文件不存在，將使用默認配置"
        if [ -f ".env.template" ]; then
            cp .env.template .env
            log_info "已從模板創建 .env 文件，請編輯後重新運行"
            exit 0
        fi
    fi

    log_info "配置文件檢查完成"
}

# 運行系統測試
run_tests() {
    log_info "運行系統測試..."

    # 運行投資組合測試
    if [ -f "test_portfolio_system.py" ]; then
        python3 test_portfolio_system.py
    fi

    log_info "系統測試完成"
}

# 啟動服務
start_service() {
    local mode=$1
    local timeframe=${2:-"5m"}

    log_info "啟動配對交易平台..."
    log_info "模式: $mode"
    log_info "時間框架: $timeframe"

    case $mode in
        "single")
            log_info "啟動單配對交易模式..."
            python3 main.py live --timeframe "$timeframe"
            ;;
        "portfolio")
            log_info "啟動投資組合交易模式..."
            python3 main.py portfolio --timeframe "$timeframe"
            ;;
        "backtest")
            log_info "啟動回測模式..."
            python3 main.py backtest
            ;;
        "dashboard")
            log_info "僅啟動監控儀表板..."
            python3 -c "
from health_server import start_health_server
import time
server = start_health_server()
print('儀表板已啟動: http://localhost:8080/dashboard')
try:
    while True:
        time.sleep(1)
except KeyboardInterrupt:
    print('儀表板已停止')
"
            ;;
        *)
            log_error "未知模式: $mode"
            show_usage
            exit 1
            ;;
    esac
}

# 顯示使用說明
show_usage() {
    echo "配對交易平台啟動腳本"
    echo ""
    echo "用法: $0 [選項] [模式] [時間框架]"
    echo ""
    echo "選項:"
    echo "  -h, --help          顯示此幫助信息"
    echo "  -t, --test          運行系統測試"
    echo "  --no-deps           跳過依賴安裝"
    echo ""
    echo "模式:"
    echo "  single              單配對交易模式"
    echo "  portfolio           投資組合交易模式 (推薦)"
    echo "  backtest            回測模式"
    echo "  dashboard           僅啟動監控儀表板"
    echo ""
    echo "時間框架:"
    echo "  1m, 5m, 15m, 1h    交易時間框架 (默認: 5m)"
    echo ""
    echo "示例:"
    echo "  $0 portfolio 5m     # 啟動投資組合模式，5分鐘時間框架"
    echo "  $0 -t               # 運行系統測試"
    echo "  $0 dashboard        # 僅啟動儀表板"
}

# 主函數
main() {
    local mode="portfolio"
    local timeframe="5m"
    local run_tests=false
    local skip_deps=false

    # 解析命令行參數
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_usage
                exit 0
                ;;
            -t|--test)
                run_tests=true
                shift
                ;;
            --no-deps)
                skip_deps=true
                shift
                ;;
            single|portfolio|backtest|dashboard)
                mode=$1
                shift
                ;;
            1m|5m|15m|1h)
                timeframe=$1
                shift
                ;;
            *)
                log_error "未知參數: $1"
                show_usage
                exit 1
                ;;
        esac
    done

    # 顯示歡迎信息
    show_welcome

    # 檢查依賴
    check_dependencies

    # 安裝依賴
    if [ "$skip_deps" = false ]; then
        install_python_deps
    fi

    # 創建目錄
    create_directories

    # 檢查配置
    check_config

    # 運行測試
    if [ "$run_tests" = true ]; then
        run_tests
        exit 0
    fi

    # 啟動服務
    start_service "$mode" "$timeframe"
}

# 運行主函數
main "$@"
