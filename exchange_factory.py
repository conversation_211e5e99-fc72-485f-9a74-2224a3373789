#!/usr/bin/env python3
"""
交易所工廠 - 解耦交易所實例化邏輯
Exchange Factory - Decouple exchange instantiation logic
"""

import ccxt
from typing import Dict, Any, Optional, Protocol
from abc import ABC, abstractmethod

from trading_exceptions import ConfigError
from logging_config import get_logger

logger = get_logger(__name__)


class ExchangeGateway(Protocol):
    """交易所網關接口"""
    
    def create_market_order(self, symbol: str, side: str, amount: float) -> Dict[str, Any]:
        """創建市價單"""
        ...
    
    def fetch_ticker(self, symbol: str) -> Dict[str, Any]:
        """獲取價格信息"""
        ...
    
    def fetch_ohlcv(self, symbol: str, timeframe: str, limit: int = 100) -> list:
        """獲取K線數據"""
        ...
    
    def fetch_balance(self) -> Dict[str, Any]:
        """獲取餘額"""
        ...


class CCXTExchangeGateway:
    """CCXT交易所網關實現"""
    
    def __init__(self, exchange: ccxt.Exchange):
        self.exchange = exchange
    
    def create_market_order(self, symbol: str, side: str, amount: float) -> Dict[str, Any]:
        """創建市價單"""
        return self.exchange.create_market_order(symbol, side, amount)
    
    def fetch_ticker(self, symbol: str) -> Dict[str, Any]:
        """獲取價格信息"""
        return self.exchange.fetch_ticker(symbol)
    
    def fetch_ohlcv(self, symbol: str, timeframe: str, limit: int = 100) -> list:
        """獲取K線數據"""
        return self.exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
    
    def fetch_balance(self) -> Dict[str, Any]:
        """獲取餘額"""
        return self.exchange.fetch_balance()


class MockExchangeGateway:
    """模擬交易所網關（用於測試）"""
    
    def __init__(self, mock_data: Optional[Dict] = None):
        self.mock_data = mock_data or {}
        self.order_counter = 1
        
    def create_market_order(self, symbol: str, side: str, amount: float) -> Dict[str, Any]:
        """模擬創建市價單"""
        order_id = f"mock_order_{self.order_counter}"
        self.order_counter += 1
        
        # 模擬價格
        mock_price = self.mock_data.get('prices', {}).get(symbol, 50000)
        
        return {
            'id': order_id,
            'symbol': symbol,
            'side': side,
            'amount': amount,
            'price': mock_price,
            'status': 'closed',
            'filled': amount
        }
    
    def fetch_ticker(self, symbol: str) -> Dict[str, Any]:
        """模擬獲取價格"""
        mock_price = self.mock_data.get('prices', {}).get(symbol, 50000)
        return {
            'symbol': symbol,
            'last': mock_price,
            'bid': mock_price * 0.999,
            'ask': mock_price * 1.001
        }
    
    def fetch_ohlcv(self, symbol: str, timeframe: str, limit: int = 100) -> list:
        """模擬獲取K線數據"""
        mock_price = self.mock_data.get('prices', {}).get(symbol, 50000)
        # 返回模擬的OHLCV數據
        return [[1640995200000, mock_price, mock_price, mock_price, mock_price, 100] for _ in range(limit)]
    
    def fetch_balance(self) -> Dict[str, Any]:
        """模擬獲取餘額"""
        return {
            'USDT': {'free': 10000, 'used': 0, 'total': 10000},
            'BTC': {'free': 0.1, 'used': 0, 'total': 0.1},
            'ETH': {'free': 1.0, 'used': 0, 'total': 1.0}
        }


class ExchangeFactory:
    """交易所工廠"""
    
    @staticmethod
    def create_exchange_gateway(config: Dict[str, Any], 
                              mock_mode: bool = False) -> ExchangeGateway:
        """
        創建交易所網關
        
        Args:
            config: 交易所配置
            mock_mode: 是否使用模擬模式
            
        Returns:
            ExchangeGateway: 交易所網關實例
        """
        if mock_mode:
            logger.info("創建模擬交易所網關")
            return MockExchangeGateway()
        
        try:
            # 提取配置
            exchange_name = config.get('exchange', {}).get('name', 'binance')
            api_key = config.get('api_key', '')
            secret = config.get('secret', '')
            passphrase = config.get('passphrase', '')
            sandbox = config.get('sandbox', True)
            
            # 驗證必需參數
            if not api_key or not secret:
                raise ConfigError("API密鑰和密鑰不能為空")
            
            # 創建CCXT交易所實例
            exchange_class = getattr(ccxt, exchange_name)
            
            exchange_config = {
                'apiKey': api_key,
                'secret': secret,
                'sandbox': sandbox,
                'enableRateLimit': True,
                'timeout': 30000,
            }
            
            # 某些交易所需要passphrase
            if passphrase:
                exchange_config['password'] = passphrase
            
            # 期貨交易配置
            trading_mode = config.get('trading_mode', 'futures')
            if trading_mode == 'futures':
                exchange_config['options'] = {
                    'defaultType': 'future',  # 設置為期貨交易
                }
                
                # 槓桿和保證金模式
                leverage = config.get('leverage', 1)
                margin_mode = config.get('margin_mode', 'isolated')
                
                logger.info(f"配置期貨交易: 槓桿 {leverage}x, 保證金模式 {margin_mode}")
            
            exchange = exchange_class(exchange_config)
            
            # 測試連接
            try:
                exchange.load_markets()
                logger.info(f"成功連接到 {exchange_name} ({'沙盒' if sandbox else '生產'}環境)")
            except Exception as e:
                logger.error(f"交易所連接測試失敗: {e}")
                raise ConfigError(f"無法連接到交易所: {e}")
            
            return CCXTExchangeGateway(exchange)
            
        except Exception as e:
            logger.error(f"創建交易所網關失敗: {e}")
            raise ConfigError(f"交易所初始化失敗: {e}")
    
    @staticmethod
    def get_supported_exchanges() -> list:
        """獲取支持的交易所列表"""
        return ['binance', 'gate', 'bitmart', 'okex', 'huobi', 'bybit']
    
    @staticmethod
    def validate_exchange_config(config: Dict[str, Any]) -> Dict[str, Any]:
        """
        驗證交易所配置
        
        Args:
            config: 配置字典
            
        Returns:
            Dict: 驗證結果
        """
        result = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        # 檢查交易所名稱
        exchange_name = config.get('exchange', {}).get('name', '')
        if not exchange_name:
            result['errors'].append("交易所名稱未設置")
            result['valid'] = False
        elif exchange_name not in ExchangeFactory.get_supported_exchanges():
            result['warnings'].append(f"交易所 {exchange_name} 可能不受支持")
        
        # 檢查API配置
        api_key = config.get('api_key', '')
        secret = config.get('secret', '')
        
        if not api_key:
            result['errors'].append("API密鑰未設置")
            result['valid'] = False
        elif api_key in ['your_api_key', 'test_api_key']:
            result['warnings'].append("API密鑰使用默認值")
        
        if not secret:
            result['errors'].append("API密鑰未設置")
            result['valid'] = False
        elif secret in ['your_secret', 'test_secret']:
            result['warnings'].append("API密鑰使用默認值")
        
        # 檢查期貨配置
        trading_mode = config.get('trading_mode', 'spot')
        if trading_mode == 'futures':
            leverage = config.get('leverage', 1)
            if leverage > 20:
                result['warnings'].append(f"槓桿倍數較高: {leverage}x")
            elif leverage > 50:
                result['errors'].append(f"槓桿倍數過高: {leverage}x")
                result['valid'] = False
        
        return result


if __name__ == "__main__":
    # 測試工廠
    print("🧪 交易所工廠測試")
    
    # 測試模擬模式
    mock_gateway = ExchangeFactory.create_exchange_gateway({}, mock_mode=True)
    print("✅ 模擬交易所網關創建成功")
    
    # 測試模擬訂單
    order = mock_gateway.create_market_order('BTC/USDT:USDT', 'buy', 0.001)
    print(f"✅ 模擬訂單: {order}")
    
    # 測試配置驗證
    test_config = {
        'exchange': {'name': 'gate'},
        'api_key': 'test_key',
        'secret': 'test_secret',
        'trading_mode': 'futures',
        'leverage': 50
    }
    
    validation = ExchangeFactory.validate_exchange_config(test_config)
    print(f"✅ 配置驗證: {validation}")
