[tool.poetry]
name = "pair-trading-v2"
version = "2.0.0"
description = "企業級配對交易量化平台 - Enterprise-grade Pairs Trading Quantitative Platform"
authors = ["Trading Team <<EMAIL>>"]
readme = "README.md"
packages = [{include = "pair_trading_v2", from = "."}]

[tool.poetry.dependencies]
python = "^3.9"

# 核心依賴 - Core Dependencies
ccxt = "^4.1.0"
pandas = "^2.1.0"
numpy = "^1.24.0"
scipy = "^1.11.0"
statsmodels = "^0.14.0"

# 數據庫 - Database
sqlalchemy = "^2.0.0"
alembic = "^1.12.0"
asyncpg = "^0.28.0"
aiosqlite = "^0.19.0"

# 異步支持 - Async Support
asyncio = "^3.4.3"
aiohttp = "^3.8.0"
aiofiles = "^23.2.0"

# 配置管理 - Configuration Management
pydantic = "^2.4.0"
pydantic-settings = "^2.0.0"
python-dotenv = "^1.0.0"
pyyaml = "^6.0"

# 日誌和監控 - Logging and Monitoring
structlog = "^23.1.0"
prometheus-client = "^0.17.0"
sentry-sdk = "^1.32.0"

# 任務調度 - Task Scheduling
apscheduler = "^3.10.0"
celery = "^5.3.0"
redis = "^4.6.0"

# 通知系統 - Notification System
python-telegram-bot = "^20.5"
discord-py = "^2.3.0"

# 機器學習 - Machine Learning
scikit-learn = "^1.3.0"
xgboost = "^1.7.0"
lightgbm = "^4.0.0"

# 可視化 - Visualization
plotly = "^5.15.0"
matplotlib = "^3.7.0"
seaborn = "^0.12.0"

# Web框架 - Web Framework
fastapi = "^0.103.0"
uvicorn = "^0.23.0"
websockets = "^11.0"

# 工具庫 - Utilities
click = "^8.1.0"
rich = "^13.5.0"
tqdm = "^4.66.0"
requests = "^2.31.0"

# 安全 - Security
cryptography = "^41.0.0"
bcrypt = "^4.0.0"

[tool.poetry.group.dev.dependencies]
# 測試框架 - Testing Framework
pytest = "^7.4.0"
pytest-asyncio = "^0.21.0"
pytest-cov = "^4.1.0"
pytest-mock = "^3.11.0"
pytest-benchmark = "^4.0.0"
pytest-xdist = "^3.3.0"

# 代碼質量 - Code Quality
black = "^23.7.0"
isort = "^5.12.0"
flake8 = "^6.0.0"
mypy = "^1.5.0"
pylint = "^2.17.0"

# 性能分析 - Performance Analysis
memory-profiler = "^0.61.0"
line-profiler = "^4.1.0"
py-spy = "^0.3.0"

# 文檔生成 - Documentation
sphinx = "^7.1.0"
sphinx-rtd-theme = "^1.3.0"

# 開發工具 - Development Tools
pre-commit = "^3.4.0"
jupyter = "^1.0.0"
ipython = "^8.14.0"

[tool.poetry.group.production.dependencies]
# 生產環境依賴 - Production Dependencies
gunicorn = "^21.2.0"
supervisor = "^4.2.0"

[tool.poetry.scripts]
# 命令行工具 - CLI Tools
pair-trading = "main:main"
backtest = "backtesting:main"
pair-select = "unified_pair_selector:main"
config-validate = "unified_config_manager:main"
test-runner = "unified_test_runner:main"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

# 工具配置 - Tool Configurations

[tool.black]
line-length = 100
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | _archive
)/
'''

[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "ccxt.*",
    "statsmodels.*",
    "scipy.*",
    "plotly.*",
    "telegram.*"
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "-ra",
    "--strict-markers",
    "--strict-config",
    "--cov=.",
    "--cov-report=term-missing",
    "--cov-report=html:test_reports/coverage/html",
    "--cov-report=xml:test_reports/coverage/coverage.xml",
]
testpaths = [
    "tests",
    "test_*.py"
]
markers = [
    "unit: Unit tests",
    "integration: Integration tests", 
    "system: System tests",
    "performance: Performance tests",
    "slow: Slow running tests",
    "network: Tests requiring network access",
    "database: Tests requiring database access"
]
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
    "ignore::UserWarning:statsmodels.*"
]

[tool.coverage.run]
source = ["."]
omit = [
    "*/tests/*",
    "*/test_*.py",
    "*/__pycache__/*",
    "*/venv/*",
    "*/env/*",
    "*/.venv/*",
    "*/_archive/*",
    "*/migrations/*",
    "setup.py"
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod"
]

[tool.pylint.messages_control]
disable = [
    "C0103",  # Invalid name
    "C0114",  # Missing module docstring
    "C0115",  # Missing class docstring
    "C0116",  # Missing function docstring
    "R0903",  # Too few public methods
    "R0913",  # Too many arguments
    "W0613",  # Unused argument
    "W0622",  # Redefined builtin
]

[tool.pylint.format]
max-line-length = 100

[tool.flake8]
max-line-length = 100
extend-ignore = [
    "E203",  # whitespace before ':'
    "E501",  # line too long
    "W503",  # line break before binary operator
]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    "_archive",
    ".venv",
    "venv"
]
