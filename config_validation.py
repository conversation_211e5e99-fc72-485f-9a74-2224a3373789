#!/usr/bin/env python3
"""
配置驗證器 - 使用pydantic進行配置類型檢查和驗證
Config Validator - Use pydantic for configuration type checking and validation
"""

try:
    from pydantic_settings import BaseSettings
    from pydantic import Field, validator, root_validator
except ImportError:
    # 降級到基本配置管理
    from pydantic import BaseModel as BaseSettings, Field
    def validator(*args, **kwargs):
        def decorator(func):
            return func
        return decorator
    def root_validator(*args, **kwargs):
        def decorator(func):
            return func
        return decorator
from typing import Dict, List, Optional, Union, Any
from enum import Enum
import os
import json
from pathlib import Path

from logging_config import get_logger

logger = get_logger(__name__)


class ExchangeType(str, Enum):
    """交易所類型枚舉"""
    BINANCE = "binance"
    OKEX = "okex"
    HUOBI = "huobi"
    BYBIT = "bybit"
    GATE = "gate"
    BITMART = "bitmart"


class TimeframeType(str, Enum):
    """時間框架類型枚舉"""
    MINUTE_1 = "1m"
    MINUTE_5 = "5m"
    MINUTE_15 = "15m"
    MINUTE_30 = "30m"
    HOUR_1 = "1h"
    HOUR_4 = "4h"
    DAY_1 = "1d"


class LogLevel(str, Enum):
    """日誌級別枚舉"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class TradingMode(str, Enum):
    """交易模式枚舉"""
    SPOT = "spot"
    FUTURES = "futures"


class FuturesType(str, Enum):
    """期貨類型枚舉"""
    LINEAR = "linear"      # U本位期貨
    INVERSE = "inverse"    # 幣本位期貨


class MarginMode(str, Enum):
    """保證金模式枚舉"""
    ISOLATED = "isolated"  # 逐倉
    CROSS = "cross"        # 全倉


class TradingConfig(BaseSettings):
    """交易配置"""

    # 交易所配置
    exchange: ExchangeType = Field(default=ExchangeType.GATE, description="交易所類型")
    api_key: Optional[str] = Field(default=None, description="API密鑰（從環境變量載入）")
    secret: Optional[str] = Field(default=None, description="API密鑰（從環境變量載入）")
    passphrase: Optional[str] = Field(default=None, description="API密碼（某些交易所需要）")
    sandbox: bool = Field(default=True, description="是否使用沙盒環境")

    # 交易模式配置
    trading_mode: TradingMode = Field(default=TradingMode.FUTURES, description="交易模式")
    futures_type: FuturesType = Field(default=FuturesType.LINEAR, description="期貨類型")
    leverage: int = Field(default=1, ge=1, le=100, description="槓桿倍數")
    margin_mode: MarginMode = Field(default=MarginMode.ISOLATED, description="保證金模式")
    
    # 交易參數
    trading_pairs: List[List[str]] = Field(
        default=[["BTC/USDT:USDT", "ETH/USDT:USDT"]],
        description="交易配對列表（期貨格式：BTC/USDT:USDT）"
    )
    timeframe: TimeframeType = Field(default=TimeframeType.MINUTE_5, description="時間框架")
    lookback_period: int = Field(default=100, ge=20, le=1000, description="回看期間")
    
    # 風險管理
    max_position_size: float = Field(default=0.1, gt=0, le=1.0, description="最大倉位大小")
    stop_loss_pct: float = Field(default=0.05, gt=0, le=0.5, description="止損百分比")
    take_profit_pct: float = Field(default=0.1, gt=0, le=1.0, description="止盈百分比")
    max_daily_trades: int = Field(default=10, ge=1, le=100, description="每日最大交易次數")
    
    # 策略參數
    entry_threshold: float = Field(default=2.0, gt=0, description="進場閾值")
    exit_threshold: float = Field(default=0.5, gt=0, description="出場閾值")
    min_half_life: float = Field(default=1.0, gt=0, description="最小半衰期")
    max_half_life: float = Field(default=100.0, gt=0, description="最大半衰期")
    
    @validator('trading_pairs')
    def validate_trading_pairs(cls, v):
        """驗證交易配對"""
        if not v:
            raise ValueError("交易配對不能為空")
        
        for pair in v:
            if not isinstance(pair, list) or len(pair) != 2:
                raise ValueError("每個交易配對必須包含兩個交易對")
            
            for symbol in pair:
                if not isinstance(symbol, str) or '/' not in symbol:
                    raise ValueError(f"無效的交易對格式: {symbol}")
        
        return v
    
    @validator('max_half_life')
    def validate_half_life_range(cls, v, values):
        """驗證半衰期範圍"""
        if 'min_half_life' in values and v <= values['min_half_life']:
            raise ValueError("最大半衰期必須大於最小半衰期")
        return v
    
    class Config:
        env_prefix = "TRADING_"
        case_sensitive = False


class DatabaseConfig(BaseSettings):
    """數據庫配置"""
    
    db_path: str = Field(default="data/trading_platform.db", description="數據庫路徑")
    backup_enabled: bool = Field(default=True, description="是否啟用備份")
    backup_interval_hours: int = Field(default=24, ge=1, le=168, description="備份間隔（小時）")
    max_backups: int = Field(default=7, ge=1, le=30, description="最大備份數量")
    
    # 性能配置
    batch_size: int = Field(default=100, ge=10, le=1000, description="批量處理大小")
    connection_timeout: float = Field(default=30.0, gt=0, description="連接超時時間")
    
    @validator('db_path')
    def validate_db_path(cls, v):
        """驗證數據庫路徑"""
        path = Path(v)
        path.parent.mkdir(parents=True, exist_ok=True)
        return str(path)
    
    class Config:
        env_prefix = "DB_"


class MonitoringConfig(BaseSettings):
    """監控配置"""
    
    # 健康檢查服務器
    health_server_enabled: bool = Field(default=True, description="是否啟用健康檢查服務器")
    health_server_port: int = Field(default=8080, ge=1024, le=65535, description="健康檢查服務器端口")
    health_server_host: str = Field(default="0.0.0.0", description="健康檢查服務器主機")
    
    # 內存監控
    memory_monitor_enabled: bool = Field(default=True, description="是否啟用內存監控")
    memory_threshold_mb: float = Field(default=1000.0, gt=0, description="內存閾值（MB）")
    memory_check_interval: int = Field(default=30, ge=5, le=300, description="內存檢查間隔（秒）")
    
    # 警報配置
    alert_enabled: bool = Field(default=True, description="是否啟用警報")
    telegram_bot_token: Optional[str] = Field(default=None, description="Telegram機器人令牌（從環境變量載入）")
    telegram_chat_id: Optional[str] = Field(default=None, description="Telegram聊天ID（從環境變量載入）")
    
    # 日誌配置
    log_level: LogLevel = Field(default=LogLevel.INFO, description="日誌級別")
    log_file_enabled: bool = Field(default=True, description="是否啟用日誌文件")
    log_file_path: str = Field(default="logs/trading_bot.log", description="日誌文件路徑")
    log_rotation_size_mb: int = Field(default=10, ge=1, le=100, description="日誌輪轉大小（MB）")
    log_retention_days: int = Field(default=30, ge=1, le=365, description="日誌保留天數")
    
    @validator('log_file_path')
    def validate_log_path(cls, v):
        """驗證日誌路徑"""
        path = Path(v)
        path.parent.mkdir(parents=True, exist_ok=True)
        return str(path)
    
    class Config:
        env_prefix = "MONITOR_"


class PerformanceConfig(BaseSettings):
    """性能配置"""
    
    # 異步配置
    async_enabled: bool = Field(default=True, description="是否啟用異步處理")
    max_concurrent_requests: int = Field(default=10, ge=1, le=100, description="最大並發請求數")
    request_timeout: float = Field(default=30.0, gt=0, description="請求超時時間")
    
    # 重試配置
    max_retries: int = Field(default=8, ge=1, le=20, description="最大重試次數")
    retry_base_delay: float = Field(default=0.5, gt=0, description="重試基礎延遲")
    retry_max_delay: float = Field(default=30.0, gt=0, description="重試最大延遲")
    circuit_breaker_enabled: bool = Field(default=True, description="是否啟用熔斷器")
    circuit_breaker_threshold: int = Field(default=8, ge=1, le=50, description="熔斷器閾值")
    
    # 緩存配置
    cache_enabled: bool = Field(default=True, description="是否啟用緩存")
    cache_ttl_seconds: int = Field(default=60, ge=1, le=3600, description="緩存TTL（秒）")
    cache_max_size: int = Field(default=500, ge=10, le=10000, description="緩存最大大小")
    
    # 內存優化
    memory_optimization_enabled: bool = Field(default=True, description="是否啟用內存優化")
    use_float32: bool = Field(default=True, description="是否使用float32")
    chunk_size: int = Field(default=10000, ge=1000, le=100000, description="分塊處理大小")
    
    @validator('retry_max_delay')
    def validate_retry_delays(cls, v, values):
        """驗證重試延遲配置"""
        if 'retry_base_delay' in values and v <= values['retry_base_delay']:
            raise ValueError("最大重試延遲必須大於基礎延遲")
        return v
    
    class Config:
        env_prefix = "PERF_"


class EventDrivenConfig(BaseSettings):
    """事件驅動配置"""

    enabled: bool = Field(default=True, description="是否啟用事件驅動架構")
    redis_url: str = Field(default="redis://localhost:6379", description="Redis連接URL")
    event_queue_size: int = Field(default=1000, ge=100, le=10000, description="事件隊列大小")
    max_workers: int = Field(default=4, ge=1, le=16, description="最大工作線程數")

    # 事件處理配置
    batch_size: int = Field(default=10, ge=1, le=100, description="批量處理大小")
    processing_timeout: float = Field(default=30.0, gt=0, description="處理超時時間")
    retry_failed_events: bool = Field(default=True, description="是否重試失敗事件")

    class Config:
        env_prefix = "EVENT_"


class StrategyConfig(BaseSettings):
    """策略配置"""

    # 策略選擇
    strategy_type: str = Field(default="pairs_trading", description="策略類型")
    strategy_params: Dict[str, Any] = Field(default_factory=dict, description="策略參數")

    # 信號配置
    signal_threshold: float = Field(default=2.0, gt=0, description="信號閾值")
    exit_threshold: float = Field(default=0.5, gt=0, description="退出閾值")

    # 風險管理
    max_position_per_pair: float = Field(default=0.1, gt=0, le=1.0, description="每對最大倉位")
    max_total_exposure: float = Field(default=0.8, gt=0, le=1.0, description="最大總敞口")
    stop_loss_pct: float = Field(default=0.05, gt=0, le=0.5, description="止損百分比")

    class Config:
        env_prefix = "STRATEGY_"


class ComprehensiveConfig(BaseSettings):
    """綜合配置 - 包含所有子配置"""

    # 子配置
    trading: TradingConfig = Field(default_factory=TradingConfig)
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    monitoring: MonitoringConfig = Field(default_factory=MonitoringConfig)
    performance: PerformanceConfig = Field(default_factory=PerformanceConfig)
    event_driven: EventDrivenConfig = Field(default_factory=EventDrivenConfig)
    strategy: StrategyConfig = Field(default_factory=StrategyConfig)

    # 全局配置
    environment: str = Field(default="development", description="運行環境")
    debug_mode: bool = Field(default=False, description="是否啟用調試模式")
    config_version: str = Field(default="3.0", description="配置版本")
    
    def validate_config_consistency(self):
        """驗證配置一致性"""
        # 檢查生產環境配置
        if self.environment == 'production':
            if self.trading.sandbox:
                raise ValueError("生產環境不能使用沙盒模式")

            if self.monitoring.log_level == LogLevel.DEBUG:
                logger.warning("生產環境建議使用INFO或更高的日誌級別")

        return self
    
    def save_to_file(self, file_path: str):
        """保存配置到文件"""
        try:
            config_dict = self.dict()
            
            # 移除敏感信息
            if 'trading' in config_dict:
                config_dict['trading']['api_key'] = "***"
                config_dict['trading']['secret'] = "***"
                if config_dict['trading'].get('passphrase'):
                    config_dict['trading']['passphrase'] = "***"
            
            import json
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=2, ensure_ascii=False)
            
            logger.info(f"配置已保存到: {file_path}")
            
        except Exception as e:
            logger.error(f"保存配置失敗: {e}")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        extra = "ignore"  # 忽略額外字段


class ConfigValidator:
    """配置驗證器"""
    
    @staticmethod
    def load_and_validate_config(config_file: Optional[str] = None) -> ComprehensiveConfig:
        """載入並驗證配置"""
        try:
            # 手動載入.env文件
            env_file = Path(".env")
            if env_file.exists():
                logger.info(f"載入環境變量文件: {env_file}")
                with open(env_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#') and '=' in line:
                            key, value = line.split('=', 1)
                            # 移除註釋
                            if '#' in value:
                                value = value.split('#')[0].strip()
                            os.environ[key] = value

            # 載入JSON配置文件（如果提供）
            json_config = {}
            if config_file and Path(config_file).exists():
                logger.info(f"載入JSON配置文件: {config_file}")
                with open(config_file, 'r', encoding='utf-8') as f:
                    json_config = json.load(f)

            # 從JSON配置中提取交易所信息並設置環境變量
            if 'exchange' in json_config:
                exchange_config = json_config['exchange']
                if 'name' in exchange_config:
                    os.environ['TRADING_EXCHANGE'] = exchange_config['name']
                if 'sandbox' in exchange_config:
                    os.environ['TRADING_SANDBOX'] = str(exchange_config['sandbox']).lower()

            # 從JSON配置中提取期貨配置
            if 'trading_mode' in json_config:
                os.environ['TRADING_TRADING_MODE'] = json_config['trading_mode']
            if 'futures_type' in json_config:
                os.environ['TRADING_FUTURES_TYPE'] = json_config['futures_type']
            if 'leverage' in json_config:
                os.environ['TRADING_LEVERAGE'] = str(json_config['leverage'])
            if 'margin_mode' in json_config:
                os.environ['TRADING_MARGIN_MODE'] = json_config['margin_mode']

            # 載入配置
            config = ComprehensiveConfig()

            # 執行一致性驗證
            config.validate_config_consistency()

            logger.info("配置驗證通過")
            logger.info(f"交易所: {config.trading.exchange}")
            logger.info(f"交易配對數量: {len(config.trading.trading_pairs)}")
            logger.info(f"時間框架: {config.trading.timeframe}")
            logger.info(f"環境: {config.environment}")
            logger.info(f"API密鑰狀態: {'已設置' if config.trading.api_key else '未設置'}")

            return config

        except Exception as e:
            logger.error(f"配置驗證失敗: {e}")
            raise
    
    @staticmethod
    def validate_runtime_config(config: ComprehensiveConfig) -> List[str]:
        """運行時配置驗證（包含安全檢查）"""
        warnings = []
        errors = []

        # 檢查必需的敏感配置
        if not config.trading.api_key:
            errors.append("API密鑰未設置，請在.env文件中設置TRADING_API_KEY")

        if not config.trading.secret:
            errors.append("API密鑰未設置，請在.env文件中設置TRADING_SECRET")

        # 檢查API密鑰格式
        if config.trading.api_key and config.trading.api_key == "your_api_key":
            errors.append("API密鑰使用默認值，請設置真實的API密鑰")

        if config.trading.secret and config.trading.secret == "your_secret":
            errors.append("API密鑰使用默認值，請設置真實的API密鑰")

        # 安全檢查：交易環境驗證
        try:
            from trading_safety_guard import validate_trading_safety

            # 轉換配置格式以適配安全檢查
            safety_config = {
                'exchange': {
                    'name': config.trading.exchange.value,
                    'sandbox': config.trading.sandbox
                }
            }

            safety_result = validate_trading_safety(safety_config)

            # 處理安全檢查結果
            if not safety_result['safe_to_trade']:
                errors.append("🚨 安全檢查失敗：系統拒絕啟動交易")

            # 添加安全警告
            for warning in safety_result.get('warnings', []):
                warnings.append(f"安全警告: {warning}")

            # 添加安全錯誤
            for error in safety_result.get('errors', []):
                errors.append(f"安全錯誤: {error}")

            # 記錄安全建議
            for rec in safety_result.get('recommendations', []):
                logger.info(f"安全建議: {rec}")

        except ImportError:
            warnings.append("安全防護模組未找到，跳過安全檢查")
        except Exception as e:
            warnings.append(f"安全檢查失敗: {e}")

        # 檢查生產環境配置
        if config.environment == "production":
            if config.trading.sandbox:
                errors.append("生產環境不能使用沙盒模式")

            if not config.trading.api_key or not config.trading.secret:
                errors.append("生產環境必須設置API密鑰")

        # 檢查內存配置
        if config.monitoring.memory_threshold_mb < 500:
            warnings.append("內存閾值設置過低，可能導致頻繁警報")

        # 檢查性能配置
        if config.performance.max_concurrent_requests > 50:
            warnings.append("並發請求數過高，可能導致API限制")

        # 檢查交易配置
        if config.trading.max_position_size > 0.5:
            warnings.append("最大倉位大小過高，風險較大")

        # 如果有錯誤，拋出異常
        if errors:
            error_message = "配置驗證失敗:\n" + "\n".join(f"  - {error}" for error in errors)
            raise ValueError(error_message)

        return warnings


# 全局配置實例
_global_config = None

def get_validated_config() -> ComprehensiveConfig:
    """獲取驗證後的全局配置"""
    global _global_config
    if _global_config is None:
        _global_config = ConfigValidator.load_and_validate_config()
    return _global_config


if __name__ == "__main__":
    # 測試配置驗證
    try:
        print("測試配置驗證...")
        
        # 載入配置
        config = ConfigValidator.load_and_validate_config()
        
        # 運行時驗證
        warnings = ConfigValidator.validate_runtime_config(config)
        if warnings:
            print("配置警告:")
            for warning in warnings:
                print(f"  - {warning}")
        
        # 保存配置示例
        config.save_to_file("config_example.json")
        
        print("配置驗證測試完成！")
        
    except Exception as e:
        print(f"配置驗證測試失敗: {e}")
        import traceback
        traceback.print_exc()
