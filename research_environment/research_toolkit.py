#!/usr/bin/env python3
"""
量化研究工具包 - 為策略研究提供完整的工具集
Quantitative Research Toolkit - Complete toolset for strategy research
"""

import warnings
from datetime import datetime
from typing import Dict, List, Optional

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import seaborn as sns

warnings.filterwarnings("ignore")

from database_manager import get_database_manager
from factor_factory.alpha_factors import get_alpha_factory
from logging_config import get_logger
from portfolio_backtester import PortfolioBacktester
from risk_management.advanced_risk_manager import get_advanced_risk_manager

logger = get_logger(__name__)

# 設置繪圖樣式
plt.style.use("seaborn-v0_8")
sns.set_palette("husl")


class ResearchToolkit:
    """量化研究工具包"""

    def __init__(self):
        self.db_manager = get_database_manager()
        self.alpha_factory = get_alpha_factory()
        self.backtester = PortfolioBacktester()
        self.risk_manager = get_advanced_risk_manager()

        # 研究緩存
        self.research_cache = {}

        logger.info("ResearchToolkit 初始化完成")

    def load_research_data(
        self, pair: List[str], start_date: str, end_date: str, timeframe: str = "1h"
    ) -> pd.DataFrame:
        """載入研究數據"""
        try:
            # 這裡應該從數據源載入價格數據
            # 暫時生成模擬數據用於演示

            date_range = pd.date_range(start=start_date, end=end_date, freq=timeframe)
            np.random.seed(42)

            # 生成相關的價格序列
            n_periods = len(date_range)
            base_returns = np.random.normal(0.0001, 0.02, n_periods)
            quote_returns = 0.8 * base_returns + np.random.normal(0, 0.01, n_periods)

            base_prices = 50000 * np.exp(np.cumsum(base_returns))
            quote_prices = 3000 * np.exp(np.cumsum(quote_returns))

            data = pd.DataFrame(
                {
                    "base_price": base_prices,
                    "quote_price": quote_prices,
                    "base_volume": np.random.exponential(1000, n_periods),
                    "quote_volume": np.random.exponential(100, n_periods),
                    "base_high": base_prices * (1 + np.random.uniform(0, 0.02, n_periods)),
                    "base_low": base_prices * (1 - np.random.uniform(0, 0.02, n_periods)),
                    "quote_high": quote_prices * (1 + np.random.uniform(0, 0.02, n_periods)),
                    "quote_low": quote_prices * (1 - np.random.uniform(0, 0.02, n_periods)),
                },
                index=date_range,
            )

            logger.info(f"載入研究數據: {pair}, {len(data)} 個數據點")
            return data

        except Exception as e:
            logger.error(f"載入研究數據失敗: {e}")
            return pd.DataFrame()

    def analyze_pair_relationship(self, data: pd.DataFrame, pair: List[str]) -> Dict:
        """分析配對關係"""
        try:
            if data.empty:
                return {}

            base_price = data["base_price"]
            quote_price = data["quote_price"]
            log_spread = np.log(base_price) - np.log(quote_price)

            # 基礎統計
            correlation = base_price.corr(quote_price)

            # 共整合檢定
            from statsmodels.tsa.stattools import coint

            _, cointegration_pvalue, _ = coint(np.log(base_price), np.log(quote_price))

            # 價差統計
            spread_mean = log_spread.mean()
            spread_std = log_spread.std()
            spread_adf_stat, spread_adf_pvalue, _, _, _, _ = __import__(
                "statsmodels.tsa.stattools", fromlist=["adfuller"]
            ).adfuller(log_spread)

            # 半衰期計算
            half_life = self._calculate_half_life(log_spread)

            # 交易機會分析
            zscore = (log_spread - spread_mean) / spread_std
            trading_opportunities = self._analyze_trading_opportunities(zscore)

            return {
                "pair": pair,
                "correlation": correlation,
                "cointegration_pvalue": cointegration_pvalue,
                "spread_statistics": {
                    "mean": spread_mean,
                    "std": spread_std,
                    "adf_statistic": spread_adf_stat,
                    "adf_pvalue": spread_adf_pvalue,
                    "half_life": half_life,
                },
                "trading_opportunities": trading_opportunities,
                "data_quality": {
                    "observations": len(data),
                    "missing_values": data.isnull().sum().sum(),
                    "date_range": [data.index.min(), data.index.max()],
                },
            }

        except Exception as e:
            logger.error(f"分析配對關係失敗: {e}")
            return {}

    def _calculate_half_life(self, series: pd.Series) -> float:
        """計算半衰期"""
        try:
            from scipy import stats

            series_lag = series.shift(1).dropna()
            series_diff = series.diff().dropna()

            # 確保兩個序列長度一致
            min_len = min(len(series_lag), len(series_diff))
            series_lag = series_lag.iloc[:min_len]
            series_diff = series_diff.iloc[:min_len]

            slope, _, _, _, _ = stats.linregress(series_lag, series_diff)

            if slope >= 0:
                return np.inf

            half_life = -np.log(2) / slope
            return half_life

        except Exception as e:
            logger.error(f"計算半衰期失敗: {e}")
            return np.inf

    def _analyze_trading_opportunities(self, zscore: pd.Series) -> Dict:
        """分析交易機會"""
        try:
            # 定義交易信號
            entry_threshold = 2.0
            exit_threshold = 0.5

            # 計算信號頻率
            entry_signals = (abs(zscore) > entry_threshold).sum()
            exit_signals = (abs(zscore) < exit_threshold).sum()

            # 計算持倉時間分佈
            in_position = False
            hold_times = []
            entry_time = None

            for i, z in enumerate(zscore):
                if not in_position and abs(z) > entry_threshold:
                    in_position = True
                    entry_time = i
                elif in_position and abs(z) < exit_threshold:
                    in_position = False
                    if entry_time is not None:
                        hold_times.append(i - entry_time)

            return {
                "entry_signals": entry_signals,
                "exit_signals": exit_signals,
                "signal_frequency": entry_signals / len(zscore) if len(zscore) > 0 else 0,
                "avg_hold_time": np.mean(hold_times) if hold_times else 0,
                "max_zscore": zscore.max(),
                "min_zscore": zscore.min(),
                "zscore_volatility": zscore.std(),
            }

        except Exception as e:
            logger.error(f"分析交易機會失敗: {e}")
            return {}

    def factor_research(
        self, data: pd.DataFrame, pair: List[str], forward_periods: int = 1
    ) -> Dict:
        """因子研究"""
        try:
            # 計算所有因子
            factor_matrix = self.alpha_factory.calculate_all_factors(data, pair)

            if factor_matrix.empty:
                logger.warning("因子矩陣為空")
                return {}

            # 計算未來收益
            log_spread = np.log(data["base_price"]) - np.log(data["quote_price"])
            future_returns = log_spread.shift(-forward_periods) - log_spread

            # 評估因子
            factor_evaluations = self.alpha_factory.evaluate_factors(data, future_returns)

            # 因子相關性分析
            factor_correlation = factor_matrix.corr()

            # 因子穩定性分析
            factor_stability = self._analyze_factor_stability(factor_matrix)

            # 因子組合優化
            optimal_factors = self._optimize_factor_combination(factor_matrix, future_returns)

            return {
                "factor_evaluations": factor_evaluations,
                "factor_correlation": factor_correlation,
                "factor_stability": factor_stability,
                "optimal_factors": optimal_factors,
                "factor_summary": self.alpha_factory.get_factor_summary(),
            }

        except Exception as e:
            logger.error(f"因子研究失敗: {e}")
            return {}

    def _analyze_factor_stability(self, factor_matrix: pd.DataFrame) -> Dict:
        """分析因子穩定性"""
        try:
            stability_metrics = {}

            for factor_name in factor_matrix.columns:
                factor_values = factor_matrix[factor_name]

                # 滾動相關性
                window_size = min(50, len(factor_values) // 4)
                if window_size < 10:
                    continue

                rolling_corr = []
                for i in range(window_size, len(factor_values) - window_size):
                    corr = factor_values.iloc[i - window_size : i].corr(
                        factor_values.iloc[i : i + window_size]
                    )
                    if not np.isnan(corr):
                        rolling_corr.append(corr)

                if rolling_corr:
                    stability_metrics[factor_name] = {
                        "mean_stability": np.mean(rolling_corr),
                        "stability_std": np.std(rolling_corr),
                        "min_stability": np.min(rolling_corr),
                    }

            return stability_metrics

        except Exception as e:
            logger.error(f"分析因子穩定性失敗: {e}")
            return {}

    def _optimize_factor_combination(
        self, factor_matrix: pd.DataFrame, target_returns: pd.Series
    ) -> Dict:
        """優化因子組合"""
        try:
            from sklearn.linear_model import Ridge
            from sklearn.model_selection import cross_val_score

            # 對齊數據
            common_index = factor_matrix.index.intersection(target_returns.index)
            X = factor_matrix.loc[common_index].fillna(0)
            y = target_returns.loc[common_index].fillna(0)

            if len(X) < 20:
                return {}

            # 使用Ridge回歸進行因子選擇
            ridge = Ridge(alpha=0.1)
            ridge.fit(X, y)

            # 交叉驗證評分
            cv_scores = cross_val_score(ridge, X, y, cv=5)

            # 因子重要性
            factor_importance = pd.Series(np.abs(ridge.coef_), index=X.columns).sort_values(
                ascending=False
            )

            # 選擇前5個最重要的因子
            top_factors = factor_importance.head(5).index.tolist()

            return {
                "top_factors": top_factors,
                "factor_weights": ridge.coef_,
                "cv_score_mean": cv_scores.mean(),
                "cv_score_std": cv_scores.std(),
                "factor_importance": factor_importance.to_dict(),
            }

        except Exception as e:
            logger.error(f"優化因子組合失敗: {e}")
            return {}

    def strategy_optimization(self, data: pd.DataFrame, pair: List[str], param_grid: Dict) -> Dict:
        """策略參數優化"""
        try:
            optimization_results = []

            # 網格搜索
            param_combinations = self._generate_param_combinations(param_grid)

            for params in param_combinations[:20]:  # 限制組合數量
                # 運行回測
                backtest_result = self._run_single_backtest(data, pair, params)

                if backtest_result:
                    optimization_results.append(
                        {"parameters": params, "performance": backtest_result}
                    )

            # 排序結果
            optimization_results.sort(
                key=lambda x: x["performance"].get("sharpe_ratio", 0), reverse=True
            )

            # 分析最優參數
            best_params = optimization_results[0]["parameters"] if optimization_results else {}

            return {
                "best_parameters": best_params,
                "optimization_results": optimization_results[:10],  # 返回前10個結果
                "parameter_sensitivity": self._analyze_parameter_sensitivity(optimization_results),
            }

        except Exception as e:
            logger.error(f"策略優化失敗: {e}")
            return {}

    def _generate_param_combinations(self, param_grid: Dict) -> List[Dict]:
        """生成參數組合"""
        try:
            from itertools import product

            keys = param_grid.keys()
            values = param_grid.values()

            combinations = []
            for combination in product(*values):
                param_dict = dict(zip(keys, combination))
                combinations.append(param_dict)

            return combinations

        except Exception as e:
            logger.error(f"生成參數組合失敗: {e}")
            return []

    def _run_single_backtest(
        self, data: pd.DataFrame, pair: List[str], params: Dict
    ) -> Optional[Dict]:
        """運行單次回測"""
        try:
            # 簡化的回測邏輯
            log_spread = np.log(data["base_price"]) - np.log(data["quote_price"])

            # 使用參數計算信號
            lookback = params.get("lookback_window", 20)
            entry_threshold = params.get("entry_threshold", 2.0)
            exit_threshold = params.get("exit_threshold", 0.5)

            # 計算Z-score
            rolling_mean = log_spread.rolling(lookback).mean()
            rolling_std = log_spread.rolling(lookback).std()
            zscore = (log_spread - rolling_mean) / rolling_std

            # 生成交易信號
            trades = []
            position = 0
            entry_price = 0

            for i in range(lookback, len(zscore)):
                current_zscore = zscore.iloc[i]
                current_spread = log_spread.iloc[i]

                # 進場信號
                if position == 0:
                    if current_zscore > entry_threshold:
                        position = -1  # 做空價差
                        entry_price = current_spread
                    elif current_zscore < -entry_threshold:
                        position = 1  # 做多價差
                        entry_price = current_spread

                # 出場信號
                elif abs(current_zscore) < exit_threshold:
                    pnl = position * (entry_price - current_spread)
                    trades.append(pnl)
                    position = 0

            if not trades:
                return None

            # 計算績效指標
            returns = pd.Series(trades)
            total_return = returns.sum()
            sharpe_ratio = returns.mean() / returns.std() if returns.std() > 0 else 0
            max_drawdown = self._calculate_max_drawdown(returns.cumsum())
            win_rate = (returns > 0).mean()

            return {
                "total_return": total_return,
                "sharpe_ratio": sharpe_ratio,
                "max_drawdown": max_drawdown,
                "win_rate": win_rate,
                "total_trades": len(trades),
                "avg_trade": returns.mean(),
            }

        except Exception as e:
            logger.error(f"運行回測失敗: {e}")
            return None

    def _calculate_max_drawdown(self, cumulative_returns: pd.Series) -> float:
        """計算最大回撤"""
        try:
            peak = cumulative_returns.expanding().max()
            drawdown = (cumulative_returns - peak) / peak
            return drawdown.min()
        except:
            return 0

    def _analyze_parameter_sensitivity(self, optimization_results: List[Dict]) -> Dict:
        """分析參數敏感性"""
        try:
            if not optimization_results:
                return {}

            # 提取參數和績效
            param_names = list(optimization_results[0]["parameters"].keys())
            sensitivity_analysis = {}

            for param_name in param_names:
                param_values = []
                sharpe_ratios = []

                for result in optimization_results:
                    param_values.append(result["parameters"][param_name])
                    sharpe_ratios.append(result["performance"]["sharpe_ratio"])

                # 計算相關性
                if len(set(param_values)) > 1:
                    correlation = np.corrcoef(param_values, sharpe_ratios)[0, 1]
                    sensitivity_analysis[param_name] = {
                        "correlation_with_performance": correlation,
                        "value_range": [min(param_values), max(param_values)],
                        "optimal_value": optimization_results[0]["parameters"][param_name],
                    }

            return sensitivity_analysis

        except Exception as e:
            logger.error(f"分析參數敏感性失敗: {e}")
            return {}

    def generate_research_report(self, pair: List[str], start_date: str, end_date: str) -> Dict:
        """生成研究報告"""
        try:
            # 載入數據
            data = self.load_research_data(pair, start_date, end_date)

            if data.empty:
                return {}

            # 配對關係分析
            pair_analysis = self.analyze_pair_relationship(data, pair)

            # 因子研究
            factor_research = self.factor_research(data, pair)

            # 策略優化
            param_grid = {
                "lookback_window": [10, 20, 30],
                "entry_threshold": [1.5, 2.0, 2.5],
                "exit_threshold": [0.3, 0.5, 0.7],
            }
            optimization_results = self.strategy_optimization(data, pair, param_grid)

            # 風險分析
            portfolio_data = {
                "total_capital": 10000,
                "positions": {
                    f"{pair[0]}-{pair[1]}": {
                        "value": 5000,
                        "correlation": pair_analysis.get("correlation", 0.8),
                        "weight": 0.5,
                    }
                },
            }

            risk_analysis = self.risk_manager.generate_risk_report(portfolio_data)

            return {
                "research_metadata": {
                    "pair": pair,
                    "date_range": [start_date, end_date],
                    "generated_at": datetime.now().isoformat(),
                },
                "pair_analysis": pair_analysis,
                "factor_research": factor_research,
                "strategy_optimization": optimization_results,
                "risk_analysis": risk_analysis,
                "recommendations": self._generate_research_recommendations(
                    pair_analysis, factor_research, optimization_results
                ),
            }

        except Exception as e:
            logger.error(f"生成研究報告失敗: {e}")
            return {}

    def _generate_research_recommendations(
        self, pair_analysis: Dict, factor_research: Dict, optimization_results: Dict
    ) -> List[str]:
        """生成研究建議"""
        recommendations = []

        # 基於配對分析的建議
        if pair_analysis:
            correlation = pair_analysis.get("correlation", 0)
            cointegration_pvalue = pair_analysis.get("cointegration_pvalue", 1)

            if correlation > 0.8 and cointegration_pvalue < 0.05:
                recommendations.append("配對關係良好，適合配對交易策略")
            elif correlation < 0.6:
                recommendations.append("相關性較低，建議尋找更好的配對")
            elif cointegration_pvalue > 0.1:
                recommendations.append("共整合關係較弱，需要謹慎交易")

        # 基於因子研究的建議
        if factor_research:
            factor_summary = factor_research.get("factor_summary", pd.DataFrame())
            if not factor_summary.empty:
                valid_factors = factor_summary[factor_summary["valid"] == True]
                if len(valid_factors) > 0:
                    recommendations.append(f"發現 {len(valid_factors)} 個有效因子，建議集成到策略中")
                else:
                    recommendations.append("未發現顯著有效的因子，建議使用傳統統計套利方法")

        # 基於優化結果的建議
        if optimization_results:
            best_params = optimization_results.get("best_parameters", {})
            if best_params:
                recommendations.append(f"建議使用優化參數: {best_params}")

        if not recommendations:
            recommendations.append("需要更多數據進行深入分析")

        return recommendations


# 全局研究工具包實例
_research_toolkit = None


def get_research_toolkit() -> ResearchToolkit:
    """獲取全局研究工具包實例"""
    global _research_toolkit
    if _research_toolkit is None:
        _research_toolkit = ResearchToolkit()
    return _research_toolkit


if __name__ == "__main__":
    # 測試研究工具包
    print("測試量化研究工具包...")

    toolkit = ResearchToolkit()

    # 生成研究報告
    pair = ["BTCUSDT", "ETHUSDT"]
    start_date = "2024-01-01"
    end_date = "2024-02-01"

    report = toolkit.generate_research_report(pair, start_date, end_date)

    if report:
        print("研究報告生成成功")
        print(f"配對: {report['research_metadata']['pair']}")
        print(f"相關性: {report['pair_analysis'].get('correlation', 0):.4f}")
        print(f"共整合P值: {report['pair_analysis'].get('cointegration_pvalue', 1):.4f}")
        print(f"建議: {report['recommendations']}")
    else:
        print("研究報告生成失敗")

    print("量化研究工具包測試完成！")
