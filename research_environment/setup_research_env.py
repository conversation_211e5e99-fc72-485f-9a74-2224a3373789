#!/usr/bin/env python3
"""
研究環境設置腳本 - 配置Jupyter Lab研究環境
Research Environment Setup - Configure Jupyter Lab research environment
"""

import json
import subprocess
import sys
from pathlib import Path


def setup_jupyter_environment():
    """設置Jupyter研究環境"""
    print("🔬 設置量化研究環境...")

    # 創建研究目錄
    research_dirs = [
        "research_environment/notebooks",
        "research_environment/data",
        "research_environment/results",
        "research_environment/models",
        "research_environment/reports",
    ]

    for dir_path in research_dirs:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
        print(f"✓ 創建目錄: {dir_path}")

    # 創建Jupyter配置
    create_jupyter_config()  # 修復不完整調用

    # 創建研究模板
    create_research_templates()  # 修復不完整調用

    # 安裝Jupyter擴展
    install_jupyter_extensions()  # 修復不完整調用

    print("✅ 研究環境設置完成！")
    print("🚀 啟動命令: jupyter lab --config=research_environment/jupyter_config.py")


def create_jupyter_config():
    """創建Jupyter配置文件"""
    config_content = """
# Jupyter Lab 配置文件
c = get_config()

# 基本設置
c.ServerApp.ip = '0.0.0.0'
c.ServerApp.port = 8888
c.ServerApp.open_browser = False
c.ServerApp.root_dir = '.'

# 安全設置
c.ServerApp.token = ''
c.ServerApp.password = ''
c.ServerApp.allow_origin = '*'
c.ServerApp.allow_remote_access = True

# 擴展設置
c.ServerApp.jpserver_extensions = {
    'jupyterlab': True,
    'jupyter_server_proxy': True
}

# 內核設置
c.MappingKernelManager.default_kernel_name = 'python3'

# 文件管理
c.ContentsManager.allow_hidden = True
c.FileContentsManager.delete_to_trash = False
"""

    config_path = Path("research_environment/jupyter_config.py")
    with open(config_path, "w") as f:
        f.write(config_content)

    print(f"✓ 創建Jupyter配置: {config_path}")


def create_research_templates():
    """創建研究模板"""

    # 策略研究模板
    strategy_template = {
        "cells": [
            {
                "cell_type": "markdown",
                "metadata": {},
                "source": [
                    "# 配對交易策略研究\n",
                    "\n",
                    "## 研究目標\n",
                    "- 分析配對關係\n",
                    "- 優化策略參數\n",
                    "- 評估風險收益\n",
                    "\n",
                    "## 研究步驟\n",
                    "1. 數據載入與清理\n",
                    "2. 配對關係分析\n",
                    "3. 因子研究\n",
                    "4. 策略回測\n",
                    "5. 風險分析\n",
                    "6. 結論與建議",
                ],
            },
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "source": [
                    "# 導入必要的庫\n",
                    "import sys\n",
                    "sys.path.append('..')\n",
                    "\n",
                    "import numpy as np\n",
                    "import pandas as pd\n",
                    "import matplotlib.pyplot as plt\n",
                    "import seaborn as sns\n",
                    "from datetime import datetime, timedelta\n",
                    "\n",
                    "# 導入平台模組\n",
                    "from research_environment.research_toolkit import get_research_toolkit\n",
                    "from factor_factory.alpha_factors import get_alpha_factory\n",
                    "from ml_predictor.predictor import get_ml_predictor\n",
                    "from database_manager import get_database_manager\n",
                    "\n",
                    "# 設置繪圖樣式\n",
                    "plt.style.use('seaborn-v0_8')\n",
                    "sns.set_palette('husl')\n",
                    "%matplotlib inline\n",
                    "\n",
                    "print('✅ 研究環境初始化完成')",
                ],
            },
            {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 數據載入"]},
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "source": [
                    "# 初始化研究工具\n",
                    "toolkit = get_research_toolkit()\n",
                    "\n",
                    "# 設置研究參數\n",
                    "pair = ['BTCUSDT', 'ETHUSDT']\n",
                    "start_date = '2024-01-01'\n",
                    "end_date = '2024-06-01'\n",
                    "\n",
                    "# 載入數據\n",
                    "data = toolkit.load_research_data(pair, start_date, end_date)\n",
                    "print(f'數據形狀: {data.shape}')\n",
                    "print(f'時間範圍: {data.index.min()} 到 {data.index.max()}')\n",
                    "\n",
                    "# 顯示基本統計\n",
                    "data.describe()",
                ],
            },
            {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 配對關係分析"]},
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "source": [
                    "# 分析配對關係\n",
                    "pair_analysis = toolkit.analyze_pair_relationship(data, pair)\n",
                    "\n",
                    "print('配對關係分析結果:')\n",
                    "print(f'相關性: {pair_analysis[\"correlation\"]:.4f}')\n",
                    "print(f'共整合P值: {pair_analysis[\"cointegration_pvalue\"]:.6f}')\n",
                    'print(f\'價差半衰期: {pair_analysis["spread_statistics"]["half_life"]:.2f}\')\n',
                    "\n",
                    "# 繪製價格圖\n",
                    "fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n",
                    "\n",
                    "# 價格走勢\n",
                    "axes[0,0].plot(data.index, data['base_price'], label=pair[0])\n",
                    "axes[0,0].plot(data.index, data['quote_price'], label=pair[1])\n",
                    "axes[0,0].set_title('價格走勢')\n",
                    "axes[0,0].legend()\n",
                    "\n",
                    "# 價差\n",
                    "log_spread = np.log(data['base_price']) - np.log(data['quote_price'])\n",
                    "axes[0,1].plot(data.index, log_spread)\n",
                    "axes[0,1].set_title('對數價差')\n",
                    "\n",
                    "# 散點圖\n",
                    "axes[1,0].scatter(data['base_price'], data['quote_price'], alpha=0.5)\n",
                    "axes[1,0].set_xlabel(pair[0])\n",
                    "axes[1,0].set_ylabel(pair[1])\n",
                    "axes[1,0].set_title('價格散點圖')\n",
                    "\n",
                    "# Z-score\n",
                    "zscore = (log_spread - log_spread.rolling(20).mean()) / log_spread.rolling(20).std()\n",
                    "axes[1,1].plot(data.index, zscore)\n",
                    "axes[1,1].axhline(y=2, color='r', linestyle='--', alpha=0.7)\n",
                    "axes[1,1].axhline(y=-2, color='r', linestyle='--', alpha=0.7)\n",
                    "axes[1,1].set_title('Z-Score')\n",
                    "\n",
                    "plt.tight_layout()\n",
                    "plt.show()",
                ],
            },
            {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 因子研究"]},
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "source": [
                    "# 因子研究\n",
                    "factor_research = toolkit.factor_research(data, pair)\n",
                    "\n",
                    "# 顯示因子總結\n",
                    "factor_summary = factor_research.get('factor_summary', pd.DataFrame())\n",
                    "if not factor_summary.empty:\n",
                    "    print('因子有效性排名:')\n",
                    "    display(factor_summary.head(10))\n",
                    "    \n",
                    "    # 繪製因子重要性\n",
                    "    plt.figure(figsize=(12, 6))\n",
                    "    valid_factors = factor_summary[factor_summary['valid'] == True]\n",
                    "    if len(valid_factors) > 0:\n",
                    "        plt.barh(valid_factors['factor_name'], valid_factors['ic_ir'].abs())\n",
                    "        plt.xlabel('Information Ratio (絕對值)')\n",
                    "        plt.title('有效因子排名')\n",
                    "        plt.tight_layout()\n",
                    "        plt.show()\n",
                    "else:\n",
                    "    print('因子研究數據不可用')",
                ],
            },
            {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 策略優化"]},
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "source": [
                    "# 策略參數優化\n",
                    "param_grid = {\n",
                    "    'lookback_window': [10, 20, 30, 50],\n",
                    "    'entry_threshold': [1.5, 2.0, 2.5, 3.0],\n",
                    "    'exit_threshold': [0.3, 0.5, 0.7, 1.0]\n",
                    "}\n",
                    "\n",
                    "optimization_results = toolkit.strategy_optimization(data, pair, param_grid)\n",
                    "\n",
                    "# 顯示最優參數\n",
                    "best_params = optimization_results.get('best_parameters', {})\n",
                    "print('最優參數:')\n",
                    "for param, value in best_params.items():\n",
                    "    print(f'  {param}: {value}')\n",
                    "\n",
                    "# 顯示優化結果\n",
                    "results = optimization_results.get('optimization_results', [])\n",
                    "if results:\n",
                    "    results_df = pd.DataFrame([\n",
                    "        {**r['parameters'], **r['performance']} \n",
                    "        for r in results[:10]\n",
                    "    ])\n",
                    "    display(results_df)",
                ],
            },
            {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 機器學習預測"]},
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "source": [
                    "# ML預測\n",
                    "ml_predictor = get_ml_predictor()\n",
                    "\n",
                    "# 準備訓練數據\n",
                    "X, y = ml_predictor.prepare_training_data(data, pair)\n",
                    "\n",
                    "if not X.empty and not y.empty:\n",
                    "    print(f'訓練數據: {X.shape[0]} 樣本, {X.shape[1]} 特徵')\n",
                    "    \n",
                    "    # 分割訓練和測試集\n",
                    "    split_idx = int(0.8 * len(X))\n",
                    "    X_train, X_test = X.iloc[:split_idx], X.iloc[split_idx:]\n",
                    "    y_train, y_test = y.iloc[:split_idx], y.iloc[split_idx:]\n",
                    "    \n",
                    "    # 訓練模型\n",
                    "    training_results = ml_predictor.train_models(X_train, y_train)\n",
                    "    print('\\n訓練結果:')\n",
                    "    for model, result in training_results.items():\n",
                    "        if 'train_score' in result:\n",
                    "            print(f'  {model}: R² = {result[\"train_score\"]:.4f}')\n",
                    "    \n",
                    "    # 評估模型\n",
                    "    evaluation_results = ml_predictor.evaluate_models(X_test, y_test)\n",
                    "    print('\\n測試結果:')\n",
                    "    for model, result in evaluation_results.items():\n",
                    "        if 'r2_score' in result:\n",
                    '            print(f\'  {model}: R² = {result["r2_score"]:.4f}, 方向準確率 = {result["direction_accuracy"]:.4f}\')\n',
                    "    \n",
                    "    # 進行預測\n",
                    "    prediction = ml_predictor.predict(data, pair)\n",
                    '    print(f\'\\n最新預測: {prediction["prediction"]:.6f} (置信度: {prediction["confidence"]:.4f})\')\n',
                    "else:\n",
                    "    print('ML訓練數據準備失敗')",
                ],
            },
            {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 生成研究報告"]},
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "source": [
                    "# 生成完整研究報告\n",
                    "research_report = toolkit.generate_research_report(pair, start_date, end_date)\n",
                    "\n",
                    "if research_report:\n",
                    "    print('📊 研究報告生成完成')\n",
                    "    print('\\n主要發現:')\n",
                    "    \n",
                    "    # 配對質量\n",
                    "    correlation = research_report['pair_analysis']['correlation']\n",
                    "    cointegration_p = research_report['pair_analysis']['cointegration_pvalue']\n",
                    "    print(f'• 配對相關性: {correlation:.4f}')\n",
                    "    print(f'• 共整合P值: {cointegration_p:.6f}')\n",
                    "    \n",
                    "    # 策略表現\n",
                    "    best_params = research_report['strategy_optimization']['best_parameters']\n",
                    "    print(f'• 最優參數: {best_params}')\n",
                    "    \n",
                    "    # 建議\n",
                    "    recommendations = research_report['recommendations']\n",
                    "    print('\\n💡 研究建議:')\n",
                    "    for i, rec in enumerate(recommendations, 1):\n",
                    "        print(f'{i}. {rec}')\n",
                    "    \n",
                    "    # 保存報告\n",
                    "    import json\n",
                    "    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n",
                    "    report_file = f'research_environment/reports/research_report_{timestamp}.json'\n",
                    "    \n",
                    "    with open(report_file, 'w', encoding='utf-8') as f:\n",
                    "        json.dump(research_report, f, indent=2, ensure_ascii=False, default=str)\n",
                    "    \n",
                    "    print(f'\\n📁 報告已保存: {report_file}')\n",
                    "else:\n",
                    "    print('❌ 研究報告生成失敗')",
                ],
            },
        ],
        "metadata": {
            "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"},
            "language_info": {"name": "python", "version": "3.9.0"},
        },
        "nbformat": 4,
        "nbformat_minor": 4,
    }

    # 保存策略研究模板
    template_path = Path("research_environment/notebooks/strategy_research_template.ipynb")
    with open(template_path, "w", encoding="utf-8") as f:
        json.dump(strategy_template, f, indent=2, ensure_ascii=False)

    print(f"✓ 創建策略研究模板: {template_path}")


def install_jupyter_extensions():
    """安裝Jupyter擴展"""
    extensions = [
        "jupyterlab",
        "notebook",
        "ipywidgets",
        "matplotlib",
        "seaborn",
        "plotly",
        "jupyter-dash",
    ]

    print("📦 安裝Jupyter擴展...")
    for ext in extensions:
        try:
            subprocess.run(
                [sys.executable, "-m", "pip", "install", ext], check=True, capture_output=True
            )
            print(f"✓ 安裝: {ext}")
        except subprocess.CalledProcessError:
            print(f"✗ 安裝失敗: {ext}")


if __name__ == "__main__":
    setup_jupyter_environment()  # 修復不完整調用
