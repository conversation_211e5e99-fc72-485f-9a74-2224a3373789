#!/usr/bin/env python3
"""
高級代碼質量修復工具
Advanced Code Quality Fixer
"""

import re
from pathlib import Path


class AdvancedCodeFixer:
    """高級代碼質量修復器"""

    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.fixes_applied = 0

    def find_python_files(self) -> List[Path]:
        """查找所有Python文件"""
        python_files = []

        # 掃描src目錄
        src_dir = self.project_root / "src"
        if src_dir.exists():
            python_files.extend(src_dir.glob("*.py"))

        # 掃描tests目錄
        tests_dir = self.project_root / "tests"
        if tests_dir.exists():
            python_files.extend(tests_dir.glob("*.py"))

        # 掃描根目錄的Python文件
        python_files.extend(self.project_root.glob("*.py"))

        return python_files

    def fix_undefined_variables(self, file_path: Path) -> bool:
        """修復未定義變量問題"""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            # 修復常見的未定義變量問題
            fixes = [
                # 修復循環變量問題
                (r"for\s+(\w+)\s+in\s+range\((\d+)\):", r"for \1 in range(\2):"),
                (r"for\s+(\w+)\s+in\s+enumerate\(", r"for \1, _ in enumerate("),
                # 修復f-string中的未定義變量
                (r'f".*{(\w+)}.*"', lambda m: self._fix_fstring_variable(m, content)),
            ]

            new_content = content
            changes_made = False

            # 特殊處理：修復循環變量未定義問題
            lines = new_content.split("\n")
            for i, line in enumerate(lines):
                # 檢查是否有未定義的循環變量
                if "for _ in range(" in line and 'f"' in lines[i + 1 : i + 5]:
                    # 找到對應的變量名
                    for j in range(i + 1, min(i + 5, len(lines))):
                        if 'f"' in lines[j] and "{i}" in lines[j]:
                            lines[i] = line.replace("for j in range(", "for i in range(")
                            changes_made = True
                            break
                        elif 'f"' in lines[j] and "{j}" in lines[j]:
                            lines[i] = line.replace("for day in range(", "for j in range(")
                            changes_made = True
                            break
                        elif 'f"' in lines[j] and "{day}" in lines[j]:
                            lines[i] = line.replace("for attempt in range(", "for day in range(")
                            changes_made = True
                            break
                        elif 'f"' in lines[j] and "{attempt}" in lines[j]:
                            lines[i] = line.replace(
                                "for iteration in range(", "for attempt in range("
                            )
                            changes_made = True
                            break
                        elif 'f"' in lines[j] and "{iteration}" in lines[j]:
                            lines[i] = line.replace(
                                "for batch in range(", "for iteration in range("
                            )
                            changes_made = True
                            break
                        elif 'f"' in lines[j] and "{batch}" in lines[j]:
                            lines[i] = line.replace("for _ in range(", "for batch in range(")
                            changes_made = True
                            break

            if changes_made:
                new_content = "\n".join(lines)

            if changes_made:
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(new_content)
                self.fixes_applied += 1
                return True

        except Exception as e:
            print(f"修復未定義變量失敗 {file_path}: {e}")

        return False

    def fix_line_length(self, file_path: Path) -> bool:
        """修復行長度問題"""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                lines = f.readlines()

            new_lines = []
            changes_made = False

            for line in lines:
                if len(line.rstrip()) > 100:
                    # 嘗試在適當位置斷行
                    stripped = line.rstrip()
                    if "," in stripped and len(stripped) > 100:
                        # 在逗號後斷行
                        parts = stripped.split(",")
                        if len(parts) > 1:
                            indent = len(line) - len(line.lstrip())
                            new_line = parts[0] + ",\n"
                            for part in parts[1:-1]:
                                new_line += " " * (indent + 4) + part.strip() + ",\n"
                            new_line += " " * (indent + 4) + parts[-1].strip() + "\n"
                            new_lines.append(new_line)
                            changes_made = True
                            continue

                new_lines.append(line)

            if changes_made:
                with open(file_path, "w", encoding="utf-8") as f:
                    f.writelines(new_lines)
                self.fixes_applied += 1
                return True

        except Exception as e:
            print(f"修復行長度失敗 {file_path}: {e}")

        return False

    def fix_whitespace_issues(self, file_path: Path) -> bool:
        """修復空白字符問題"""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            # 修復空白字符問題
            fixes = [
                # 修復冒號前的空白
                (r"\s+:", ":"),
                # 修復模運算符周圍的空白
                (r"(\w+)%(\w+)", r"\1 % \2"),
                # 修復其他空白問題
                (r"\s+,", ","),
            ]

            new_content = content
            changes_made = False

            for pattern, replacement in fixes:
                if re.search(pattern, new_content):
                    new_content = re.sub(pattern, replacement, new_content)
                    changes_made = True

            if changes_made:
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(new_content)
                self.fixes_applied += 1
                return True

        except Exception as e:
            print(f"修復空白字符問題失敗 {file_path}: {e}")

        return False

    def fix_comparison_issues(self, file_path: Path) -> bool:
        """修復比較問題"""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            # 修復比較問題
            fixes = [
                # 修復與True的比較
                (r"==\s*True\b", "is True"),
                (r"!=\s*True\b", "is not True"),
                # 修復raise AssertionError()
                (r"raise AssertionError()", "raise AssertionError()"),
            ]

            new_content = content
            changes_made = False

            for pattern, replacement in fixes:
                if re.search(pattern, new_content):
                    new_content = re.sub(pattern, replacement, new_content)
                    changes_made = True

            if changes_made:
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(new_content)
                self.fixes_applied += 1
                return True

        except Exception as e:
            print(f"修復比較問題失敗 {file_path}: {e}")

        return False

    def fix_import_issues(self, file_path: Path) -> bool:
        """修復導入問題"""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                lines = f.readlines()

            # 找到第一個非註釋、非空行的導入語句位置
            first_import_line = -1
            for i, line in enumerate(lines):
                stripped = line.strip()
                if (
                    stripped
                    and not stripped.startswith("#")
                    and not stripped.startswith('"""')
                    and not stripped.startswith("'''")
                ):
                    if stripped.startswith("import ") or stripped.startswith("from "):
                        first_import_line = i
                        break
                    elif (
                        not stripped.startswith("#!/")
                        and not stripped.startswith('"""')
                        and not stripped.startswith("'''")
                    ):
                        # 找到第一個非導入語句
                        break

            if first_import_line == -1:
                return False

            # 移動sys.path.append到導入語句之前
            new_lines = []
            sys_path_lines = []
            changes_made = False

            for i, line in enumerate(lines):
                if "sys.path.append" in line:
                    sys_path_lines.append(line)
                    changes_made = True
                elif i == first_import_line and sys_path_lines:
                    # 在第一個導入語句前插入sys.path.append
                    new_lines.extend(sys_path_lines)
                    new_lines.append("\n")
                    new_lines.append(line)
                else:
                    new_lines.append(line)

            if changes_made:
                with open(file_path, "w", encoding="utf-8") as f:
                    f.writelines(new_lines)
                self.fixes_applied += 1
                return True

        except Exception as e:
            print(f"修復導入問題失敗 {file_path}: {e}")

        return False

    def run_fixes(self):
        """運行所有修復"""
        print("🔧 開始高級代碼質量修復...")

        python_files = self.find_python_files()
        print(f"找到 {len(python_files)} 個Python文件")

        for file_path in python_files:
            print(f"處理: {file_path}")

            # 應用各種修復
            self.fix_undefined_variables(file_path)
            self.fix_whitespace_issues(file_path)
            self.fix_comparison_issues(file_path)
            self.fix_import_issues(file_path)

        print(f"✅ 高級修復完成，共應用 {self.fixes_applied} 個修復")


def main():
    """主函數"""
    project_root = Path(__file__).parent
    fixer = AdvancedCodeFixer(str(project_root))
    fixer.run_fixes()


if __name__ == "__main__":
    main()
