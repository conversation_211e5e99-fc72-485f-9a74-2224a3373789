#!/bin/bash

# 企業級智能量化交易系統 - 一鍵啟動腳本
# Enterprise Intelligent Trading System - One-Click Start Script

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印帶顏色的消息
print_message() {
    echo -e "${2}${1}${NC}"
}

print_header() {
    echo "=================================================================="
    print_message "$1" "$BLUE"
    echo "=================================================================="
}

print_success() {
    print_message "✅ $1" "$GREEN"
}

print_warning() {
    print_message "⚠️  $1" "$YELLOW"
}

print_error() {
    print_message "❌ $1" "$RED"
}

# 檢查 Python 環境
check_python() {
    print_message "🐍 檢查 Python 環境..." "$BLUE"

    if ! command -v python3 &> /dev/null; then
        print_error "Python3 未安裝"
        exit 1
    fi

    python_version=$(python3 --version | cut -d' ' -f2)
    print_success "Python 版本: $python_version"
}

# 檢查依賴
check_dependencies() {
    print_message "📦 檢查依賴..." "$BLUE"

    if [ ! -f "requirements.txt" ]; then
        print_error "requirements.txt 文件不存在"
        exit 1
    fi

    # 安裝依賴
    print_message "正在安裝依賴..." "$YELLOW"
    pip3 install -r requirements.txt > /dev/null 2>&1
    print_success "依賴安裝完成"
}

# 運行測試
run_tests() {
    print_message "🧪 運行測試套件..." "$BLUE"

    if python3 -m pytest --tb=short -v > test_results.log 2>&1; then
        test_count=$(grep -c "PASSED" test_results.log || echo "0")
        print_success "所有測試通過 ($test_count 個測試)"
        rm -f test_results.log
    else
        print_error "測試失敗，請檢查 test_results.log"
        exit 1
    fi
}

# 檢查配置
check_config() {
    print_message "⚙️  檢查配置..." "$BLUE"

    if [ ! -f ".env" ]; then
        print_warning ".env 文件不存在，創建模板..."
        cat > .env << EOF
# 交易所配置
EXCHANGE_NAME=gateio
EXCHANGE_SANDBOX=true
EXCHANGE_API_KEY=your_api_key_here
EXCHANGE_SECRET=your_secret_here

# 資金管理
TOTAL_CAPITAL=100000
MAX_POSITION_SIZE=0.1
RISK_LIMIT=0.02

# Telegram 配置 (可選)
TELEGRAM_BOT_TOKEN=your_bot_token
TELEGRAM_CHAT_ID=your_chat_id
EOF
        print_warning "請編輯 .env 文件設置您的 API 密鑰"
    else
        print_success "配置文件存在"
    fi
}

# 創建必要目錄
create_directories() {
    print_message "📁 創建必要目錄..." "$BLUE"

    directories=("logs" "data" "records" "backups")
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            print_success "創建目錄: $dir"
        fi
    done
}

# 啟動系統
start_system() {
    print_message "🚀 啟動企業級交易系統..." "$BLUE"

    # 檢查是否已有進程在運行
    if pgrep -f "quick_start_production.py" > /dev/null; then
        print_warning "系統已在運行"
        print_message "如需重啟，請先運行: ./stop_trading.sh" "$YELLOW"
        exit 1
    fi

    # 啟動系統
    print_message "正在啟動..." "$YELLOW"
    python3 quick_start_production.py &

    # 獲取進程 ID
    SYSTEM_PID=$!
    echo $SYSTEM_PID > trading_system.pid

    # 等待系統啟動
    sleep 5

    # 檢查系統是否成功啟動
    if ps -p $SYSTEM_PID > /dev/null; then
        print_success "系統啟動成功 (PID: $SYSTEM_PID)"

        # 等待健康檢查端點可用
        print_message "等待健康檢查端點..." "$YELLOW"
        for i in {1..30}; do
            if curl -s http://localhost:8080/health > /dev/null 2>&1; then
                print_success "健康檢查端點已就緒"
                break
            fi
            sleep 1
        done

        # 顯示系統信息
        echo ""
        print_header "🎉 系統啟動成功！"
        echo ""
        print_message "📊 監控地址:" "$BLUE"
        echo "   健康檢查: http://localhost:8080/health"
        echo "   系統狀態: http://localhost:8080/status"
        echo "   系統指標: http://localhost:8080/metrics"
        echo ""
        print_message "🔧 管理命令:" "$BLUE"
        echo "   查看日誌: tail -f logs/trading_system.log"
        echo "   停止系統: ./stop_trading.sh"
        echo "   重啟系統: ./restart_trading.sh"
        echo ""
        print_message "📱 系統將在後台運行，按 Ctrl+C 不會停止系統" "$GREEN"

    else
        print_error "系統啟動失敗"
        rm -f trading_system.pid
        exit 1
    fi
}

# 主函數
main() {
    print_header "企業級智能量化交易系統啟動"
    echo "基於深度分析結果 - 100% 測試通過的生產就緒系統"
    echo ""

    # 執行檢查和啟動流程
    check_python
    check_dependencies
    run_tests
    check_config
    create_directories
    start_system

    echo ""
    print_header "🚀 系統已成功啟動並在後台運行！"
}

# 錯誤處理
trap 'print_error "啟動過程中發生錯誤"; exit 1' ERR

# 執行主函數
main "$@"
