#!/usr/bin/env python3
"""
語法分類診斷工具 (Syntax Triage Tool)
使用 py_compile 進行精確的語法錯誤定位
"""

import py_compile
import sys
from pathlib import Path
from typing import List, Dict, Tuple
import tempfile
import os


class SyntaxTriageTool:
    """語法分類診斷工具"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.syntax_errors = []
        self.valid_files = []
        
    def find_python_files(self) -> List[Path]:
        """查找所有Python文件"""
        python_files = []
        
        # 掃描主要目錄
        for pattern in ["*.py", "src/*.py", "tests/*.py"]:
            python_files.extend(self.project_root.glob(pattern))
        
        # 排除一些不需要檢查的文件
        exclude_patterns = [
            "__pycache__",
            ".git",
            "venv",
            "env",
            ".pytest_cache"
        ]
        
        filtered_files = []
        for file_path in python_files:
            if not any(pattern in str(file_path) for pattern in exclude_patterns):
                filtered_files.append(file_path)
        
        return filtered_files
    
    def check_file_syntax(self, file_path: Path) -> Tuple[bool, str]:
        """檢查單個文件的語法"""
        try:
            # 使用臨時文件避免創建 .pyc 文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as temp_file:
                with open(file_path, 'r', encoding='utf-8') as source_file:
                    temp_file.write(source_file.read())
                temp_file_path = temp_file.name
            
            try:
                # 嘗試編譯
                py_compile.compile(temp_file_path, doraise=True)
                return True, "語法正確"
            except py_compile.PyCompileError as e:
                return False, str(e)
            finally:
                # 清理臨時文件
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
                    
        except Exception as e:
            return False, f"文件讀取錯誤: {e}"
    
    def categorize_syntax_error(self, error_msg: str) -> str:
        """對語法錯誤進行分類"""
        error_msg_lower = error_msg.lower()
        
        if "unexpected indent" in error_msg_lower:
            return "🔴 縮進錯誤"
        elif "unmatched" in error_msg_lower or "does not match" in error_msg_lower:
            return "🟠 括號不匹配"
        elif "unexpected eof" in error_msg_lower or "eof while scanning" in error_msg_lower:
            return "🟡 文件結尾錯誤"
        elif "invalid syntax" in error_msg_lower:
            return "🔵 語法無效"
        elif "expected" in error_msg_lower:
            return "🟢 缺少語法元素"
        elif "cannot parse" in error_msg_lower:
            return "🟣 解析錯誤"
        else:
            return "⚪ 其他語法錯誤"
    
    def run_triage(self):
        """運行語法分類診斷"""
        print("🔍 開始語法健康檢查...")
        
        python_files = self.find_python_files()
        print(f"找到 {len(python_files)} 個Python文件")
        
        print("\n" + "="*80)
        print("正在檢查語法錯誤...")
        print("="*80)
        
        for file_path in python_files:
            is_valid, error_msg = self.check_file_syntax(file_path)
            
            if is_valid:
                self.valid_files.append(file_path)
                print(f"✅ {file_path}")
            else:
                error_category = self.categorize_syntax_error(error_msg)
                self.syntax_errors.append({
                    'file': file_path,
                    'error': error_msg,
                    'category': error_category
                })
                print(f"❌ {file_path}")
                print(f"   {error_category}: {error_msg}")
        
        self.generate_report()
    
    def generate_report(self):
        """生成語法錯誤報告"""
        print("\n" + "="*80)
        print("📊 語法健康檢查報告")
        print("="*80)
        
        total_files = len(self.valid_files) + len(self.syntax_errors)
        valid_count = len(self.valid_files)
        error_count = len(self.syntax_errors)
        
        print(f"總文件數: {total_files}")
        print(f"語法正確: {valid_count} ({valid_count/total_files*100:.1f}%)")
        print(f"語法錯誤: {error_count} ({error_count/total_files*100:.1f}%)")
        
        if self.syntax_errors:
            print("\n" + "-"*60)
            print("🎯 按優先級排列的修復清單")
            print("-"*60)
            
            # 按錯誤類型分組
            error_groups = {}
            for error_info in self.syntax_errors:
                category = error_info['category']
                if category not in error_groups:
                    error_groups[category] = []
                error_groups[category].append(error_info)
            
            # 定義優先級順序
            priority_order = [
                "🔴 縮進錯誤",
                "🟠 括號不匹配", 
                "🟡 文件結尾錯誤",
                "🟢 缺少語法元素",
                "🔵 語法無效",
                "🟣 解析錯誤",
                "⚪ 其他語法錯誤"
            ]
            
            for category in priority_order:
                if category in error_groups:
                    print(f"\n{category} ({len(error_groups[category])} 個文件):")
                    for error_info in error_groups[category][:5]:  # 只顯示前5個
                        print(f"  • {error_info['file']}")
                        # 提取錯誤行號
                        if "line" in error_info['error']:
                            print(f"    {error_info['error']}")
                    
                    if len(error_groups[category]) > 5:
                        print(f"    ... 還有 {len(error_groups[category]) - 5} 個文件")
        
        # 生成修復建議
        self.generate_fix_suggestions()
    
    def generate_fix_suggestions(self):
        """生成修復建議"""
        if not self.syntax_errors:
            print("\n🎉 所有文件語法正確！可以繼續預提交鉤子配置。")
            return
        
        print("\n" + "-"*60)
        print("💡 修復建議")
        print("-"*60)
        
        # 找出最關鍵的文件
        critical_files = [
            "main.py",
            "src/pair_trading_bot.py", 
            "src/portfolio_manager.py",
            "src/unified_config_manager.py",
            "src/unified_portfolio_manager.py"
        ]
        
        critical_errors = []
        for error_info in self.syntax_errors:
            file_name = str(error_info['file'])
            if any(critical in file_name for critical in critical_files):
                critical_errors.append(error_info)
        
        if critical_errors:
            print("\n🚨 優先修復核心文件:")
            for error_info in critical_errors:
                print(f"  1. {error_info['file']}")
                print(f"     {error_info['category']}: {error_info['error']}")
        
        print(f"\n📋 建議的修復順序:")
        print(f"  1. 先修復 {len(critical_errors)} 個核心文件")
        print(f"  2. 按錯誤類型批量修復（縮進錯誤 → 括號不匹配 → 其他）")
        print(f"  3. 每修復 3-5 個文件就運行一次驗證")
        
        print(f"\n🔧 下一步命令:")
        if critical_errors:
            first_file = critical_errors[0]['file']
            print(f"  # 修復第一個關鍵文件")
            print(f"  python -m py_compile {first_file}")
            print(f"  pre-commit run --files {first_file}")


def main():
    """主函數"""
    project_root = Path.cwd()
    triage_tool = SyntaxTriageTool(str(project_root))
    triage_tool.run_triage()


if __name__ == "__main__":
    main()
