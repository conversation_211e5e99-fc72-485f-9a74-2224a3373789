#!/usr/bin/env python3
"""
重構的主入口 - 使用命令模式的現代化架構
Refactored Main Entry - Modern Architecture with Command Pattern
"""

import argparse
import asyncio
import sys
from datetime import datetime
from pathlib import Path

# 添加項目根目錄到路徑
sys.path.append(str(Path(__file__).parent))

from src.command_system import CommandType, get_command_executor
from src.config_validation import ConfigValidator
from src.exception_system import get_exception_manager, handle_exception
from src.logging_config import get_logger, setup_logging

# 設置日誌
setup_logging()
_ = get_logger(__name__)


def create_argument_parser() -> argparse.ArgumentParser:
    """創建命令行參數解析器"""
    parser = argparse.ArgumentParser()
        description="配對交易機器人 - 企業級量化交易平台",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  %(prog)s live --config config.json --health-port 8080
  %(prog)s backtest --config config.json --start-date 2024-01-01 --end-date 2024-12-31
  %(prog)s portfolio --config config.json --health-port 8081
  %(prog)s health --config config.json
        """,
    )

    # 子命令
    subparsers = parser.add_subparsers(dest="command", help="可用命令")

    # 實時交易命令
    live_parser = subparsers.add_parser("live", help="運行實時交易")
    live_parser.add_argument("--config", "-c", required=True, help="配置文件路徑")
    live_parser.add_argument("--health-port", type=int, default=8080, help="健康檢查服務器端口")
    live_parser.add_argument("--dry-run", action="store_true", help="模擬運行（不執行實際交易）")

    # 回測命令
    backtest_parser = subparsers.add_parser("backtest", help="運行回測")
    backtest_parser.add_argument("--config", "-c", required=True, help="配置文件路徑")
    backtest_parser.add_argument("--start-date", required=True, help="回測開始日期 (YYYY-MM-DD)")
    backtest_parser.add_argument("--end-date", required=True, help="回測結束日期 (YYYY-MM-DD)")
    backtest_parser.add_argument("--initial-capital", type=float, default=10000, help="初始資金")
    backtest_parser.add_argument("--report-path", default="backtest_report.html", help="報告輸出路徑")

    # 投資組合交易命令
    portfolio_parser = subparsers.add_parser("portfolio", help="運行投資組合交易")
    portfolio_parser.add_argument("--config", "-c", required=True, help="配置文件路徑")
    portfolio_parser.add_argument("--health-port", type=int, default=8080, help="健康檢查服務器端口")
    portfolio_parser.add_argument("--max-pairs", type=int, help="最大交易對數量")

    # 健康檢查命令
    health_parser = subparsers.add_parser("health", help="執行系統健康檢查")
    health_parser.add_argument("--config", "-c", required=True, help="配置文件路徑")
    health_parser.add_argument("--detailed", action="store_true", help="詳細健康檢查")

    # 配置驗證命令
    validate_parser = subparsers.add_parser("validate", help="驗證配置文件")
    validate_parser.add_argument("--config", "-c", required=True, help="配置文件路徑")
    validate_parser.add_argument("--strict", action="store_true", help="嚴格驗證模式")

    # 為每個子命令添加通用選項
    for subparser in [
        live_parser,
        backtest_parser,
        portfolio_parser,
        health_parser,
        validate_parser,
    ]:
        subparser.add_argument("--verbose", "-v", action="store_true", help="詳細輸出")
        subparser.add_argument()
            "--log-level",
            choices=["DEBUG", "INFO", "WARNING", "ERROR"],
            default="INFO",
            help="日誌級別",
        )

    # 全局選項
    parser.add_argument("--version", action="version", version="配對交易機器人 v3.0")

    return parser


async def validate_config(config_file: str, strict: bool = False) -> bool:
    """驗證配置文件"""
    try:
        logger.info(f"驗證配置文件: {config_file}")

        # 檢查文件是否存在
        if not Path(config_file).exists():
            logger.error(f"配置文件不存在: {config_file}")
            return False

        # 載入並驗證配置
        config = ConfigValidator.load_and_validate_config(config_file)

        # 運行時驗證
        warnings = ConfigValidator.validate_runtime_config(config)

        if warnings:
            logger.warning("配置警告:")
            for warning in warnings:
                logger.warning(f"  - {warning}")

            if strict:
                logger.error("嚴格模式下不允許警告")
                return False

        logger.info("配置驗證通過")
        return True

    except Exception as e:
        trading_exception = handle_exception(e)
        logger.error(f"配置驗證失敗: {trading_exception.message}")
        return False


async def execute_command(args) -> int:
    """執行命令"""
    try:
        # 驗證配置文件
        if not await validate_config(args.config, getattr(args, "strict", False)):
            return 1

        # 獲取命令執行器
        executor = get_command_executor()

        # 根據命令類型執行
        if args.command == "live":
            result = await executor.execute_command()
                CommandType.LIVE_TRADING,
                config_file=args.config,
                health_port=args.health_port,
                dry_run=getattr(args, "dry_run", False),
            )

        elif args.command == "backtest":
            result = await executor.execute_command()
                CommandType.BACKTEST,
                config_file=args.config,
                start_date=args.start_date,
                end_date=args.end_date,
                initial_capital=args.initial_capital,
                report_path=args.report_path,
            )

        elif args.command == "portfolio":
            result = await executor.execute_command()
                CommandType.PORTFOLIO_TRADING,
                config_file=args.config,
                health_port=args.health_port,
                max_pairs=getattr(args, "max_pairs", None),
            )

        elif args.command == "health":
            result = await executor.execute_command()
                CommandType.HEALTH_CHECK,
                config_file=args.config,
                detailed=getattr(args, "detailed", False),
            )

        elif args.command == "validate":
            # 配置驗證已在上面完成
            result = {"status": "completed", "message": "配置驗證通過"}

        else:
            logger.error(f"不支持的命令: {args.command}")
            return 1

        # 處理結果
        if result.get("status") == "completed":
            logger.info("命令執行成功")

            # 打印結果摘要
            if args.verbose:
                logger.info(f"執行結果: {result}")

            return 0

        elif result.get("status") == "interrupted":
            logger.info("命令被中斷")
            return 130  # SIGINT exit code

        else:
            logger.error(f"命令執行失敗: {result.get('error', '未知錯誤')}")
            return 1

    except KeyboardInterrupt:
        logger.info("收到中斷信號，正在退出...")
        return 130

    except Exception as e:
        trading_exception = handle_exception(e)
        logger.error(f"命令執行異常: {trading_exception.message}")

        if args.verbose:
            logger.error(f"異常詳情: {trading_exception.to_dict()}")

        return 1


async def print_system_info():
    """打印系統信息"""
    logger.info("=" * 60)
    logger.info("配對交易機器人 v3.0 - 企業級量化交易平台")
    logger.info("=" * 60)
    logger.info(f"啟動時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"Python版本: {sys.version}")
    logger.info(f"工作目錄: {Path.cwd()}")

    # 檢查關鍵依賴
    try:
        import ccxt
        import numpy as np
        import pandas as pd

        logger.info(f"Pandas: {pd.__version__}")
        logger.info(f"NumPy: {np.__version__}")
        logger.info(f"CCXT: {ccxt.__version__}")
    except ImportError as e:
        logger.warning(f"依賴檢查失敗: {e}")

    logger.info("=" * 60)


async def print_final_stats():
    """打印最終統計"""
    try:
        # 獲取異常統計
        exception_manager = get_exception_manager()
        exception_stats = exception_manager.get_exception_stats()

        if exception_stats["total_exceptions"] > 0:
            logger.info("=" * 60)
            logger.info("異常統計:")
            logger.info(f"總異常數: {exception_stats['total_exceptions']}")

            if exception_stats.get("category_counts"):
                logger.info("按分類統計:")
                for category, count in exception_stats["category_counts"].items():
                    logger.info(f"  {category}: {count}")

        logger.info("=" * 60)
        logger.info("程序執行完成")

    except Exception as e:
        logger.error(f"打印統計失敗: {e}")


async def main():
    """主函數"""
    try:
        # 創建參數解析器
        parser = create_argument_parser()
        args = parser.parse_args()

        # 如果沒有提供命令，顯示幫助
        if not args.command:
            parser.print_help()
            return 1

        # 設置日誌級別
        if hasattr(args, "log_level"):
            import logging

            logging.getLogger().setLevel(getattr(logging, args.log_level))

        # 打印系統信息
        if args.verbose:
            await print_system_info()

        # 執行命令
        exit_code = await execute_command(args)

        # 打印最終統計
        if args.verbose:
            await print_final_stats()

        return exit_code

    except Exception as e:
        logger.error(f"程序執行失敗: {e}")
        return 1


def run():
    """同步入口點"""
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("程序被中斷")
        sys.exit(130)
    except Exception as e:
        logger.error(f"程序異常退出: {e}")
        sys.exit(1)


if __name__ == "__main__":
    run()
