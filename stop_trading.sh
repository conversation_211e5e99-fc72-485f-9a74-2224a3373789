#!/bin/bash

# 企業級智能量化交易系統 - 停止腳本
# Enterprise Intelligent Trading System - Stop Script

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印帶顏色的消息
print_message() {
    echo -e "${2}${1}${NC}"
}

print_success() {
    print_message "✅ $1" "$GREEN"
}

print_warning() {
    print_message "⚠️  $1" "$YELLOW"
}

print_error() {
    print_message "❌ $1" "$RED"
}

# 停止系統
stop_system() {
    print_message "🛑 停止企業級交易系統..." "$BLUE"

    # 檢查 PID 文件
    if [ -f "trading_system.pid" ]; then
        PID=$(cat trading_system.pid)

        # 檢查進程是否存在
        if ps -p $PID > /dev/null 2>&1; then
            print_message "正在停止系統 (PID: $PID)..." "$YELLOW"

            # 發送 SIGTERM 信號
            kill -TERM $PID

            # 等待進程優雅退出
            for i in {1..30}; do
                if ! ps -p $PID > /dev/null 2>&1; then
                    print_success "系統已優雅停止"
                    rm -f trading_system.pid
                    return 0
                fi
                sleep 1
            done

            # 如果進程仍在運行，強制終止
            print_warning "進程未響應，強制終止..."
            kill -KILL $PID 2>/dev/null || true
            rm -f trading_system.pid
            print_success "系統已強制停止"

        else
            print_warning "PID 文件存在但進程不存在"
            rm -f trading_system.pid
        fi
    else
        # 查找可能的進程
        PIDS=$(pgrep -f "quick_start_production.py" || true)

        if [ -n "$PIDS" ]; then
            print_message "發現運行中的交易系統進程..." "$YELLOW"
            for PID in $PIDS; do
                print_message "停止進程 $PID..." "$YELLOW"
                kill -TERM $PID

                # 等待進程退出
                for i in {1..10}; do
                    if ! ps -p $PID > /dev/null 2>&1; then
                        print_success "進程 $PID 已停止"
                        break
                    fi
                    sleep 1
                done

                # 如果仍在運行，強制終止
                if ps -p $PID > /dev/null 2>&1; then
                    kill -KILL $PID 2>/dev/null || true
                    print_warning "進程 $PID 已強制終止"
                fi
            done
        else
            print_warning "未發現運行中的交易系統"
        fi
    fi
}

# 清理資源
cleanup_resources() {
    print_message "🧹 清理資源..." "$BLUE"

    # 清理臨時文件
    rm -f trading_system.pid
    rm -f test_results.log

    # 檢查端口占用
    if lsof -i :8080 > /dev/null 2>&1; then
        print_warning "端口 8080 仍被占用"
        PIDS=$(lsof -t -i :8080 || true)
        if [ -n "$PIDS" ]; then
            for PID in $PIDS; do
                print_message "終止占用端口的進程 $PID..." "$YELLOW"
                kill -TERM $PID 2>/dev/null || true
            done
        fi
    fi

    print_success "資源清理完成"
}

# 顯示系統狀態
show_status() {
    print_message "📊 檢查系統狀態..." "$BLUE"

    # 檢查進程
    PIDS=$(pgrep -f "quick_start_production.py" || true)
    if [ -n "$PIDS" ]; then
        print_warning "仍有交易系統進程在運行:"
        for PID in $PIDS; do
            echo "  PID: $PID"
        done
    else
        print_success "沒有交易系統進程在運行"
    fi

    # 檢查端口
    if lsof -i :8080 > /dev/null 2>&1; then
        print_warning "端口 8080 仍被占用"
    else
        print_success "端口 8080 已釋放"
    fi

    # 檢查 PID 文件
    if [ -f "trading_system.pid" ]; then
        print_warning "PID 文件仍存在"
    else
        print_success "PID 文件已清理"
    fi
}

# 主函數
main() {
    echo "=================================================================="
    print_message "企業級智能量化交易系統停止" "$BLUE"
    echo "=================================================================="
    echo ""

    stop_system
    cleanup_resources
    show_status

    echo ""
    echo "=================================================================="
    print_success "🎯 系統已完全停止！"
    echo "=================================================================="
    echo ""
    print_message "💡 提示:" "$BLUE"
    echo "   重新啟動: ./start_trading.sh"
    echo "   查看日誌: tail -f logs/trading_system.log"
    echo ""
}

# 執行主函數
main "$@"
