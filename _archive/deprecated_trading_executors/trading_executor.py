import ccxt
from typing import Dict, List, Optional
from datetime import datetime
from utils import calculate_position_size, save_trade_record
from logging_config import get_logger
from enhanced_retry_handler import enhanced_retry, RetryStrategy

logger = get_logger(__name__)


class TradingExecutor:
    """交易執行類，負責處理訂單執行"""
    
    def __init__(self, exchange: ccxt.Exchange, config: Dict):
        self.exchange = exchange
        self.config = config
        self.trading_pair = config['trading_pair']
        self.position_size_usd = config['position_size_usd']
        
        # 當前持倉信息
        self.current_position = {
            'is_active': False,
            'is_long_base': None,  # True: 做多base/做空quote, False: 做空base/做多quote
            'entry_time': None,
            'entry_bar_index': None,
            'base_entry_price': None,
            'quote_entry_price': None,
            'base_position_size': None,
            'quote_position_size': None,
            'base_order_id': None,
            'quote_order_id': None,
            'actual_position_value': None  # 實際開倉總價值
        }

        # 交易歷史記錄
        self.trade_history = []
        
        logger.info("TradingExecutor 初始化完成")
    
    def get_account_balance(self) -> Dict:
        """獲取賬戶餘額"""
        try:
            balance = self.exchange.fetch_balance()
            logger.debug("賬戶餘額獲取成功")
            return balance
        except Exception as e:
            logger.error(f"獲取賬戶餘額失敗: {e}")
            return {}
    
    @enhanced_retry(
        max_retries=3,
        base_delay=1.0,
        max_delay=15.0,
        strategy=RetryStrategy.EXPONENTIAL,
        exceptions=(ccxt.NetworkError, ccxt.ExchangeNotAvailable, ccxt.RequestTimeout),
        timeout=30.0
    )
    def place_market_order(self, symbol: str, side: str, amount: float) -> Optional[Dict]:
        """下市價單 - 帶重試機制"""
        try:
            # 設置請求超時
            if hasattr(self.exchange, 'timeout'):
                self.exchange.timeout = 20000  # 20秒超時

            order = self.exchange.create_market_order(symbol, side, amount)

            if not order or 'id' not in order:
                raise ccxt.ExchangeError(f"訂單創建失敗，返回數據無效: {order}")

            logger.info(f"市價單執行成功 - {symbol}: {side} {amount}, 訂單ID: {order.get('id')}")
            return order

        except (ccxt.InsufficientFunds, ccxt.InvalidOrder, ccxt.BadSymbol,
                ccxt.AuthenticationError, ccxt.PermissionDenied) as e:
            # 永久性錯誤，不重試
            logger.error(f"市價單執行失敗（永久性錯誤）- {symbol}: {side} {amount}, 錯誤: {e}")
            raise
        except Exception as e:
            logger.error(f"市價單執行失敗 - {symbol}: {side} {amount}, 錯誤: {e}")
            raise
    
    def enter_long_base_short_quote(self, base_price: float, quote_price: float, 
                                   bar_index: int) -> bool:
        """進場：做多 Base，做空 Quote"""
        try:
            base_symbol, quote_symbol = self.trading_pair
            
            # 計算倉位大小
            base_position_size = calculate_position_size(base_price, self.position_size_usd)
            quote_position_size = calculate_position_size(quote_price, self.position_size_usd)
            
            # 執行訂單
            base_order = self.place_market_order(base_symbol, 'buy', base_position_size)
            quote_order = self.place_market_order(quote_symbol, 'sell', quote_position_size)
            
            if base_order and quote_order:
                # 計算實際倉位價值 (假設 position_size_usd 是每條腿的目標價值)
                actual_base_value = base_price * base_position_size
                actual_quote_value = quote_price * quote_position_size
                actual_total_value = actual_base_value + actual_quote_value

                # 更新持倉信息
                self.current_position.update({
                    'is_active': True,
                    'is_long_base': True,
                    'entry_time': datetime.now(),
                    'entry_bar_index': bar_index,
                    'base_entry_price': base_price,
                    'quote_entry_price': quote_price,
                    'base_position_size': base_position_size,
                    'quote_position_size': quote_position_size,
                    'base_order_id': base_order.get('id'),
                    'quote_order_id': quote_order.get('id'),
                    'actual_position_value': actual_total_value
                })
                
                # 記錄交易
                trade_record = {
                    'action': 'enter_long_base_short_quote',
                    'base_symbol': base_symbol,
                    'quote_symbol': quote_symbol,
                    'base_price': base_price,
                    'quote_price': quote_price,
                    'base_position_size': base_position_size,
                    'quote_position_size': quote_position_size,
                    'bar_index': bar_index
                }
                save_trade_record(trade_record)
                
                logger.info(f"進場成功 - 做多 {base_symbol} @ {base_price}, 做空 {quote_symbol} @ {quote_price}")
                return True
            else:
                logger.error("進場失敗 - 訂單執行不完整")
                return False
                
        except Exception as e:
            logger.error(f"進場失敗: {e}")
            return False
    
    def enter_short_base_long_quote(self, base_price: float, quote_price: float, 
                                   bar_index: int) -> bool:
        """進場：做空 Base，做多 Quote"""
        try:
            base_symbol, quote_symbol = self.trading_pair
            
            # 計算倉位大小
            base_position_size = calculate_position_size(base_price, self.position_size_usd)
            quote_position_size = calculate_position_size(quote_price, self.position_size_usd)
            
            # 執行訂單
            base_order = self.place_market_order(base_symbol, 'sell', base_position_size)
            quote_order = self.place_market_order(quote_symbol, 'buy', quote_position_size)
            
            if base_order and quote_order:
                # 計算實際倉位價值 (假設 position_size_usd 是每條腿的目標價值)
                actual_base_value = base_price * base_position_size
                actual_quote_value = quote_price * quote_position_size
                actual_total_value = actual_base_value + actual_quote_value

                # 更新持倉信息
                self.current_position.update({
                    'is_active': True,
                    'is_long_base': False,
                    'entry_time': datetime.now(),
                    'entry_bar_index': bar_index,
                    'base_entry_price': base_price,
                    'quote_entry_price': quote_price,
                    'base_position_size': base_position_size,
                    'quote_position_size': quote_position_size,
                    'base_order_id': base_order.get('id'),
                    'quote_order_id': quote_order.get('id'),
                    'actual_position_value': actual_total_value
                })
                
                # 記錄交易
                trade_record = {
                    'action': 'enter_short_base_long_quote',
                    'base_symbol': base_symbol,
                    'quote_symbol': quote_symbol,
                    'base_price': base_price,
                    'quote_price': quote_price,
                    'base_position_size': base_position_size,
                    'quote_position_size': quote_position_size,
                    'bar_index': bar_index
                }
                save_trade_record(trade_record)
                
                logger.info(f"進場成功 - 做空 {base_symbol} @ {base_price}, 做多 {quote_symbol} @ {quote_price}")
                return True
            else:
                logger.error("進場失敗 - 訂單執行不完整")
                return False
                
        except Exception as e:
            logger.error(f"進場失敗: {e}")
            return False
    
    def exit_position(self, exit_reason: str, current_base_price: float = None, 
                     current_quote_price: float = None) -> bool:
        """平倉"""
        try:
            if not self.current_position['is_active']:
                logger.warning("嘗試平倉但當前無持倉")
                return False
            
            base_symbol, quote_symbol = self.trading_pair
            
            # 確定平倉方向
            if self.current_position['is_long_base']:
                # 原來做多base/做空quote，現在要做空base/做多quote
                base_side = 'sell'
                quote_side = 'buy'
            else:
                # 原來做空base/做多quote，現在要做多base/做空quote
                base_side = 'buy'
                quote_side = 'sell'
            
            # 執行平倉訂單
            base_order = self.place_market_order(
                base_symbol, base_side, self.current_position['base_position_size']
            )
            quote_order = self.place_market_order(
                quote_symbol, quote_side, self.current_position['quote_position_size']
            )
            
            if base_order and quote_order:
                # 計算盈虧
                pnl = self.calculate_current_pnl(current_base_price, current_quote_price)

                # 記錄交易到歷史
                trade_history_record = {
                    'entry_time': self.current_position['entry_time'],
                    'exit_time': datetime.now(),
                    'pnl': pnl,
                    'exit_reason': exit_reason,
                    'is_long_base': self.current_position['is_long_base'],
                    'hold_time': (datetime.now() - self.current_position['entry_time']).total_seconds()
                }
                self.trade_history.append(trade_history_record)

                # 記錄交易到文件
                trade_record = {
                    'action': 'exit_position',
                    'exit_reason': exit_reason,
                    'base_symbol': base_symbol,
                    'quote_symbol': quote_symbol,
                    'entry_base_price': self.current_position['base_entry_price'],
                    'entry_quote_price': self.current_position['quote_entry_price'],
                    'exit_base_price': current_base_price,
                    'exit_quote_price': current_quote_price,
                    'pnl': pnl,
                    'hold_time': (datetime.now() - self.current_position['entry_time']).total_seconds()
                }
                save_trade_record(trade_record)
                
                # 重置持倉信息
                self.current_position = {
                    'is_active': False,
                    'is_long_base': None,
                    'entry_time': None,
                    'entry_bar_index': None,
                    'base_entry_price': None,
                    'quote_entry_price': None,
                    'base_position_size': None,
                    'quote_position_size': None,
                    'base_order_id': None,
                    'quote_order_id': None,
                    'actual_position_value': None
                }
                
                logger.info(f"平倉成功 - 原因: {exit_reason}, 盈虧: {pnl:.2f}")
                return True
            else:
                logger.error("平倉失敗 - 訂單執行不完整")
                return False
                
        except Exception as e:
            logger.error(f"平倉失敗: {e}")
            return False
    
    def calculate_current_pnl(self, current_base_price: float = None, 
                             current_quote_price: float = None) -> float:
        """計算當前盈虧"""
        try:
            if not self.current_position['is_active']:
                return 0.0
            
            # 確保傳入了當前價格，否則記錄錯誤並返回0
            if current_base_price is None or current_quote_price is None:
                logger.error("計算盈虧時缺少當前價格，無法準確計算。")
                return 0.0
            
            entry_base_price = self.current_position['base_entry_price']
            entry_quote_price = self.current_position['quote_entry_price']
            base_size = self.current_position['base_position_size']
            quote_size = self.current_position['quote_position_size']
            
            if self.current_position['is_long_base']:
                # 做多base，做空quote
                base_pnl = (current_base_price - entry_base_price) * base_size
                quote_pnl = (entry_quote_price - current_quote_price) * quote_size
            else:
                # 做空base，做多quote
                base_pnl = (entry_base_price - current_base_price) * base_size
                quote_pnl = (current_quote_price - entry_quote_price) * quote_size
            
            total_pnl = base_pnl + quote_pnl
            return total_pnl
            
        except Exception as e:
            logger.error(f"計算盈虧失敗: {e}")
            return 0.0
    
    def get_position_info(self) -> Dict:
        """獲取持倉信息"""
        position_info = self.current_position.copy()
        if position_info['is_active']:
            position_info['current_pnl'] = self.calculate_current_pnl()
        return position_info
    
    def is_position_active(self) -> bool:
        """檢查是否有活躍持倉"""
        return self.current_position['is_active']

    def get_actual_position_value(self) -> float:
        """獲取實際倉位價值"""
        if self.current_position['is_active']:
            return self.current_position.get('actual_position_value', 0.0)
        return 0.0

    def get_trade_history(self) -> List[Dict]:
        """獲取交易歷史"""
        return self.trade_history.copy()

    def get_trade_statistics(self) -> Dict:
        """獲取交易統計信息"""
        if not self.trade_history:
            return {
                'total_trades': 0,
                'winning_trades': 0,
                'losing_trades': 0,
                'total_pnl': 0.0,
                'avg_win': 0.0,
                'avg_loss': 0.0,
                'win_rate': 0.0,
                'profit_loss_ratio': 0.0
            }

        total_trades = len(self.trade_history)
        winning_trades = [t for t in self.trade_history if t['pnl'] > 0]
        losing_trades = [t for t in self.trade_history if t['pnl'] < 0]

        total_pnl = sum(t['pnl'] for t in self.trade_history)
        avg_win = sum(t['pnl'] for t in winning_trades) / len(winning_trades) if winning_trades else 0.0
        avg_loss = sum(abs(t['pnl']) for t in losing_trades) / len(losing_trades) if losing_trades else 0.0
        win_rate = len(winning_trades) / total_trades if total_trades > 0 else 0.0
        profit_loss_ratio = avg_win / avg_loss if avg_loss > 0 else float('inf') if avg_win > 0 else 0.0

        return {
            'total_trades': total_trades,
            'winning_trades': len(winning_trades),
            'losing_trades': len(losing_trades),
            'total_pnl': total_pnl,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'win_rate': win_rate,
            'profit_loss_ratio': profit_loss_ratio
        }
