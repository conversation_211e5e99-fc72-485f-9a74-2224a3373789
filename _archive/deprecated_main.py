#!/usr/bin/env python3
"""
進階配對交易機器人主程序
"""

import argparse
import time
import signal
import sys
from apscheduler.schedulers.blocking import BlockingScheduler

from pair_trading_bot import PairTradingBot
from backtesting import BacktestEngine
from pair_selection import PairSelectionTool
from config_manager import ConfigManager
from alert_manager import get_alert_manager, AlertLevel
from health_server import start_health_server
from dynamic_config import get_dynamic_config_manager
from telegram_bot import get_telegram_bot
from retry_handler import retry_api_call
from portfolio_manager import PortfolioManager
from logging_config import setup_logging, get_logger
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path

# 設置日誌
setup_logging()  # 修復不完整調用
logger = get_logger(__name__)

# 全局變量
bot = None
scheduler = None
running = False


def signal_handler(signum, frame):
    """信號處理器，用於優雅關閉"""
    global running, scheduler
    logger.info(f"接收到信號 {signum}，正在關閉程序...")
    running = False
    if scheduler:
        scheduler.shutdown()
    sys.exit(0)


def run_trading_bot():
    """運行交易機器人（增強版）"""
    global bot
    alert_manager = get_alert_manager() # 將警報管理器初始化移到函數開頭
    try:
        if bot is None:
            bot = PairTradingBot()

            # 發送啟動警報
            alert_manager.send_startup_alert()

        # 使用重試機制執行單次迭代
        success = retry_api_call(bot.run_single_iteration)

        if success:
            # 獲取狀態報告
            status = bot.get_status_report()

            # 更新健康狀態
            alert_manager.update_health_status({
                'status': 'ok',
                'current_position': status.get('position_info', {}),
                'last_zscore': status.get('current_zscore', 0)
            })

            logger.info(f"交易狀態: {status.get('trading_state', 'unknown')}, "
                       f"Z-score: {status.get('current_zscore', 'N/A'):.4f}")

            # 檢查是否有新交易
            position_info = status.get('position_info', {})
            if position_info.get('is_active') and not bot._last_trade_notified:
                alert_manager.send_trade_alert("進場", {
                    "交易對": bot.config['trading_pair'],
                    "Z-score": status.get('current_zscore', 0),
                    "倉位方向": "做多Base" if position_info.get('is_long_base') else "做空Base"
                })
                bot._last_trade_notified = True
            elif not position_info.get('is_active'):
                bot._last_trade_notified = False
        else:
            logger.warning("交易機器人迭代失敗")

            # 發送錯誤警報
            alert_manager.send_error_alert("迭代失敗", "交易機器人單次迭代執行失敗")

    except Exception as e:
        logger.error(f"運行交易機器人失敗: {e}")

        # 發送緊急警報
        alert_manager.send_critical_alert(
            "機器人運行失敗",
            f"交易機器人運行過程中發生嚴重錯誤: {str(e)}",
            {"錯誤類型": type(e).__name__}
        )


def run_live_trading(timeframe: str = "1m", enable_health_server: bool = True,
                    enable_telegram: bool = True):
    """運行實時交易（全自動化增強版）"""
    global running, scheduler

    logger.info("啟動全自動化實時交易模式...")

    # 註冊信號處理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        # 初始化警報管理器
        alert_manager = get_alert_manager()

        # 啟動健康檢查服務器
        health_server = None
        if enable_health_server:
            try:
                health_server = start_health_server()
                logger.info("健康檢查服務器已啟動: http://0.0.0.0:8080")
            except Exception as e:
                logger.warning(f"健康檢查服務器啟動失敗: {e}")

        # 啟動 Telegram 機器人
        telegram_bot = None
        if enable_telegram:
            try:
                telegram_bot = get_telegram_bot()
                if telegram_bot:
                    telegram_bot.start_polling()
                    logger.info("Telegram 控制機器人已啟動")
            except Exception as e:
                logger.warning(f"Telegram 機器人啟動失敗: {e}")

        # 初始化動態配置管理器
        config_manager = get_dynamic_config_manager()

        # 初始化機器人
        global bot
        bot = PairTradingBot()

        # 設置調度器
        scheduler = BlockingScheduler()

        # 根據時間框架設置執行頻率
        if timeframe == "1m":
            scheduler.add_job(run_trading_bot, 'cron', minute='*')
        elif timeframe == "5m":
            scheduler.add_job(run_trading_bot, 'cron', minute='*/5')
        elif timeframe == "15m":
            scheduler.add_job(run_trading_bot, 'cron', minute='*/15')
        elif timeframe == "1h":
            scheduler.add_job(run_trading_bot, 'cron', minute='0')
        else:
            logger.error(f"不支持的時間框架: {timeframe}")
            return

        # 添加配置重新載入任務（每小時檢查一次）
        scheduler.add_job(
            lambda: config_manager.execute_remote_command('reload_config'),
            'cron',
            minute='0'
        )

        logger.info(f"全自動化交易機器人已啟動，時間框架: {timeframe}")
        logger.info("功能狀態:")
        logger.info(f"  - 健康檢查服務器: {'✓' if health_server else '✗'}")
        logger.info(f"  - Telegram 控制: {'✓' if telegram_bot else '✗'}")
        logger.info("  - 動態配置: ✓")
        logger.info("  - 警報系統: ✓")

        running = True

        # 發送啟動完成警報
        alert_manager.send_alert(
            AlertLevel.INFO,
            "全自動化系統啟動",
            "配對交易機器人全自動化系統已成功啟動",
            {
                "時間框架": timeframe,
                "健康檢查": "已啟用" if health_server else "未啟用",
                "Telegram控制": "已啟用" if telegram_bot else "未啟用"
            }
        )

        # 開始調度
        scheduler.start()

    except KeyboardInterrupt:
        logger.info("用戶中斷，正在關閉...")
    except Exception as e:
        logger.error(f"實時交易失敗: {e}")

        # 發送錯誤警報
        alert_manager.send_critical_alert(
            "系統啟動失敗",
            f"全自動化系統啟動失敗: {str(e)}",
            {"錯誤類型": type(e).__name__}
        )
    finally:
        # 發送關閉警報 (提前發送，確保在清理資源前發出)
        if 'alert_manager' in locals(): # 確保 alert_manager 已定義
            alert_manager.send_shutdown_alert()

        # 清理資源
        if scheduler:
            scheduler.shutdown()

        # 停止 Telegram 機器人
        if 'telegram_bot' in locals() and telegram_bot:
            telegram_bot.stop_polling()

        logger.info("全自動化交易機器人已關閉")


def run_portfolio_trading(timeframe: str = "5m", enable_health_server: bool = True,
                         enable_telegram: bool = True):
    """運行投資組合交易模式"""
    global running, scheduler

    logger.info("啟動投資組合交易模式...")

    # 註冊信號處理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        # 初始化警報管理器
        alert_manager = get_alert_manager()

        # 啟動健康檢查服務器
        health_server = None
        if enable_health_server:
            try:
                health_server = start_health_server()
                logger.info("健康檢查服務器已啟動: http://0.0.0.0:8080")
                logger.info("儀表板地址: http://0.0.0.0:8080/dashboard")
            except Exception as e:
                logger.warning(f"健康檢查服務器啟動失敗: {e}")

        # 啟動 Telegram 機器人
        telegram_bot = None
        if enable_telegram:
            try:
                telegram_bot = get_telegram_bot()
                if telegram_bot:
                    telegram_bot.start_polling()
                    logger.info("Telegram 控制機器人已啟動")
            except Exception as e:
                logger.warning(f"Telegram 機器人啟動失敗: {e}")

        # 初始化投資組合管理器
        portfolio_manager = PortfolioManager(total_capital=100000)

        # 初始化投資組合
        if not portfolio_manager.initialize_portfolio():
            logger.error("投資組合初始化失敗")
            return

        # 啟動投資組合交易
        if not portfolio_manager.start_portfolio_trading():
            logger.error("投資組合交易啟動失敗")
            return

        logger.info("投資組合交易模式已啟動")
        logger.info("功能狀態:")
        logger.info(f"  - 健康檢查服務器: {'✓' if health_server else '✗'}")
        logger.info(f"  - Web 儀表板: {'✓' if health_server else '✗'}")
        logger.info(f"  - Telegram 控制: {'✓' if telegram_bot else '✗'}")
        logger.info("  - 投資組合管理: ✓")

        running = True

        # 發送啟動完成警報
        alert_manager.send_alert(
            AlertLevel.INFO,
            "投資組合交易系統啟動",
            "投資組合交易系統已成功啟動",
            {
                "模式": "投資組合交易",
                "健康檢查": "已啟用" if health_server else "未啟用",
                "Web儀表板": "已啟用" if health_server else "未啟用",
                "Telegram控制": "已啟用" if telegram_bot else "未啟用"
            }
        )

        # 保持運行
        try:
            while running:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("用戶中斷，正在關閉...")

    except Exception as e:
        logger.error(f"投資組合交易失敗: {e}")

        # 發送錯誤警報
        alert_manager.send_critical_alert(
            "投資組合系統啟動失敗",
            f"投資組合交易系統啟動失敗: {str(e)}",
            {"錯誤類型": type(e).__name__}
        )
    finally:
        # 發送關閉警報 (提前發送，確保在清理資源前發出)
        if 'alert_manager' in locals(): # 確保 alert_manager 已定義
            alert_manager.send_shutdown_alert()

        # 清理資源
        if 'portfolio_manager' in locals():
            portfolio_manager.stop_portfolio_trading()

        # 停止 Telegram 機器人
        if 'telegram_bot' in locals() and telegram_bot:
            telegram_bot.stop_polling()

        logger.info("投資組合交易系統已關閉")


def run_backtest(data_path: str):
    """運行回測"""
    try:
        logger.info(f"開始回測，數據文件: {data_path}")
        
        # 創建回測引擎
        backtest_engine = BacktestEngine()
        
        # 載入數據
        if not backtest_engine.load_historical_data(data_path):
            logger.error("載入回測數據失敗")
            return
        
        # 執行回測
        results = backtest_engine.run_backtest()
        
        if results:
            # 生成報告
            report = backtest_engine.generate_report()
            print(report)
            
            # 保存結果
            backtest_engine.save_results()
            
            logger.info("回測完成")
        else:
            logger.error("回測執行失敗")
            
    except Exception as e:
        logger.error(f"回測失敗: {e}")


def run_pair_selection():
    """運行配對篩選"""
    try:
        logger.info("開始配對篩選分析...")
        
        # 創建篩選工具
        pair_tool = PairSelectionTool()
        
        # 執行分析
        results = pair_tool.run_analysis()
        
        if results:
            # 生成報告
            report = pair_tool.generate_report()
            print(report)
            
            # 保存結果
            pair_tool.save_results()
            
            # 繪製相關性矩陣
            pair_tool.plot_correlation_matrix("pair_selection_results/correlation_matrix.png")
            
            logger.info("配對篩選完成")
        else:
            logger.error("配對篩選失敗")
            
    except Exception as e:
        logger.error(f"配對篩選失敗: {e}")


def run_tests():
    """運行單元測試"""
    try:
        import subprocess
        import sys

        logger.info("運行單元測試...")

        # 運行測試
        result = subprocess.run([
            sys.executable, '-m', 'unittest', 'test_pair_trading_bot', '-v'
        ], capture_output=True, text=True)

        print("測試輸出:")
        print(result.stdout)

        if result.stderr:
            print("錯誤輸出:")
            print(result.stderr)

        if result.returncode == 0:
            logger.info("所有測試通過！")
        else:
            logger.error("部分測試失敗")

        return result.returncode == 0

    except Exception as e:
        logger.error(f"運行測試失敗: {e}")
        return False


def run_improved_backtest(data_path: str):
    """運行改進版回測"""
    try:
        from improved_backtest import ImprovedBacktestEngine

        logger.info(f"開始改進版回測，數據文件: {data_path}")

        # 創建回測引擎
        backtest_engine = ImprovedBacktestEngine()

        # 載入數據
        if not backtest_engine.load_data(data_path):
            logger.error("載入回測數據失敗")
            return

        # 執行回測
        results = backtest_engine.run_backtest()

        if results:
            # 生成報告
            report = backtest_engine.generate_report()
            print(report)

            # 保存結果
            backtest_engine.save_results()

            logger.info("改進版回測完成")
        else:
            logger.error("改進版回測執行失敗")

    except Exception as e:
        logger.error(f"改進版回測失敗: {e}")


def check_system_compatibility():
    """檢查系統兼容性"""
    try:
        config_manager = ConfigManager()
        system_info = config_manager.get_system_info()

        print("=== 系統兼容性檢查 ===")
        print(f"操作系統: {system_info['system']} {system_info['version']}")
        print(f"架構: {system_info['machine']}")
        print(f"Python 版本: {system_info['python_version']}")
        print(f"編碼: {system_info['encoding']}")

        # 檢查必要的目錄
        required_dirs = ['data', 'logs', 'records']

        print("\n=== 目錄檢查 ===")
        for dir_name in required_dirs:
            dir_path = Path(dir_name)
            if dir_path.exists():
                print(f"✓ {dir_name}/ 目錄存在")
            else:
                dir_path.mkdir(exist_ok=True)
                print(f"✓ {dir_name}/ 目錄已創建")

        # 檢查配置文件
        print("\n=== 配置檢查 ===")
        config_path = Path("config.json")
        if config_path.exists():
            print("✓ config.json 存在")
            try:
                config = config_manager.load_config()
                print("✓ 配置文件格式正確")

                api_valid = config_manager.validate_api_config()
                if api_valid:
                    print("✓ API 配置有效")
                else:
                    print("⚠ API 配置需要設置（請檢查 .env 文件）")

            except Exception as e:
                print(f"✗ 配置文件載入失敗: {e}")
        else:
            print("✗ config.json 不存在")

        # 檢查 .env 文件
        env_path = Path(".env")
        if env_path.exists():
            print("✓ .env 文件存在")
        else:
            print("⚠ .env 文件不存在，將使用默認配置")
            print("  提示: 複製 .env.template 為 .env 並填入 API 密鑰")

        print("\n系統兼容性檢查完成！")
        return True

    except Exception as e:
        logger.error(f"系統兼容性檢查失敗: {e}")
        return False


def generate_sample_data():
    """生成示例數據用於測試"""
    try:
        
        logger.info("生成示例數據...")
        
        # 生成時間序列
        start_date = datetime.now() - timedelta(days=30)
        dates = pd.date_range(start=start_date, periods=30*24, freq='h')
        
        # 生成模擬價格數據
        np.random.seed(42)
        
        # BTC 價格（基準）
        btc_returns = np.random.normal(0, 0.02, len(dates))
        btc_prices = [50000]  # 起始價格
        for ret in btc_returns[1:]:
            btc_prices.append(btc_prices[-1] * (1 + ret))
        
        # ETH 價格（與BTC相關）
        eth_correlation = 0.8
        eth_returns = []
        for i, btc_ret in enumerate(btc_returns):
            correlated_component = eth_correlation * btc_ret
            random_component = (1 - eth_correlation) * np.random.normal(0, 0.025)
            eth_returns.append(correlated_component + random_component)
        
        eth_prices = [3000]  # 起始價格
        for ret in eth_returns[1:]:
            eth_prices.append(eth_prices[-1] * (1 + ret))
        
        # 創建DataFrame
        sample_data = pd.DataFrame({
            'base_price': btc_prices,
            'quote_price': eth_prices,
            'base_volume': np.random.uniform(1000, 5000, len(dates)),
            'quote_volume': np.random.uniform(5000, 20000, len(dates))
        }, index=dates)
        
        # 保存數據（使用 pathlib 確保跨平台兼容）
        data_dir = Path("data")
        data_dir.mkdir(exist_ok=True)
        sample_data.to_csv(data_dir / "sample_data.csv")
        
        logger.info("示例數據已生成: data/sample_data.csv")
        
    except Exception as e:
        logger.error(f"生成示例數據失敗: {e}")


def main():
    """主函數"""
    parser = argparse.ArgumentParser(description="進階配對交易機器人")
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 實時交易命令
    live_parser = subparsers.add_parser('live', help='運行實時交易')
    live_parser.add_argument('--timeframe', '-t', default='1m',
                            choices=['1m', '5m', '15m', '1h'],
                            help='時間框架 (默認: 1m)')

    # 投資組合交易命令
    portfolio_parser = subparsers.add_parser('portfolio', help='運行投資組合交易')
    portfolio_parser.add_argument('--timeframe', '-t', default='5m',
                                choices=['1m', '5m', '15m', '1h'],
                                help='交易時間框架 (默認: 5m)')
    portfolio_parser.add_argument('--no-dashboard', action='store_true',
                                help='禁用Web儀表板')
    portfolio_parser.add_argument('--no-telegram', action='store_true',
                                help='禁用Telegram控制')
    
    # 回測命令
    backtest_parser = subparsers.add_parser('backtest', help='運行回測')
    backtest_parser.add_argument('--data', '-d', required=True,
                                help='歷史數據文件路徑')
    
    # 配對篩選命令
    subparsers.add_parser('select', help='運行配對篩選')
    
    # 生成示例數據命令
    subparsers.add_parser('generate-data', help='生成示例數據')
    
    # 狀態檢查命令
    subparsers.add_parser('status', help='檢查機器人狀態')

    # 測試命令
    subparsers.add_parser('test', help='運行單元測試')

    # 改進版回測命令
    improved_backtest_parser = subparsers.add_parser('improved-backtest', help='運行改進版回測')
    improved_backtest_parser.add_argument('--data', '-d',
                                        default='data/demo_data.csv',
                                        help='歷史數據文件路徑')

    # 系統兼容性檢查命令
    subparsers.add_parser('check-system', help='檢查系統兼容性')

    # 配置管理命令
    config_parser = subparsers.add_parser('config', help='配置管理工具')
    config_parser.add_argument('--create-env', action='store_true', help='創建 .env 模板文件')
    config_parser.add_argument('--validate', action='store_true', help='驗證配置文件')
    
    args = parser.parse_args()
    
    if args.command == 'live':
        run_live_trading(args.timeframe)
    elif args.command == 'portfolio':
        run_portfolio_trading(
            timeframe=args.timeframe,
            enable_health_server=not args.no_dashboard,
            enable_telegram=not args.no_telegram
        )
    elif args.command == 'backtest':
        run_backtest(args.data)
    elif args.command == 'select':
        run_pair_selection()  # 修復不完整調用
    elif args.command == 'generate-data':
        generate_sample_data()  # 修復不完整調用
    elif args.command == 'status':
        try:
            bot = PairTradingBot()
            status = bot.get_status_report()
            print(f"機器人狀態: {status}")
        except Exception as e:
            logger.error(f"獲取狀態失敗: {e}")
    elif args.command == 'test':
        run_tests()  # 修復不完整調用
    elif args.command == 'improved-backtest':
        run_improved_backtest(args.data)
    elif args.command == 'check-system':
        check_system_compatibility()  # 修復不完整調用
    elif args.command == 'config':
        try:
            config_manager = ConfigManager()
            if args.create_env:
                config_manager.create_env_template()
                print("環境變量模板已創建: .env.example")
            elif args.validate:
                config = config_manager.load_config()
                api_valid = config_manager.validate_api_config()
                print(f"配置驗證結果: {'通過' if api_valid else '失敗'}")
            else:
                print("請指定配置操作: --create-env 或 --validate")
        except Exception as e:
            logger.error(f"配置管理失敗: {e}")
    else:
        parser.print_help()


if __name__ == "__main__":
    try:
        main()  # 修復不完整調用
    except KeyboardInterrupt:
        logger.info("程序被用戶中斷")
    except Exception as e:
        logger.error(f"程序執行失敗: {e}")
        sys.exit(1)
