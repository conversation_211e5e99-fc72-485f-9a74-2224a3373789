#!/usr/bin/env python3
"""
自適應配對選擇系統 - 自動選擇和切換最佳交易配對
Adaptive Pair Selector - Automatically selects and switches to optimal trading pairs
"""

import json
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from pathlib import Path

from pair_selection import PairSelectionTool
from strategy_health_monitor import get_strategy_health_monitor
from dynamic_config import get_dynamic_config_manager
from alert_manager import get_alert_manager, AlertLevel
from logging_config import get_logger

logger = get_logger(__name__)


class AdaptivePairSelector:
    """自適應配對選擇器"""
    
    def __init__(self, config_path: str = "config.json"):
        self.config_manager = get_dynamic_config_manager(config_path)
        self.health_monitor = get_strategy_health_monitor()
        self.alert_manager = get_alert_manager()
        
        # 配對數據文件
        self.potential_pairs_file = Path("pair_selection_results/potential_pairs.json")
        self.pair_performance_file = Path("records/pair_performance.json")
        
        # 配對池
        self.potential_pairs = []
        self.pair_performance_history = {}
        
        # 載入配對數據
        self._load_potential_pairs()
        self._load_pair_performance()
        
        logger.info("AdaptivePairSelector 初始化完成")
    
    def _load_potential_pairs(self):
        """載入潛在配對列表"""
        try:
            if self.potential_pairs_file.exists():
                with open(self.potential_pairs_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.potential_pairs = data.get('pairs', [])
                    
                logger.info(f"載入潛在配對: {len(self.potential_pairs)} 個")
            else:
                logger.warning("潛在配對文件不存在，將使用默認配對")
                self._generate_default_pairs()
                
        except Exception as e:
            logger.error(f"載入潛在配對失敗: {e}")
            self._generate_default_pairs()
    
    def _generate_default_pairs(self):
        """生成默認配對列表"""
        default_pairs = [
            {
                'symbol1': 'BTCUSDT',
                'symbol2': 'ETHUSDT',
                'correlation': 0.85,
                'cointegration_p_value': 0.01,
                'score': 85.0,
                'meets_criteria': True
            },
            {
                'symbol1': 'ETHUSDT',
                'symbol2': 'BNBUSDT',
                'correlation': 0.75,
                'cointegration_p_value': 0.03,
                'score': 75.0,
                'meets_criteria': True
            }
        ]
        
        self.potential_pairs = default_pairs
        self._save_potential_pairs()
    
    def _save_potential_pairs(self):
        """保存潛在配對列表"""
        try:
            self.potential_pairs_file.parent.mkdir(parents=True, exist_ok=True)
            
            data = {
                'pairs': self.potential_pairs,
                'last_updated': datetime.now().isoformat(),
                'total_pairs': len(self.potential_pairs)
            }
            
            with open(self.potential_pairs_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"保存潛在配對失敗: {e}")
    
    def _load_pair_performance(self):
        """載入配對表現歷史"""
        try:
            if self.pair_performance_file.exists():
                with open(self.pair_performance_file, 'r', encoding='utf-8') as f:
                    self.pair_performance_history = json.load(f)
                    
                logger.info(f"載入配對表現歷史: {len(self.pair_performance_history)} 個配對")
                
        except Exception as e:
            logger.error(f"載入配對表現歷史失敗: {e}")
            self.pair_performance_history = {}
    
    def _save_pair_performance(self):
        """保存配對表現歷史"""
        try:
            self.pair_performance_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.pair_performance_file, 'w', encoding='utf-8') as f:
                json.dump(self.pair_performance_history, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"保存配對表現歷史失敗: {e}")
    
    def update_potential_pairs(self, force_update: bool = False) -> bool:
        """更新潛在配對列表"""
        try:
            # 檢查是否需要更新
            if not force_update and self.potential_pairs_file.exists():
                file_age = datetime.now() - datetime.fromtimestamp(
                    self.potential_pairs_file.stat().st_mtime
                )
                if file_age < timedelta(days=1):  # 24小時內不重複更新
                    logger.info("潛在配對列表仍然新鮮，跳過更新")
                    return True
            
            logger.info("開始更新潛在配對列表...")
            
            # 創建配對選擇工具
            pair_selector = PairSelectionTool()
            
            # 執行配對分析
            results = pair_selector.run_analysis()
            
            if not results:
                logger.error("配對分析失敗")
                return False
            
            # 獲取符合條件的配對
            qualified_pairs = pair_selector.get_top_pairs(20, only_qualified=True)
            
            if not qualified_pairs:
                logger.warning("未找到符合條件的配對")
                return False
            
            # 更新潛在配對列表
            self.potential_pairs = qualified_pairs
            self._save_potential_pairs()
            
            # 發送更新通知
            self.alert_manager.send_alert(
                AlertLevel.INFO,
                "配對列表更新",
                f"潛在配對列表已更新，共找到 {len(qualified_pairs)} 個優質配對",
                {
                    "更新時間": datetime.now().isoformat(),
                    "配對數量": len(qualified_pairs),
                    "前3名配對": [f"{p['symbol1']}-{p['symbol2']}" for p in qualified_pairs[:3]]
                }
            )
            
            logger.info(f"潛在配對列表更新完成: {len(qualified_pairs)} 個配對")
            return True
            
        except Exception as e:
            logger.error(f"更新潛在配對列表失敗: {e}")
            return False
    
    def record_pair_performance(self, pair: List[str], performance_data: Dict):
        """記錄配對表現"""
        try:
            if not pair or len(pair) != 2:
                return
            
            pair_key = f"{pair[0]}-{pair[1]}"
            
            if pair_key not in self.pair_performance_history:
                self.pair_performance_history[pair_key] = {
                    'trades': [],
                    'total_pnl': 0.0,
                    'total_trades': 0,
                    'win_rate': 0.0,
                    'avg_pnl': 0.0,
                    'sharpe_ratio': 0.0,
                    'last_used': None
                }
            
            pair_perf = self.pair_performance_history[pair_key]
            
            # 更新交易記錄
            trade_record = {
                'timestamp': datetime.now().isoformat(),
                'pnl': performance_data.get('pnl', 0),
                'trade_type': performance_data.get('trade_type', 'unknown'),
                'exit_reason': performance_data.get('exit_reason', 'unknown')
            }
            
            pair_perf['trades'].append(trade_record)
            pair_perf['last_used'] = datetime.now().isoformat()
            
            # 保持最近50筆交易
            if len(pair_perf['trades']) > 50:
                pair_perf['trades'] = pair_perf['trades'][-50:]
            
            # 重新計算統計數據
            self._recalculate_pair_stats(pair_key)
            
            # 保存數據
            self._save_pair_performance()
            
            logger.info(f"記錄配對表現: {pair_key}, PnL={performance_data.get('pnl', 0):.2f}")
            
        except Exception as e:
            logger.error(f"記錄配對表現失敗: {e}")
    
    def _recalculate_pair_stats(self, pair_key: str):
        """重新計算配對統計數據"""
        try:
            pair_perf = self.pair_performance_history[pair_key]
            trades = pair_perf['trades']
            
            if not trades:
                return
            
            # 計算基本統計
            pnls = [trade['pnl'] for trade in trades]
            pair_perf['total_pnl'] = sum(pnls)
            pair_perf['total_trades'] = len(trades)
            pair_perf['avg_pnl'] = np.mean(pnls)
            
            # 計算勝率
            winning_trades = [pnl for pnl in pnls if pnl > 0]
            pair_perf['win_rate'] = len(winning_trades) / len(pnls) if pnls else 0
            
            # 計算夏普比率
            if len(pnls) > 1:
                std_pnl = np.std(pnls)
                if std_pnl > 0:
                    pair_perf['sharpe_ratio'] = (np.mean(pnls) / std_pnl) * np.sqrt(252)
                else:
                    pair_perf['sharpe_ratio'] = 0
            else:
                pair_perf['sharpe_ratio'] = 0
                
        except Exception as e:
            logger.error(f"重新計算配對統計失敗: {e}")
    
    def get_best_available_pair(self, exclude_degraded: bool = True) -> Optional[List[str]]:
        """獲取最佳可用配對"""
        try:
            if not self.potential_pairs:
                logger.warning("沒有可用的潛在配對")
                return None
            
            # 過濾配對
            available_pairs = []
            
            for pair_data in self.potential_pairs:
                pair = [pair_data['symbol1'], pair_data['symbol2']]
                
                # 排除降級的配對
                if exclude_degraded and self.health_monitor.is_pair_degraded(pair):
                    continue
                
                # 計算綜合評分
                score = self._calculate_pair_score(pair_data)
                
                available_pairs.append({
                    'pair': pair,
                    'score': score,
                    'original_score': pair_data.get('score', 0),
                    'correlation': pair_data.get('correlation', 0),
                    'cointegration_p_value': pair_data.get('cointegration_p_value', 1)
                })
            
            if not available_pairs:
                logger.warning("沒有可用的配對（所有配對都已降級）")
                return None
            
            # 按評分排序
            available_pairs.sort(key=lambda x: x['score'], reverse=True)
            
            best_pair = available_pairs[0]
            
            logger.info(f"選擇最佳配對: {best_pair['pair']}, 評分: {best_pair['score']:.2f}")
            
            return best_pair['pair']
            
        except Exception as e:
            logger.error(f"獲取最佳配對失敗: {e}")
            return None
    
    def _calculate_pair_score(self, pair_data: Dict) -> float:
        """計算配對綜合評分"""
        try:
            pair_key = f"{pair_data['symbol1']}-{pair_data['symbol2']}"
            
            # 基礎評分（來自配對分析）
            base_score = pair_data.get('score', 0)
            
            # 歷史表現評分
            performance_score = 0
            if pair_key in self.pair_performance_history:
                perf = self.pair_performance_history[pair_key]
                
                # 根據勝率、夏普比率和平均盈利調整評分
                win_rate_bonus = perf.get('win_rate', 0) * 20  # 最多+20分
                sharpe_bonus = min(perf.get('sharpe_ratio', 0) * 10, 15)  # 最多+15分
                avg_pnl_bonus = min(perf.get('avg_pnl', 0) / 10, 10)  # 最多+10分
                
                performance_score = win_rate_bonus + sharpe_bonus + avg_pnl_bonus
            
            # 時間衰減因子（最近使用的配對得分略低）
            time_penalty = 0
            if pair_key in self.pair_performance_history:
                last_used = self.pair_performance_history[pair_key].get('last_used')
                if last_used:
                    last_used_dt = datetime.fromisoformat(last_used)
                    hours_since_used = (datetime.now() - last_used_dt).total_seconds() / 3600
                    if hours_since_used < 24:  # 24小時內使用過
                        time_penalty = (24 - hours_since_used) / 24 * 10  # 最多-10分
            
            final_score = base_score + performance_score - time_penalty
            
            return max(final_score, 0)  # 確保評分不為負
            
        except Exception as e:
            logger.error(f"計算配對評分失敗: {e}")
            return pair_data.get('score', 0)
    
    def switch_to_best_pair(self) -> bool:
        """切換到最佳配對"""
        try:
            current_pair = self.config_manager.get_value('trading_pair')
            best_pair = self.get_best_available_pair()
            
            if not best_pair:
                logger.error("無法找到可用的配對進行切換")
                return False
            
            # 檢查是否需要切換
            if current_pair == best_pair:
                logger.info("當前配對已是最佳配對，無需切換")
                return True
            
            logger.info(f"準備切換配對: {current_pair} -> {best_pair}")
            
            # 更新配置
            success = self.config_manager.set_value('trading_pair', best_pair)
            
            if success:
                # 發送切換通知
                self.alert_manager.send_alert(
                    AlertLevel.WARNING,
                    "自動配對切換",
                    "系統已自動切換到新的交易配對",
                    {
                        "原配對": f"{current_pair[0]}-{current_pair[1]}" if current_pair else "無",
                        "新配對": f"{best_pair[0]}-{best_pair[1]}",
                        "切換原因": "策略自適應優化",
                        "切換時間": datetime.now().isoformat()
                    }
                )
                
                logger.info(f"配對切換成功: {best_pair}")
                return True
            else:
                logger.error("配對切換失敗：配置更新失敗")
                return False
                
        except Exception as e:
            logger.error(f"切換配對失敗: {e}")
            return False
    
    def get_pair_statistics(self) -> Dict:
        """獲取配對統計信息"""
        try:
            stats = {
                'total_potential_pairs': len(self.potential_pairs),
                'pairs_with_history': len(self.pair_performance_history),
                'degraded_pairs': len(self.health_monitor.health_status.get('degraded_pairs', set())),
                'top_performers': []
            }
            
            # 獲取表現最好的配對
            if self.pair_performance_history:
                sorted_pairs = sorted(
                    self.pair_performance_history.items(),
                    key=lambda x: x[1].get('sharpe_ratio', 0),
                    reverse=True
                )
                
                stats['top_performers'] = [
                    {
                        'pair': pair_key,
                        'sharpe_ratio': data.get('sharpe_ratio', 0),
                        'win_rate': data.get('win_rate', 0),
                        'total_trades': data.get('total_trades', 0),
                        'total_pnl': data.get('total_pnl', 0)
                    }
                    for pair_key, data in sorted_pairs[:5]
                ]
            
            return stats
            
        except Exception as e:
            logger.error(f"獲取配對統計失敗: {e}")
            return {}


# 全局自適應配對選擇器實例
_adaptive_pair_selector = None

def get_adaptive_pair_selector() -> AdaptivePairSelector:
    """獲取全局自適應配對選擇器實例"""
    global _adaptive_pair_selector
    if _adaptive_pair_selector is None:
        _adaptive_pair_selector = AdaptivePairSelector()
    return _adaptive_pair_selector


if __name__ == "__main__":
    # 測試自適應配對選擇器
    print("測試自適應配對選擇器...")
    
    selector = AdaptivePairSelector()
    
    # 測試獲取最佳配對
    best_pair = selector.get_best_available_pair()
    print(f"最佳配對: {best_pair}")
    
    # 測試記錄表現
    if best_pair:
        selector.record_pair_performance(best_pair, {
            'pnl': 25.5,
            'trade_type': 'test',
            'exit_reason': 'take_profit'
        })
    
    # 獲取統計信息
    stats = selector.get_pair_statistics()
    print(f"配對統計: {stats}")
    
    print("自適應配對選擇器測試完成！")
