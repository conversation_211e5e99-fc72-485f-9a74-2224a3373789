#!/usr/bin/env python3
"""
重構系統集成測試 - 測試所有新組件的集成
Refactored System Integration Test - Test integration of all new components
"""

import sys
from typing import Dict, Any

sys.path.append('.')

from config_loader import ConfigLoader
from exchange_factory import ExchangeFactory
from safe_trading_executor import SafeTradingExecutor
from smart_capital_management import SmartCapitalManager, RiskLevel
from math_utils import PairsTradingMath
from trading_exceptions import *
from logging_config import setup_logging, get_logger

setup_logging()
logger = get_logger(__name__)


class RefactoredSystemTester:
    """重構系統測試器"""
    
    def __init__(self):
        self.test_results = {
            'config_loading': False,
            'exchange_factory': False,
            'safe_executor': False,
            'capital_management': False,
            'math_utils': False,
            'exception_handling': False,
            'integration': False
        }
    
    def test_config_loading(self) -> bool:
        """測試配置載入"""
        print("\n🔧 測試配置載入系統...")
        
        try:
            # 測試環境變量載入
            env_vars = ConfigLoader.load_env_file(".env")
            print(f"  ✅ 環境變量載入: {len(env_vars)} 個變量")
            
            # 測試JSON配置載入
            json_config = ConfigLoader.load_json_config("config.json")
            print(f"  ✅ JSON配置載入成功")
            
            # 測試完整配置載入
            complete_config = ConfigLoader.load_complete_config()
            print(f"  ✅ 完整配置載入成功")
            
            # 測試配置驗證
            validation = ConfigLoader.validate_required_config(complete_config)
            print(f"  ✅ 配置驗證: {'通過' if validation['valid'] else '失敗'}")
            
            self.test_results['config_loading'] = True
            return True
            
        except Exception as e:
            print(f"  ❌ 配置載入測試失敗: {e}")
            return False
    
    def test_exchange_factory(self) -> bool:
        """測試交易所工廠"""
        print("\n🏭 測試交易所工廠...")
        
        try:
            # 測試模擬交易所
            mock_gateway = ExchangeFactory.create_exchange_gateway({}, mock_mode=True)
            print(f"  ✅ 模擬交易所創建成功")
            
            # 測試模擬訂單
            order = mock_gateway.create_market_order('BTC/USDT:USDT', 'buy', 0.001)
            print(f"  ✅ 模擬訂單執行: {order['id']}")
            
            # 測試配置驗證
            test_config = {
                'exchange': {'name': 'gate'},
                'api_key': 'test_key',
                'secret': 'test_secret'
            }
            validation = ExchangeFactory.validate_exchange_config(test_config)
            print(f"  ✅ 交易所配置驗證: {'通過' if validation['valid'] else '失敗'}")
            
            self.test_results['exchange_factory'] = True
            return True
            
        except Exception as e:
            print(f"  ❌ 交易所工廠測試失敗: {e}")
            return False
    
    def test_safe_executor(self) -> bool:
        """測試安全交易執行器"""
        print("\n🛡️ 測試安全交易執行器...")
        
        try:
            # 創建模擬交易所
            mock_gateway = ExchangeFactory.create_exchange_gateway({}, mock_mode=True)
            
            # 創建安全執行器
            executor = SafeTradingExecutor(mock_gateway, 1000)
            print(f"  ✅ 安全執行器創建成功")
            
            # 測試單個訂單
            order_result = executor.place_market_order_safe('BTC/USDT:USDT', 'buy', 0.001)
            print(f"  ✅ 安全訂單執行: {'成功' if order_result.success else '失敗'}")
            
            # 測試配對交易（模擬成功場景）
            pair_result = executor.execute_pair_trade_atomic(
                'BTC/USDT:USDT', 'ETH/USDT:USDT',
                'buy', 'sell',
                0.001, 0.1
            )
            print(f"  ✅ 配對交易執行: {'成功' if pair_result['success'] else '失敗'}")
            
            self.test_results['safe_executor'] = True
            return True
            
        except Exception as e:
            print(f"  ❌ 安全執行器測試失敗: {e}")
            return False
    
    def test_capital_management(self) -> bool:
        """測試資金管理"""
        print("\n💰 測試智能資金管理...")
        
        try:
            # 創建資金管理器
            manager = SmartCapitalManager(10000, RiskLevel.MODERATE, 50)
            print(f"  ✅ 資金管理器創建成功")
            
            # 測試風險指標
            metrics = manager.get_risk_metrics()
            print(f"  ✅ 風險指標: 有效槓桿 {metrics['effective_leverage_target']}x")
            
            # 測試倉位計算
            position = manager.calculate_position_size('BTC/USDT:USDT', 50000, 0.8)
            print(f"  ✅ 倉位計算: ${position['position_value']:,.0f}, 風險 {position['risk_percentage']:.2f}%")
            
            # 測試配對交易計算
            pair_result = manager.calculate_pair_position(
                'BTC/USDT:USDT', 50000,
                'ETH/USDT:USDT', 3000,
                0.8
            )
            print(f"  ✅ 配對交易計算: 總風險 {pair_result['total_risk_percentage']:.2f}%")
            
            self.test_results['capital_management'] = True
            return True
            
        except Exception as e:
            print(f"  ❌ 資金管理測試失敗: {e}")
            return False
    
    def test_math_utils(self) -> bool:
        """測試數學工具"""
        print("\n📊 測試數學工具...")
        
        try:
            import numpy as np
            
            # 生成測試數據
            np.random.seed(42)
            prices1 = 50000 + np.cumsum(np.random.randn(100) * 100)
            prices2 = 3000 + np.cumsum(np.random.randn(100) * 50)
            
            # 測試對數價差
            spread = PairsTradingMath.calculate_log_spread(prices1, prices2)
            print(f"  ✅ 對數價差計算: {len(spread)} 個數據點")
            
            # 測試Z-score
            zscore = PairsTradingMath.calculate_zscore(spread)
            latest_zscore = zscore[-1] if hasattr(zscore, '__getitem__') else zscore
            print(f"  ✅ Z-score計算: 最新值 {latest_zscore:.3f}")
            
            # 測試相關係數
            correlation = PairsTradingMath.calculate_correlation(prices1, prices2)
            print(f"  ✅ 相關係數: {correlation:.3f}")
            
            # 測試倉位計算
            position_size = PairsTradingMath.calculate_position_size(50000, 1000)
            print(f"  ✅ 倉位計算: {position_size:.6f} BTC")
            
            self.test_results['math_utils'] = True
            return True
            
        except Exception as e:
            print(f"  ❌ 數學工具測試失敗: {e}")
            return False
    
    def test_exception_handling(self) -> bool:
        """測試異常處理"""
        print("\n⚠️ 測試異常處理...")
        
        try:
            # 測試基礎異常
            try:
                raise ConfigError("測試配置錯誤", {'test': 'data'})
            except ConfigError as e:
                print(f"  ✅ ConfigError捕獲: {e.message}")
            
            # 測試執行異常
            try:
                raise ExecutionError("測試執行錯誤")
            except ExecutionError as e:
                print(f"  ✅ ExecutionError捕獲: {e.message}")
            
            # 測試部分成交異常
            try:
                raise PartialFillError(
                    "測試部分成交錯誤",
                    {'id': 'test_order'},
                    {'symbol': 'BTC/USDT:USDT'}
                )
            except PartialFillError as e:
                print(f"  ✅ PartialFillError捕獲: {e.requires_compensation}")
            
            self.test_results['exception_handling'] = True
            return True
            
        except Exception as e:
            print(f"  ❌ 異常處理測試失敗: {e}")
            return False
    
    def test_integration(self) -> bool:
        """測試系統集成"""
        print("\n🔗 測試系統集成...")
        
        try:
            # 載入配置
            config = ConfigLoader.load_complete_config()
            
            # 創建交易所網關
            exchange_gateway = ExchangeFactory.create_exchange_gateway(config, mock_mode=True)
            
            # 創建安全執行器
            executor = SafeTradingExecutor(exchange_gateway, 1000)
            
            # 創建資金管理器
            capital_manager = SmartCapitalManager(10000, RiskLevel.MODERATE, 50)
            
            # 模擬完整的交易流程
            print("  🎯 模擬完整交易流程...")
            
            # 1. 計算倉位大小
            btc_position = capital_manager.calculate_position_size('BTC/USDT:USDT', 50000, 0.8)
            eth_position = capital_manager.calculate_position_size('ETH/USDT:USDT', 3000, 0.8)
            
            print(f"    📊 BTC倉位: ${btc_position['position_value']:,.0f}")
            print(f"    📊 ETH倉位: ${eth_position['position_value']:,.0f}")
            
            # 2. 執行配對交易
            pair_result = executor.execute_pair_trade_atomic(
                'BTC/USDT:USDT', 'ETH/USDT:USDT',
                'buy', 'sell',
                btc_position['quantity'], eth_position['quantity']
            )
            
            print(f"    ✅ 配對交易: {'成功' if pair_result['success'] else '失敗'}")
            
            # 3. 計算數學指標
            spread = PairsTradingMath.calculate_log_spread(50000, 3000)
            print(f"    📈 當前價差: {spread:.6f}")
            
            self.test_results['integration'] = True
            return True
            
        except Exception as e:
            print(f"  ❌ 系統集成測試失敗: {e}")
            return False
    
    def run_all_tests(self) -> Dict[str, bool]:
        """運行所有測試"""
        print("🧪 重構系統集成測試")
        print("=" * 60)
        
        # 運行各項測試
        tests = [
            ('配置載入', self.test_config_loading),
            ('交易所工廠', self.test_exchange_factory),
            ('安全執行器', self.test_safe_executor),
            ('資金管理', self.test_capital_management),
            ('數學工具', self.test_math_utils),
            ('異常處理', self.test_exception_handling),
            ('系統集成', self.test_integration)
        ]
        
        for test_name, test_func in tests:
            try:
                success = test_func()
                status = "✅ 通過" if success else "❌ 失敗"
                print(f"\n{test_name}: {status}")
            except Exception as e:
                print(f"\n{test_name}: ❌ 異常 - {e}")
                self.test_results[test_name.lower().replace(' ', '_')] = False
        
        # 總結
        print(f"\n" + "=" * 60)
        print("📊 測試結果總結:")
        
        passed = sum(self.test_results.values())
        total = len(self.test_results)
        
        for test_name, result in self.test_results.items():
            status = "✅" if result else "❌"
            print(f"  {status} {test_name.replace('_', ' ').title()}")
        
        print(f"\n總體結果: {passed}/{total} 測試通過 ({passed/total*100:.1f}%)")
        
        if passed == total:
            print("🎉 所有測試通過！重構系統運行正常！")
        else:
            print("⚠️ 部分測試失敗，需要進一步檢查")
        
        return self.test_results


def main():
    """主函數"""
    tester = RefactoredSystemTester()
    results = tester.run_all_tests()
    
    # 返回適當的退出碼
    all_passed = all(results.values())
    return 0 if all_passed else 1


if __name__ == "__main__":
    sys.exit(main())
