import pandas as pd
import numpy as np
from typing import Dict, List, Tuple
from datetime import datetime
from itertools import combinations
import json

from scipy.stats import pearsonr
from statsmodels.tsa.stattools import coint
from statsmodels.tsa.stattools import adfuller

from utils import load_config, get_exchange_instance
from logging_config import get_logger

logger = get_logger(__name__)


class PairSelectionTool:
    """配對篩選工具"""
    
    def __init__(self, config_path: str = "config.json"):
        self.config = load_config(config_path)
        self.pair_config = self.config.get('pair_selection', {})
        
        # 篩選參數
        self.symbols = self.pair_config.get('symbols', [])
        self.lookback_days = self.pair_config.get('lookback_days', 90)
        self.min_correlation = self.pair_config.get('min_correlation', 0.7)
        self.max_cointegration_pvalue = self.pair_config.get('max_cointegration_pvalue', 0.05)
        
        # 交易所實例
        self.exchange = get_exchange_instance(self.config)
        
        # 數據存儲
        self.price_data = {}
        self.analysis_results = []
        
        logger.info(f"PairSelectionTool 初始化完成，將分析 {len(self.symbols)} 個資產")
    
    def fetch_historical_data(self, symbol: str, days: int = None) -> pd.DataFrame:
        """獲取歷史數據"""
        try:
            if days is None:
                days = self.lookback_days
            
            # 計算需要的數據點數（假設1小時K線）
            limit = days * 24
            
            ohlcv = self.exchange.fetch_ohlcv(symbol, '1h', limit=limit)
            
            df = pd.DataFrame(
                ohlcv,
                columns=['timestamp', 'open', 'high', 'low', 'close', 'volume']
            )
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            
            logger.debug(f"獲取 {symbol} 歷史數據成功: {len(df)} 條記錄")
            return df
            
        except Exception as e:
            logger.error(f"獲取 {symbol} 歷史數據失敗: {e}")
            return pd.DataFrame()
    
    def download_all_data(self) -> bool:
        """下載所有資產的歷史數據"""
        try:
            logger.info("開始下載歷史數據...")
            
            for symbol in self.symbols:
                logger.info(f"下載 {symbol} 數據...")
                data = self.fetch_historical_data(symbol)
                
                if not data.empty:
                    self.price_data[symbol] = data
                else:
                    logger.warning(f"{symbol} 數據下載失敗，將跳過此資產")
            
            logger.info(f"數據下載完成，成功獲取 {len(self.price_data)} 個資產的數據")
            return len(self.price_data) >= 2
            
        except Exception as e:
            logger.error(f"下載歷史數據失敗: {e}")
            return False
    
    def calculate_correlation(self, symbol1: str, symbol2: str) -> Tuple[float, float]:
        """計算皮爾遜相關係數"""
        try:
            if symbol1 not in self.price_data or symbol2 not in self.price_data:
                return np.nan, np.nan
            
            data1 = self.price_data[symbol1]['close']
            data2 = self.price_data[symbol2]['close']
            
            # 確保數據對齊
            common_index = data1.index.intersection(data2.index)
            if len(common_index) < 30:  # 至少需要30個數據點
                return np.nan, np.nan
            
            aligned_data1 = data1.loc[common_index]
            aligned_data2 = data2.loc[common_index]
            
            correlation, p_value = pearsonr(aligned_data1, aligned_data2)
            
            return correlation, p_value
            
        except Exception as e:
            logger.error(f"計算 {symbol1}-{symbol2} 相關性失敗: {e}")
            return np.nan, np.nan
    
    def test_cointegration(self, symbol1: str, symbol2: str) -> Tuple[float, float, bool]:
        """測試共整合關係"""
        try:
            if symbol1 not in self.price_data or symbol2 not in self.price_data:
                return np.nan, np.nan, False
            
            data1 = self.price_data[symbol1]['close']
            data2 = self.price_data[symbol2]['close']
            
            # 確保數據對齊
            common_index = data1.index.intersection(data2.index)
            if len(common_index) < 50:  # 共整合檢定需要更多數據點
                return np.nan, np.nan, False
            
            aligned_data1 = data1.loc[common_index]
            aligned_data2 = data2.loc[common_index]
            
            # 使用對數價格進行共整合檢定
            log_data1 = np.log(aligned_data1)
            log_data2 = np.log(aligned_data2)
            
            # Engle-Granger 共整合檢定
            coint_stat, p_value, critical_values = coint(log_data1, log_data2)
            
            # 檢查是否通過共整合檢定
            is_cointegrated = p_value < self.max_cointegration_pvalue
            
            return coint_stat, p_value, is_cointegrated
            
        except Exception as e:
            logger.error(f"測試 {symbol1}-{symbol2} 共整合失敗: {e}")
            return np.nan, np.nan, False
    
    def test_stationarity(self, symbol1: str, symbol2: str) -> Tuple[float, bool]:
        """測試價差的平穩性（ADF檢定）"""
        try:
            if symbol1 not in self.price_data or symbol2 not in self.price_data:
                return np.nan, False
            
            data1 = self.price_data[symbol1]['close']
            data2 = self.price_data[symbol2]['close']
            
            # 確保數據對齊
            common_index = data1.index.intersection(data2.index)
            if len(common_index) < 50:
                return np.nan, False
            
            aligned_data1 = data1.loc[common_index]
            aligned_data2 = data2.loc[common_index]
            
            # 計算對數價差
            log_spread = np.log(aligned_data1) - np.log(aligned_data2)
            
            # ADF檢定
            adf_stat, p_value, used_lag, nobs, critical_values, icbest = adfuller(
                log_spread.dropna(), autolag='AIC'
            )
            
            # 檢查是否平穩（p值小於0.05表示拒絕單位根假設，即序列平穩）
            is_stationary = p_value < 0.05
            
            return p_value, is_stationary
            
        except Exception as e:
            logger.error(f"測試 {symbol1}-{symbol2} 價差平穩性失敗: {e}")
            return np.nan, False
    
    def analyze_pair(self, symbol1: str, symbol2: str) -> Dict:
        """分析單個配對"""
        try:
            # 計算相關性
            correlation, corr_p_value = self.calculate_correlation(symbol1, symbol2)
            
            # 測試共整合
            coint_stat, coint_p_value, is_cointegrated = self.test_cointegration(symbol1, symbol2)
            
            # 測試價差平穩性
            adf_p_value, is_stationary = self.test_stationarity(symbol1, symbol2)
            
            # 計算評分
            score = self._calculate_pair_score(
                correlation, coint_p_value, adf_p_value, is_cointegrated, is_stationary
            )
            
            result = {
                'symbol1': symbol1,
                'symbol2': symbol2,
                'correlation': correlation,
                'correlation_p_value': corr_p_value,
                'cointegration_stat': coint_stat,
                'cointegration_p_value': coint_p_value,
                'is_cointegrated': is_cointegrated,
                'adf_p_value': adf_p_value,
                'is_stationary': is_stationary,
                'score': score,
                'meets_criteria': self._meets_selection_criteria(
                    correlation, coint_p_value, is_cointegrated, is_stationary
                )
            }
            
            return result
            
        except Exception as e:
            logger.error(f"分析配對 {symbol1}-{symbol2} 失敗: {e}")
            return {}
    
    def _calculate_pair_score(self, correlation: float, coint_p_value: float,
                             adf_p_value: float, is_cointegrated: bool,
                             is_stationary: bool) -> float:
        """計算配對評分"""
        try:
            score = 0.0
            
            # 相關性評分（0-30分）
            if not np.isnan(correlation):
                score += abs(correlation) * 30
            
            # 共整合評分（0-40分）
            if is_cointegrated and not np.isnan(coint_p_value):
                score += (1 - coint_p_value) * 40
            
            # 平穩性評分（0-30分）
            if is_stationary and not np.isnan(adf_p_value):
                score += (1 - adf_p_value) * 30
            
            return min(score, 100.0)  # 最高100分
            
        except Exception as e:
            logger.error(f"計算配對評分失敗: {e}")
            return 0.0
    
    def _meets_selection_criteria(self, correlation: float, coint_p_value: float,
                                 is_cointegrated: bool, is_stationary: bool) -> bool:
        """檢查是否符合篩選標準"""
        try:
            # 相關性標準
            if np.isnan(correlation) or abs(correlation) < self.min_correlation:
                return False
            
            # 共整合標準
            if not is_cointegrated:
                return False
            
            # 平穩性標準（可選）
            # if not is_stationary:
            #     return False
            
            return True
            
        except Exception as e:
            logger.error(f"檢查篩選標準失敗: {e}")
            return False
    
    def run_analysis(self) -> List[Dict]:
        """執行完整的配對分析"""
        try:
            logger.info("開始配對分析...")
            
            # 下載數據
            if not self.download_all_data():
                logger.error("數據下載失敗，無法進行分析")
                return []
            
            # 生成所有可能的配對組合
            available_symbols = list(self.price_data.keys())
            pairs = list(combinations(available_symbols, 2))
            
            logger.info(f"將分析 {len(pairs)} 個配對組合")
            
            # 分析每個配對
            self.analysis_results = []
            for i, (symbol1, symbol2) in enumerate(pairs):
                logger.info(f"分析配對 {i+1}/{len(pairs)}: {symbol1} - {symbol2}")
                
                result = self.analyze_pair(symbol1, symbol2)
                if result:
                    self.analysis_results.append(result)
            
            # 按評分排序
            self.analysis_results.sort(key=lambda x: x.get('score', 0), reverse=True)
            
            logger.info(f"配對分析完成，共分析 {len(self.analysis_results)} 個配對")
            return self.analysis_results
            
        except Exception as e:
            logger.error(f"配對分析失敗: {e}")
            return []
    
    def get_top_pairs(self, n: int = 10, only_qualified: bool = True) -> List[Dict]:
        """獲取排名前N的配對"""
        try:
            if not self.analysis_results:
                logger.warning("請先執行配對分析")
                return []
            
            results = self.analysis_results.copy()
            
            if only_qualified:
                results = [r for r in results if r.get('meets_criteria', False)]
            
            return results[:n]
            
        except Exception as e:
            logger.error(f"獲取頂級配對失敗: {e}")
            return []
    
    def generate_report(self, top_n: int = 20) -> str:
        """生成配對分析報告"""
        try:
            if not self.analysis_results:
                return "請先執行配對分析"
            
            top_pairs = self.get_top_pairs(top_n, only_qualified=False)
            qualified_pairs = [p for p in top_pairs if p.get('meets_criteria', False)]
            
            report = f"""
=== 配對篩選分析報告 ===
生成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

篩選參數:
- 分析資產數量: {len(self.symbols)}
- 回看天數: {self.lookback_days}
- 最小相關性: {self.min_correlation}
- 最大共整合P值: {self.max_cointegration_pvalue}

分析結果:
- 總配對數量: {len(self.analysis_results)}
- 符合標準的配對: {len(qualified_pairs)}
- 符合率: {len(qualified_pairs)/len(self.analysis_results)*100:.1f}%

排名前 {len(top_pairs)} 的配對:
"""
            
            for i, pair in enumerate(top_pairs, 1):
                status = "✓" if pair.get('meets_criteria', False) else "✗"
                report += f"""
{i:2d}. {status} {pair['symbol1']} - {pair['symbol2']}
    相關性: {pair['correlation']:.4f}
    共整合P值: {pair['cointegration_p_value']:.4f}
    ADF P值: {pair['adf_p_value']:.4f}
    評分: {pair['score']:.1f}/100
"""
            
            return report
            
        except Exception as e:
            logger.error(f"生成配對分析報告失敗: {e}")
            return ""
    
    def save_results(self, results_dir: str = "pair_selection_results"):
        """保存分析結果"""
        try:
            from pathlib import Path

            results_path = Path(results_dir)
            results_path.mkdir(exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 保存詳細結果
            results_file = results_path / f"pair_analysis_{timestamp}.json"
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(self.analysis_results, f, indent=2, default=str, ensure_ascii=False)

            # 保存報告
            report_file = results_path / f"pair_report_{timestamp}.txt"
            report = self.generate_report()
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)

            # 保存符合標準的配對（用於配置文件）
            qualified_pairs = self.get_top_pairs(10, only_qualified=True)
            if qualified_pairs:
                config_file = results_path / f"recommended_pairs_{timestamp}.json"
                recommended = []
                for pair in qualified_pairs:
                    recommended.append([pair['symbol1'], pair['symbol2']])

                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump({
                        "recommended_pairs": recommended,
                        "analysis_date": datetime.now().isoformat(),
                        "selection_criteria": {
                            "min_correlation": self.min_correlation,
                            "max_cointegration_pvalue": self.max_cointegration_pvalue
                        }
                    }, f, indent=2, ensure_ascii=False)

            logger.info(f"配對分析結果已保存到: {results_path}")
            
        except Exception as e:
            logger.error(f"保存配對分析結果失敗: {e}")
    
    def plot_correlation_matrix(self, save_path: str = None):
        """繪製相關性矩陣熱圖"""
        try:
            import matplotlib.pyplot as plt
            import seaborn as sns
            
            if not self.price_data:
                logger.warning("無價格數據可繪製")
                return
            
            # 準備數據
            symbols = list(self.price_data.keys())
            correlation_matrix = np.zeros((len(symbols), len(symbols)))
            
            for i, symbol1 in enumerate(symbols):
                for j, symbol2 in enumerate(symbols):
                    if i == j:
                        correlation_matrix[i, j] = 1.0
                    else:
                        corr, _ = self.calculate_correlation(symbol1, symbol2)
                        correlation_matrix[i, j] = corr if not np.isnan(corr) else 0
            
            # 繪製熱圖
            plt.figure(figsize=(12, 10))
            sns.heatmap(
                correlation_matrix,
                xticklabels=symbols,
                yticklabels=symbols,
                annot=True,
                cmap='RdYlBu_r',
                center=0,
                fmt='.3f'
            )
            plt.title('資產相關性矩陣')
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                logger.info(f"相關性矩陣圖已保存: {save_path}")
            
            plt.show()
            
        except Exception as e:
            logger.error(f"繪製相關性矩陣失敗: {e}")
