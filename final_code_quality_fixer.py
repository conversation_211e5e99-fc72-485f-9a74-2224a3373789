#!/usr/bin/env python3
"""
最終代碼質量修復工具
Final Code Quality Fixer
"""

import re
from pathlib import Path
from typing import List


class FinalCodeQualityFixer:
    """最終代碼質量修復器"""

    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.fixes_applied = 0

    def find_python_files(self) -> List[Path]:
        """查找所有Python文件"""
        python_files = []

        # 掃描src目錄
        src_dir = self.project_root / "src"
        if src_dir.exists():
            python_files.extend(src_dir.glob("*.py"))

        # 掃描tests目錄
        tests_dir = self.project_root / "tests"
        if tests_dir.exists():
            python_files.extend(tests_dir.glob("*.py"))

        # 掃描根目錄的Python文件
        python_files.extend(self.project_root.glob("*.py"))

        return python_files

    def add_missing_imports(self, file_path: Path) -> bool:
        """添加缺失的導入"""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            _ = content
            _ = False

            # 檢查需要添加的導入
            imports_to_add = []

            # 檢查typing相關的導入
            if any([
                name in content
                for name in ["Dict", "List", "Optional", "Any", "Callable", "Union", "Tuple"]
            ]):
                if "from typing import" not in content and "import typing" not in content:
                    typing_names = []
                    for name in ["Dict", "List", "Optional", "Any", "Callable", "Union", "Tuple"]:
                        if (
                            f"{name}[" in content
                            or f"{name}," in content
                            or f"{name}]" in content
                            or f": {name}" in content
                        ):
                            typing_names.append(name)
                    if typing_names:
                        imports_to_add.append(
                            f"from typing import {', '.join(sorted(set(typing_names)))}"
                        )

            # 檢查dataclass
            if "@dataclass" in content and "from dataclasses import dataclass" not in content:
                imports_to_add.append("from dataclasses import dataclass")

            # 檢查datetime
            if ()
                "datetime." in content
                and "import datetime" not in content
                and "from datetime import" not in content
            ):
                imports_to_add.append("import datetime")

            # 檢查Mock和patch
            if (
                any(name in content for name in ["Mock", "patch"])
                and "unittest.mock" not in content
            ):
                mock_names = []
                if "Mock" in content:
                    mock_names.append("Mock")
                if "patch" in content:
                    mock_names.append("patch")
                if mock_names:
                    imports_to_add.append(
                        f"from unittest.mock import {', '.join(sorted(set(mock_names)))}"
                    )

            # 添加導入
            if imports_to_add:
                lines = new_content.split("\n")

                # 找到導入區域的結束位置
                import_end_line = 0
                for i, line in enumerate(lines):
                    if line.strip().startswith("import ") or line.strip().startswith("from "):
                        import_end_line = i + 1
                    elif (
                        line.strip()
                        and not line.strip().startswith("#")
                        and not line.strip().startswith('"""')
                        and not line.strip().startswith("'''")
                    ):
                        if not (
                            line.strip().startswith("import ") or line.strip().startswith("from ")
                        ):
                            break

                # 插入新的導入
                for import_line in reversed(imports_to_add):
                    lines.insert(import_end_line, import_line)

                new_content = "\n".join(lines)
                changes_made = True

            if changes_made:
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(new_content)
                self.fixes_applied += 1
                return True

        except Exception as e:
            print(f"添加導入失敗 {file_path}: {e}")

        return False

    def fix_unused_variables(self, file_path: Path) -> bool:
        """修復未使用的變量"""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            new_content = content
            _ = False

            # 修復未使用的變量
            _ = [
                # 將未使用的變量改為下劃線
                (r"(\s+)(\w+)\s*=\s*([^=\n]+)(\s*#.*)?$", self._fix_unused_assignment),
                # 修復循環變量
                (r"for\s+(\w+)\s+in\s+([^:]+):", self._fix_loop_variable),
            ]

            lines = new_content.split("\n")
            for i, line in enumerate(lines):
                # 檢查是否有未使用的變量賦值
                if " = " in line and not line.strip().startswith("#"):
                    # 簡單的啟發式檢查
                    var_match = re.match(r"(\s*)(\w+)\s*=\s*(.+)", line)
                    if var_match:
                        indent, var_name, assignment = var_match.groups()

                        # 檢查變量是否在後續行中使用
                        var_used = False
                        for j in range(i + 1, min(i + 20, len(lines))):  # 檢查接下來20行
                            if var_name in lines[j] and not lines[j].strip().startswith("#"):
                                var_used = True
                                break

                        # 如果變量未使用且不是特殊變量，改為下劃線
                        if not var_used and var_name not in [
                            "self",
                            "cls",
                            "result",
                            "response",
                            "data",
                            "config",
                        ]:
                            lines[i] = f"{indent}_ = {assignment}"
                            changes_made = True

            if changes_made:
                new_content = "\n".join(lines)
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(new_content)
                self.fixes_applied += 1
                return True

        except Exception as e:
            print(f"修復未使用變量失敗 {file_path}: {e}")

        return False

    def _fix_unused_assignment(self, match):
        """修復未使用的賦值"""
        return match.group(0)  # 暫時返回原始內容

    def _fix_loop_variable(self, match):
        """修復循環變量"""
        return match.group(0)  # 暫時返回原始內容

    def fix_line_length_issues(self, file_path: Path) -> bool:
        """修復行長度問題"""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                lines = f.readlines()

            new_lines = []
            changes_made = False

            for line in lines:
                if len(line.rstrip()) > 100:
                    # 嘗試修復長行
                    stripped = line.rstrip()

                    # 如果是函數定義或類定義，嘗試在參數處斷行
                    if (
                        ("def " in stripped or "class " in stripped)
                        and "(" in stripped
                        and ")" in stripped
                    ):
                        # 在參數列表中斷行
                        indent = len(line) - len(line.lstrip())
                        if ", " in stripped:
                            parts = stripped.split(", ")
                            if len(parts) > 1:
                                new_line = parts[0] + ",\n"
                                for part in parts[1:-1]:
                                    new_line += " " * (indent + 4) + part + ",\n"
                                new_line += " " * (indent + 4) + parts[-1] + "\n"
                                new_lines.append(new_line)
                                changes_made = True
                                continue

                    # 如果是字符串，嘗試使用括號換行
                    if '"' in stripped and len(stripped) > 100:
                        # 簡單的字符串斷行
                        if stripped.count('"') >= 2:
                            indent = len(line) - len(line.lstrip())
                            # 嘗試在適當位置斷行
                            mid_point = len(stripped) // 2
                            break_point = stripped.find(" ", mid_point)
                            if break_point > 0:
                                part1 = stripped[:break_point]
                                part2 = stripped[break_point:].lstrip()
                                new_line = part1 + "\n" + " " * indent + part2 + "\n"
                                new_lines.append(new_line)
                                changes_made = True
                                continue

                new_lines.append(line)

            if changes_made:
                with open(file_path, "w", encoding="utf-8") as f:
                    f.writelines(new_lines)
                self.fixes_applied += 1
                return True

        except Exception as e:
            print(f"修復行長度失敗 {file_path}: {e}")

        return False

    def fix_whitespace_issues(self, file_path: Path) -> bool:
        """修復空白字符問題"""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            new_content = content
            changes_made = False

            # 修復空白字符問題
            fixes = [
                # 修復冒號前的空白 (E203)
                (r"\s+:", ":"),
                # 修復逗號前的空白
                (r"\s+,", ","),
                # 修復括號內的空白
                (r"\(\s+", "("),
                (r"\s+\)", ")"),
            ]

            for pattern, replacement in fixes:
                if re.search(pattern, new_content):
                    new_content = re.sub(pattern, replacement, new_content)
                    changes_made = True

            if changes_made:
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(new_content)
                self.fixes_applied += 1
                return True

        except Exception as e:
            print(f"修復空白字符問題失敗 {file_path}: {e}")

        return False

    def run_fixes(self):
        """運行所有修復"""
        print("🔧 開始最終代碼質量修復...")

        python_files = self.find_python_files()
        print(f"找到 {len(python_files)} 個Python文件")

        for file_path in python_files:
            print(f"處理: {file_path}")

            # 應用各種修復
            self.add_missing_imports(file_path)
            self.fix_unused_variables(file_path)
            self.fix_whitespace_issues(file_path)

        print(f"✅ 最終修復完成，共應用 {self.fixes_applied} 個修復")


def main():
    """主函數"""
    project_root = Path(__file__).parent
    fixer = FinalCodeQualityFixer(str(project_root))
    fixer.run_fixes()


if __name__ == "__main__":
    main()  # 修復不完整調用
