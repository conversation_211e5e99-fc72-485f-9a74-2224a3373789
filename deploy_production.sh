#!/bin/bash
# deploy_production.sh - 生產級智能投組系統一鍵部署腳本
# Production-Grade Intelligent Portfolio System One-Click Deployment Script

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日誌函數
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 檢查命令是否存在
check_command() {
    if ! command -v $1 >/dev/null 2>&1; then
        log_error "$1 未安裝，請先安裝 $1"
        exit 1
    fi
}

# 檢查端口是否可用
check_port() {
    if netstat -tuln | grep -q ":$1 "; then
        log_warning "端口 $1 已被占用"
        return 1
    else
        log_success "端口 $1 可用"
        return 0
    fi
}

# 主函數
main() {
    echo "🚀 智能投組系統生產級部署"
    echo "基於對沖基金級架構標準"
    echo "============================================================"

    # 1. 環境檢查
    log_info "📋 檢查部署環境..."

    # 檢查必要命令
    check_command "python3"
    check_command "pip3"
    check_command "docker"
    check_command "docker-compose"

    # 檢查Python版本
    PYTHON_VERSION=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1,2)
    if [[ $(echo "$PYTHON_VERSION >= 3.9" | bc -l) -eq 1 ]]; then
        log_success "Python版本: $PYTHON_VERSION ✓"
    else
        log_error "Python版本過低: $PYTHON_VERSION，需要3.9+"
        exit 1
    fi

    # 檢查系統資源
    log_info "💻 檢查系統資源..."

    CPU_CORES=$(nproc)
    MEMORY_GB=$(free -g | awk '/^Mem:/ {print $2}')
    DISK_GB=$(df -BG / | awk 'NR==2 {print $4}' | sed 's/G//')

    log_info "  CPU核心數: $CPU_CORES"
    log_info "  內存大小: ${MEMORY_GB}GB"
    log_info "  可用磁盤: ${DISK_GB}GB"

    # 資源檢查
    if [ $CPU_CORES -lt 4 ]; then
        log_warning "CPU核心數較少，建議至少4核心"
    fi

    if [ $MEMORY_GB -lt 8 ]; then
        log_warning "內存較少，建議至少8GB"
    fi

    if [ $DISK_GB -lt 50 ]; then
        log_error "磁盤空間不足，需要至少50GB"
        exit 1
    fi

    # 檢查網絡連接
    log_info "🌐 檢查網絡連接..."
    if ping -c 1 8.8.8.8 >/dev/null 2>&1; then
        log_success "外網連接正常"
    else
        log_error "外網連接異常"
        exit 1
    fi

    # 檢查端口可用性
    log_info "🔌 檢查端口可用性..."
    PORTS=(8080 3000 9090 5432 6379)
    for port in "${PORTS[@]}"; do
        check_port $port
    done

    # 2. 修復資源管理問題
    log_info "🔧 修復資源管理問題..."
    if [ -f "fix_resource_management.py" ]; then
        python3 fix_resource_management.py
        log_success "資源管理問題已修復"
    else
        log_warning "資源管理修復腳本不存在"
    fi

    # 3. 創建目錄結構
    log_info "📁 創建目錄結構..."
    mkdir -p logs data monitoring/dashboards config backups
    log_success "目錄結構創建完成"

    # 4. 安裝Python依賴
    log_info "📦 安裝Python依賴..."
    if [ -f "requirements.txt" ]; then
        pip3 install -r requirements.txt
        log_success "Python依賴安裝完成"
    else
        log_warning "requirements.txt 不存在，跳過依賴安裝"
    fi

    # 5. 創建Docker配置
    log_info "🐳 創建Docker配置..."

    # 創建docker-compose.yml
    cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  portfolio-system:
    build: .
    ports:
      - "8080:8080"
    environment:
      - DATABASE_URL=********************************************************/portfolio_db
      - REDIS_URL=redis://redis:6379/0
      - LOG_LEVEL=INFO
      - PYTHONPATH=/app
    depends_on:
      - postgres
      - redis
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
      - ./config:/app/config
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python3", "-c", "import requests; requests.get('http://localhost:8080/health')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: portfolio_db
      POSTGRES_USER: portfolio_user
      POSTGRES_PASSWORD: portfolio_pass
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U portfolio_user -d portfolio_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes --maxmemory 2gb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/dashboards:/etc/grafana/provisioning/dashboards
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:
EOF

    # 創建Dockerfile
    cat > Dockerfile << 'EOF'
FROM python:3.11-slim

WORKDIR /app

# 安裝系統依賴
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libpq-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 複製依賴文件
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 複製應用代碼
COPY . .

# 創建非root用戶
RUN useradd -m -u 1000 trader && chown -R trader:trader /app
USER trader

# 暴露端口
EXPOSE 8080

# 健康檢查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# 啟動命令
CMD ["python3", "intelligent_portfolio_system.py"]
EOF

    # 創建數據庫初始化腳本
    cat > init.sql << 'EOF'
-- 數據庫初始化腳本
CREATE TABLE IF NOT EXISTS strategy_states (
    strategy_id TEXT PRIMARY KEY,
    state_data TEXT NOT NULL,
    health_score REAL DEFAULT 0.5,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS portfolio_allocations (
    strategy_id TEXT PRIMARY KEY,
    allocated_capital REAL NOT NULL,
    target_allocation REAL NOT NULL,
    current_allocation REAL NOT NULL,
    health_score REAL DEFAULT 0.5,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS trade_records (
    id SERIAL PRIMARY KEY,
    strategy_id TEXT NOT NULL,
    symbol TEXT NOT NULL,
    side TEXT NOT NULL,
    amount REAL NOT NULL,
    price REAL,
    pnl REAL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    metadata TEXT
);

CREATE TABLE IF NOT EXISTS system_states (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS performance_history (
    id SERIAL PRIMARY KEY,
    strategy_id TEXT NOT NULL,
    daily_return REAL NOT NULL,
    cumulative_return REAL,
    date DATE NOT NULL,
    UNIQUE(strategy_id, date)
);

-- 創建索引
CREATE INDEX IF NOT EXISTS idx_trade_records_strategy_id ON trade_records(strategy_id);
CREATE INDEX IF NOT EXISTS idx_trade_records_timestamp ON trade_records(timestamp);
CREATE INDEX IF NOT EXISTS idx_performance_history_strategy_date ON performance_history(strategy_id, date);
EOF

    log_success "Docker配置創建完成"

    # 6. 創建監控配置
    log_info "📊 創建監控配置..."
    mkdir -p monitoring

    cat > monitoring/prometheus.yml << 'EOF'
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'portfolio-system'
    static_configs:
      - targets: ['portfolio-system:8080']
    metrics_path: /metrics
    scrape_interval: 5s
EOF

    log_success "監控配置創建完成"

    # 7. 構建和啟動服務
    log_info "🚀 構建和啟動服務..."

    # 構建Docker鏡像
    docker-compose build
    log_success "Docker鏡像構建完成"

    # 啟動數據庫服務
    log_info "💾 啟動數據庫服務..."
    docker-compose up -d postgres redis

    # 等待數據庫就緒
    log_info "⏳ 等待數據庫就緒..."
    sleep 20

    # 檢查數據庫連接
    if docker-compose exec -T postgres pg_isready -U portfolio_user -d portfolio_db; then
        log_success "數據庫連接正常"
    else
        log_error "數據庫連接失敗"
        exit 1
    fi

    # 啟動所有服務
    log_info "🚀 啟動所有服務..."
    docker-compose up -d

    # 等待服務就緒
    log_info "⏳ 等待服務啟動..."
    sleep 30

    # 8. 健康檢查
    log_info "🏥 執行健康檢查..."

    # 檢查各服務狀態
    SERVICES=("portfolio-system" "postgres" "redis" "prometheus" "grafana")
    for service in "${SERVICES[@]}"; do
        if docker-compose ps $service | grep -q "Up"; then
            log_success "$service 服務運行正常"
        else
            log_error "$service 服務啟動失敗"
        fi
    done

    # 檢查Web服務
    if curl -f http://localhost:8080/health >/dev/null 2>&1; then
        log_success "Portfolio System健康檢查通過"
    else
        log_warning "Portfolio System健康檢查失敗，可能仍在啟動中"
    fi

    # 9. 顯示部署結果
    echo ""
    echo "🎉 部署完成！"
    echo "============================================================"
    echo "📊 服務訪問地址:"
    echo "  🎯 Portfolio System: http://localhost:8080"
    echo "  📈 Prometheus: http://localhost:9090"
    echo "  📊 Grafana: http://localhost:3000 (admin/admin123)"
    echo ""
    echo "📋 管理命令:"
    echo "  查看日誌: docker-compose logs -f"
    echo "  停止服務: docker-compose down"
    echo "  重啟服務: docker-compose restart"
    echo "  查看狀態: docker-compose ps"
    echo ""
    echo "📁 重要目錄:"
    echo "  日誌目錄: ./logs"
    echo "  數據目錄: ./data"
    echo "  配置目錄: ./config"
    echo "  備份目錄: ./backups"
    echo ""
    echo "🔧 下一步操作:"
    echo "  1. 配置交易所API密鑰"
    echo "  2. 調整策略參數"
    echo "  3. 設置監控告警"
    echo "  4. 配置自動備份"
    echo ""
    log_success "智能投組系統已成功部署到生產環境！"
}

# 執行主函數
main "$@"
