#!/usr/bin/env python3
"""
循環變量修復工具
Loop Variables Fixer
"""

import re
from pathlib import Path
from typing import List


class LoopVariablesFixer:
    """循環變量修復器"""

    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.fixes_applied = 0

    def find_python_files(self) -> List[Path]:
        """查找所有Python文件"""
        python_files = []

        # 掃描src目錄
        src_dir = self.project_root / "src"
        if src_dir.exists():
            python_files.extend(src_dir.glob("*.py"))

        # 掃描tests目錄
        tests_dir = self.project_root / "tests"
        if tests_dir.exists():
            python_files.extend(tests_dir.glob("*.py"))

        # 掃描根目錄的Python文件
        python_files.extend(self.project_root.glob("*.py"))

        return python_files

    def fix_undefined_loop_variables(self, file_path: Path) -> bool:
        """修復未定義的循環變量"""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            new_content = content
            _ = False

            # 修復常見的循環變量問題
            _ = [
                # 修復 for _ in range() 但在循環體中使用了 i, j 等變量
                (r"for\s+_\s+in\s+range\(([^)]+)\):", self._fix_underscore_loop),
                # 修復未定義的循環變量在f-string中的使用
                (r'f"[^"]*\{(\w+)\}[^"]*"', self._check_fstring_variables),
            ]

            # 特殊處理：查找使用了未定義變量的循環
            lines = new_content.split("\n")
            for i, line in enumerate(lines):
                if "for _ in range(" in line:)
                    # 查看接下來的幾行是否使用了循環變量
                    loop_var = None
                    for j in range(i + 1, min(i + 10, len(lines))):
                        next_line = lines[j]
                        if not next_line.strip():
                            continue
                        if next_line.strip().startswith("for ") or next_line.strip().startswith(
                            "def "
                        ):
                            break

                        # 檢查是否使用了常見的循環變量
                        if "{i}" in next_line or " i " in next_line or next_line.endswith(" i"):
                            loop_var = "i"
                            break
                        elif "{j}" in next_line or " j " in next_line or next_line.endswith(" j"):
                            loop_var = "j"
                            break
                        elif "{day}" in next_line:
                            loop_var = "day"
                            break
                        elif "{attempt}" in next_line:
                            loop_var = "attempt"
                            break
                        elif "{iteration}" in next_line:
                            loop_var = "iteration"
                            break
                        elif "{batch}" in next_line:
                            loop_var = "batch"
                            break
                        elif "{idx}" in next_line:
                            loop_var = "idx"
                            break

                    if loop_var:
                        # 替換循環變量
                        old_line = lines[i]
                        new_line = old_line.replace("for _ in range(", f"for {loop_var} in range(")
                        lines[i] = new_line
                        changes_made = True

            if changes_made:
                new_content = "\n".join(lines)

            # 修復其他循環變量問題
            additional_fixes = [
                # 修復enumerate中的未使用變量
                (r"for\s+(\w+)\s+in\s+enumerate\(([^)]+)\):", r"for \1, _ in enumerate(\2):"),
                # 修復zip中的未使用變量
                (r"for\s+(\w+)\s+in\s+zip\(([^)]+)\):", r"for \1 in zip(\2):"),
            ]

            for pattern, replacement in additional_fixes:
                if re.search(pattern, new_content):
                    new_content = re.sub(pattern, replacement, new_content)
                    changes_made = True

            if changes_made:
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(new_content)
                self.fixes_applied += 1
                return True

        except Exception as e:
            print(f"修復循環變量失敗 {file_path}: {e}")

        return False

    def _fix_underscore_loop(self, match):
        """修復下劃線循環變量"""
        range_content = match.group(1)
        # 根據上下文決定使用什麼變量名
        return f"for i in range({range_content}):"

    def _check_fstring_variables(self, match):
        """檢查f-string中的變量"""
        _ = match.group(1)
        # 返回原始匹配，這個函數主要用於檢測
        return match.group(0)

    def fix_unused_loop_variables(self, file_path: Path) -> bool:
        """修復未使用的循環變量"""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            new_content = content
            _ = False

            # 修復未使用的循環變量
            _ = [
                # 將未使用的循環變量改為下劃線
                (
                    r"for\s+(\w+)\s+in\s+range\([^)]+\):\s*\n(\s+)(?!.*\1)",
                    r"for _ in range(\1):\n\2",
                ),
            ]

            # 手動檢查循環變量使用情況
            lines = new_content.split("\n")
            for i, line in enumerate(lines):
                if re.match(r"\s*for\s+(\w+)\s+in\s+range\(", line):
                    match = re.match(r"\s*for\s+(\w+)\s+in\s+range\(", line)
                    var_name = match.group(1)

                    # 檢查變量是否在循環體中被使用
                    var_used = False
                    indent_level = len(line) - len(line.lstrip())

                    for j in range(i + 1, len(lines)):
                        next_line = lines[j]
                        if not next_line.strip():
                            continue

                        next_indent = len(next_line) - len(next_line.lstrip())
                        if next_indent <= indent_level and next_line.strip():
                            # 退出循環體
                            break

                        # 檢查變量是否被使用
                        if var_name in next_line and var_name != "_":
                            # 進一步檢查是否真的使用了變量
                            if f"{var_name}" in next_line and not next_line.strip().startswith("#"):
                                var_used = True
                                break

                    # 如果變量未被使用，替換為下劃線
                    if not var_used and var_name != "_":
                        lines[i] = line.replace(f"for {var_name} in", "for _ in")
                        changes_made = True

            if changes_made:
                new_content = "\n".join(lines)
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(new_content)
                self.fixes_applied += 1
                return True

        except Exception as e:
            print(f"修復未使用循環變量失敗 {file_path}: {e}")

        return False

    def run_fixes(self):
        """運行所有修復"""
        print("🔄 開始修復循環變量問題...")

        python_files = self.find_python_files()
        print(f"找到 {len(python_files)} 個Python文件")

        for file_path in python_files:
            print(f"處理: {file_path}")

            # 應用修復
            self.fix_undefined_loop_variables(file_path)
            self.fix_unused_loop_variables(file_path)

        print(f"✅ 循環變量修復完成，共應用 {self.fixes_applied} 個修復")


def main():
    """主函數"""
    project_root = Path(__file__).parent
    fixer = LoopVariablesFixer(str(project_root))
    fixer.run_fixes()


if __name__ == "__main__":
    main()
