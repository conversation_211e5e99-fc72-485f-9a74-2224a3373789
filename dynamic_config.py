#!/usr/bin/env python3
"""
動態配置管理器 - 支持運行時配置更新
Dynamic Configuration Manager - Supports runtime configuration updates
"""

import json
import threading
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Callable
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
from alert_manager import get_alert_manager, AlertLevel
from logging_config import get_logger

logger = get_logger(__name__)


class ConfigChangeHandler(FileSystemEventHandler):
    """配置文件變更處理器"""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager
        super().__init__()
    
    def on_modified(self, event):
        if not event.is_directory and event.src_path.endswith('.json'):
            logger.info(f"檢測到配置文件變更: {event.src_path}")
            self.config_manager._reload_config()


class DynamicConfigManager:
    """動態配置管理器"""
    
    def __init__(self, config_path: str = "config.json", watch_changes: bool = True):
        self.config_path = Path(config_path)
        self.watch_changes = watch_changes
        self.config = {}
        self.last_modified = None
        self.change_callbacks = []
        self.lock = threading.RLock()
        
        # 文件監控
        self.observer = None
        self.event_handler = None
        
        # 遠程控制命令隊列
        self.command_queue = []
        self.command_lock = threading.Lock()
        
        # 載入初始配置
        self._load_config()
        
        # 啟動文件監控
        if self.watch_changes:
            self._start_file_watcher()
        
        logger.info(f"DynamicConfigManager 初始化完成: {config_path}")
    
    def _load_config(self) -> bool:
        """載入配置文件"""
        try:
            if not self.config_path.exists():
                logger.error(f"配置文件不存在: {self.config_path}")
                return False
            
            with self.lock:
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    new_config = json.load(f)
                
                # 檢查配置是否有變更
                if new_config != self.config:
                    old_config = self.config.copy()
                    self.config = new_config
                    self.last_modified = datetime.now()
                    
                    logger.info("配置已更新")
                    
                    # 觸發變更回調
                    self._trigger_change_callbacks(old_config, new_config)
                    
                    # 發送警報
                    alert_manager = get_alert_manager()
                    alert_manager.send_alert(
                        AlertLevel.INFO,
                        "配置更新",
                        "配置文件已重新載入",
                        {
                            "更新時間": self.last_modified.isoformat(),
                            "配置文件": str(self.config_path)
                        }
                    )
                
                return True
                
        except Exception as e:
            logger.error(f"載入配置失敗: {e}")
            return False
    
    def _reload_config(self):
        """重新載入配置（防抖動）"""
        # 簡單的防抖動機制
        time.sleep(0.1)
        self._load_config()
    
    def _start_file_watcher(self):
        """啟動文件監控"""
        try:
            self.event_handler = ConfigChangeHandler(self)
            self.observer = Observer()
            self.observer.schedule(
                self.event_handler,
                str(self.config_path.parent),
                recursive=False
            )
            self.observer.start()
            
            logger.info("配置文件監控已啟動")
            
        except Exception as e:
            logger.error(f"啟動文件監控失敗: {e}")
    
    def _stop_file_watcher(self):
        """停止文件監控"""
        try:
            if self.observer:
                self.observer.stop()
                self.observer.join()
                logger.info("配置文件監控已停止")
                
        except Exception as e:
            logger.error(f"停止文件監控失敗: {e}")
    
    def _trigger_change_callbacks(self, old_config: Dict, new_config: Dict):
        """觸發配置變更回調"""
        for callback in self.change_callbacks:
            try:
                callback(old_config, new_config)
            except Exception as e:
                logger.error(f"配置變更回調執行失敗: {e}")
    
    def get_config(self) -> Dict:
        """獲取當前配置"""
        with self.lock:
            return self.config.copy()
    
    def get_value(self, key_path: str, default: Any = None) -> Any:
        """獲取配置值（支持點分隔路徑）"""
        try:
            with self.lock:
                keys = key_path.split('.')
                value = self.config
                
                for key in keys:
                    if isinstance(value, dict) and key in value:
                        value = value[key]
                    else:
                        return default
                
                return value
                
        except Exception as e:
            logger.error(f"獲取配置值失敗: {key_path}, {e}")
            return default
    
    def set_value(self, key_path: str, value: Any, save_to_file: bool = True) -> bool:
        """設置配置值（支持點分隔路徑）"""
        try:
            with self.lock:
                keys = key_path.split('.')
                config_ref = self.config
                
                # 導航到目標位置
                for key in keys[:-1]:
                    if key not in config_ref:
                        config_ref[key] = {}
                    config_ref = config_ref[key]
                
                # 設置值
                old_value = config_ref.get(keys[-1])
                config_ref[keys[-1]] = value
                
                logger.info(f"配置值已更新: {key_path} = {value} (舊值: {old_value})")
                
                # 保存到文件
                if save_to_file:
                    self._save_config()
                
                # 發送警報
                alert_manager = get_alert_manager()
                alert_manager.send_alert(
                    AlertLevel.INFO,
                    "配置參數更新",
                    f"參數 {key_path} 已更新",
                    {
                        "參數": key_path,
                        "新值": value,
                        "舊值": old_value
                    }
                )
                
                return True
                
        except Exception as e:
            logger.error(f"設置配置值失敗: {key_path} = {value}, {e}")
            return False
    
    def _save_config(self):
        """保存配置到文件"""
        try:
            # 創建備份
            backup_path = self.config_path.with_suffix('.bak')
            if self.config_path.exists():
                self.config_path.rename(backup_path)
            
            # 保存新配置
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            
            self.last_modified = datetime.now()
            logger.info("配置已保存到文件")
            
        except Exception as e:
            logger.error(f"保存配置失敗: {e}")
            # 恢復備份
            backup_path = self.config_path.with_suffix('.bak')
            if backup_path.exists():
                backup_path.rename(self.config_path)
            raise
    
    def add_change_callback(self, callback: Callable[[Dict, Dict], None]):
        """添加配置變更回調"""
        self.change_callbacks.append(callback)
        logger.info(f"已添加配置變更回調: {callback.__name__}")
    
    def remove_change_callback(self, callback: Callable[[Dict, Dict], None]):
        """移除配置變更回調"""
        if callback in self.change_callbacks:
            self.change_callbacks.remove(callback)
            logger.info(f"已移除配置變更回調: {callback.__name__}")
    
    def execute_remote_command(self, command: str, params: Dict = None) -> Dict:
        """執行遠程控制命令"""
        try:
            params = params or {}
            
            if command == "set_parameter":
                key = params.get('key')
                value = params.get('value')
                
                if not key:
                    return {"status": "error", "message": "缺少參數 key"}
                
                success = self.set_value(key, value)
                
                return {
                    "status": "success" if success else "error",
                    "message": f"參數 {key} 已更新為 {value}" if success else "參數更新失敗"
                }
            
            elif command == "get_parameter":
                key = params.get('key')
                
                if not key:
                    return {"status": "error", "message": "缺少參數 key"}
                
                value = self.get_value(key)
                
                return {
                    "status": "success",
                    "key": key,
                    "value": value
                }
            
            elif command == "reload_config":
                success = self._load_config()
                
                return {
                    "status": "success" if success else "error",
                    "message": "配置重新載入成功" if success else "配置重新載入失敗"
                }
            
            elif command == "get_all_config":
                return {
                    "status": "success",
                    "config": self.get_config()
                }
            
            else:
                return {
                    "status": "error",
                    "message": f"未知命令: {command}"
                }
                
        except Exception as e:
            logger.error(f"執行遠程命令失敗: {command}, {e}")
            return {
                "status": "error",
                "message": str(e)
            }
    
    def get_status(self) -> Dict:
        """獲取配置管理器狀態"""
        return {
            "config_path": str(self.config_path),
            "last_modified": self.last_modified.isoformat() if self.last_modified else None,
            "watch_changes": self.watch_changes,
            "callback_count": len(self.change_callbacks),
            "config_size": len(json.dumps(self.config))
        }
    
    def shutdown(self):
        """關閉配置管理器"""
        try:
            self._stop_file_watcher()
            logger.info("DynamicConfigManager 已關閉")
            
        except Exception as e:
            logger.error(f"關閉 DynamicConfigManager 失敗: {e}")


# 全局動態配置管理器實例
_dynamic_config_manager = None

def get_dynamic_config_manager(config_path: str = "config.json") -> DynamicConfigManager:
    """獲取全局動態配置管理器實例"""
    global _dynamic_config_manager
    if _dynamic_config_manager is None:
        _dynamic_config_manager = DynamicConfigManager(config_path)
    return _dynamic_config_manager


if __name__ == "__main__":
    # 測試動態配置管理器
    print("測試動態配置管理器...")
    
    config_manager = DynamicConfigManager()
    
    # 測試獲取配置
    print(f"當前配置: {config_manager.get_config()}")
    
    # 測試獲取特定值
    trading_pair = config_manager.get_value('trading_pair')
    print(f"交易對: {trading_pair}")
    
    # 測試設置值
    config_manager.set_value('test_parameter', 123.45, save_to_file=False)
    test_value = config_manager.get_value('test_parameter')
    print(f"測試參數: {test_value}")
    
    # 測試遠程命令
    result = config_manager.execute_remote_command('get_parameter', {'key': 'timeframe'})
    print(f"遠程命令結果: {result}")
    
    # 測試狀態
    status = config_manager.get_status()
    print(f"配置管理器狀態: {status}")
    
    print("動態配置管理器測試完成！")
