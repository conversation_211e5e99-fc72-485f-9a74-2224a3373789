# Pre-commit hooks configuration for Pair Trading V2
# 配對交易 V2 的 Pre-commit hooks 配置

repos:
  # Python 代碼格式化 - Python Code Formatting
  - repo: https://github.com/psf/black
    rev: 23.12.1
    hooks:
      - id: black
        name: Black Code Formatter
        description: "格式化 Python 代碼 (Format Python code)"
        language_version: python3
        args: [--line-length=100]
        files: \.py$
        exclude: ^(_archive/|migrations/)

  # Import 排序 - Import Sorting  
  - repo: https://github.com/pycqa/isort
    rev: 5.13.2
    hooks:
      - id: isort
        name: isort Import Sorter
        description: "排序和格式化 Python imports (Sort and format Python imports)"
        args: [--profile=black, --line-length=100]
        files: \.py$
        exclude: ^(_archive/|migrations/)

  # 代碼質量檢查 - Code Quality Linting
  - repo: https://github.com/pycqa/flake8
    rev: 7.0.0
    hooks:
      - id: flake8
        name: Flake8 Linter
        description: "Python 代碼質量檢查 (Python code quality linting)"
        args: [--max-line-length=100, --extend-ignore=E203,E501,W503]
        files: \.py$
        exclude: ^(_archive/|migrations/|__pycache__/)
        additional_dependencies:
          - flake8-docstrings
          - flake8-import-order
          - flake8-bugbear

  # 類型檢查 - Type Checking
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.8.0
    hooks:
      - id: mypy
        name: MyPy Type Checker
        description: "Python 靜態類型檢查 (Python static type checking)"
        files: ^src/.*\.py$
        exclude: ^(_archive/|tests/|migrations/)
        args: [--ignore-missing-imports, --disallow-untyped-defs]
        additional_dependencies:
          - types-requests
          - types-PyYAML
          - types-redis
          - types-setuptools

  # 安全檢查 - Security Scanning
  - repo: https://github.com/PyCQA/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        name: Bandit Security Linter
        description: "Python 安全漏洞掃描 (Python security vulnerability scanning)"
        args: [-r, -f, json, -o, security_report.json]
        files: \.py$
        exclude: ^(tests/|_archive/)

  # 通用文件檢查 - General File Checks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      # 檢查文件大小
      - id: check-added-large-files
        name: Check Large Files
        description: "檢查大文件 (Check for large files)"
        args: [--maxkb=1000]
      
      # 檢查合併衝突標記
      - id: check-merge-conflict
        name: Check Merge Conflicts
        description: "檢查合併衝突標記 (Check for merge conflict markers)"
      
      # 檢查 YAML 語法
      - id: check-yaml
        name: Check YAML Syntax
        description: "檢查 YAML 文件語法 (Check YAML file syntax)"
        files: \.(yaml|yml)$
      
      # 檢查 JSON 語法
      - id: check-json
        name: Check JSON Syntax
        description: "檢查 JSON 文件語法 (Check JSON file syntax)"
        files: \.json$
      
      # 檢查 TOML 語法
      - id: check-toml
        name: Check TOML Syntax
        description: "檢查 TOML 文件語法 (Check TOML file syntax)"
        files: \.toml$
      
      # 移除行尾空白
      - id: trailing-whitespace
        name: Trim Trailing Whitespace
        description: "移除行尾空白 (Remove trailing whitespace)"
        exclude: \.md$
      
      # 確保文件以換行符結尾
      - id: end-of-file-fixer
        name: Fix End of Files
        description: "確保文件以換行符結尾 (Ensure files end with newline)"
      
      # 檢查執行權限
      - id: check-executables-have-shebangs
        name: Check Executable Shebangs
        description: "檢查可執行文件是否有 shebang (Check executable files have shebangs)"

  # Python 文檔字符串檢查 - Python Docstring Checking
  - repo: https://github.com/pycqa/pydocstyle
    rev: 6.3.0
    hooks:
      - id: pydocstyle
        name: pydocstyle Docstring Checker
        description: "Python 文檔字符串風格檢查 (Python docstring style checking)"
        files: ^src/.*\.py$
        exclude: ^(tests/|_archive/|migrations/)
        args: [--convention=google, --add-ignore=D100,D101,D102,D103,D104,D105]

  # 依賴安全檢查 - Dependency Security Scanning
  - repo: https://github.com/Lucas-C/pre-commit-hooks-safety
    rev: v1.3.2
    hooks:
      - id: python-safety-dependencies-check
        name: Safety Dependency Check
        description: "檢查依賴包安全漏洞 (Check dependency security vulnerabilities)"
        files: requirements.*\.txt$

  # Jupyter Notebook 清理 - Jupyter Notebook Cleaning
  - repo: https://github.com/nbQA-dev/nbQA
    rev: 1.7.1
    hooks:
      - id: nbqa-black
        name: nbQA Black (Jupyter)
        description: "格式化 Jupyter Notebook 代碼 (Format Jupyter Notebook code)"
        files: \.ipynb$
        additional_dependencies: [black]
      
      - id: nbqa-isort
        name: nbQA isort (Jupyter)
        description: "排序 Jupyter Notebook imports (Sort Jupyter Notebook imports)"
        files: \.ipynb$
        additional_dependencies: [isort]

# Pre-commit 配置 - Pre-commit Configuration
default_language_version:
  python: python3

# CI 配置 - CI Configuration
ci:
  autofix_commit_msg: |
    [pre-commit.ci] auto fixes from pre-commit.com hooks
    
    for more information, see https://pre-commit.ci
  autofix_prs: true
  autoupdate_branch: ''
  autoupdate_commit_msg: '[pre-commit.ci] pre-commit autoupdate'
  autoupdate_schedule: weekly
  skip: [mypy, bandit, python-safety-dependencies-check]
  submodules: false

# 排除文件模式 - Exclude File Patterns
exclude: |
  (?x)^(
    _archive/.*|
    \.git/.*|
    __pycache__/.*|
    \.pytest_cache/.*|
    \.mypy_cache/.*|
    \.coverage|
    \.env.*|
    logs/.*|
    data/.*|
    test_reports/.*|
    test_results/.*|
    records/.*|
    pair_selection_results/.*
  )$
