# Pre-commit hooks configuration for Pair Trading V2 (簡化版本 - 語法修復階段)
# 配對交易 V2 的 Pre-commit hooks 配置 (Simplified for syntax repair phase)

repos:
  # 核心語法檢查 - Core Syntax Checking (最重要)
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: check-ast
        name: Python AST Check
        description: "檢查Python抽象語法樹 (Check Python Abstract Syntax Tree)"
        files: \.py$

  # Python 代碼格式化 - Python Code Formatting
  - repo: https://github.com/psf/black
    rev: 23.12.1
    hooks:
      - id: black
        name: Black Code Formatter
        description: "格式化 Python 代碼 (Format Python code)"
        language_version: python3
        args: [--line-length=100]
        files: \.py$
        exclude: ^(_archive/|migrations/)

  # Import 排序 - Import Sorting
  - repo: https://github.com/pycqa/isort
    rev: 5.13.2
    hooks:
      - id: isort
        name: isort Import Sorter
        description: "排序和格式化 Python imports (Sort and format Python imports)"
        args: [--profile=black, --line-length=100]
        files: \.py$
        exclude: ^(_archive/|migrations/)

  # 基本文件檢查 - Basic File Checks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      # 移除行尾空白
      - id: trailing-whitespace
        name: Trim Trailing Whitespace
        description: "移除行尾空白 (Remove trailing whitespace)"
        exclude: \.md$

      # 確保文件以換行符結尾
      - id: end-of-file-fixer
        name: Fix End of Files
        description: "確保文件以換行符結尾 (Ensure files end with newline)"

# Pre-commit 配置 - Pre-commit Configuration
default_language_version:
  python: python3

# 排除文件模式 - Exclude File Patterns (簡化版本)
exclude: |
  (?x)^(
    _archive/.*|
    __pycache__/.*|
    \.pytest_cache/.*|
    \.env.*|
    logs/.*|
    data/.*
  )$
