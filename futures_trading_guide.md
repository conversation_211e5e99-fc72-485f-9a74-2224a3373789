# 期貨交易配對策略指南
# Futures Trading Pairs Strategy Guide

## 🎯 為什麼需要期貨交易？

### **配對交易的核心需求**：
配對交易需要能夠**同時做多和做空**兩個相關資產：
- **做多**: 買入被低估的資產
- **做空**: 賣出被高估的資產

### **現貨 vs 期貨對比**：

| 特性 | 現貨交易 | 期貨交易 |
|------|----------|----------|
| **做多** | ✅ 支持 | ✅ 支持 |
| **做空** | ❌ 只能賣出已有資產 | ✅ 真正做空 |
| **配對交易** | ❌ 無法實現 | ✅ 完美支持 |
| **槓桿** | ❌ 無槓桿 | ✅ 1-20x |
| **資金效率** | 低 | 高 |

## 🔧 當前系統配置

### **✅ 已升級到期貨交易**：

#### **交易所**: Gate.io
- **期貨支持**: ✅ 38個U本位合約
- **做空支持**: ✅ 完全支持
- **槓桿設置**: ✅ 1-20x可調
- **沙盒測試**: ✅ 安全環境

#### **配置文件**: `config_futures.json`
```json
{
    "trading_mode": "futures",
    "futures_type": "linear",     // U本位期貨
    "leverage": 1,                // 1x槓桿（安全）
    "margin_mode": "isolated",    // 逐倉模式
    "trading_pairs": [
        ["BTC/USDT:USDT", "ETH/USDT:USDT"]
    ]
}
```

## 📊 期貨交易對格式

### **期貨符號格式**：
- **現貨**: `BTC/USDT`
- **期貨**: `BTC/USDT:USDT`

### **可用的期貨合約**：
```
BTC/USDT:USDT    - 比特幣U本位永續合約
ETH/USDT:USDT    - 以太坊U本位永續合約
AVAX/USDT:USDT   - Avalanche U本位永續合約
ADA/USDT:USDT    - Cardano U本位永續合約
DOT/USDT:USDT    - Polkadot U本位永續合約
```

## 🛡️ 風險管理配置

### **推薦設置**：

#### **1. 槓桿設置**
```json
"leverage": 1  // 建議使用1x槓桿
```
- **1x槓桿**: 最安全，避免強制平倉
- **2-3x槓桿**: 中等風險，提高資金效率
- **>5x槓桿**: 高風險，不建議新手使用

#### **2. 保證金模式**
```json
"margin_mode": "isolated"  // 逐倉模式
```
- **逐倉**: 風險隔離，單個交易對虧損不影響其他
- **全倉**: 風險共享，不建議配對交易使用

#### **3. 倉位大小**
```json
"position_size_usd": 500,
"max_position_size": 0.05  // 最大5%倉位
```

#### **4. 止損設置**
```json
"stop_loss_pct": 0.02  // 2%止損
```

## 🔄 配對交易邏輯

### **做多做空機制**：

#### **信號觸發時**：
1. **Z-score > 2.5**: 
   - **做空高估資產** (如BTC)
   - **做多低估資產** (如ETH)

2. **Z-score < -2.5**:
   - **做多低估資產** (如BTC)  
   - **做空高估資產** (如ETH)

#### **平倉條件**：
- **Z-score回歸0附近**
- **達到止損點**
- **達到止盈點**

## 💰 資金費率考慮

### **永續合約特點**：
- **資金費率**: 每8小時收取一次
- **費率範圍**: 通常-0.1%到+0.1%
- **影響**: 長期持倉成本

### **資金費率管理**：
```json
"funding_rate_threshold": 0.01  // 1%閾值
```
- 當資金費率過高時，考慮平倉
- 監控資金費率變化趨勢

## 🧪 測試流程

### **1. 沙盒測試**
```bash
# 使用期貨配置進行驗證
python3 main_refactored.py validate --config config_futures.json

# 健康檢查
python3 main_refactored.py health --config config_futures.json

# 期貨支持測試
python3 test_futures_support.py
```

### **2. 回測驗證**
```bash
# 期貨回測
python3 main_refactored.py backtest --config config_futures.json \
    --start-date 2024-01-01 --end-date 2024-12-31
```

### **3. 模擬交易**
```bash
# 沙盒環境模擬
python3 main_refactored.py live --config config_futures.json --dry-run
```

## 🚀 生產環境部署

### **步驟1: 獲取期貨API權限**
1. 登錄Gate.io
2. 創建API密鑰
3. **重要**: 啟用期貨交易權限
4. 禁用提現權限

### **步驟2: 更新配置**
```bash
# 更新.env
TRADING_API_KEY=your_real_gate_futures_api_key
TRADING_SECRET=your_real_gate_futures_secret

# 更新config_futures.json
"sandbox": false
```

### **步驟3: 資金準備**
- 將USDT轉入期貨賬戶
- 建議先用小額測試（如$500-1000）
- 確保有足夠保證金

### **步驟4: 啟動交易**
```bash
# 創建確認文件
echo 'CONFIRMED' > .trading_confirmation

# 啟動期貨交易
python3 main_refactored.py live --config config_futures.json
```

## ⚠️ 重要注意事項

### **期貨交易風險**：
1. **槓桿風險**: 即使1x槓桿也有強制平倉風險
2. **資金費率**: 長期持倉會產生費用
3. **流動性風險**: 某些合約流動性可能不足
4. **系統風險**: 交易所維護或故障

### **配對交易特殊風險**：
1. **相關性變化**: 資產相關性可能突然改變
2. **同向波動**: 兩個資產可能同時大幅波動
3. **基差風險**: 期貨價格與現貨價格差異

### **建議**：
- 從小額開始測試
- 密切監控倉位和保證金
- 設置嚴格的風險限制
- 準備緊急平倉計劃

## 📊 監控指標

### **關鍵指標**：
1. **保證金比率**: >50%安全
2. **未實現盈亏**: 實時監控
3. **資金費率**: 每8小時檢查
4. **相關性係數**: 每日計算
5. **Z-score**: 實時監控

### **警報設置**：
```json
"alerts": {
    "margin_alerts": true,
    "funding_rate_alerts": true,
    "liquidation_alerts": true
}
```

## 🎉 總結

### **✅ 系統已完全支持期貨配對交易**：
1. **真正的做空能力**: 可以賣出沒有的資產
2. **完整的配對交易**: 同時做多做空
3. **風險管理**: 1x槓桿 + 逐倉模式
4. **安全測試**: 沙盒環境完整測試

### **🚀 準備就緒**：
- 技術實現: ✅ 完成
- 配置文件: ✅ 完成  
- 安全機制: ✅ 完成
- 測試環境: ✅ 完成

**您的配對交易系統現在已經具備真正的做多做空能力！** 🎯
