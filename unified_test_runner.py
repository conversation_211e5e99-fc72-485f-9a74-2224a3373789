#!/usr/bin/env python3
"""
統一測試執行系統 - 標準化測試流程和覆蓋率報告
Unified Test Runner - Standardized testing process and coverage reporting
"""

import os
import sys
import subprocess
import json
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).resolve().parent / 'src'))

from logging_config import get_logger

logger = get_logger(__name__)


class TestType(str, Enum):
    """測試類型"""
    UNIT = "unit"
    INTEGRATION = "integration"
    SYSTEM = "system"
    PERFORMANCE = "performance"
    ALL = "all"


class TestResult(str, Enum):
    """測試結果"""
    PASSED = "passed"
    FAILED = "failed"
    SKIPPED = "skipped"
    ERROR = "error"


@dataclass
class TestSuiteResult:
    """測試套件結果"""
    name: str
    test_type: TestType
    total_tests: int
    passed: int
    failed: int
    skipped: int
    errors: int
    duration: float
    coverage_percentage: Optional[float]
    result: TestResult
    details: List[str]
    warnings: List[str]


class UnifiedTestRunner:
    """統一測試執行器"""
    
    def __init__(self, project_root: Optional[str] = None):
        self.project_root = Path(project_root) if project_root else Path.cwd()
        self.test_dir = self.project_root / "tests"
        self.reports_dir = self.project_root / "test_reports"
        self.coverage_dir = self.reports_dir / "coverage"
        
        # 創建必要目錄
        self.reports_dir.mkdir(exist_ok=True)
        self.coverage_dir.mkdir(exist_ok=True)
        
        # 測試配置
        self.pytest_args = [
            "--verbose",
            "--tb=short",
            "--strict-markers",
            "--disable-warnings",
            f"--junitxml={self.reports_dir}/junit.xml"
        ]
        
        self.coverage_args = [
            "--cov=.",
            "--cov-report=html:test_reports/coverage/html",
            "--cov-report=xml:test_reports/coverage/coverage.xml",
            "--cov-report=term-missing",
            "--cov-fail-under=80"
        ]
        
        logger.info(f"測試執行器初始化，項目根目錄: {self.project_root}")
    
    def run_all_tests(self, include_coverage: bool = True) -> Dict[TestType, TestSuiteResult]:
        """執行所有測試"""
        logger.info("🚀 開始執行完整測試套件")
        
        results = {}
        test_types = [TestType.UNIT, TestType.INTEGRATION, TestType.SYSTEM]
        
        for test_type in test_types:
            try:
                result = self.run_test_suite(test_type, include_coverage)
                results[test_type] = result
                
                if result.result == TestResult.FAILED:
                    logger.error(f"測試套件 {test_type.value} 失敗")
                else:
                    logger.info(f"測試套件 {test_type.value} 完成")
                    
            except Exception as e:
                logger.error(f"執行測試套件 {test_type.value} 時發生錯誤: {e}")
                results[test_type] = TestSuiteResult(
                    name=f"{test_type.value}_tests",
                    test_type=test_type,
                    total_tests=0,
                    passed=0,
                    failed=0,
                    skipped=0,
                    errors=1,
                    duration=0.0,
                    coverage_percentage=None,
                    result=TestResult.ERROR,
                    details=[str(e)],
                    warnings=[]
                )
        
        # 生成綜合報告
        self._generate_comprehensive_report(results)
        
        logger.info("✅ 完整測試套件執行完成")
        return results
    
    def run_test_suite(self, test_type: TestType, include_coverage: bool = True) -> TestSuiteResult:
        """執行特定類型的測試套件"""
        logger.info(f"執行 {test_type.value} 測試套件")
        
        start_time = time.time()
        
        # 構建測試命令
        cmd = self._build_test_command(test_type, include_coverage)
        
        try:
            # 執行測試
            result = subprocess.run(
                cmd,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=300  # 5分鐘超時
            )
            
            duration = time.time() - start_time
            
            # 解析結果
            test_result = self._parse_test_output(result, test_type, duration)
            
            # 獲取覆蓋率信息
            if include_coverage:
                test_result.coverage_percentage = self._extract_coverage_percentage(result.stdout)
            
            return test_result
            
        except subprocess.TimeoutExpired:
            logger.error(f"測試套件 {test_type.value} 執行超時")
            return TestSuiteResult(
                name=f"{test_type.value}_tests",
                test_type=test_type,
                total_tests=0,
                passed=0,
                failed=0,
                skipped=0,
                errors=1,
                duration=time.time() - start_time,
                coverage_percentage=None,
                result=TestResult.ERROR,
                details=["測試執行超時"],
                warnings=[]
            )
        
        except Exception as e:
            logger.error(f"執行測試套件 {test_type.value} 失敗: {e}")
            return TestSuiteResult(
                name=f"{test_type.value}_tests",
                test_type=test_type,
                total_tests=0,
                passed=0,
                failed=0,
                skipped=0,
                errors=1,
                duration=time.time() - start_time,
                coverage_percentage=None,
                result=TestResult.ERROR,
                details=[str(e)],
                warnings=[]
            )
    
    def _build_test_command(self, test_type: TestType, include_coverage: bool) -> List[str]:
        """構建測試命令"""
        cmd = ["python3", "-m", "pytest"]
        
        # 添加基本參數
        cmd.extend(self.pytest_args)
        
        # 添加覆蓋率參數
        if include_coverage:
            cmd.extend(self.coverage_args)
        
        # 根據測試類型添加特定參數
        if test_type == TestType.UNIT:
            cmd.extend(["-m", "not integration and not system"])
        elif test_type == TestType.INTEGRATION:
            cmd.extend(["-m", "integration"])
        elif test_type == TestType.SYSTEM:
            cmd.extend(["-m", "system"])
        elif test_type == TestType.PERFORMANCE:
            cmd.extend(["-m", "performance", "--benchmark-only"])
        
        # 添加測試目錄
        # 直接在項目根目錄下搜索所有符合 pytest 規則的測試文件
        # 這避免了對固定 'tests/' 目錄的依賴
        cmd.append(str(self.project_root))
        
        return cmd
    
    def _parse_test_output(self, result: subprocess.CompletedProcess, 
                          test_type: TestType, duration: float) -> TestSuiteResult:
        """解析測試輸出"""
        stdout = result.stdout
        stderr = result.stderr
        
        # 初始化計數器
        total_tests = 0
        passed = 0
        failed = 0
        skipped = 0
        errors = 0
        
        details = []
        warnings = []
        
        # 解析pytest輸出
        lines = stdout.split('\n')
        for line in lines:
            line = line.strip()
            
            # 解析測試結果統計
            if "passed" in line and "failed" in line:
                # 例如: "5 passed, 2 failed, 1 skipped in 10.23s"
                parts = line.split()
                for i, part in enumerate(parts):
                    if part == "passed" and i > 0:
                        passed = int(parts[i-1])
                    elif part == "failed" and i > 0:
                        failed = int(parts[i-1])
                    elif part == "skipped" and i > 0:
                        skipped = int(parts[i-1])
                    elif part == "error" and i > 0:
                        errors = int(parts[i-1])
            
            # 收集警告
            if "warning" in line.lower():
                warnings.append(line)
            
            # 收集失敗詳情
            if "FAILED" in line or "ERROR" in line:
                details.append(line)
        
        total_tests = passed + failed + skipped + errors
        
        # 確定整體結果
        if result.returncode == 0:
            test_result = TestResult.PASSED
        elif failed > 0 or errors > 0:
            test_result = TestResult.FAILED
        else:
            test_result = TestResult.SKIPPED
        
        # 添加stderr到詳情
        if stderr:
            details.extend(stderr.split('\n'))
        
        return TestSuiteResult(
            name=f"{test_type.value}_tests",
            test_type=test_type,
            total_tests=total_tests,
            passed=passed,
            failed=failed,
            skipped=skipped,
            errors=errors,
            duration=duration,
            coverage_percentage=None,  # 將在後續設置
            result=test_result,
            details=details,
            warnings=warnings
        )
    
    def _extract_coverage_percentage(self, output: str) -> Optional[float]:
        """從輸出中提取覆蓋率百分比"""
        lines = output.split('\n')
        for line in lines:
            if "TOTAL" in line and "%" in line:
                # 例如: "TOTAL    1000    200    80%"
                parts = line.split()
                for part in parts:
                    if part.endswith('%'):
                        try:
                            return float(part[:-1])
                        except ValueError:
                            continue
        return None
    
    def _generate_comprehensive_report(self, results: Dict[TestType, TestSuiteResult]):
        """生成綜合測試報告"""
        report_file = self.reports_dir / "comprehensive_report.json"
        
        # 計算總體統計
        total_tests = sum(r.total_tests for r in results.values())
        total_passed = sum(r.passed for r in results.values())
        total_failed = sum(r.failed for r in results.values())
        total_skipped = sum(r.skipped for r in results.values())
        total_errors = sum(r.errors for r in results.values())
        total_duration = sum(r.duration for r in results.values())
        
        # 計算平均覆蓋率
        coverage_values = [r.coverage_percentage for r in results.values() if r.coverage_percentage is not None]
        avg_coverage = sum(coverage_values) / len(coverage_values) if coverage_values else None
        
        report_data = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "summary": {
                "total_tests": total_tests,
                "passed": total_passed,
                "failed": total_failed,
                "skipped": total_skipped,
                "errors": total_errors,
                "duration": round(total_duration, 2),
                "success_rate": round((total_passed / total_tests * 100) if total_tests > 0 else 0, 2),
                "average_coverage": round(avg_coverage, 2) if avg_coverage else None
            },
            "test_suites": {}
        }
        
        # 添加各測試套件詳情
        for test_type, result in results.items():
            report_data["test_suites"][test_type.value] = {
                "name": result.name,
                "total_tests": result.total_tests,
                "passed": result.passed,
                "failed": result.failed,
                "skipped": result.skipped,
                "errors": result.errors,
                "duration": round(result.duration, 2),
                "coverage_percentage": result.coverage_percentage,
                "result": result.result.value,
                "warnings_count": len(result.warnings),
                "details_count": len(result.details)
            }
        
        # 保存報告
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"綜合測試報告已保存: {report_file}")
            
            # 打印摘要
            self._print_test_summary(report_data)
            
        except Exception as e:
            logger.error(f"保存測試報告失敗: {e}")
    
    def _print_test_summary(self, report_data: Dict):
        """打印測試摘要"""
        summary = report_data["summary"]
        
        print("\n" + "="*60)
        print("📊 測試執行摘要")
        print("="*60)
        print(f"總測試數量: {summary['total_tests']}")
        print(f"通過: {summary['passed']} ✅")
        print(f"失敗: {summary['failed']} ❌")
        print(f"跳過: {summary['skipped']} ⏭️")
        print(f"錯誤: {summary['errors']} 💥")
        print(f"成功率: {summary['success_rate']}%")
        print(f"執行時間: {summary['duration']}秒")
        if summary['average_coverage']:
            print(f"平均覆蓋率: {summary['average_coverage']}%")
        print("="*60)
        
        # 打印各測試套件結果
        for suite_name, suite_data in report_data["test_suites"].items():
            status_icon = "✅" if suite_data["result"] == "passed" else "❌"
            print(f"{status_icon} {suite_name.upper()}: {suite_data['passed']}/{suite_data['total_tests']} 通過")
    
    def run_specific_test(self, test_file: str, include_coverage: bool = False) -> TestSuiteResult:
        """執行特定測試文件"""
        logger.info(f"執行特定測試: {test_file}")
        
        cmd = ["python3", "-m", "pytest", test_file, "-v"]
        if include_coverage:
            cmd.extend(self.coverage_args)
        
        start_time = time.time()
        
        try:
            result = subprocess.run(
                cmd,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=120
            )
            
            duration = time.time() - start_time
            return self._parse_test_output(result, TestType.UNIT, duration)
            
        except Exception as e:
            logger.error(f"執行特定測試失敗: {e}")
            return TestSuiteResult(
                name=test_file,
                test_type=TestType.UNIT,
                total_tests=0,
                passed=0,
                failed=0,
                skipped=0,
                errors=1,
                duration=time.time() - start_time,
                coverage_percentage=None,
                result=TestResult.ERROR,
                details=[str(e)],
                warnings=[]
            )
    
    def generate_coverage_report(self):
        """生成覆蓋率報告"""
        logger.info("生成覆蓋率報告")
        
        try:
            # 運行覆蓋率報告生成
            cmd = ["python", "-m", "coverage", "html", "-d", str(self.coverage_dir / "html")]
            subprocess.run(cmd, cwd=self.project_root, check=True)
            
            cmd = ["python", "-m", "coverage", "xml", "-o", str(self.coverage_dir / "coverage.xml")]
            subprocess.run(cmd, cwd=self.project_root, check=True)
            
            logger.info(f"覆蓋率報告已生成: {self.coverage_dir}")
            
        except Exception as e:
            logger.error(f"生成覆蓋率報告失敗: {e}")


# 全局測試執行器實例
_test_runner: Optional[UnifiedTestRunner] = None


def get_unified_test_runner(project_root: Optional[str] = None) -> UnifiedTestRunner:
    """獲取統一測試執行器實例"""
    global _test_runner
    if _test_runner is None:
        _test_runner = UnifiedTestRunner(project_root)
    return _test_runner


async def main():
    """測試統一測試執行器"""
    print("🧪 測試統一測試執行器")
    
    try:
        runner = get_unified_test_runner()
        
        # 執行所有測試
        print("\n執行所有測試...")
        results = runner.run_all_tests(include_coverage=True)
        
    except Exception as e:
        print(f"  ❌ 測試失敗: {e}")
    
    print("✅ 統一測試執行器測試完成")


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
