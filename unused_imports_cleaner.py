#!/usr/bin/env python3
"""
未使用導入清理工具
Unused Imports Cleaner
"""

import ast
import re
from pathlib import Path
from typing import Dict, List, Set, Tuple


class UnusedImportsCleaner:
    """未使用導入清理器"""

    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.fixes_applied = 0

    def find_python_files(self) -> List[Path]:
        """查找所有Python文件"""
        python_files = []

        # 掃描src目錄
        src_dir = self.project_root / "src"
        if src_dir.exists():
            python_files.extend(src_dir.glob("*.py"))

        # 掃描tests目錄
        tests_dir = self.project_root / "tests"
        if tests_dir.exists():
            python_files.extend(tests_dir.glob("*.py"))

        # 掃描根目錄的Python文件
        python_files.extend(self.project_root.glob("*.py"))

        return python_files

    def analyze_imports_and_usage(self, file_path: Path) -> <PERSON>ple[Set[str], Set[str]]:
        """分析導入和使用情況"""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            # 解析AST
            tree = ast.parse(content)

            # 收集導入的名稱
            imported_names = set()
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        name = alias.asname if alias.asname else alias.name
                        imported_names.add(name)
                elif isinstance(node, ast.ImportFrom):
                    for alias in node.names:
                        name = alias.asname if alias.asname else alias.name
                        imported_names.add(name)

            # 收集使用的名稱
            used_names = set()
            for node in ast.walk(tree):
                if isinstance(node, ast.Name):
                    used_names.add(node.id)
                elif isinstance(node, ast.Attribute):
                    # 處理屬性訪問，如 module.function
                    if isinstance(node.value, ast.Name):
                        used_names.add(node.value.id)

            return imported_names, used_names

        except Exception as e:
            print(f"分析失敗 {file_path}: {e}")
            return set(), set()

    def get_import_lines(self, file_path: Path) -> Dict[str, List[int]]:
        """獲取導入語句的行號"""
        _ = {}

        try:
            with open(file_path, "r", encoding="utf-8") as f:
                lines = f.readlines()

            for i, line in enumerate(lines, 1):
                stripped = line.strip()
                if stripped.startswith("import ") or stripped.startswith("from "):
                    # 提取導入的名稱
                    if stripped.startswith("import "):
                        # import module1, module2
                        import_part = stripped[7:].strip()
                        names = [name.strip().split(" as ")[0] for name in import_part.split(",")]
                    else:
                        # from module import name1, name2
                        if " import " in stripped:
                            import_part = stripped.split(" import ")[1].strip()
                            names = [
                                name.strip().split(" as ")[0] for name in import_part.split(",")
                            ]
                        else:
                            continue

                    for name in names:
                        name = name.strip()
                        if name and name != "*":
                            if name not in import_lines:
                                import_lines[name] = []
                            import_lines[name].append(i)

        except Exception as e:
            print(f"獲取導入行號失敗 {file_path}: {e}")

        return import_lines

    def remove_unused_imports(self, file_path: Path) -> bool:
        """移除未使用的導入"""
        try:
            # 分析導入和使用情況
            imported_names, used_names = self.analyze_imports_and_usage(file_path)

            # 找出未使用的導入
            _ = imported_names - used_names

            # 特殊處理：保留一些可能被間接使用的導入
            _ = {
                "typing",
                "Optional",
                "List",
                "Dict",
                "Any",
                "Union",
                "Tuple",
                "asyncio",
                "logging",
                "sys",
                "os",
                "json",
                "datetime",
                "pytest",
                "unittest",
                "mock",
            }

            # 過濾掉需要保留的導入
            unused_imports = unused_imports - keep_imports

            if not unused_imports:
                return False

            # 讀取文件內容
            with open(file_path, "r", encoding="utf-8") as f:
                lines = f.readlines()

            # 標記要刪除的行
            lines_to_remove = set()

            for i, line in enumerate(lines):
                stripped = line.strip()
                if stripped.startswith("import ") or stripped.startswith("from "):
                    # 檢查這行是否包含未使用的導入
                    for unused in unused_imports:
                        if f" {unused}" in line or f"{unused}," in line or line.endswith(unused):
                            # 進一步檢查是否整行都是未使用的導入
                            if (
                                stripped.startswith(f"import {unused}")
                                or stripped.startswith(f"from ")
                                and f" import {unused}" in stripped
                            ):
                                lines_to_remove.add(i)
                                break

            if lines_to_remove:
                # 創建新的文件內容
                new_lines = [line for i, line in enumerate(lines) if i not in lines_to_remove]

                # 寫回文件
                with open(file_path, "w", encoding="utf-8") as f:
                    f.writelines(new_lines)

                print(f"  移除了 {len(lines_to_remove)} 行未使用的導入")
                self.fixes_applied += len(lines_to_remove)
                return True

        except Exception as e:
            print(f"移除未使用導入失敗 {file_path}: {e}")

        return False

    def fix_specific_unused_imports(self, file_path: Path) -> bool:
        """修復特定的未使用導入問題"""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            # 常見的未使用導入模式
            unused_patterns = [
                r"^import json\s*$",
                r"^import datetime\s*$",
                r"^import sys\s*$",
                r"^import os\s*$",
                r"^from typing import.*$",
                r"^from datetime import.*$",
            ]

            new_content = content
            changes_made = False

            # 檢查每個模式
            for pattern in unused_patterns:
                matches = re.findall(pattern, content, re.MULTILINE)
                if matches:
                    # 檢查是否真的未使用
                    for match in matches:
                        module_name = match.split()[-1].split(".")[0]
                        if module_name not in content.replace(match, ""):
                            new_content = new_content.replace(match + "\n", "")
                            changes_made = True

            if changes_made:
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(new_content)
                self.fixes_applied += 1
                return True

        except Exception as e:
            print(f"修復特定未使用導入失敗 {file_path}: {e}")

        return False

    def run_cleanup(self):
        """運行清理"""
        print("🧹 開始清理未使用的導入...")

        python_files = self.find_python_files()
        print(f"找到 {len(python_files)} 個Python文件")

        for file_path in python_files:
            print(f"處理: {file_path}")

            # 嘗試移除未使用的導入
            self.remove_unused_imports(file_path)
            self.fix_specific_unused_imports(file_path)

        print(f"✅ 清理完成，共應用 {self.fixes_applied} 個修復")


def main():
    """主函數"""
    project_root = Path(__file__).parent
    cleaner = UnusedImportsCleaner(str(project_root))
    cleaner.run_cleanup()


if __name__ == "__main__":
    main()
