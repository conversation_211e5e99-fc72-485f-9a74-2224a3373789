#!/usr/bin/env python3
"""
高槓桿風險檢查器 - 50倍槓桿專用風險管理
High Leverage Risk Checker - 50x leverage specific risk management
"""

import sys
import time
from datetime import datetime
from typing import Dict, Any, List
import json

sys.path.append('.')

from config_validation import ConfigValidator
from logging_config import setup_logging, get_logger

setup_logging()
logger = get_logger(__name__)


class HighLeverageRiskChecker:
    """高槓桿風險檢查器"""
    
    def __init__(self):
        self.risk_level = "EXTREME"
        self.max_safe_leverage = 5
        self.liquidation_buffer = 0.02  # 2%緩衝
        
    def check_high_leverage_risks(self, config_file: str) -> Dict[str, Any]:
        """檢查高槓桿風險"""
        print("🚨 高槓桿風險檢查 - 50倍槓桿")
        print("=" * 60)
        
        result = {
            'safe_to_proceed': False,
            'risk_level': 'EXTREME',
            'critical_warnings': [],
            'risk_factors': [],
            'safety_requirements': [],
            'liquidation_scenarios': [],
            'recommendations': []
        }
        
        try:
            # 載入配置
            config = ConfigValidator.load_and_validate_config(config_file)
            
            # 檢查槓桿設置
            leverage = getattr(config.trading, 'leverage', 1)
            margin_mode = getattr(config.trading, 'margin_mode', 'isolated')
            
            print(f"📊 配置檢查:")
            print(f"  槓桿倍數: {leverage}x")
            print(f"  保證金模式: {margin_mode}")
            
            # 關鍵風險檢查
            self._check_leverage_risks(result, leverage)
            self._check_margin_mode_risks(result, margin_mode)
            self._calculate_liquidation_scenarios(result, leverage)
            self._generate_safety_requirements(result, leverage)
            self._provide_risk_mitigation(result)
            
            # 最終安全評估
            critical_count = len(result['critical_warnings'])
            if critical_count == 0:
                result['safe_to_proceed'] = True
            
            return result
            
        except Exception as e:
            result['critical_warnings'].append(f"配置檢查失敗: {e}")
            return result
    
    def _check_leverage_risks(self, result: Dict, leverage: int):
        """檢查槓桿風險"""
        print(f"\n🔍 槓桿風險分析:")
        
        if leverage >= 50:
            result['critical_warnings'].extend([
                "🚨 極高槓桿風險: 50倍槓桿",
                "🚨 強制平倉風險: 價格波動2%即可能爆倉",
                "🚨 資金損失風險: 可能在數分鐘內損失全部資金"
            ])
            print(f"  ❌ 極高風險: {leverage}x槓桿")
            
        elif leverage >= 20:
            result['risk_factors'].append(f"高槓桿風險: {leverage}x")
            print(f"  ⚠️ 高風險: {leverage}x槓桿")
            
        elif leverage >= 10:
            result['risk_factors'].append(f"中等槓桿風險: {leverage}x")
            print(f"  ⚠️ 中等風險: {leverage}x槓桿")
            
        else:
            print(f"  ✅ 相對安全: {leverage}x槓桿")
        
        # 槓桿相關風險因子
        if leverage >= 20:
            result['risk_factors'].extend([
                "價格波動敏感性極高",
                "資金費率影響放大",
                "滑點成本放大",
                "情緒壓力極大"
            ])
    
    def _check_margin_mode_risks(self, result: Dict, margin_mode: str):
        """檢查保證金模式風險"""
        print(f"\n🔍 保證金模式分析:")
        
        if margin_mode == "cross":
            result['critical_warnings'].extend([
                "🚨 全倉模式風險: 所有資金都可能損失",
                "🚨 連鎖反應風險: 一個交易對爆倉影響全部資金"
            ])
            print(f"  ❌ 極高風險: 全倉模式")
            
            result['risk_factors'].extend([
                "全倉模式下無風險隔離",
                "單一交易對可能導致全部資金損失",
                "無法控制單個交易對的最大損失"
            ])
            
        else:
            print(f"  ✅ 相對安全: 逐倉模式")
    
    def _calculate_liquidation_scenarios(self, result: Dict, leverage: int):
        """計算強制平倉場景"""
        print(f"\n📊 強制平倉場景分析:")
        
        # 計算強制平倉價格距離
        liquidation_distance = 1 / leverage * 0.9  # 考慮維持保證金
        
        scenarios = [
            {
                'scenario': 'BTC價格下跌',
                'liquidation_distance': f"{liquidation_distance:.2%}",
                'example': f"BTC從$50,000跌到${50000 * (1 - liquidation_distance):.0f}"
            },
            {
                'scenario': 'ETH價格下跌', 
                'liquidation_distance': f"{liquidation_distance:.2%}",
                'example': f"ETH從$3,000跌到${3000 * (1 - liquidation_distance):.0f}"
            },
            {
                'scenario': '兩個資產同向波動',
                'liquidation_distance': f"{liquidation_distance/2:.2%}",
                'example': "配對交易最危險的情況"
            }
        ]
        
        result['liquidation_scenarios'] = scenarios
        
        for scenario in scenarios:
            print(f"  🎯 {scenario['scenario']}: {scenario['liquidation_distance']} 波動即爆倉")
            print(f"     例子: {scenario['example']}")
        
        if liquidation_distance < 0.05:  # 小於5%
            result['critical_warnings'].append(
                f"🚨 極危險: 僅需{liquidation_distance:.1%}價格波動即強制平倉"
            )
    
    def _generate_safety_requirements(self, result: Dict, leverage: int):
        """生成安全要求"""
        print(f"\n🛡️ 安全要求:")
        
        requirements = [
            "✅ 24/7實時監控系統",
            "✅ 自動止損機制",
            "✅ 緊急平倉程序",
            "✅ 充足的保證金緩衝",
            "✅ 快速網絡連接",
            "✅ 備用資金準備",
            "✅ 心理承受能力",
            "✅ 豐富的交易經驗"
        ]
        
        result['safety_requirements'] = requirements
        
        for req in requirements:
            print(f"  {req}")
        
        # 資金要求
        min_buffer = leverage * 0.1  # 槓桿倍數的10%作為緩衝
        result['safety_requirements'].append(
            f"💰 建議保證金緩衝: {min_buffer:.0f}%以上"
        )
        print(f"  💰 建議保證金緩衝: {min_buffer:.0f}%以上")
    
    def _provide_risk_mitigation(self, result: Dict):
        """提供風險緩解建議"""
        print(f"\n💡 風險緩解建議:")
        
        mitigations = [
            "🔄 從小額開始測試（如$100）",
            "⏰ 設置極短的止損（0.5%）",
            "📱 啟用所有警報通知",
            "🤖 使用自動風險管理",
            "📊 實時監控相關性變化",
            "⚡ 準備緊急平倉腳本",
            "💰 只使用可承受全部損失的資金",
            "🧠 保持冷靜，避免情緒交易"
        ]
        
        result['recommendations'] = mitigations
        
        for mitigation in mitigations:
            print(f"  {mitigation}")
    
    def generate_risk_acknowledgment(self) -> str:
        """生成風險確認書"""
        acknowledgment = """
🚨 高槓桿交易風險確認書 🚨

我確認並理解以下風險：

1. 💀 資金損失風險
   - 50倍槓桿可能導致快速且巨大的損失
   - 可能在數分鐘內損失全部資金
   - 損失可能超過初始投資

2. 🔥 強制平倉風險
   - 價格波動2%即可能觸發強制平倉
   - 全倉模式下所有資金都有風險
   - 市場波動可能導致無法控制的損失

3. ⚡ 技術風險
   - 網絡延遲可能導致無法及時平倉
   - 系統故障可能導致巨大損失
   - 交易所風險和流動性風險

4. 🧠 心理風險
   - 高槓桿交易壓力極大
   - 可能導致情緒化決策
   - 影響日常生活和健康

我確認：
✅ 我有豐富的交易經驗
✅ 我能承受全部資金損失
✅ 我有24/7監控能力
✅ 我理解配對交易的特殊風險
✅ 我已準備好緊急應對措施

簽名確認: ________________
日期: ________________
"""
        return acknowledgment


def main():
    """主函數"""
    import argparse
    
    parser = argparse.ArgumentParser(description="高槓桿風險檢查")
    parser.add_argument('--config', required=True, help='配置文件路徑')
    parser.add_argument('--acknowledge-risks', action='store_true', help='確認理解風險')
    
    args = parser.parse_args()
    
    checker = HighLeverageRiskChecker()
    
    # 執行風險檢查
    result = checker.check_high_leverage_risks(args.config)
    
    print(f"\n" + "=" * 60)
    print("🚨 風險檢查結果")
    print("=" * 60)
    
    print(f"風險等級: {result['risk_level']}")
    print(f"安全繼續: {'✅ 是' if result['safe_to_proceed'] else '❌ 否'}")
    
    if result['critical_warnings']:
        print(f"\n🚨 關鍵警告:")
        for warning in result['critical_warnings']:
            print(f"  {warning}")
    
    if result['liquidation_scenarios']:
        print(f"\n📊 強制平倉場景:")
        for scenario in result['liquidation_scenarios']:
            print(f"  {scenario['scenario']}: {scenario['liquidation_distance']}")
    
    # 生成風險確認書
    if args.acknowledge_risks:
        acknowledgment = checker.generate_risk_acknowledgment()
        with open('risk_acknowledgment.txt', 'w', encoding='utf-8') as f:
            f.write(acknowledgment)
        print(f"\n📄 風險確認書已生成: risk_acknowledgment.txt")
    
    print(f"\n⚠️ 重要提醒:")
    print(f"高槓桿交易風險極大，請謹慎考慮！")
    print(f"建議先在低槓桿下充分測試策略！")
    
    return 0 if result['safe_to_proceed'] else 1


if __name__ == "__main__":
    sys.exit(main())
