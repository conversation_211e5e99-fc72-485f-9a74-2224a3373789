#!/usr/bin/env python3
"""
統一投組管理器測試
Test for Unified Portfolio Manager
"""

import unittest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock

from unified_portfolio_manager import UnifiedPortfolioManager, get_unified_portfolio_manager
from global_event_bus import Event, EventType
from strategy_framework import BaseStrategy, StrategyType, TradingSignal


class MockStrategy(BaseStrategy):
    """模擬策略用於測試"""

    def __init__(self, strategy_id: str, health_score: float = 0.5):
        self.strategy_id = strategy_id
        self.health_score = health_score

    def get_strategy_type(self) -> StrategyType:
        """獲取策略類型"""
        return StrategyType.PAIRS_TRADING

    def analyze_market(self, market_data):
        return []

    def calculate_position_size(self, signal, available_capital):
        return 0.1

    def get_current_positions(self):
        return {}

    def validate_signal(self, signal: TradingSignal, current_positions):
        """驗證信號"""
        return True

    def update_health_score(self, new_score):
        self.health_score = new_score


class TestUnifiedPortfolioManager(unittest.TestCase):
    """統一投組管理器測試類"""

    def setUp(self):
        """測試設置"""
        self.total_capital = 100000
        self.config = {
            'rebalance_frequency': 1,  # 1天用於測試
            'min_allocation': 0.05,
            'max_allocation': 0.4
        }
        self.manager = UnifiedPortfolioManager(self.total_capital, self.config)

        # 創建測試策略
        self.strategy_1 = MockStrategy("strategy_1", 0.8)
        self.strategy_2 = MockStrategy("strategy_2", 0.6)
        self.strategy_3 = MockStrategy("strategy_3", 0.7)

        # 添加測試策略
        self.manager.add_strategy(self.strategy_1, 0.3)
        self.manager.add_strategy(self.strategy_2, 0.4)
        self.manager.add_strategy(self.strategy_3, 0.3)
    
    def test_initialization(self):
        """測試初始化"""
        self.assertEqual(self.manager.total_capital, self.total_capital)
        self.assertEqual(len(self.manager.strategy_allocations), 3)
        self.assertFalse(self.manager.is_running)
        self.assertIsNone(self.manager.system_start_time)
        self.assertEqual(self.manager.rebalance_frequency, 1)
    
    def test_add_strategy(self):
        """測試添加策略"""
        # 添加新策略
        strategy_4 = MockStrategy("strategy_4", 0.5)
        self.manager.add_strategy(strategy_4, 0.2)

        self.assertEqual(len(self.manager.strategy_allocations), 4)
        self.assertIn("strategy_4", self.manager.strategy_allocations)

        allocation = self.manager.strategy_allocations["strategy_4"]
        self.assertEqual(allocation.target_allocation, 0.2)
        self.assertEqual(allocation.health_score, 0.5)
    
    def test_optimal_allocation_calculation(self):
        """測試最優分配計算"""
        optimal_allocation = self.manager.calculate_optimal_allocation()
        
        # 驗證分配結果
        self.assertIsInstance(optimal_allocation, dict)
        self.assertEqual(len(optimal_allocation), 3)
        
        # 驗證分配總和接近1
        total_allocation = sum(optimal_allocation.values())
        self.assertAlmostEqual(total_allocation, 1.0, places=2)
        
        # 驗證每個策略的分配在合理範圍內
        for strategy_id, allocation in optimal_allocation.items():
            self.assertGreaterEqual(allocation, self.config['min_allocation'])
            self.assertLessEqual(allocation, self.config['max_allocation'])
    
    def test_health_score_impact_on_allocation(self):
        """測試健康分數對分配的影響"""
        # 設置不同的健康分數
        self.manager.strategy_allocations["strategy_1"].health_score = 0.9  # 高健康分數
        self.manager.strategy_allocations["strategy_2"].health_score = 0.3  # 低健康分數
        self.manager.strategy_allocations["strategy_3"].health_score = 0.6  # 中等健康分數
        
        optimal_allocation = self.manager.calculate_optimal_allocation()
        
        # 高健康分數的策略應該獲得更多分配
        self.assertGreater(optimal_allocation["strategy_1"], optimal_allocation["strategy_2"])
    
    def test_correlation_penalty(self):
        """測試相關性懲罰"""
        # 模擬高相關性情況
        with patch.object(self.manager, '_calculate_correlation_penalty') as mock_penalty:
            mock_penalty.return_value = 0.8  # 高相關性懲罰
            
            optimal_allocation = self.manager.calculate_optimal_allocation()
            
            # 驗證相關性懲罰被調用
            mock_penalty.assert_called()
            
            # 驗證分配仍然有效
            self.assertIsInstance(optimal_allocation, dict)
            total_allocation = sum(optimal_allocation.values())
            self.assertAlmostEqual(total_allocation, 1.0, places=2)
    
    def test_system_overview(self):
        """測試系統概覽"""
        overview = self.manager.get_system_overview()
        
        # 驗證概覽結構
        self.assertIsNotNone(overview.timestamp)
        self.assertIsInstance(overview.system_stats, dict)
        self.assertIsInstance(overview.capital_overview, dict)
        self.assertIsInstance(overview.strategy_overview, list)
        self.assertIsInstance(overview.performance_metrics, dict)
        self.assertIsInstance(overview.risk_metrics, dict)
        
        # 驗證系統統計
        self.assertEqual(overview.system_stats['total_strategies'], 3)
        self.assertEqual(overview.system_stats['is_running'], False)
        
        # 驗證資金概覽
        self.assertEqual(overview.capital_overview['total_capital'], self.total_capital)
        
        # 驗證策略概覽
        self.assertEqual(len(overview.strategy_overview), 3)
    
    def test_health_data(self):
        """測試健康數據"""
        health_data = self.manager.get_health_data()
        
        # 驗證健康數據結構
        self.assertIn('status', health_data)
        self.assertIn('system_stats', health_data)
        self.assertIn('capital_overview', health_data)
        self.assertIn('strategy_count', health_data)
        self.assertIn('last_update', health_data)
        
        # 驗證狀態
        self.assertEqual(health_data['status'], 'stopped')  # 系統未啟動
        self.assertEqual(health_data['strategy_count'], 3)
    
    def test_allocation_concentration(self):
        """測試分配集中度計算"""
        concentration = self.manager._calculate_allocation_concentration()
        
        # 驗證集中度在合理範圍內
        self.assertGreaterEqual(concentration, 0.0)
        self.assertLessEqual(concentration, 1.0)
        
        # 測試極端情況：單一策略100%分配
        self.manager.strategy_allocations["strategy_1"].current_allocation = 1.0
        self.manager.strategy_allocations["strategy_2"].current_allocation = 0.0
        self.manager.strategy_allocations["strategy_3"].current_allocation = 0.0
        
        concentration = self.manager._calculate_allocation_concentration()
        self.assertAlmostEqual(concentration, 1.0, places=2)
    
    def test_should_rebalance(self):
        """測試重新平衡條件"""
        # 初始狀態應該需要重新平衡
        self.assertTrue(self.manager._should_rebalance())
        
        # 設置最近重新平衡時間
        self.manager.last_rebalance_time = datetime.now()
        self.assertFalse(self.manager._should_rebalance())
        
        # 設置過期的重新平衡時間
        self.manager.last_rebalance_time = datetime.now() - timedelta(days=2)
        self.assertTrue(self.manager._should_rebalance())


class TestUnifiedPortfolioManagerAsync(unittest.IsolatedAsyncioTestCase):
    """統一投組管理器異步測試類"""
    
    async def asyncSetUp(self):
        """異步測試設置"""
        self.total_capital = 100000
        self.config = {
            'rebalance_frequency': 1,
            'min_allocation': 0.05,
            'max_allocation': 0.4
        }
        self.manager = UnifiedPortfolioManager(self.total_capital, self.config)

        # 創建和添加測試策略
        self.strategy_1 = MockStrategy("strategy_1", 0.8)
        self.strategy_2 = MockStrategy("strategy_2", 0.6)

        self.manager.add_strategy(self.strategy_1, 0.3)
        self.manager.add_strategy(self.strategy_2, 0.4)
    
    async def test_start_stop_system(self):
        """測試系統啟動和停止"""
        # 測試啟動
        self.assertFalse(self.manager.is_running)
        
        with patch.object(self.manager.event_bus, 'publish', new_callable=AsyncMock) as mock_publish:
            await self.manager.start_system()
            
            self.assertTrue(self.manager.is_running)
            self.assertIsNotNone(self.manager.system_start_time)
            
            # 驗證啟動事件被發布
            mock_publish.assert_called()
            
        # 測試停止
        with patch.object(self.manager.event_bus, 'publish', new_callable=AsyncMock) as mock_publish:
            await self.manager.stop_system()
            
            self.assertFalse(self.manager.is_running)
            
            # 驗證停止事件被發布
            mock_publish.assert_called()
    
    async def test_initial_allocation(self):
        """測試初始分配"""
        with patch.object(self.manager.event_bus, 'publish', new_callable=AsyncMock) as mock_publish:
            await self.manager._perform_initial_allocation()
            
            # 驗證分配事件被發布
            mock_publish.assert_called()
            
            # 驗證分配被應用
            for allocation in self.manager.strategy_allocations.values():
                self.assertGreater(allocation.allocated_capital, 0)
    
    async def test_rebalancing(self):
        """測試重新平衡"""
        # 設置需要重新平衡的條件
        self.manager.last_rebalance_time = None
        
        with patch.object(self.manager.event_bus, 'publish', new_callable=AsyncMock) as mock_publish:
            await self.manager._perform_rebalancing()
            
            # 驗證重新平衡時間被更新
            self.assertIsNotNone(self.manager.last_rebalance_time)
    
    async def test_strategy_health_changed_event(self):
        """測試策略健康變化事件處理"""
        # 創建健康變化事件
        event = Event(
            event_type=EventType.STRATEGY_HEALTH_CHANGED,
            source="test",
            data={
                'strategy_id': 'strategy_1',
                'old_health': 0.8,
                'new_health': 0.3  # 大幅下降
            },
            timestamp=datetime.now()
        )
        
        with patch.object(self.manager, '_perform_rebalancing', new_callable=AsyncMock) as mock_rebalance:
            await self.manager._on_strategy_health_changed(event)
            
            # 驗證觸發了重新平衡
            mock_rebalance.assert_called_once()
    
    async def test_order_filled_event(self):
        """測試訂單成交事件處理"""
        # 檢查初始狀態
        initial_strategy_1_history = len(self.manager.performance_history.get('strategy_1', []))

        # 創建訂單成交事件
        event = Event(
            event_type=EventType.ORDER_FILLED,
            source="test",
            data={
                'strategy_id': 'strategy_1',
                'pnl': 150.0
            },
            timestamp=datetime.now()
        )

        await self.manager._on_order_filled(event)

        # 驗證性能歷史被更新
        self.assertIn('strategy_1', self.manager.performance_history)
        self.assertEqual(len(self.manager.performance_history['strategy_1']), initial_strategy_1_history + 1)
        self.assertEqual(self.manager.performance_history['strategy_1'][-1], 150.0)
    
    async def test_risk_alert_event(self):
        """測試風險警報事件處理"""
        # 設置初始分配
        self.manager.strategy_allocations["strategy_1"].current_allocation = 0.4
        
        # 創建高風險警報事件
        event = Event(
            event_type=EventType.RISK_LIMIT_EXCEEDED,
            source="test",
            data={
                'strategy_id': 'strategy_1',
                'risk_type': 'high_drawdown',
                'severity': 'high'
            },
            timestamp=datetime.now()
        )
        
        with patch.object(self.manager.event_bus, 'publish', new_callable=AsyncMock) as mock_publish:
            await self.manager._on_risk_alert(event)
            
            # 驗證分配被減少
            new_allocation = self.manager.strategy_allocations["strategy_1"].current_allocation
            self.assertLess(new_allocation, 0.4)
            
            # 驗證緊急重新平衡事件被發布
            mock_publish.assert_called()


class TestGlobalInstance(unittest.TestCase):
    """測試全局實例"""
    
    def test_get_unified_portfolio_manager(self):
        """測試獲取統一投組管理器實例"""
        manager1 = get_unified_portfolio_manager(50000)
        manager2 = get_unified_portfolio_manager(100000)  # 不同參數
        
        # 應該返回同一個實例（單例模式）
        self.assertIs(manager1, manager2)
        
        # 第一次創建時的參數應該被保留
        self.assertEqual(manager1.total_capital, 50000)


if __name__ == '__main__':
    # 運行測試
    unittest.main(verbosity=2)
