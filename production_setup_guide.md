# 生產環境配置指南
# Production Environment Setup Guide

## 🚨 重要安全提醒

**在進行真實交易前，請務必閱讀並理解以下安全要求！**

## 📋 當前配置狀態

### 當前使用的交易所
- **交易所**: Binance（幣安）
- **模式**: 沙盒模式（測試環境）
- **API狀態**: 測試密鑰（不會進行真實交易）

## 🔧 配置真實交易的步驟

### 步驟1：獲取Binance API密鑰

1. **登錄Binance**
   - 訪問 https://www.binance.com
   - 登錄您的賬戶

2. **創建API密鑰**
   - 進入 "API管理"
   - 點擊 "創建API"
   - 設置API標籤（如：PairTradingBot）

3. **⚠️ 重要權限設置**
   ```
   ✅ 允許現貨交易
   ❌ 禁用期貨交易
   ❌ 禁用提現
   ❌ 禁用內部轉賬
   ```

4. **IP白名單（推薦）**
   - 添加您的服務器IP地址
   - 提高安全性

### 步驟2：更新環境變量

編輯 `.env` 文件：

```bash
# 交易所 API 配置 (生產環境)
TRADING_API_KEY=your_real_binance_api_key_here
TRADING_SECRET=your_real_binance_secret_here
TRADING_PASSPHRASE=  # Binance不需要

# 環境配置
ENVIRONMENT=production  # 改為production
```

### 步驟3：更新配置文件

編輯 `config.json`：

```json
{
    "exchange": {
        "name": "binance",
        "sandbox": false  // 改為false使用真實環境
    }
}
```

## 🛡️ 安全檢查清單

在啟用真實交易前，請確認：

- [ ] API密鑰只有現貨交易權限
- [ ] 已禁用提現權限
- [ ] 已設置IP白名單
- [ ] 已設置合理的倉位限制
- [ ] 已進行充分的回測
- [ ] 已在沙盒環境測試

## 🧪 測試流程

### 1. 沙盒測試（當前狀態）
```bash
# 驗證配置
python3 main_refactored.py validate --config config.json

# 健康檢查
python3 main_refactored.py health --config config.json

# 回測（推薦先進行）
python3 main_refactored.py backtest --config config.json \
    --start-date 2024-01-01 --end-date 2024-12-31
```

### 2. 生產環境測試
```bash
# 小額測試
python3 main_refactored.py live --config config.json --dry-run

# 真實交易（小心！）
python3 main_refactored.py live --config config.json
```

## ⚙️ 支持的交易所

當前系統支持以下交易所：

1. **Binance** ✅ (當前配置)
   - 全球最大交易所
   - 流動性好，手續費低
   - API穩定可靠

2. **OKX** ✅
   - 需要設置 TRADING_PASSPHRASE
   - 支持多種交易對

3. **Huobi** ✅
   - 亞洲主要交易所
   - 良好的API支持

4. **Bybit** ✅
   - 衍生品交易所
   - 現貨交易支持

## 🔄 切換交易所

如需切換到其他交易所，修改 `config.json`：

```json
{
    "exchange": {
        "name": "okex",  // 或 "huobi", "bybit"
        "sandbox": true  // 先在沙盒測試
    }
}
```

並相應更新 `.env` 文件中的API密鑰。

## 📊 風險管理設置

在 `config.json` 中設置風險參數：

```json
{
    "risk_management": {
        "max_position_size": 0.1,     // 最大倉位10%
        "stop_loss_pct": 0.02,        // 2%止損
        "max_daily_trades": 10,       // 每日最大交易次數
        "max_drawdown_pct": 0.05      // 最大回撤5%
    }
}
```

## 🚨 緊急停止

如需緊急停止交易：

```bash
# 方法1：優雅停止
Ctrl+C

# 方法2：強制停止
pkill -f main_refactored.py

# 方法3：通過健康檢查端點
curl -X POST http://localhost:8080/control -d '{"action":"stop"}'
```

## 📞 支持與幫助

如遇到問題：

1. 檢查日誌文件 `logs/`
2. 運行健康檢查 `python3 main_refactored.py health`
3. 查看配置驗證 `python3 main_refactored.py validate`

## ⚠️ 免責聲明

- 量化交易存在風險，可能導致資金損失
- 請只使用您能承受損失的資金
- 建議先進行充分的回測和小額測試
- 本軟件僅供學習和研究使用
