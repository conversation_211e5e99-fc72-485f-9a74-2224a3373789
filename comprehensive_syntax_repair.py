#!/usr/bin/env python3
"""
綜合語法修復工具
Comprehensive Syntax Repair Tool
"""

import ast
import re
from pathlib import Path
from typing import List, Dict, Tuple


class ComprehensiveSyntaxRepairer:
    """綜合語法修復器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.fixes_applied = 0
        
    def is_valid_python(self, content: str) -> bool:
        """檢查Python代碼是否語法正確"""
        try:
            ast.parse(content)
            return True
        except SyntaxError:
            return False
    
    def fix_common_syntax_patterns(self, content: str) -> str:
        """修復常見的語法模式錯誤"""
        new_content = content
        
        # 修復模式列表
        patterns = [
            # 修復不完整的函數調用
            (r'(\s+)if\s+\(\)\s*:', r'\1if True:'),
            (r'(\s+)elif\s+\(\)\s*:', r'\1elif True:'),
            (r'(\s+)while\s+\(\)\s*:', r'\1while True:'),
            
            # 修復不完整的函數定義
            (r'def\s+(\w+)\(\)\s*$', r'def \1():\n    pass'),
            
            # 修復不完整的類定義
            (r'class\s+(\w+).*:\s*$', r'class \1:\n    pass'),
            
            # 修復不完整的導入語句
            (r'from\s+([.\w]+)\s+import\s+\(\)\s*$', r'# from \1 import  # 修復不完整導入'),
            
            # 修復不完整的列表/字典
            (r'=\s*\[\s*$', r'= []'),
            (r'=\s*\{\s*$', r'= {}'),
            
            # 修復不完整的條件語句
            (r'if\s+any\(\[\s*$', r'if any(['),
            (r'if\s+all\(\[\s*$', r'if all(['),
            
            # 修復不完整的字符串格式化
            (r'f"([^"]*)"(?!\s*[\.\[\(])', r'f"\1"'),
            
            # 修復不完整的異常處理
            (r'raise\s+(\w+)\(\)\s*,\s*([^)]+)', r'raise \1(\2)'),
            
            # 修復不完整的參數列表
            (r'(\w+)\(\s*$', r'\1()'),
        ]
        
        for pattern, replacement in patterns:
            new_content = re.sub(pattern, replacement, new_content, flags=re.MULTILINE)
        
        return new_content
    
    def fix_parentheses_balance(self, content: str) -> str:
        """修復括號平衡問題"""
        lines = content.split('\n')
        new_lines = []
        
        for line in lines:
            # 檢查括號平衡
            open_parens = line.count('(')
            close_parens = line.count(')')
            
            if open_parens > close_parens:
                # 缺少閉合括號
                missing = open_parens - close_parens
                if line.rstrip().endswith(',') or line.rstrip().endswith('('):
                    # 如果行末是逗號或開括號，可能是多行語句
                    new_lines.append(line)
                else:
                    # 在行末添加缺少的閉合括號
                    new_lines.append(line + ')' * missing)
            elif close_parens > open_parens:
                # 多餘的閉合括號
                excess = close_parens - open_parens
                # 移除多餘的閉合括號
                new_line = line
                for _ in range(excess):
                    new_line = new_line.replace(')', '', 1)
                new_lines.append(new_line)
            else:
                new_lines.append(line)
        
        return '\n'.join(new_lines)
    
    def fix_incomplete_statements(self, content: str) -> str:
        """修復不完整的語句"""
        lines = content.split('\n')
        new_lines = []
        i = 0
        
        while i < len(lines):
            line = lines[i]
            stripped = line.strip()
            
            # 檢查不完整的多行語句
            if (stripped.endswith('(') or stripped.endswith(',') or 
                stripped.endswith('[') or stripped.endswith('{')):
                # 查找對應的閉合符號
                bracket_count = 0
                paren_count = 0
                brace_count = 0
                
                for char in stripped:
                    if char == '(': paren_count += 1
                    elif char == ')': paren_count -= 1
                    elif char == '[': bracket_count += 1
                    elif char == ']': bracket_count -= 1
                    elif char == '{': brace_count += 1
                    elif char == '}': brace_count -= 1
                
                # 如果有未閉合的符號，嘗試在後續行中找到閉合
                j = i + 1
                found_close = False
                while j < len(lines) and not found_close:
                    next_line = lines[j].strip()
                    if (paren_count > 0 and ')' in next_line) or \
                       (bracket_count > 0 and ']' in next_line) or \
                       (brace_count > 0 and '}' in next_line):
                        found_close = True
                    j += 1
                
                if not found_close:
                    # 添加適當的閉合符號
                    if paren_count > 0:
                        line += ')' * paren_count
                    if bracket_count > 0:
                        line += ']' * bracket_count
                    if brace_count > 0:
                        line += '}' * brace_count
            
            new_lines.append(line)
            i += 1
        
        return '\n'.join(new_lines)
    
    def repair_file(self, file_path: Path) -> bool:
        """修復單個文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            # 如果文件已經語法正確，跳過
            if self.is_valid_python(original_content):
                return False
            
            # 應用各種修復
            content = original_content
            content = self.fix_common_syntax_patterns(content)
            content = self.fix_parentheses_balance(content)
            content = self.fix_incomplete_statements(content)
            
            # 檢查修復後是否語法正確
            if content != original_content:
                # 嘗試解析修復後的代碼
                try:
                    ast.parse(content)
                    # 語法正確，保存文件
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    self.fixes_applied += 1
                    print(f"✅ 修復成功: {file_path}")
                    return True
                except SyntaxError as e:
                    print(f"⚠️  修復後仍有語法錯誤 {file_path}: {e}")
                    return False
            
            return False
            
        except Exception as e:
            print(f"❌ 修復失敗 {file_path}: {e}")
            return False
    
    def repair_all_files(self):
        """修復所有Python文件"""
        print("🔧 開始綜合語法修復...")
        
        # 查找所有Python文件
        python_files = []
        for pattern in ["*.py", "src/*.py", "tests/*.py"]:
            python_files.extend(self.project_root.glob(pattern))
        
        print(f"找到 {len(python_files)} 個Python文件")
        
        success_count = 0
        for file_path in python_files:
            if self.repair_file(file_path):
                success_count += 1
        
        print(f"✅ 綜合語法修復完成")
        print(f"   成功修復: {success_count} 個文件")
        print(f"   總計修復: {self.fixes_applied} 個問題")


def main():
    """主函數"""
    project_root = Path(__file__).parent
    repairer = ComprehensiveSyntaxRepairer(str(project_root))
    repairer.repair_all_files()


if __name__ == "__main__":
    main()
