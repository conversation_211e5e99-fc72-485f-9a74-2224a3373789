#!/usr/bin/env python3
"""
代碼質量自動修復工具
Automated Code Quality Fixer
"""

import ast
import re
from pathlib import Path
from typing import List


class CodeQualityFixer:
    """代碼質量自動修復器"""

    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.python_files = []
        self.fixes_applied = 0

    def find_python_files(self) -> List[Path]:
        """查找所有Python文件"""
        python_files = []

        # 掃描src目錄
        src_dir = self.project_root / "src"
        if src_dir.exists():
            python_files.extend(src_dir.glob("*.py"))

        # 掃描tests目錄
        tests_dir = self.project_root / "tests"
        if tests_dir.exists():
            python_files.extend(tests_dir.glob("*.py"))

        # 掃描根目錄的Python文件
        python_files.extend(self.project_root.glob("*.py"))

        return python_files

    def fix_unused_imports(self, file_path: Path) -> bool:
        """修復未使用的導入"""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            # 解析AST來檢測未使用的導入
            tree = ast.parse(content)

            # 收集所有導入和使用的名稱
            imports = {}
            used_names = set()

            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        name = alias.asname if alias.asname else alias.name
                        imports[name] = node
                elif isinstance(node, ast.ImportFrom):
                    for alias in node.names:
                        name = alias.asname if alias.asname else alias.name
                        imports[name] = node
                elif isinstance(node, ast.Name):
                    used_names.add(node.id)
                elif isinstance(node, ast.Attribute):
                    # 處理模塊.屬性的情況
                    if isinstance(node.value, ast.Name):
                        used_names.add(node.value.id)

            # 找出未使用的導入
            unused_imports = set(imports.keys()) - used_names

            if unused_imports:
                lines = content.split("\n")
                new_lines = []

                for line in lines:
                    should_remove = False
                    for unused in unused_imports:
                        # 檢查是否是未使用的導入行
                        if (
                            f"import {unused}" in line
                            or f"from {unused}" in line
                            or f", {unused}" in line
                            or f"{unused}," in line
                        ):
                            should_remove = True
                            break

                    if not should_remove:
                        new_lines.append(line)

                if len(new_lines) != len(lines):
                    with open(file_path, "w", encoding="utf-8") as f:
                        f.write("\n".join(new_lines))
                    self.fixes_applied += 1
                    return True

        except Exception as e:
            print(f"修復未使用導入失敗 {file_path}: {e}")

        return False

    def fix_f_string_placeholders(self, file_path: Path) -> bool:
        """修復f-string缺少占位符的問題"""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            # 查找沒有占位符的f-string
            pattern = r'"([^"]*)"'
            matches = re.finditer(pattern, content)

            changes_made = False
            new_content = content

            for match in matches:
                f_string = match.group(0)
                inner_content = match.group(1)

                # 檢查是否包含{}占位符
                if "{" not in inner_content and "}" not in inner_content:
                    # 將f-string轉換為普通字符串
                    replacement = f'"{inner_content}"'
                    new_content = new_content.replace(f_string, replacement, 1)
                    changes_made = True

            if changes_made:
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(new_content)
                self.fixes_applied += 1
                return True

        except Exception as e:
            print(f"修復f-string問題失敗 {file_path}: {e}")

        return False

    def fix_bare_except(self, file_path: Path) -> bool:
        """修復bare except問題"""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            # 替換bare except
            patterns = [
                (r"except Exception:", "except Exception:"),
                (r"except\s*:", "except Exception:"),
            ]

            new_content = content
            changes_made = False

            for pattern, replacement in patterns:
                if re.search(pattern, new_content):
                    new_content = re.sub(pattern, replacement, new_content)
                    changes_made = True

            if changes_made:
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(new_content)
                self.fixes_applied += 1
                return True

        except Exception as e:
            print(f"修復bare except問題失敗 {file_path}: {e}")

        return False

    def fix_unused_variables(self, file_path: Path) -> bool:
        """修復未使用變量問題"""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            # 簡單的未使用變量修復
            patterns = [
                # 將未使用的循環變量改為_
                (r"for\s+(\w+)\s+in\s+range\(", r"for _ in range("),
                (r"for\s+(\w+)\s+in\s+enumerate\(", r"for _, _ in enumerate("),
            ]

            new_content = content
            changes_made = False

            for pattern, replacement in patterns:
                if re.search(pattern, new_content):
                    new_content = re.sub(pattern, replacement, new_content)
                    changes_made = True

            if changes_made:
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(new_content)
                self.fixes_applied += 1
                return True

        except Exception as e:
            print(f"修復未使用變量問題失敗 {file_path}: {e}")

        return False

    def run_fixes(self):
        """運行所有修復"""
        print("🔧 開始自動修復代碼質量問題...")

        python_files = self.find_python_files()
        print(f"找到 {len(python_files)} 個Python文件")

        for file_path in python_files:
            print(f"處理: {file_path}")

            # 應用各種修復
            self.fix_f_string_placeholders(file_path)
            self.fix_bare_except(file_path)
            self.fix_unused_variables(file_path)

        print(f"✅ 修復完成，共應用 {self.fixes_applied} 個修復")


def main():
    """主函數"""
    project_root = Path(__file__).parent
    fixer = CodeQualityFixer(str(project_root))
    fixer.run_fixes()


if __name__ == "__main__":
    main()
