#!/usr/bin/env python3
"""
修復 health_server.py 中的語法錯誤
"""

import re
import sys

def fix_health_server():
    """修復 health_server.py 中的語法錯誤"""
    file_path = "src/health_server.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"🔧 開始修復 {file_path}...")
        
        # 修復模式1: return () jsonify() 調用
        pattern1 = r'return \(\)\s+jsonify\(\)\s*#[^\n]*\n\s+\{'
        replacement1 = r'return jsonify({'
        content = re.sub(pattern1, replacement1, content, flags=re.MULTILINE)
        
        # 修復模式2: 多行 jsonify 調用的結尾
        pattern2 = r'\}\s+\),\s+\d+,\s+\)'
        replacement2 = r'}), 200'
        content = re.sub(pattern2, replacement2, content, flags=re.MULTILINE)
        
        # 修復模式3: 不完整的函數調用
        pattern3 = r'jsonify\(\)\s*#[^\n]*\n\s+\{'
        replacement3 = r'jsonify({'
        content = re.sub(pattern3, replacement3, content, flags=re.MULTILINE)
        
        # 修復模式4: 多餘的縮進和括號
        pattern4 = r'\}\s+\),\s+\d+,\s+\)\s*$'
        replacement4 = r'}), 200'
        content = re.sub(pattern4, replacement4, content, flags=re.MULTILINE)
        
        # 修復模式5: 函數調用缺少開括號
        pattern5 = r'(\w+\.\w+)\(\)\s*#[^\n]*\n\s+"([^"]+)"'
        replacement5 = r'\1("\2"'
        content = re.sub(pattern5, replacement5, content, flags=re.MULTILINE)
        
        # 修復模式6: 多行函數調用的參數
        pattern6 = r'send_critical_alert\(\)\s*#[^\n]*\n\s+"([^"]+)", "([^"]+)", \{([^}]+)\}\s+\)'
        replacement6 = r'send_critical_alert("\1", "\2", {\3})'
        content = re.sub(pattern6, replacement6, content, flags=re.MULTILINE)
        
        # 寫回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ {file_path} 修復完成")
        return True
        
    except Exception as e:
        print(f"❌ 修復 {file_path} 失敗: {e}")
        return False

def validate_syntax(file_path):
    """驗證文件語法"""
    try:
        import py_compile
        py_compile.compile(file_path, doraise=True)
        print(f"✅ {file_path} 語法檢查通過")
        return True
    except py_compile.PyCompileError as e:
        print(f"❌ {file_path} 語法錯誤: {e}")
        return False

if __name__ == "__main__":
    print("🚀 開始修復 health_server.py...")
    
    if fix_health_server():
        if validate_syntax("src/health_server.py"):
            print("🎉 修復成功！")
            sys.exit(0)
        else:
            print("⚠️ 修復後仍有語法錯誤，需要手動檢查")
            sys.exit(1)
    else:
        print("💥 修復失敗")
        sys.exit(1)
