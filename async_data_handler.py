#!/usr/bin/env python3
"""
異步數據處理器 - 解決高負載性能問題
Async Data Handler - Solve high-load performance issues
"""

import asyncio
import aiohttp
import ccxt.pro as ccxtpro
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
import time
from concurrent.futures import ThreadPoolExecutor
import json

from logging_config import get_logger
from enhanced_retry_handler import enhanced_async_retry, RetryStrategy
from performance_optimization.vectorized_calculations import get_vectorized_calculator

logger = get_logger(__name__)


class AsyncDataHandler:
    """異步數據處理器 - 高性能並發數據獲取和處理"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.trading_pairs = config.get('trading_pairs', [])
        self.timeframe = config.get('timeframe', '5m')
        self.lookback_period = config.get('lookback_period', 100)
        
        # 異步交易所連接
        self.exchange = None
        self.session = None
        
        # 性能優化組件
        self.vectorized_calculator = get_vectorized_calculator()
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # 數據緩存
        self.data_cache = {}
        self.cache_ttl = 60  # 緩存60秒
        self.last_update = {}
        
        # 並發控制
        self.semaphore = asyncio.Semaphore(10)  # 限制並發請求數
        self.rate_limiter = asyncio.Semaphore(5)  # 速率限制
        
        logger.info("AsyncDataHandler 初始化完成")
    
    async def initialize(self):
        """初始化異步組件"""
        try:
            # 初始化異步交易所
            exchange_id = self.config.get('exchange', 'binance')
            exchange_class = getattr(ccxtpro, exchange_id)
            
            self.exchange = exchange_class({
                'apiKey': self.config.get('api_key', ''),
                'secret': self.config.get('secret', ''),
                'sandbox': self.config.get('sandbox', True),
                'enableRateLimit': True,
                'timeout': 30000,
            })
            
            # 初始化HTTP會話
            timeout = aiohttp.ClientTimeout(total=30)
            self.session = aiohttp.ClientSession(timeout=timeout)
            
            logger.info("異步組件初始化完成")
            
        except Exception as e:
            logger.error(f"異步組件初始化失敗: {e}")
            raise
    
    async def cleanup(self):
        """清理資源"""
        try:
            if self.exchange:
                await self.exchange.close()
            
            if self.session:
                await self.session.close()
            
            self.executor.shutdown(wait=True)
            
            logger.info("異步組件清理完成")
            
        except Exception as e:
            logger.error(f"清理資源失敗: {e}")
    
    @enhanced_async_retry(
        max_retries=3,
        base_delay=1.0,
        max_delay=30.0,
        strategy=RetryStrategy.EXPONENTIAL,
        timeout=30.0
    )
    async def fetch_ohlcv_async(self, symbol: str, limit: int = None) -> pd.DataFrame:
        """異步獲取OHLCV數據"""
        async with self.semaphore:  # 控制並發
            try:
                if limit is None:
                    limit = self.lookback_period + 50
                
                # 檢查緩存
                cache_key = f"{symbol}_{self.timeframe}_{limit}"
                if self._is_cache_valid(cache_key):
                    logger.debug(f"使用緩存數據: {symbol}")
                    return self.data_cache[cache_key]
                
                # 異步獲取數據
                ohlcv = await self.exchange.fetch_ohlcv(symbol, self.timeframe, limit=limit)
                
                if not ohlcv:
                    raise Exception(f"獲取 {symbol} 數據為空")
                
                # 在線程池中處理數據轉換（CPU密集型）
                df = await asyncio.get_event_loop().run_in_executor(
                    self.executor, self._process_ohlcv_data, ohlcv
                )
                
                # 更新緩存
                self.data_cache[cache_key] = df
                self.last_update[cache_key] = time.time()
                
                logger.debug(f"異步獲取 {symbol} 數據成功，共 {len(df)} 條記錄")
                return df
                
            except Exception as e:
                logger.error(f"異步獲取 {symbol} 數據失敗: {e}")
                raise
    
    def _process_ohlcv_data(self, ohlcv: List) -> pd.DataFrame:
        """處理OHLCV數據（在線程池中執行）"""
        df = pd.DataFrame(
            ohlcv, 
            columns=['timestamp', 'open', 'high', 'low', 'close', 'volume']
        )
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        df.set_index('timestamp', inplace=True)
        return df
    
    async def fetch_multiple_ohlcv_async(self, symbols: List[str]) -> Dict[str, pd.DataFrame]:
        """並發獲取多個交易對的OHLCV數據"""
        try:
            start_time = time.time()
            
            # 創建並發任務
            tasks = []
            for symbol in symbols:
                task = asyncio.create_task(
                    self.fetch_ohlcv_async(symbol),
                    name=f"fetch_{symbol}"
                )
                tasks.append((symbol, task))
            
            # 等待所有任務完成
            results = {}
            completed_tasks = await asyncio.gather(
                *[task for _, task in tasks], 
                return_exceptions=True
            )
            
            # 處理結果
            for (symbol, _), result in zip(tasks, completed_tasks):
                if isinstance(result, Exception):
                    logger.error(f"獲取 {symbol} 數據失敗: {result}")
                    results[symbol] = pd.DataFrame()
                else:
                    results[symbol] = result
            
            elapsed_time = time.time() - start_time
            success_count = sum(1 for df in results.values() if not df.empty)
            
            logger.info(f"並發獲取 {len(symbols)} 個交易對數據完成，"
                       f"成功 {success_count} 個，耗時 {elapsed_time:.2f} 秒")
            
            return results
            
        except Exception as e:
            logger.error(f"並發獲取數據失敗: {e}")
            return {}
    
    @enhanced_async_retry(
        max_retries=3,
        base_delay=0.5,
        max_delay=10.0,
        strategy=RetryStrategy.EXPONENTIAL,
        timeout=15.0
    )
    async def fetch_ticker_async(self, symbol: str) -> Dict:
        """異步獲取ticker數據"""
        async with self.rate_limiter:  # 速率限制
            try:
                ticker = await self.exchange.fetch_ticker(symbol)
                
                if not ticker or 'last' not in ticker:
                    raise Exception(f"獲取 {symbol} ticker 數據無效")
                
                return ticker
                
            except Exception as e:
                logger.error(f"異步獲取 {symbol} ticker 失敗: {e}")
                raise
    
    async def fetch_multiple_tickers_async(self, symbols: List[str]) -> Dict[str, Dict]:
        """並發獲取多個交易對的ticker數據"""
        try:
            start_time = time.time()
            
            # 創建並發任務
            tasks = [
                asyncio.create_task(self.fetch_ticker_async(symbol), name=f"ticker_{symbol}")
                for symbol in symbols
            ]
            
            # 等待所有任務完成
            results = {}
            completed_tasks = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 處理結果
            for symbol, result in zip(symbols, completed_tasks):
                if isinstance(result, Exception):
                    logger.error(f"獲取 {symbol} ticker 失敗: {result}")
                    results[symbol] = {}
                else:
                    results[symbol] = result
            
            elapsed_time = time.time() - start_time
            success_count = sum(1 for ticker in results.values() if ticker)
            
            logger.info(f"並發獲取 {len(symbols)} 個ticker完成，"
                       f"成功 {success_count} 個，耗時 {elapsed_time:.2f} 秒")
            
            return results
            
        except Exception as e:
            logger.error(f"並發獲取ticker失敗: {e}")
            return {}
    
    async def calculate_features_async(self, price_data: Dict[str, pd.DataFrame], 
                                     pairs: List[List[str]]) -> Dict:
        """異步計算特徵（CPU密集型任務在線程池中執行）"""
        try:
            start_time = time.time()
            
            # 在線程池中並行計算特徵
            tasks = []
            for pair in pairs:
                task = asyncio.get_event_loop().run_in_executor(
                    self.executor,
                    self._calculate_pair_features,
                    price_data,
                    pair
                )
                tasks.append((pair, task))
            
            # 等待所有計算完成
            results = {}
            completed_tasks = await asyncio.gather(
                *[task for _, task in tasks],
                return_exceptions=True
            )
            
            # 處理結果
            for (pair, _), result in zip(tasks, completed_tasks):
                pair_key = f"{pair[0]}-{pair[1]}"
                if isinstance(result, Exception):
                    logger.error(f"計算 {pair_key} 特徵失敗: {result}")
                    results[pair_key] = {}
                else:
                    results[pair_key] = result
            
            elapsed_time = time.time() - start_time
            logger.info(f"並行計算 {len(pairs)} 個配對特徵完成，耗時 {elapsed_time:.2f} 秒")
            
            return results
            
        except Exception as e:
            logger.error(f"異步計算特徵失敗: {e}")
            return {}
    
    def _calculate_pair_features(self, price_data: Dict[str, pd.DataFrame], 
                               pair: List[str]) -> Dict:
        """計算單個配對的特徵（在線程池中執行）"""
        try:
            base_symbol, quote_symbol = pair
            
            if base_symbol not in price_data or quote_symbol not in price_data:
                return {}
            
            base_df = price_data[base_symbol]
            quote_df = price_data[quote_symbol]
            
            if base_df.empty or quote_df.empty:
                return {}
            
            # 對齊數據
            common_index = base_df.index.intersection(quote_df.index)
            if len(common_index) < 20:
                return {}
            
            base_prices = base_df.loc[common_index, 'close'].values
            quote_prices = quote_df.loc[common_index, 'close'].values
            
            # 使用向量化計算器
            spread_stats = self.vectorized_calculator.calculate_spread_statistics_vectorized(
                base_prices, quote_prices, window=20
            )
            
            base_indicators = self.vectorized_calculator.calculate_technical_indicators_vectorized(
                base_prices,
                high=base_df.loc[common_index, 'high'].values,
                low=base_df.loc[common_index, 'low'].values,
                volume=base_df.loc[common_index, 'volume'].values
            )
            
            quote_indicators = self.vectorized_calculator.calculate_technical_indicators_vectorized(
                quote_prices,
                high=quote_df.loc[common_index, 'high'].values,
                low=quote_df.loc[common_index, 'low'].values,
                volume=quote_df.loc[common_index, 'volume'].values
            )
            
            return {
                'spread_statistics': spread_stats,
                'base_indicators': base_indicators,
                'quote_indicators': quote_indicators,
                'data_points': len(common_index)
            }
            
        except Exception as e:
            logger.error(f"計算配對 {pair} 特徵失敗: {e}")
            return {}
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """檢查緩存是否有效"""
        if cache_key not in self.data_cache:
            return False
        
        if cache_key not in self.last_update:
            return False
        
        return time.time() - self.last_update[cache_key] < self.cache_ttl
    
    def clear_cache(self):
        """清理緩存"""
        self.data_cache.clear()
        self.last_update.clear()
        logger.info("數據緩存已清理")
    
    async def get_performance_stats(self) -> Dict:
        """獲取性能統計"""
        return {
            'cache_size': len(self.data_cache),
            'cache_hit_rate': self._calculate_cache_hit_rate(),
            'active_tasks': len([task for task in asyncio.all_tasks() if not task.done()]),
            'executor_threads': self.executor._threads,
            'semaphore_available': self.semaphore._value,
            'rate_limiter_available': self.rate_limiter._value
        }
    
    def _calculate_cache_hit_rate(self) -> float:
        """計算緩存命中率"""
        # 簡化實現，實際應該跟蹤請求統計
        return 0.8 if self.data_cache else 0.0


# 全局異步數據處理器實例
_async_data_handler = None

async def get_async_data_handler(config: Dict) -> AsyncDataHandler:
    """獲取全局異步數據處理器實例"""
    global _async_data_handler
    if _async_data_handler is None:
        _async_data_handler = AsyncDataHandler(config)
        await _async_data_handler.initialize()
    return _async_data_handler


if __name__ == "__main__":
    # 測試異步數據處理器
    async def test_async_data_handler():
        config = {
            'exchange': 'binance',
            'trading_pairs': ['BTC/USDT', 'ETH/USDT', 'ADA/USDT', 'DOT/USDT'],
            'timeframe': '5m',
            'lookback_period': 100,
            'sandbox': True
        }
        
        handler = AsyncDataHandler(config)
        await handler.initialize()
        
        try:
            # 測試並發獲取數據
            symbols = ['BTC/USDT', 'ETH/USDT', 'ADA/USDT']
            
            start_time = time.time()
            data = await handler.fetch_multiple_ohlcv_async(symbols)
            elapsed_time = time.time() - start_time
            
            print(f"並發獲取 {len(symbols)} 個交易對數據耗時: {elapsed_time:.2f} 秒")
            
            # 測試特徵計算
            pairs = [['BTC/USDT', 'ETH/USDT'], ['BTC/USDT', 'ADA/USDT']]
            features = await handler.calculate_features_async(data, pairs)
            
            print(f"計算特徵完成，配對數: {len(features)}")
            
            # 獲取性能統計
            stats = await handler.get_performance_stats()
            print(f"性能統計: {stats}")
            
        finally:
            await handler.cleanup()
    
    # 運行測試
    asyncio.run(test_async_data_handler())
