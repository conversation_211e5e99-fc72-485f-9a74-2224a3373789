#!/bin/bash

# 企業級智能量化交易系統 - 重啟腳本
# Enterprise Intelligent Trading System - Restart Script

echo "🔄 重啟企業級智能量化交易系統..."
echo "=================================================================="

# 停止系統
echo "第一步: 停止當前系統"
./stop_trading.sh

echo ""
echo "等待 3 秒..."
sleep 3

# 啟動系統
echo "第二步: 啟動系統"
./start_trading.sh

echo ""
echo "=================================================================="
echo "🎉 系統重啟完成！"
