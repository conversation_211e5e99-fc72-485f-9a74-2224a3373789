import logging
from typing import Any, Dict, List, Optional

# 配置logger
logger = logging.getLogger(__name__)

#!/usr/bin/env python3
"""
策略框架 - 抽象化策略接口，支持多策略架構
Strategy Framework - Abstract strategy interface for multi-strategy architecture
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime
from enum import Enum

import pandas as pd

from logging_config import get_logger

_ = get_logger(__name__)


class StrategyType(Enum):
    """策略類型枚舉"""

    _ = "pairs_trading"
    _ = "trend_following"
    _ = "arbitrage"
    _ = "mean_reversion"
    _ = "momentum"
    _ = "market_making"


class SignalType(Enum):
    """信號類型枚舉"""

    _ = "buy"
    _ = "sell"
    _ = "hold"
    _ = "close_long"
    _ = "close_short"
    _ = "close_all"


class SignalStrength(Enum):
    """信號強度枚舉"""

    _ = 0.3
    _ = 0.6
    STRONG = 0.8
    _ = 1.0


@dataclass
class TradingSignal:
    """交易信號"""

    strategy_id: str
    signal_type: SignalType
    strength: float  # 0.0 - 1.0
    symbols: List[str]  # 涉及的交易對
    confidence: float  # 0.0 - 1.0
    metadata: Dict[str, Any]  # 額外信息
    timestamp: datetime
    expiry: Optional[datetime] = None  # 信號過期時間

    def is_valid(self) -> bool:
        """檢查信號是否仍然有效"""
        if self.expiry is None:
            return True
        return datetime.now() < self.expiry

    def is_strong_enough(self, threshold: float = 0.5) -> bool:
        """檢查信號強度是否足夠"""
        return self.strength >= threshold


@dataclass
class StrategyPerformance:
    """策略績效指標"""

    strategy_id: str
    total_trades: int
    winning_trades: int
    losing_trades: int
    total_pnl: float
    max_drawdown: float
    sharpe_ratio: float
    win_rate: float
    avg_win: float
    avg_loss: float
    profit_factor: float
    last_updated: datetime


class BaseStrategy(ABC):
    """基礎策略抽象類"""

    def __init__(self, strategy_id: str, config: Dict[str, Any]):
        """
        初始化策略

        Args:
            strategy_id: 策略唯一標識
            config: 策略配置
        """
        self.strategy_id = strategy_id
        self.config = config
        self.is_active = True
        self.performance = StrategyPerformance(
            strategy_id=strategy_id,
            total_trades=0,
            winning_trades=0,
            losing_trades=0,
            total_pnl=0.0,
            max_drawdown=0.0,
            sharpe_ratio=0.0,
            win_rate=0.0,
            avg_win=0.0,
            avg_loss=0.0,
            profit_factor=0.0,
            last_updated=datetime.now(),
        )

        logger.info(f"策略初始化: {strategy_id} ({self.get_strategy_type().value})")

    @abstractmethod
    def get_strategy_type(self) -> StrategyType:
        """獲取策略類型"""
        pass

    @abstractmethod
    def analyze_market(self, market_data: Dict[str, pd.DataFrame]) -> List[TradingSignal]:
        """
        分析市場數據並生成交易信號

        Args:
            market_data: 市場數據，格式為 {symbol: DataFrame}

        Returns:
            List[TradingSignal]: 交易信號列表
        """
        pass

    @abstractmethod
    def calculate_position_size(
        self, signal: TradingSignal, available_capital: float
    ) -> Dict[str, float]:
        """
        計算倉位大小

        Args:
            signal: 交易信號
            available_capital: 可用資金

        Returns:
            Dict[str, float]: {symbol: position_size}
        """
        pass

    @abstractmethod
    def validate_signal(self, signal: TradingSignal, current_positions: Dict[str, float]) -> bool:
        """
        驗證信號是否可執行

        Args:
            signal: 交易信號
            current_positions: 當前持倉

        Returns:
            bool: 是否可執行
        """
        pass

    def update_performance(self, trade_result: Dict[str, Any]):
        """更新策略績效"""
        try:
            pnl = trade_result.get("pnl", 0.0)
            self.performance.total_trades += 1
            self.performance.total_pnl += pnl

            if pnl > 0:
                self.performance.winning_trades += 1
                self.performance.avg_win = (
                    self.performance.avg_win * (self.performance.winning_trades - 1) + pnl
                ) / self.performance.winning_trades
            else:
                self.performance.losing_trades += 1
                self.performance.avg_loss = (
                    self.performance.avg_loss * (self.performance.losing_trades - 1) + abs(pnl)
                ) / self.performance.losing_trades

            # 更新勝率
            self.performance.win_rate = (
                self.performance.winning_trades / self.performance.total_trades
            )

            # 更新盈虧比
            if self.performance.avg_loss > 0:
                self.performance.profit_factor = (
                    self.performance.avg_win / self.performance.avg_loss
                )

            self.performance.last_updated = datetime.now()

            logger.debug(
                f"策略 {self.strategy_id} 績效更新: PnL={pnl:.2f}, 勝率={self.performance.win_rate:.2%}"
            )

        except Exception as e:
            logger.error(f"更新策略績效失敗: {e}")

    def get_health_score(self) -> float:
        """
        計算策略健康分數 (0-1)

        Returns:
            float: 健康分數
        """
        try:
            if self.performance.total_trades < 10:
                return 0.5  # 交易次數不足，中性分數

            # 綜合評分因子
            win_rate_score = self.performance.win_rate
            pnl_score = min(max(self.performance.total_pnl / 1000, 0), 1)  # 標準化PnL
            profit_factor_score = min(self.performance.profit_factor / 2, 1)

            # 加權平均
            health_score = win_rate_score * 0.4 + pnl_score * 0.3 + profit_factor_score * 0.3

            return min(max(health_score, 0), 1)

        except Exception as e:
            logger.error(f"計算健康分數失敗: {e}")
            return 0.0

    def should_pause(self) -> bool:
        """判斷策略是否應該暫停"""
        health_score = self.get_health_score()
        consecutive_losses = self._get_consecutive_losses()

        # 暫停條件
        if health_score < 0.3:
            logger.warning(f"策略 {self.strategy_id} 健康分數過低: {health_score:.2f}")
            return True

        if consecutive_losses >= 5:
            logger.warning(f"策略 {self.strategy_id} 連續虧損: {consecutive_losses}")
            return True

        return False

    def _get_consecutive_losses(self) -> int:
        """獲取連續虧損次數（簡化實現）"""
        # 這裡應該實現實際的連續虧損計算邏輯
        # 暫時返回0作為佔位符
        return 0

    def get_required_symbols(self) -> List[str]:
        """獲取策略需要的交易對"""
        return self.config.get("symbols", [])

    def get_timeframes(self) -> List[str]:
        """獲取策略需要的時間框架"""
        return self.config.get("timeframes", ["5m"])

    def get_config_summary(self) -> Dict[str, Any]:
        """獲取配置摘要"""
        return {
            "strategy_id": self.strategy_id,
            "strategy_type": self.get_strategy_type().value,
            "is_active": self.is_active,
            "symbols": self.get_required_symbols(),
            "timeframes": self.get_timeframes(),
            "health_score": self.get_health_score(),
        }


class StrategyManager:
    """策略管理器"""

    def __init__(self):
        self.strategies: Dict[str, BaseStrategy] = {}
        self.active_strategies: List[str] = []

    def register_strategy(self, strategy: BaseStrategy):
        """註冊策略"""
        self.strategies[strategy.strategy_id] = strategy
        if strategy.is_active:
            self.active_strategies.append(strategy.strategy_id)

        logger.info(f"策略已註冊: {strategy.strategy_id}")

    def get_strategy(self, strategy_id: str) -> Optional[BaseStrategy]:
        """獲取策略"""
        return self.strategies.get(strategy_id)

    def get_active_strategies(self) -> List[BaseStrategy]:
        """獲取活躍策略"""
        return [
            self.strategies[sid]
            for sid in self.active_strategies
            if sid in self.strategies and self.strategies[sid].is_active
        ]

    def pause_strategy(self, strategy_id: str):
        """暫停策略"""
        if strategy_id in self.strategies:
            self.strategies[strategy_id].is_active = False
            if strategy_id in self.active_strategies:
                self.active_strategies.remove(strategy_id)
            logger.info(f"策略已暫停: {strategy_id}")

    def resume_strategy(self, strategy_id: str):
        """恢復策略"""
        if strategy_id in self.strategies:
            self.strategies[strategy_id].is_active = True
            if strategy_id not in self.active_strategies:
                self.active_strategies.append(strategy_id)
            logger.info(f"策略已恢復: {strategy_id}")

    def get_all_required_symbols(self) -> List[str]:
        """獲取所有策略需要的交易對"""
        symbols = set()
        for strategy in self.get_active_strategies():
            symbols.update(strategy.get_required_symbols())
        return list(symbols)

    def get_strategy_performance_summary(self) -> Dict[str, Dict[str, Any]]:
        """獲取所有策略的績效摘要"""
        summary = {}
        for strategy_id, strategy in self.strategies.items():
            summary[strategy_id] = {
                "type": strategy.get_strategy_type().value,
                "is_active": strategy.is_active,
                "total_trades": strategy.performance.total_trades,
                "win_rate": strategy.performance.win_rate,
                "total_pnl": strategy.performance.total_pnl,
                "health_score": strategy.get_health_score(),
            }
        return summary


if __name__ == "__main__":
    # 測試策略框架
    print("🧪 策略框架測試")

    # 創建策略管理器
    _ = StrategyManager()
    print("✅ 策略管理器創建成功")

    # 測試信號創建
    signal = TradingSignal(
        strategy_id="test_strategy",
        signal_type=SignalType.BUY,
        strength=0.8,
        symbols=["BTC/USDT:USDT"],
        confidence=0.9,
        metadata={"reason": "test"},
        timestamp=datetime.now(),
    )

    print(f"✅ 交易信號創建: {signal.signal_type.value}, 強度: {signal.strength}")
    print(f"✅ 信號有效性: {signal.is_valid()}")
    print(f"✅ 信號強度足夠: {signal.is_strong_enough()}")

    print("✅ 策略框架測試完成")
