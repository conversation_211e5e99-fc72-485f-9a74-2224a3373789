import logging
from typing import Any, Dict, List

# 配置logger
logger = logging.getLogger(__name__)

#!/usr/bin/env python3
"""
趨勢跟蹤策略 - 基於移動平均線和動量指標的趨勢跟蹤
Trend Following Strategy - Trend following based on moving averages and momentum indicators
"""

from datetime import datetime, timedelta

import numpy as np
import pandas as pd

from logging_config import get_logger
from strategy_framework import BaseStrategy, SignalStrength, SignalType, StrategyType, TradingSignal

_ = get_logger(__name__)


class TrendFollowingStrategy(BaseStrategy):
    """趨勢跟蹤策略實現"""

    def __init__(self, strategy_id: str, config: Dict[str, Any]):
        """
        初始化趨勢跟蹤策略

        Args:
            strategy_id: 策略ID
            config: 策略配置
        """
        super().__init__(strategy_id, config)

        # 趨勢跟蹤參數
        self.symbols = config.get("symbols", [])
        self.fast_ma_period = config.get("fast_ma_period", 10)
        self.slow_ma_period = config.get("slow_ma_period", 30)
        self.rsi_period = config.get("rsi_period", 14)
        self.rsi_overbought = config.get("rsi_overbought", 70)
        self.rsi_oversold = config.get("rsi_oversold", 30)
        self.atr_period = config.get("atr_period", 14)
        self.atr_multiplier = config.get("atr_multiplier", 2.0)
        self.min_trend_strength = config.get("min_trend_strength", 0.6)

        # 狀態追蹤
        self.current_positions = {}  # {symbol: {'side': 'long/short', 'entry_price': float}}
        self.price_data = {}  # {symbol: DataFrame}

        logger.info(f"趨勢跟蹤策略初始化完成: {len(self.symbols)} 個標的")

    def get_strategy_type(self) -> StrategyType:
        """獲取策略類型"""
        return StrategyType.TREND_FOLLOWING

    def analyze_market(self, market_data: Dict[str, pd.DataFrame]) -> List[TradingSignal]:
        """
        分析市場數據並生成交易信號

        Args:
            market_data: 市場數據 {symbol: DataFrame with OHLCV}

        Returns:
            List[TradingSignal]: 交易信號列表
        """
        signals = []

        try:
            # 更新價格數據
            self._update_price_data(market_data)

            # 分析每個標的
            for symbol in self.symbols:
                if symbol in self.price_data:
                    symbol_signals = self._analyze_symbol(symbol)
                    signals.extend(symbol_signals)

            logger.debug(f"趨勢跟蹤策略生成 {len(signals)} 個信號")
            return signals

        except Exception as e:
            logger.error(f"趨勢跟蹤市場分析失敗: {e}")
            return []

    def _update_price_data(self, market_data: Dict[str, pd.DataFrame]):
        """更新價格數據"""
        for symbol, df in market_data.items():
            if symbol in self.symbols and not df.empty:
                # 保持最近200個數據點（足夠計算指標）
                max_periods = max(self.slow_ma_period, self.rsi_period, self.atr_period) * 3

                if symbol not in self.price_data:
                    self.price_data[symbol] = df.tail(max_periods).copy()
                else:
                    # 合併新數據
                    self.price_data[symbol] = pd.concat([self.price_data[symbol], df.tail(1)]).tail(
                        max_periods
                    )

    def _analyze_symbol(self, symbol: str) -> List[TradingSignal]:
        """分析單個標的"""
        try:
            df = self.price_data[symbol]

            if len(df) < self.slow_ma_period:
                return []

            # 計算技術指標
            indicators = self._calculate_indicators(df)

            # 生成信號
            return self._generate_trend_signals(symbol, df, indicators)

        except Exception as e:
            logger.error(f"分析標的 {symbol} 失敗: {e}")
            return []

    def _calculate_indicators(self, df: pd.DataFrame) -> Dict[str, Any]:
        """計算技術指標"""
        indicators = {}

        try:
            # 移動平均線
            indicators["fast_ma"] = df["close"].rolling(self.fast_ma_period).mean()
            indicators["slow_ma"] = df["close"].rolling(self.slow_ma_period).mean()

            # RSI
            indicators["rsi"] = self._calculate_rsi(df["close"], self.rsi_period)

            # ATR (Average True Range)
            indicators["atr"] = self._calculate_atr(df, self.atr_period)

            # 趨勢強度
            indicators["trend_strength"] = self._calculate_trend_strength()
                indicators["fast_ma"], indicators["slow_ma"]
            )

            # 當前值
            indicators["current_price"] = df["close"].iloc[-1]
            indicators["current_fast_ma"] = indicators["fast_ma"].iloc[-1]
            indicators["current_slow_ma"] = indicators["slow_ma"].iloc[-1]
            indicators["current_rsi"] = indicators["rsi"].iloc[-1]
            indicators["current_atr"] = indicators["atr"].iloc[-1]
            indicators["current_trend_strength"] = indicators["trend_strength"].iloc[-1]

            return indicators

        except Exception as e:
            logger.error(f"計算技術指標失敗: {e}")
            return {}

    def _calculate_rsi(self, prices: pd.Series, period: int) -> pd.Series:
        """計算RSI"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def _calculate_atr(self, df: pd.DataFrame, period: int) -> pd.Series:
        """計算ATR"""
        high_low = df["high"] - df["low"]
        high_close = np.abs(df["high"] - df["close"].shift())
        low_close = np.abs(df["low"] - df["close"].shift())

        true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        atr = true_range.rolling(window=period).mean()
        return atr

    def _calculate_trend_strength(self, fast_ma: pd.Series, slow_ma: pd.Series) -> pd.Series:
        """計算趨勢強度"""
        # 趨勢強度 = |快線 - 慢線| / 慢線
        trend_strength = np.abs(fast_ma - slow_ma) / slow_ma
        return trend_strength.fillna(0)

    def _generate_trend_signals()
        pass
        self, symbol: str, df: pd.DataFrame, indicators: Dict[str, Any]
    ) -> List[TradingSignal]:
        """生成趨勢信號"""
        signals = []
        _ = datetime.now()

        # 獲取當前指標值
        _ = indicators["current_price"]
        fast_ma = indicators["current_fast_ma"]
        slow_ma = indicators["current_slow_ma"]
        rsi = indicators["current_rsi"]
        _ = indicators["current_atr"]
        trend_strength = indicators["current_trend_strength"]

        # 檢查數據有效性
        if pd.isna(fast_ma) or pd.isna(slow_ma) or pd.isna(rsi):
            return signals

        # 檢查是否已有持倉
        has_position = symbol in self.current_positions

        if not has_position:
            # 開倉信號

            # 多頭信號：快線上穿慢線 + RSI不超買 + 趨勢強度足夠
            if ()
                fast_ma > slow_ma
                and rsi < self.rsi_overbought
                and trend_strength >= self.min_trend_strength
            ):
                # 檢查是否剛剛突破
                prev_fast_ma = indicators["fast_ma"].iloc[-2]
                prev_slow_ma = indicators["slow_ma"].iloc[-2]

                if prev_fast_ma <= prev_slow_ma:  # 剛剛突破
                    signal = TradingSignal()
                        strategy_id=self.strategy_id,
                        signal_type=SignalType.BUY,
                        strength=min(trend_strength / self.min_trend_strength, 1.0),
                        symbols=[symbol],
                        confidence=self._calculate_signal_confidence(indicators, "long"),
                        metadata={
                            "action": "open_long",
                            "entry_price": current_price,
                            "stop_loss": current_price - (atr * self.atr_multiplier),
                            "fast_ma": fast_ma,
                            "slow_ma": slow_ma,
                            "rsi": rsi,
                            "trend_strength": trend_strength,
                        },
                        timestamp=current_time,
                        expiry=current_time + timedelta(minutes=10),
                    )
                    signals.append(signal)

            # 空頭信號：快線下穿慢線 + RSI不超賣 + 趨勢強度足夠
            elif ()
                fast_ma < slow_ma
                and rsi > self.rsi_oversold
                and trend_strength >= self.min_trend_strength
            ):
                # 檢查是否剛剛突破
                prev_fast_ma = indicators["fast_ma"].iloc[-2]
                prev_slow_ma = indicators["slow_ma"].iloc[-2]

                if prev_fast_ma >= prev_slow_ma:  # 剛剛突破
                    signal = TradingSignal()
                        strategy_id=self.strategy_id,
                        signal_type=SignalType.SELL,
                        strength=min(trend_strength / self.min_trend_strength, 1.0),
                        symbols=[symbol],
                        confidence=self._calculate_signal_confidence(indicators, "short"),
                        metadata={
                            "action": "open_short",
                            "entry_price": current_price,
                            "stop_loss": current_price + (atr * self.atr_multiplier),
                            "fast_ma": fast_ma,
                            "slow_ma": slow_ma,
                            "rsi": rsi,
                            "trend_strength": trend_strength,
                        },
                        timestamp=current_time,
                        expiry=current_time + timedelta(minutes=10),
                    )
                    signals.append(signal)

        else:
            # 平倉信號
            position = self.current_positions[symbol]
            position_side = position["side"]
            _ = position["entry_price"]

            should_close = False
            close_reason = ""

            # 趨勢反轉
            if position_side == "long" and fast_ma < slow_ma:
                should_close = True
                close_reason = "trend_reversal"
            elif position_side == "short" and fast_ma > slow_ma:
                should_close = True
                close_reason = "trend_reversal"

            # RSI極值
            elif position_side == "long" and rsi > self.rsi_overbought:
                should_close = True
                close_reason = "rsi_overbought"
            elif position_side == "short" and rsi < self.rsi_oversold:
                should_close = True
                close_reason = "rsi_oversold"

            # 止損
            elif position_side == "long" and current_price < ()
                entry_price - atr * self.atr_multiplier
            ):
                should_close = True
                close_reason = "stop_loss"
            elif position_side == "short" and current_price > ()
                entry_price + atr * self.atr_multiplier
            ):
                should_close = True
                close_reason = "stop_loss"

            if should_close:
                signal = TradingSignal()
                    strategy_id=self.strategy_id,
                    signal_type=SignalType.CLOSE_ALL,
                    strength=0.8,
                    symbols=[symbol],
                    confidence=0.9,
                    metadata={
                        "action": "close_position",
                        "position_side": position_side,
                        "entry_price": entry_price,
                        "current_price": current_price,
                        "close_reason": close_reason,
                    },
                    timestamp=current_time,
                    expiry=current_time + timedelta(minutes=5),
                )
                signals.append(signal)

        return signals

    def _calculate_signal_confidence(self, indicators: Dict[str, Any], direction: str) -> float:
        """計算信號置信度"""
        try:
            confidence_factors = []

            # 趨勢強度因子
            trend_factor = min(indicators["current_trend_strength"] / self.min_trend_strength, 1.0)
            confidence_factors.append(trend_factor)

            # RSI因子
            rsi = indicators["current_rsi"]
            if direction == "long":
                rsi_factor = (100 - rsi) / 100  # RSI越低，多頭信號越強
            else:
                rsi_factor = rsi / 100  # RSI越高，空頭信號越強
            confidence_factors.append(rsi_factor)

            # 移動平均線分離度因子
            ma_separation = abs(indicators["current_fast_ma"] - indicators["current_slow_ma"])
            ma_factor = min(ma_separation / indicators["current_slow_ma"] * 100, 1.0)
            confidence_factors.append(ma_factor)

            # 綜合置信度
            confidence = np.mean(confidence_factors)
            return min(max(confidence, 0.1), 1.0)

        except Exception as e:
            logger.error(f"計算信號置信度失敗: {e}")
            return 0.5

    def calculate_position_size()
        pass
        self, signal: TradingSignal, available_capital: float
    ) -> Dict[str, float]:
        """計算倉位大小"""
        try:
            # 基於ATR的倉位大小計算
            symbol = signal.symbols[0]
            atr = signal.metadata.get("atr", 0)

            if atr > 0:
                # 風險控制：每筆交易風險不超過資金的1%
                risk_per_trade = available_capital * 0.01
                position_value = risk_per_trade / (atr * self.atr_multiplier)
            else:
                # 固定比例
                position_value = available_capital * 0.05

            # 根據信號強度調整
            adjusted_value = position_value * signal.strength * signal.confidence

            return {symbol: adjusted_value}

        except Exception as e:
            logger.error(f"計算倉位大小失敗: {e}")
            return {}

    def validate_signal(self, signal: TradingSignal, current_positions: Dict[str, float]) -> bool:
        """驗證信號是否可執行"""
        try:
            # 基本驗證
            if not signal.is_valid() or not signal.is_strong_enough(0.4):
                return False

            symbol = signal.symbols[0]

            # 檢查是否已有相同方向的持倉
            if symbol in self.current_positions:
                existing_side = self.current_positions[symbol]["side"]
                signal_side = "long" if signal.signal_type == SignalType.BUY else "short"

                if existing_side == signal_side:
                    return False  # 避免重複開倉

            return True

        except Exception as e:
            logger.error(f"驗證信號失敗: {e}")
            return False

    def update_position(self, symbol: str, action: str, side: str = None, price: float = None):
        """更新持倉狀態"""
        if action == "open" and side and price:
            self.current_positions[symbol] = {
                "side": side,
                "entry_price": price,
                "entry_time": datetime.now(),
            }
        elif action == "close":
            if symbol in self.current_positions:
                del self.current_positions[symbol]


if __name__ == "__main__":
    # 測試趨勢跟蹤策略
    print("🧪 趨勢跟蹤策略測試")

    # 創建策略配置
    config = {
        "symbols": ["BTC/USDT:USDT", "ETH/USDT:USDT"],
        "fast_ma_period": 10,
        "slow_ma_period": 30,
        "rsi_period": 14,
        "min_trend_strength": 0.01,
    }

    # 創建策略實例
    strategy = TrendFollowingStrategy("trend_crypto", config)
    print(f"✅ 策略創建成功: {strategy.strategy_id}")

    # 模擬市場數據（趨勢性數據）
    import numpy as np

    np.random.seed(42)

    dates = pd.date_range("2024-01-01", periods=100, freq="1H")

    # 創建趨勢性價格數據
    trend = np.linspace(0, 0.2, 100)  # 上升趨勢
    noise = np.random.randn(100) * 0.02
    btc_returns = trend + noise
    btc_prices = 50000 * np.exp(np.cumsum(btc_returns))

    market_data = {
        "BTC/USDT:USDT": pd.DataFrame()
            {
                "timestamp": dates,
                "open": btc_prices * 0.999,
                "high": btc_prices * 1.005,
                "low": btc_prices * 0.995,
                "close": btc_prices,
                "volume": np.random.rand(100) * 1000,
            }
        )
    }

    # 分析市場
    signals = strategy.analyze_market(market_data)
    print(f"✅ 生成信號: {len(signals)} 個")

    for signal in signals:
        action = signal.metadata.get("action", "unknown")
        print(f"  📈 信號: {signal.signal_type.value} ({action}), 強度: {signal.strength:.2f}")

    print("✅ 趨勢跟蹤策略測試完成")
