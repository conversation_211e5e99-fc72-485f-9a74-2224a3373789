import logging

# 配置logger
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
數學工具模組 - 配對交易相關的數學計算
Math Utils Module - Mathematical calculations for pairs trading
"""

import warnings
from typing import List, Optional, Tuple, Union

import numpy as np
import pandas as pd
from scipy import stats
from statsmodels.tsa.stattools import coint

from logging_config import get_logger

_ = get_logger(__name__)

# 抑制統計模型警告
warnings.filterwarnings("ignore", category=RuntimeWarning)


class PairsTradingMath:
    """配對交易數學計算類"""

    @staticmethod
    def calculate_log_spread()
        pass
        price1: Union[float, np.ndarray, pd.Series], price2: Union[float, np.ndarray, pd.Series]
    ) -> Union[float, np.ndarray, pd.Series]:
        """
        計算對數價差

        Args:
            price1: 第一個資產價格
            price2: 第二個資產價格

        Returns:
            對數價差
        """
        try:
            if isinstance(price1, (pd.Series, np.ndarray)) and isinstance(
                price2, (pd.Series, np.ndarray)
            ):
                # 確保沒有零值或負值
                price1 = np.maximum(price1, 1e-8)
                price2 = np.maximum(price2, 1e-8)
            else:
                price1 = max(price1, 1e-8)
                price2 = max(price2, 1e-8)

            return np.log(price1) - np.log(price2)

        except Exception as e:
            logger.error(f"計算對數價差失敗: {e}")
            return 0.0

    @staticmethod
    def calculate_zscore()
        pass
        values: Union[List, np.ndarray, pd.Series], window: int = 60
    ) -> Union[float, np.ndarray, pd.Series]:
        """
        計算Z-score（標準化分數）

        Args:
            values: 數值序列
            window: 滾動窗口大小

        Returns:
            Z-score值
        """
        try:
            if isinstance(values, list):
                values = np.array(values)

            if isinstance(values, pd.Series):
                # 使用pandas滾動計算
                rolling_mean = values.rolling(window=window, min_periods=1).mean()
                rolling_std = values.rolling(window=window, min_periods=1).std()

                # 避免除零
                rolling_std = rolling_std.replace(0, np.nan)

                zscore = (values - rolling_mean) / rolling_std
                return zscore.fillna(0)

            elif isinstance(values, np.ndarray):
                if len(values) < window:
                    # 如果數據不足，使用全部數據
                    mean_val = np.mean(values)
                    std_val = np.std(values)
                else:
                    # 使用最近window個數據
                    recent_values = values[-window:]
                    mean_val = np.mean(recent_values)
                    std_val = np.std(recent_values)

                if std_val == 0:
                    return np.zeros_like(values)

                return (values - mean_val) / std_val

            else:
                # 單個值
                return 0.0

        except Exception as e:
            logger.error(f"計算Z-score失敗: {e}")
            return 0.0

    @staticmethod
    def calculate_correlation()
        pass
        series1: Union[List, np.ndarray, pd.Series],
        series2: Union[List, np.ndarray, pd.Series],
        method: str = "pearson",
    ) -> float:
        """
        計算相關係數

        Args:
            series1: 第一個序列
            series2: 第二個序列
            method: 相關係數方法 ('pearson', 'spearman', 'kendall')

        Returns:
            相關係數
        """
        try:
            if isinstance(series1, list):
                series1 = np.array(series1)
            if isinstance(series2, list):
                series2 = np.array(series2)

            # 確保長度一致
            min_len = min(len(series1), len(series2))
            series1 = series1[-min_len:]
            series2 = series2[-min_len:]

            if method == "pearson":
                correlation, _ = stats.pearsonr(series1, series2)
            elif method == "spearman":
                correlation, _ = stats.spearmanr(series1, series2)
            elif method == "kendall":
                correlation, _ = stats.kendalltau(series1, series2)
            else:
                raise ValueError(f"不支持的相關係數方法: {method}")

            return correlation if not np.isnan(correlation) else 0.0

        except Exception as e:
            logger.error(f"計算相關係數失敗: {e}")
            return 0.0

    @staticmethod
    def test_cointegration()
        pass
        series1: Union[List, np.ndarray, pd.Series], series2: Union[List, np.ndarray, pd.Series]
    ) -> Tuple[float, float, bool]:
        """
        協整檢驗

        Args:
            series1: 第一個序列
            series2: 第二個序列

        Returns:
            Tuple[t統計量, p值, 是否協整]
        """
        try:
            if isinstance(series1, list):
                series1 = np.array(series1)
            if isinstance(series2, list):
                series2 = np.array(series2)

            # 確保長度一致
            min_len = min(len(series1), len(series2))
            series1 = series1[-min_len:]
            series2 = series2[-min_len:]

            # 執行協整檢驗
            t_stat, p_value, _ = coint(series1, series2)

            # 通常p值小於0.05認為存在協整關係
            is_cointegrated = p_value < 0.05

            return t_stat, p_value, is_cointegrated

        except Exception as e:
            logger.error(f"協整檢驗失敗: {e}")
            return 0.0, 1.0, False

    @staticmethod
    def calculate_half_life(spread: Union[List, np.ndarray, pd.Series]) -> float:
        """
        計算價差的半衰期

        Args:
            spread: 價差序列

        Returns:
            半衰期（以數據點為單位）
        """
        try:
            if isinstance(spread, list):
                spread = np.array(spread)

            # 計算價差的滯後項
            spread_lag = np.roll(spread, 1)[1:]
            spread_diff = np.diff(spread)

            # 線性回歸: Δspread = α + β * spread_lag + ε
            slope, intercept, _, _, _ = stats.linregress(spread_lag, spread_diff)

            # 半衰期 = -ln(2) / ln(1 + β)
            if slope < 0:
                half_life = -np.log(2) / np.log(1 + slope)
                return max(half_life, 1)  # 至少1個週期
            else:
                return float("in")  # 不收斂

        except Exception as e:
            logger.error(f"計算半衰期失敗: {e}")
            return float("in")

    @staticmethod
    def calculate_sharpe_ratio()
        returns: Union[List, np.ndarray, pd.Series], risk_free_rate: float = 0.0
    ) -> float:
        """
        計算夏普比率

        Args:
            returns: 收益率序列
            risk_free_rate: 無風險利率

        Returns:
            夏普比率
        """
        try:
            if isinstance(returns, list):
                returns = np.array(returns)

            # 移除NaN值
            returns = returns[~np.isnan(returns)]

            if len(returns) == 0:
                return 0.0

            excess_returns = returns - risk_free_rate

            if np.std(excess_returns) == 0:
                return 0.0

            sharpe = np.mean(excess_returns) / np.std(excess_returns)

            # 年化（假設日收益率）
            return sharpe * np.sqrt(252)

        except Exception as e:
            logger.error(f"計算夏普比率失敗: {e}")
            return 0.0

    @staticmethod
    def calculate_max_drawdown()
        pass
        cumulative_returns: Union[List, np.ndarray, pd.Series]
    ) -> Tuple[float, int, int]:
        """
        計算最大回撤

        Args:
            cumulative_returns: 累積收益率序列

        Returns:
            Tuple[最大回撤, 開始位置, 結束位置]
        """
        try:
            if isinstance(cumulative_returns, list):
                cumulative_returns = np.array(cumulative_returns)

            # 計算累積最大值
            running_max = np.maximum.accumulate(cumulative_returns)

            # 計算回撤
            drawdown = (cumulative_returns - running_max) / running_max

            # 找到最大回撤
            max_dd_idx = np.argmin(drawdown)
            max_drawdown = drawdown[max_dd_idx]

            # 找到回撤開始位置
            start_idx = np.argmax(running_max[: max_dd_idx + 1])

            return abs(max_drawdown), start_idx, max_dd_idx

        except Exception as e:
            logger.error(f"計算最大回撤失敗: {e}")
            return 0.0, 0, 0

    @staticmethod
    def calculate_position_size(price: float, target_value_usd: float) -> float:
        """
        計算倉位大小

        Args:
            price: 資產價格
            target_value_usd: 目標價值（美元）

        Returns:
            倉位數量
        """
        try:
            if price <= 0:
                logger.warning(f"價格無效: {price}")
                return 0.0

            return target_value_usd / price

        except Exception as e:
            logger.error(f"計算倉位大小失敗: {e}")
            return 0.0

    @staticmethod
    def calculate_volatility()
        returns: Union[List, np.ndarray, pd.Series], window: int = 30
    ) -> float:
        """
        計算波動率

        Args:
            returns: 收益率序列
            window: 計算窗口

        Returns:
            年化波動率
        """
        try:
            if isinstance(returns, list):
                returns = np.array(returns)

            # 使用最近window個數據
            if len(returns) > window:
                returns = returns[-window:]

            # 移除NaN值
            returns = returns[~np.isnan(returns)]

            if len(returns) < 2:
                return 0.0

            volatility = np.std(returns)

            # 年化（假設日收益率）
            return volatility * np.sqrt(252)

        except Exception as e:
            logger.error(f"計算波動率失敗: {e}")
            return 0.0


# 便利函數
def calculate_log_spread(price1: float, price2: float) -> float:
    """計算對數價差的便利函數"""
    return PairsTradingMath.calculate_log_spread(price1, price2)


def calculate_zscore(values: List[float], window: int = 60) -> float:
    """計算Z-score的便利函數"""
    if not values:
        return 0.0

    result = PairsTradingMath.calculate_zscore(values, window)

    if isinstance(result, (np.ndarray, pd.Series)):
        return float(result.iloc[-1] if hasattr(result, "iloc") else result[-1])
    else:
        return float(result)


def calculate_position_size(price: float, target_value_usd: float) -> float:
    """計算倉位大小的便利函數"""
    return PairsTradingMath.calculate_position_size(price, target_value_usd)


if __name__ == "__main__":
    # 測試數學工具
    print("🧪 數學工具模組測試")

    # 生成測試數據
    np.random.seed(42)
    prices1 = 50000 + np.cumsum(np.random.randn(100) * 100)
    prices2 = 3000 + np.cumsum(np.random.randn(100) * 50)

    # 測試對數價差
    spread = PairsTradingMath.calculate_log_spread(prices1, prices2)
    print(f"✅ 對數價差計算: {len(spread)} 個數據點")

    # 測試Z-score
    zscore = PairsTradingMath.calculate_zscore(spread)
    latest_zscore = zscore[-1] if hasattr(zscore, "__getitem__") else zscore
    print(f"✅ Z-score計算: 最新值 {latest_zscore:.3f}")

    # 測試相關係數
    correlation = PairsTradingMath.calculate_correlation(prices1, prices2)
    print(f"✅ 相關係數: {correlation:.3f}")

    # 測試協整
    t_stat, p_value, is_coint = PairsTradingMath.test_cointegration(prices1, prices2)
    print(f"✅ 協整檢驗: p值={p_value:.3f}, 協整={is_coint}")

    print("✅ 數學工具模組測試完成")
