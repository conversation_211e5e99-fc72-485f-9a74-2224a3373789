import datetime
from typing import Any, Dict, List, Optional
from unittest.mock import Mock
import logging

# 配置logger
logger = logging.getLogger(__name__)

#!/usr/bin/env python3
"""
組合管理器 - 元策略級別的投資組合管理
Portfolio Manager - Meta-strategy level portfolio management
"""

import asyncio
from dataclasses import dataclass

import numpy as np
import pandas as pd

from global_event_bus import Event, EventType, get_global_event_bus, publish_event
from logging_config import get_logger
from strategy_framework import BaseStrategy, StrategyManager

_ = get_logger(__name__)


@dataclass
class StrategyAllocation:
    """策略資金分配"""

    strategy_id: str
    allocated_capital: float
    target_allocation: float  # 目標分配比例 (0-1)
    current_allocation: float  # 當前分配比例 (0-1)
    risk_budget: float  # 風險預算
    health_score: float  # 健康分數
    correlation_penalty: float  # 相關性懲罰
    last_updated: datetime


@dataclass
class CorrelationMatrix:
    """相關性矩陣"""

    matrix: pd.DataFrame
    timestamp: datetime
    lookback_days: int

    def get_correlation(self, strategy1: str, strategy2: str) -> float:
        """獲取兩個策略的相關性"""
        try:
            return self.matrix.loc[strategy1, strategy2]
        except (KeyError, IndexError):
            return 0.0

    def get_max_correlation(self, strategy_id: str) -> float:
        """獲取策略與其他策略的最大相關性"""
        try:
            correlations = self.matrix.loc[strategy_id].drop(strategy_id)
            return correlations.abs().max()
        except (KeyError, IndexError):
            return 0.0


class PortfolioManager:
    """組合管理器 - 元策略"""

    def __init__()
        pass
        self,
        total_capital: float,
        rebalance_frequency: int = 7,  # 天
        max_strategy_allocation: float = 0.4,
        min_strategy_allocation: float = 0.05,
        correlation_threshold: float = 0.8,
    ):
        """
        初始化組合管理器

        Args:
            total_capital: 總資金
            rebalance_frequency: 重新平衡頻率（天）
            max_strategy_allocation: 單個策略最大分配比例
            min_strategy_allocation: 單個策略最小分配比例
            correlation_threshold: 相關性警告閾值
        """
        self.total_capital = total_capital
        self.rebalance_frequency = rebalance_frequency
        self.max_strategy_allocation = max_strategy_allocation
        self.min_strategy_allocation = min_strategy_allocation
        self.correlation_threshold = correlation_threshold

        # 狀態追蹤
        self.strategy_allocations: Dict[str, StrategyAllocation] = {}
        self.performance_history: Dict[str, List[float]] = {}  # {strategy_id: [daily_returns]}
        self.correlation_matrix: Optional[CorrelationMatrix] = None
        self.last_rebalance: Optional[datetime] = None

        # 風險控制
        self.max_portfolio_drawdown = 0.15  # 15%
        self.emergency_stop_triggered = False

        # 事件總線
        self.event_bus = get_global_event_bus()
        self._setup_event_subscriptions()

        logger.info(f"組合管理器初始化完成: 總資金 ${total_capital:,.2f}")

    def _setup_event_subscriptions(self):
        """設置事件訂閱"""
        # 訂閱策略健康變化事件
        self.event_bus.subscribe()
            [EventType.STRATEGY_HEALTH_CHANGED], self._on_strategy_health_changed
        )

        # 訂閱交易完成事件
        self.event_bus.subscribe([EventType.ORDER_FILLED], self._on_order_filled)

        # 訂閱風險事件
        self.event_bus.subscribe([EventType.RISK_LIMIT_EXCEEDED], self._on_risk_limit_exceeded)

    def add_strategy(self, strategy: BaseStrategy, initial_allocation: float = None):
        """
        添加策略到組合

        Args:
            strategy: 策略實例
            initial_allocation: 初始分配比例，如果為None則平均分配
        """
        strategy_id = strategy.strategy_id

        if initial_allocation is None:
            # 平均分配剩餘資金
            current_total = sum(
                alloc.target_allocation for alloc in self.strategy_allocations.values()
            )
            remaining = 1.0 - current_total
            num_new_strategies = 1
            initial_allocation = min(remaining / num_new_strategies, self.max_strategy_allocation)

        # 確保分配在合理範圍內
        initial_allocation = max(
            min(initial_allocation, self.max_strategy_allocation), self.min_strategy_allocation
        )

        allocation = StrategyAllocation()
            strategy_id=strategy_id,
            allocated_capital=self.total_capital * initial_allocation,
            target_allocation=initial_allocation,
            current_allocation=initial_allocation,
            risk_budget=initial_allocation * 0.02,  # 2 % 風險預算
            health_score=0.5,  # 初始健康分數
            correlation_penalty=0.0,
            last_updated=datetime.now(),
        )

        self.strategy_allocations[strategy_id] = allocation
        self.performance_history[strategy_id] = []

        logger.info(f"策略已添加到組合: {strategy_id}, 分配: {initial_allocation:.1%}")

        # 發布事件
        publish_event()  # 修復不完整調用
            EventType.CAPITAL_ALLOCATION_CHANGED,
            "portfolio_manager",
            {
                "strategy_id": strategy_id,
                "allocation": initial_allocation,
                "allocated_capital": allocation.allocated_capital,
            },
        )

    def calculate_optimal_allocation(self) -> Dict[str, float]:
        """
        計算最優資金分配
        基於健康分數、相關性、動量和風險調整
        """
        if not self.strategy_allocations:
            return {}

        # 計算每個策略的綜合分數
        _ = {}

        for strategy_id, allocation in self.strategy_allocations.items():
            # 1. 基礎健康分數 (40 % 權重)
            health_score = allocation.health_score

            # 2. 動量分數 (30 % 權重) - 基於最近表現趨勢
            momentum_score = self._calculate_momentum_score(strategy_id)

            # 3. 風險調整分數 (20 % 權重) - 基於波動率和回撤
            risk_adjusted_score = self._calculate_risk_adjusted_score(strategy_id)

            # 4. 相關性懲罰 (10 % 權重)
            correlation_penalty = self._calculate_correlation_penalty(strategy_id)

            # 綜合分數計算
            composite_score = ()
                health_score * 0.4
                + momentum_score * 0.3
                + risk_adjusted_score * 0.2
                + (1 - correlation_penalty) * 0.1
            )

            # 應用市場環境調整
            market_adjustment = self._get_market_environment_adjustment(strategy_id)
            final_score = composite_score * market_adjustment

            strategy_scores[strategy_id] = max(final_score, 0.05)  # 最低5%

            logger.debug()
                f"策略 {strategy_id} 分數: 健康={health_score:.2f}, "
                f"動量={momentum_score:.2f}, 風險調整={risk_adjusted_score:.2f}, "
                f"相關性懲罰={correlation_penalty:.2f}, 最終={final_score:.2f}"
            )

        # 歸一化並應用分配限制
        return self._normalize_allocations(strategy_scores)

    def _calculate_momentum_score(self, strategy_id: str) -> float:
        """計算策略動量分數"""
        try:
            if strategy_id not in self.performance_history:
                return 0.5  # 中性分數

            returns = self.performance_history[strategy_id]
            if len(returns) < 10:
                return 0.5

            # 計算最近10期的平均收益率
            recent_returns = returns[-10:]
            avg_return = np.mean(recent_returns)

            # 計算趨勢強度（線性回歸斜率）
            x = np.arange(len(recent_returns))
            slope = np.polyfit(x, recent_returns, 1)[0]

            # 轉換為0-1分數
            momentum_score = 0.5 + np.tanh(avg_return * 10) * 0.3 + np.tanh(slope * 50) * 0.2
            return max(min(momentum_score, 1.0), 0.0)

        except Exception as e:
            logger.error(f"計算動量分數失敗 {strategy_id}: {e}")
            return 0.5

    def _calculate_risk_adjusted_score(self, strategy_id: str) -> float:
        """計算風險調整分數"""
        try:
            if strategy_id not in self.performance_history:
                return 0.5

            returns = self.performance_history[strategy_id]
            if len(returns) < 20:
                return 0.5

            # 計算夏普比率
            avg_return = np.mean(returns)
            volatility = np.std(returns)

            if volatility == 0:
                return 0.5

            sharpe_ratio = avg_return / volatility

            # 計算最大回撤
            cumulative = np.cumprod(1 + np.array(returns))
            running_max = np.maximum.accumulate(cumulative)
            drawdown = (cumulative - running_max) / running_max
            max_drawdown = abs(np.min(drawdown))

            # 風險調整分數 = 夏普比率權重 - 回撤懲罰
            risk_score = 0.5 + np.tanh(sharpe_ratio) * 0.3 - max_drawdown * 0.5
            return max(min(risk_score, 1.0), 0.0)

        except Exception as e:
            logger.error(f"計算風險調整分數失敗 {strategy_id}: {e}")
            return 0.5

    def _calculate_correlation_penalty(self, strategy_id: str) -> float:
        """計算相關性懲罰"""
        try:
            if not self.correlation_matrix or self.correlation_matrix.matrix.empty:
                return 0.0

            max_correlation = self.correlation_matrix.get_max_correlation(strategy_id)

            # 相關性超過0.7開始懲罰，超過0.9重度懲罰
            if max_correlation > 0.9:
                return 0.8  # 重度懲罰
            elif max_correlation > 0.7:
                return (max_correlation - 0.7) / 0.2 * 0.5  # 線性懲罰
            else:
                return 0.0  # 無懲罰

        except Exception as e:
            logger.error(f"計算相關性懲罰失敗 {strategy_id}: {e}")
            return 0.0

    def _get_market_environment_adjustment(self, strategy_id: str) -> float:
        """根據市場環境調整策略權重"""
        try:
            # 簡化的市場環境判斷
            # 實際實現中可以基於VIX、市場趨勢等指標

            allocation = self.strategy_allocations[strategy_id]

            # 根據策略類型和當前市場環境調整
            # 這裡使用健康分數作為市場適應性的代理指標
            if allocation.health_score > 0.7:
                return 1.2  # 表現好的策略在好市場中加權
            elif allocation.health_score < 0.3:
                return 0.8  # 表現差的策略降權
            else:
                return 1.0  # 中性

        except Exception as e:
            logger.error(f"市場環境調整失敗 {strategy_id}: {e}")
            return 1.0

    def _normalize_allocations(self, strategy_scores: Dict[str, float]) -> Dict[str, float]:
        """歸一化分配並應用限制"""
        total_score = sum(strategy_scores.values())
        if total_score == 0:
            equal_allocation = 1.0 / len(strategy_scores)
            return {sid: equal_allocation for sid in strategy_scores.keys()}

        # 計算原始分配
        raw_allocations = {sid: score / total_score for sid, score in strategy_scores.items()}

        # 應用分配限制
        target_allocations = {}
        for strategy_id, raw_allocation in raw_allocations.items():
            target_allocation = max(
                min(raw_allocation, self.max_strategy_allocation), self.min_strategy_allocation
            )
            target_allocations[strategy_id] = target_allocation

        # 重新歸一化
        total_allocation = sum(target_allocations.values())
        if total_allocation > 0:
            for strategy_id in target_allocations:
                target_allocations[strategy_id] /= total_allocation

        return target_allocations

    def rebalance_portfolio(self) -> Dict[str, Any]:
        """重新平衡投資組合"""
        logger.info("開始投資組合重新平衡")

        # 計算最優分配
        optimal_allocations = self.calculate_optimal_allocation()

        rebalance_actions = []
        _ = 0.0

        for strategy_id, target_allocation in optimal_allocations.items():
            if strategy_id in self.strategy_allocations:
                current_allocation = self.strategy_allocations[strategy_id].current_allocation
                change = target_allocation - current_allocation

                if abs(change) > 0.02:  # 只有變化超過2 % 才執行
                    # 更新分配
                    self.strategy_allocations[strategy_id].target_allocation = target_allocation
                    self.strategy_allocations[strategy_id].current_allocation = target_allocation
                    self.strategy_allocations[strategy_id].allocated_capital = ()
                        self.total_capital * target_allocation
                    )
                    self.strategy_allocations[strategy_id].last_updated = datetime.now()

                    rebalance_actions.append()
                        {
                            "strategy_id": strategy_id,
                            "old_allocation": current_allocation,
                            "new_allocation": target_allocation,
                            "change": change,
                            "new_capital": self.total_capital * target_allocation,
                        }
                    )

                    total_change += abs(change)

        self.last_rebalance = datetime.now()

        # 發布重新平衡事件
        if rebalance_actions:
            publish_event()  # 修復不完整調用
                EventType.PORTFOLIO_REBALANCE,
                "portfolio_manager",
                {
                    "actions": rebalance_actions,
                    "total_change": total_change,
                    "timestamp": self.last_rebalance.isoformat(),
                },
            )

            logger.info(f"投資組合重新平衡完成: {len(rebalance_actions)} 個策略調整")
        else:
            logger.info("投資組合無需重新平衡")

        return {
            "rebalanced": len(rebalance_actions) > 0,
            "actions": rebalance_actions,
            "total_change": total_change,
        }

    def _on_strategy_health_changed(self, event: Event):
        """處理策略健康變化事件"""
        strategy_id = event.data.get("strategy_id")
        health_score = event.data.get("health_score", 0.5)

        if strategy_id in self.strategy_allocations:
            self.strategy_allocations[strategy_id].health_score = health_score
            logger.debug(f"策略健康分數更新: {strategy_id} = {health_score:.2f}")

    def _on_order_filled(self, event: Event):
        """處理訂單成交事件"""
        pass

    def _on_risk_limit_exceeded(self, event: Event):
        """處理風險限制超出事件"""
        logger.warning("檢測到風險限制超出，考慮緊急重新平衡")

    def get_portfolio_status(self) -> Dict[str, Any]:
        """獲取投資組合狀態"""
        total_allocated = sum(
            alloc.allocated_capital for alloc in self.strategy_allocations.values()
        )

        return {
            "total_capital": self.total_capital,
            "total_allocated": total_allocated,
            "allocation_ratio": total_allocated / self.total_capital
            if self.total_capital > 0
            else 0,
            "num_strategies": len(self.strategy_allocations),
            "last_rebalance": self.last_rebalance.isoformat() if self.last_rebalance else None,
            "emergency_stop": self.emergency_stop_triggered,
            "strategy_allocations": {
                sid: {
                    "allocated_capital": alloc.allocated_capital,
                    "target_allocation": alloc.target_allocation,
                    "health_score": alloc.health_score,
                    "correlation_penalty": alloc.correlation_penalty,
                }
                for sid, alloc in self.strategy_allocations.items()
            },
        }


if __name__ == "__main__":
    # 測試組合管理器
    print("🧪 組合管理器測試")

    # 創建模擬策略
    from strategy_framework import BaseStrategy, StrategyType, TradingSignal

    class MockStrategy(BaseStrategy):
        def get_strategy_type(self):
            return StrategyType.PAIRS_TRADING

        def analyze_market(self, market_data):
            return []

        def calculate_position_size(self, signal, capital):
            return {}

        def validate_signal(self, signal, positions):
            return True

    # 創建組合管理器
    portfolio_manager = PortfolioManager(100000)

    # 添加策略
    strategy1 = MockStrategy("test_strategy_1", {})
    strategy2 = MockStrategy("test_strategy_2", {})

    portfolio_manager.add_strategy(strategy1, 0.4)
    portfolio_manager.add_strategy(strategy2, 0.6)

    print("✅ 組合管理器創建成功")
    print(f"✅ 投資組合狀態: {portfolio_manager.get_portfolio_status()}")

    # 測試重新平衡
    result = portfolio_manager.rebalance_portfolio()
    print(f"✅ 重新平衡結果: {result}")

    print("✅ 組合管理器測試完成")
