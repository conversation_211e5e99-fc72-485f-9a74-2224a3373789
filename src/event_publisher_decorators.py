#!/usr/bin/env python3
"""
事件發布器裝飾器 - 自動發布關鍵事件
Event Publisher Decorator - Automatically publish critical events
"""

from datetime import datetime
from functools import wraps
from typing import Any, Callable

from global_event_bus import EventType, get_global_event_bus, publish_event


def publish_on_trade(func: Callable) -> Callable:
    """交易執行時自動發布事件"""

    @wraps(func)
    def wrapper(*args, **kwargs):
        result = func(*args, **kwargs)

        # 發布交易事件
        if result and isinstance(result, dict):
            publish_event()
                EventType.ORDER_FILLED,
                func.__name__,
                {
                    "function": func.__name__,
                    "result": result,
                    "timestamp": datetime.now().isoformat(),
                    "args": str(args)[:200],  # 限制長度
                    "kwargs": str(kwargs)[:200],
                },
            )

        return result

    return wrapper


def publish_on_signal(func: Callable) -> Callable:
    """信號生成時自動發布事件"""

    @wraps(func)
    def wrapper(*args, **kwargs):
        result = func(*args, **kwargs)

        # 發布信號事件
        if result:
            publish_event()
                EventType.SIGNAL_GENERATED,
                func.__name__,
                {
                    "function": func.__name__,
                    "signals_count": len(result) if isinstance(result, list) else 1,
                    "timestamp": datetime.now().isoformat(),
                },
            )

        return result

    return wrapper


def publish_on_health_change(func: Callable) -> Callable:
    """健康分數變化時自動發布事件"""

    @wraps(func)
    def wrapper(self, *args, **kwargs):
        old_health = getattr(self, "health_score", 0.5)
        result = func(self, *args, **kwargs)
        new_health = getattr(self, "health_score", 0.5)

        # 如果健康分數有顯著變化
        if abs(new_health - old_health) > 0.1:
            publish_event()
                EventType.STRATEGY_HEALTH_CHANGED,
                self.__class__.__name__,
                {
                    "strategy_id": getattr(self, "strategy_id", "unknown"),
                    "old_health": old_health,
                    "new_health": new_health,
                    "change": new_health - old_health,
                    "timestamp": datetime.now().isoformat(),
                },
            )

        return result

    return wrapper


# 自動事件發布類
class AutoEventPublisher:
    """自動事件發布器"""

    def __init__(self, source_name: str):
        self.source_name = source_name
        self.event_bus = get_global_event_bus()

    def publish_market_data_update(self, symbol: str, data: dict):
        """發布市場數據更新事件"""
        publish_event()
            EventType.MARKET_DATA_UPDATE,
            self.source_name,
            {
                "symbol": symbol,
                "data_keys": list(data.keys()),
                "timestamp": datetime.now().isoformat(),
            },
        )

    def publish_risk_alert(self, risk_type: str, details: dict):
        """發布風險警報事件"""
        publish_event()
            EventType.RISK_LIMIT_EXCEEDED,
            self.source_name,
            {
                "risk_type": risk_type,
                "details": details,
                "severity": "high",
                "timestamp": datetime.now().isoformat(),
            },
        )

    def publish_system_status(self, status: dict):
        """發布系統狀態事件"""
        publish_event()
            EventType.HEALTH_CHECK,
            self.source_name,
            {"status": status, "timestamp": datetime.now().isoformat()},
        )
