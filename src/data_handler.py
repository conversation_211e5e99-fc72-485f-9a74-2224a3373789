from datetime import datetime
from pathlib import Path  # 將 Path 導入移到頂部
from typing import Dict, Tuple

import ccxt
import numpy as np
import pandas as pd
import requests

from enhanced_retry_handler import RetryStrategy, enhanced_retry
from logging_config import get_logger
from utils import calculate_log_spread, calculate_zscore
import logging

# 配置logger
logger = logging.getLogger(__name__)

_ = get_logger(__name__)


class DataHandler:
    """數據處理類，負責獲取和處理價格數據"""

    def __init__(self, exchange: ccxt.Exchange, config: Dict, mode: str = "LIVE"):
        self.exchange = exchange
        self.config = config
        self.trading_pair = config["trading_pair"]
        self.timeframe = config["timeframe"]
        self.lookback_period = config["lookback_period"]
        self.mode = mode  # "LIVE" 或 "BACKTEST"

        # 數據存儲
        self.price_data = pd.DataFrame()
        self.spread_data = pd.Series(dtype=float)
        self.zscore_data = pd.Series(dtype=float)

        # 回測模式專用
        self.backtest_data = None
        self.current_index = 0

        logger.info(f"DataHandler 初始化完成，模式: {mode}，交易對: {self.trading_pair}")

    @enhanced_retry()
        max_retries=3,
        base_delay=1.0,
        max_delay=30.0,
        strategy=RetryStrategy.EXPONENTIAL,
        exceptions=()
            ccxt.NetworkError,
            ccxt.ExchangeNotAvailable,
            ccxt.RequestTimeout,
            requests.exceptions.ConnectionError,
            requests.exceptions.Timeout,
        ),
        timeout=30.0,
    )
    def fetch_ohlcv_data(self, symbol: str, limit: int = None) -> pd.DataFrame:
        """獲取 OHLCV 數據 - 帶重試機制"""
        try:
            if limit is None:
                limit = self.lookback_period + 50  # 多獲取一些數據以確保足夠

            # 設置請求超時
            if hasattr(self.exchange, "timeout"):
                self.exchange.timeout = 20000  # 20秒超時

            ohlcv = self.exchange.fetch_ohlcv(symbol, self.timeframe, limit=limit)

            if not ohlcv:
                raise ccxt.ExchangeError(f"獲取 {symbol} 數據為空")

            df = pd.DataFrame()
                ohlcv, columns=["timestamp", "open", "high", "low", "close", "volume"]
            )
            df["timestamp"] = pd.to_datetime(df["timestamp"], unit="ms")
            df.set_index("timestamp", inplace=True)

            logger.debug(f"獲取 {symbol} 數據成功，共 {len(df)} 條記錄")
            return df

        except (ccxt.AuthenticationError, ccxt.PermissionDenied, ccxt.BadSymbol) as e:
            # 永久性錯誤，不重試
            logger.error(f"獲取 {symbol} 數據失敗（永久性錯誤）: {e}")
            raise
        except Exception as e:
            logger.error(f"獲取 {symbol} 數據失敗: {e}")
            raise

    @enhanced_retry()
        max_retries=3,
        base_delay=0.5,
        max_delay=10.0,
        strategy=RetryStrategy.EXPONENTIAL,
        exceptions=(ccxt.NetworkError, ccxt.ExchangeNotAvailable, ccxt.RequestTimeout),
        timeout=15.0,
    )
    def fetch_current_prices(self) -> Tuple[float, float]:
        """獲取當前價格 - 帶重試機制"""
        try:
            base_symbol, quote_symbol = self.trading_pair

            # 設置請求超時
            if hasattr(self.exchange, "timeout"):
                self.exchange.timeout = 10000  # 10秒超時

            base_ticker = self.exchange.fetch_ticker(base_symbol)
            quote_ticker = self.exchange.fetch_ticker(quote_symbol)

            if not base_ticker or "last" not in base_ticker:
                raise ccxt.ExchangeError(f"獲取 {base_symbol} ticker 數據無效")
            if not quote_ticker or "last" not in quote_ticker:
                raise ccxt.ExchangeError(f"獲取 {quote_symbol} ticker 數據無效")

            base_price = base_ticker["last"]
            quote_price = quote_ticker["last"]

            logger.debug(f"當前價格 - {base_symbol}: {base_price}, {quote_symbol}: {quote_price}")
            return base_price, quote_price

        except (ccxt.AuthenticationError, ccxt.PermissionDenied, ccxt.BadSymbol) as e:
            # 永久性錯誤，不重試
            logger.error(f"獲取當前價格失敗（永久性錯誤）: {e}")
            raise
        except Exception as e:
            logger.error(f"獲取當前價格失敗: {e}")
            raise

    def load_backtest_data(self, data: pd.DataFrame) -> bool:
        """載入回測數據"""
        try:
            if self.mode != "BACKTEST":
                logger.warning("只有在 BACKTEST 模式下才能載入回測數據")
                return False

            self.backtest_data = data.copy()
            self.current_index = 0

            logger.info(f"回測數據載入成功，共 {len(data)} 條記錄")
            return True

        except Exception as e:
            logger.error(f"載入回測數據失敗: {e}")
            return False

    def update_data(self) -> bool:
        """更新價格數據和計算指標"""
        if self.mode == "BACKTEST":
            return self._update_backtest_data()
        else:
            return self._update_live_data()

    def _update_live_data(self) -> bool:
        """更新實時數據"""
        try:
            base_symbol, quote_symbol = self.trading_pair

            # 獲取兩個資產的價格數據
            base_data = self.fetch_ohlcv_data(base_symbol)
            quote_data = self.fetch_ohlcv_data(quote_symbol)

            # 確保數據對齊
            common_index = base_data.index.intersection(quote_data.index)
            if len(common_index) < self.lookback_period:
                logger.warning(f"可用數據不足，需要 {self.lookback_period} 條，實際 {len(common_index)} 條")
                return False

            base_data = base_data.loc[common_index]
            quote_data = quote_data.loc[common_index]

            # 合併數據
            self.price_data = pd.DataFrame()
                {
                    "base_price": base_data["close"],
                    "quote_price": quote_data["close"],
                    "base_volume": base_data["volume"],
                    "quote_volume": quote_data["volume"],
                }
            )

            # 計算價差
            self.spread_data = self.price_data.apply(
                lambda row: calculate_log_spread(row["base_price"], row["quote_price"]), axis=1
            )

            # 計算 Z-score
            # 使用 Pandas 滾動窗口計算均值和標準差
            rolling_mean = self.spread_data.rolling(window=self.lookback_period).mean()
            rolling_std = self.spread_data.rolling(window=self.lookback_period).std()

            # 計算 Z-score
            # 避免除以零，如果標準差為零，Z-score 設為 0
            self.zscore_data = (self.spread_data - rolling_mean) / rolling_std
            self.zscore_data = self.zscore_data.replace([np.inf, -np.inf], np.nan).fillna(0.0)

            logger.info(f"數據更新成功，最新 Z-score: {self.get_current_zscore():.4f}")
            return True

        except Exception as e:
            logger.error(f"更新實時數據失敗: {e}")
            return False

    def _update_backtest_data(self) -> bool:
        """更新回測數據"""
        try:
            if self.backtest_data is None or self.current_index >= len(self.backtest_data):
                return False

            # 獲取當前時間點及之前的數據
            end_index = self.current_index + 1
            start_index = max(0, end_index - self.lookback_period - 50)

            current_data = self.backtest_data.iloc[start_index:end_index].copy()

            # 更新價格數據
            self.price_data = current_data[
                ["base_price", "quote_price", "base_volume", "quote_volume"]
            ].copy()

            # 計算價差
            self.spread_data = self.price_data.apply(
                lambda row: calculate_log_spread(row["base_price"], row["quote_price"]), axis=1
            )

            # 計算 Z-score
            # 使用 Pandas 滾動窗口計算均值和標準差
            rolling_mean = self.spread_data.rolling(window=self.lookback_period).mean()
            rolling_std = self.spread_data.rolling(window=self.lookback_period).std()

            # 計算 Z-score
            # 避免除以零，如果標準差為零，Z-score 設為 0
            self.zscore_data = (self.spread_data - rolling_mean) / rolling_std
            self.zscore_data = self.zscore_data.replace([np.inf, -np.inf], np.nan).fillna(0.0)

            # 移動到下一個時間點
            self.current_index += 1

            logger.debug(
                f"回測數據更新成功，當前索引: {self.current_index}, Z-score: {self.get_current_zscore():.4f}"
            )
            return True

        except Exception as e:
            logger.error(f"更新回測數據失敗: {e}")
            return False

    def has_more_data(self) -> bool:
        """檢查是否還有更多數據（回測模式）"""
        if self.mode == "BACKTEST" and self.backtest_data is not None:
            return self.current_index < len(self.backtest_data)
        return True  # 實時模式總是有更多數據

    def get_current_prices(self) -> Tuple[float, float]:
        """獲取最新價格"""
        if self.price_data.empty:
            if self.mode == "LIVE":  # 只有在 LIVE 模式下才從交易所獲取當前價格
                return self.fetch_current_prices()
            else:
                logger.warning("在 BACKTEST 模式下無法獲取當前價格，且 price_data 為空。")
                return np.nan, np.nan  # 返回 NaN 表示無效價格

        latest_data = self.price_data.iloc[-1]
        return latest_data["base_price"], latest_data["quote_price"]

    def get_current_spread(self) -> float:
        """獲取最新價差"""
        if self.spread_data.empty:
            return np.nan
        return self.spread_data.iloc[-1]

    def get_current_zscore(self) -> float:
        """獲取最新 Z-score"""
        if self.zscore_data.empty:
            return np.nan
        return self.zscore_data.iloc[-1]

    def get_historical_data(self, periods: int = None) -> pd.DataFrame:
        """獲取歷史數據"""
        if periods is None:
            return self.price_data.copy()

        return self.price_data.tail(periods).copy()

    def get_spread_statistics(self, periods: int = None) -> Dict:
        """獲取價差統計信息"""
        try:
            if periods is None:
                spread_data = self.spread_data.dropna()
            else:
                spread_data = self.spread_data.tail(periods).dropna()

            if len(spread_data) == 0:
                return {}

            stats = {
                "mean": spread_data.mean(),
                "std": spread_data.std(),
                "min": spread_data.min(),
                "max": spread_data.max(),
                "current": self.get_current_spread(),
                "current_zscore": self.get_current_zscore(),
            }

            return stats

        except Exception as e:
            logger.error(f"計算價差統計失敗: {e}")
            return {}

    def save_data_to_csv(self, filename: str = None):
        """保存數據到 CSV 文件"""
        try:
            if filename is None:
                timestamp = datetime.now().strftime("%Y % m%d_ % H%M % S")
                filename = f"price_data_{timestamp}.csv"

            # 合併所有數據
            combined_data = self.price_data.copy()
            combined_data["spread"] = self.spread_data
            combined_data["zscore"] = self.zscore_data

            # 創建數據目錄（使用 pathlib 確保跨平台兼容）
            data_dir = Path("data")
            data_dir.mkdir(exist_ok=True)
            filepath = data_dir / filename

            combined_data.to_csv(filepath)
            logger.info(f"數據已保存到: {filepath}")

        except Exception as e:
            logger.error(f"保存數據失敗: {e}")

    def load_data_from_csv(self, filepath: str) -> bool:
        """從 CSV 文件載入數據"""
        try:
            file_path = Path(filepath)

            if not file_path.exists():
                logger.error(f"數據文件不存在: {filepath}")
                return False

            df = pd.read_csv(file_path, index_col=0, parse_dates=True)

            # 分離數據
            self.price_data = df[
                ["base_price", "quote_price", "base_volume", "quote_volume"]
            ].copy()
            self.spread_data = df["spread"].copy()
            self.zscore_data = df["zscore"].copy()

            logger.info(f"數據載入成功: {filepath}")
            return True

        except Exception as e:
            logger.error(f"載入數據失敗: {e}")
            return False
