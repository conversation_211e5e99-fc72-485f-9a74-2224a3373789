import logging

# 配置logger
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
策略健康監控系統 - 監控策略表現並觸發自適應調整
Strategy Health Monitor - Monitors strategy performance and triggers adaptive adjustments
"""

import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple

import numpy as np
import pandas as pd
from scipy.stats import pearsonr
from statsmodels.tsa.stattools import coint

from alert_manager import AlertLevel, get_alert_manager
from dynamic_config import get_dynamic_config_manager
from logging_config import get_logger

_ = get_logger(__name__)


class StrategyHealthMonitor:
    """策略健康監控器"""

    def __init__(self, config_path: str = "config.json"):
        self.config_manager = get_dynamic_config_manager(config_path)
        self.alert_manager = get_alert_manager()

        # 載入健康檢查配置
        self.health_config = self._load_health_config()

        # 策略健康狀態
        self.health_status = {
            "current_pair": None,
            "consecutive_losses": 0,
            "recent_trades": [],
            "sharpe_ratio": 0.0,
            "cointegration_pvalue": 1.0,
            "last_health_check": None,
            "strategy_active": True,
            "degraded_pairs": set(),
            "available_pairs": [],
        }

        # 交易歷史追蹤
        self.trade_history_file = Path("records/trade_history_health.json")
        self._load_trade_history()

        logger.info("StrategyHealthMonitor 初始化完成")

    def _load_health_config(self) -> Dict:
        """載入健康檢查配置"""
        default_config = {
            "max_consecutive_losses": 5,
            "min_sharpe_ratio": -0.5,
            "max_cointegration_pvalue": 0.1,
            "min_correlation": 0.6,
            "health_check_interval_hours": 6,
            "trade_history_window": 20,
            "pair_cooldown_hours": 24,
        }

        # 從配置文件獲取
        config_health = self.config_manager.get_value("strategy_health", {})

        # 合併配置
        health_config = {**default_config, **config_health}

        logger.info(f"策略健康配置: {health_config}")
        return health_config

    def _load_trade_history(self):
        """載入交易歷史"""
        try:
            if self.trade_history_file.exists():
                with open(self.trade_history_file, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    self.health_status["recent_trades"] = data.get("recent_trades", [])
                    self.health_status["degraded_pairs"] = set(data.get("degraded_pairs", []))

                logger.info(f"載入交易歷史: {len(self.health_status['recent_trades'])} 筆交易")

        except Exception as e:
            logger.error(f"載入交易歷史失敗: {e}")

    def _save_trade_history(self):
        """保存交易歷史"""
        try:
            self.trade_history_file.parent.mkdir(exist_ok=True)

            data = {
                "recent_trades": self.health_status["recent_trades"],
                "degraded_pairs": list(self.health_status["degraded_pairs"]),
                "last_updated": datetime.now().isoformat(),
            }

            with open(self.trade_history_file, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

        except Exception as e:
            logger.error(f"保存交易歷史失敗: {e}")

    def record_trade(self, trade_data: Dict):
        """記錄交易數據"""
        try:
            trade_record = {
                "timestamp": datetime.now().isoformat(),
                "pair": trade_data.get("pair", self.health_status["current_pair"]),
                "pnl": trade_data.get("pnl", 0),
                "trade_type": trade_data.get("trade_type", "unknown"),
                "exit_reason": trade_data.get("exit_reason", "unknown"),
                "hold_time": trade_data.get("hold_time", 0),
            }

            # 添加到歷史記錄
            self.health_status["recent_trades"].append(trade_record)

            # 保持窗口大小
            max_trades = self.health_config["trade_history_window"]
            if len(self.health_status["recent_trades"]) > max_trades:
                self.health_status["recent_trades"] = self.health_status["recent_trades"][
                    -max_trades:
                ]

            # 更新連續虧損計數
            self._update_consecutive_losses()

            # 保存歷史
            self._save_trade_history()

            logger.info()
                f"記錄交易: PnL={trade_record['pnl']:.2f}, 連續虧損={self.health_status['consecutive_losses']}"
            )

        except Exception as e:
            logger.error(f"記錄交易失敗: {e}")

    def _update_consecutive_losses(self):
        """更新連續虧損計數"""
        consecutive_losses = 0

        # 從最新交易開始倒序計算
        for trade in reversed(self.health_status["recent_trades"]):
            if trade["pnl"] < 0:
                consecutive_losses += 1
            else:
                break

        self.health_status["consecutive_losses"] = consecutive_losses

    def calculate_sharpe_ratio(self) -> float:
        """計算夏普比率"""
        try:
            if len(self.health_status["recent_trades"]) < 5:
                return 0.0

            returns = [trade["pnl"] for trade in self.health_status["recent_trades"]]

            if not returns:
                return 0.0

            mean_return = np.mean(returns)
            std_return = np.std(returns)

            if std_return == 0:
                return 0.0

            # 年化夏普比率（假設每天1筆交易）
            sharpe = (mean_return / std_return) * np.sqrt(252)

            self.health_status["sharpe_ratio"] = sharpe
            return sharpe

        except Exception as e:
            logger.error(f"計算夏普比率失敗: {e}")
            return 0.0

    def check_cointegration(self, price_data: pd.DataFrame) -> Tuple[float, bool]:
        """檢查當前交易對的共整合性"""
        try:
            if len(price_data) < 50:
                return 1.0, False

            base_prices = price_data["base_price"].dropna()
            quote_prices = price_data["quote_price"].dropna()

            if len(base_prices) != len(quote_prices) or len(base_prices) < 50:
                return 1.0, False

            # 使用對數價格
            log_base = np.log(base_prices)
            log_quote = np.log(quote_prices)

            # Engle-Granger 共整合檢定
            _, p_value, _ = coint(log_base, log_quote)

            self.health_status["cointegration_pvalue"] = p_value
            is_cointegrated = p_value < self.health_config["max_cointegration_pvalue"]

            return p_value, is_cointegrated

        except Exception as e:
            logger.error(f"檢查共整合失敗: {e}")
            return 1.0, False

    def check_correlation(self, price_data: pd.DataFrame) -> Tuple[float, bool]:
        """檢查當前交易對的相關性"""
        try:
            if len(price_data) < 30:
                return 0.0, False

            base_prices = price_data["base_price"].dropna()
            quote_prices = price_data["quote_price"].dropna()

            if len(base_prices) != len(quote_prices) or len(base_prices) < 30:
                return 0.0, False

            correlation, _ = pearsonr(base_prices, quote_prices)

            is_correlated = abs(correlation) >= self.health_config["min_correlation"]

            return correlation, is_correlated

        except Exception as e:
            logger.error(f"檢查相關性失敗: {e}")
            return 0.0, False

    def perform_health_check(self, price_data: Optional[pd.DataFrame] = None) -> Dict:
        """執行完整的健康檢查"""
        try:
            logger.info("開始策略健康檢查...")

            # 更新當前交易對
            current_pair = self.config_manager.get_value("trading_pair")
            self.health_status["current_pair"] = current_pair

            # 計算夏普比率
            sharpe_ratio = self.calculate_sharpe_ratio()

            # 檢查共整合性和相關性
            cointegration_ok = True
            correlation_ok = True

            if price_data is not None:
                coint_pvalue, cointegration_ok = self.check_cointegration(price_data)
                correlation, correlation_ok = self.check_correlation(price_data)

            # 檢查連續虧損
            consecutive_losses_ok = ()
                self.health_status["consecutive_losses"]
                < self.health_config["max_consecutive_losses"]
            )

            # 檢查夏普比率
            sharpe_ok = sharpe_ratio >= self.health_config["min_sharpe_ratio"]

            # 綜合健康狀態
            is_healthy = all([consecutive_losses_ok, sharpe_ok, cointegration_ok, correlation_ok])

            health_report = {
                "timestamp": datetime.now().isoformat(),
                "current_pair": current_pair,
                "is_healthy": is_healthy,
                "consecutive_losses": self.health_status["consecutive_losses"],
                "consecutive_losses_ok": consecutive_losses_ok,
                "sharpe_ratio": sharpe_ratio,
                "sharpe_ok": sharpe_ok,
                "cointegration_pvalue": self.health_status.get("cointegration_pvalue", 1.0),
                "cointegration_ok": cointegration_ok,
                "correlation_ok": correlation_ok,
                "total_trades": len(self.health_status["recent_trades"]),
            }

            self.health_status["last_health_check"] = datetime.now()

            # 發送健康報告
            self._send_health_alert(health_report)

            # 如果不健康，觸發策略降級
            if not is_healthy:
                self._trigger_strategy_degradation(health_report)

            logger.info(f"健康檢查完成: {'健康' if is_healthy else '不健康'}")
            return health_report

        except Exception as e:
            logger.error(f"健康檢查失敗: {e}")
            return {"is_healthy": False, "error": str(e)}

    def _send_health_alert(self, health_report: Dict):
        """發送健康檢查警報"""
        try:
            if health_report["is_healthy"]:
                level = AlertLevel.INFO
                title = "策略健康檢查 - 正常"
                message = "策略運行狀態良好"
            else:
                level = AlertLevel.WARNING
                title = "策略健康檢查 - 異常"
                message = "檢測到策略表現異常，可能需要調整"

            self.alert_manager.send_alert()
                level,
                title,
                message,
                {
                    "交易對": health_report["current_pair"],
                    "連續虧損": health_report["consecutive_losses"],
                    "夏普比率": f"{health_report['sharpe_ratio']:.4f}",
                    "共整合P值": f"{health_report['cointegration_pvalue']:.4f}",
                    "總交易數": health_report["total_trades"],
                },
            )

        except Exception as e:
            logger.error(f"發送健康警報失敗: {e}")

    def _trigger_strategy_degradation(self, health_report: Dict):
        """觸發策略降級"""
        try:
            current_pair = health_report["current_pair"]

            logger.warning(f"觸發策略降級: {current_pair}")

            # 標記當前交易對為降級
            if current_pair:
                pair_key = self._get_pair_key(current_pair)
                self.health_status["degraded_pairs"].add(pair_key)

            # 發送緊急警報
            self.alert_manager.send_critical_alert()
                "策略降級觸發",
                f"交易對 {current_pair} 表現不佳，已觸發策略降級",
                {
                    "降級原因": self._get_degradation_reasons(health_report),
                    "連續虧損": health_report["consecutive_losses"],
                    "夏普比率": health_report["sharpe_ratio"],
                },
            )

            # 保存狀態
            self._save_trade_history()

        except Exception as e:
            logger.error(f"觸發策略降級失敗: {e}")

    def _get_degradation_reasons(self, health_report: Dict) -> List[str]:
        """獲取降級原因"""
        reasons = []

        if not health_report.get("consecutive_losses_ok", True):
            reasons.append(f"連續虧損 {health_report['consecutive_losses']} 次")

        if not health_report.get("sharpe_ok", True):
            reasons.append(f"夏普比率過低 {health_report['sharpe_ratio']:.4f}")

        if not health_report.get("cointegration_ok", True):
            reasons.append(f"共整合性減弱 p={health_report['cointegration_pvalue']:.4f}")

        if not health_report.get("correlation_ok", True):
            reasons.append("相關性不足")

        return reasons

    def should_check_health(self) -> bool:
        """檢查是否應該執行健康檢查"""
        if self.health_status["last_health_check"] is None:
            return True

        last_check = self.health_status["last_health_check"]
        hours_since_check = (datetime.now() - last_check).total_seconds() / 3600

        return hours_since_check >= self.health_config["health_check_interval_hours"]

    def is_pair_degraded(self, pair: List[str]) -> bool:
        """檢查交易對是否已降級"""
        if not pair or len(pair) != 2:
            return False

        pair_key = self._get_pair_key(pair)
        return pair_key in self.health_status["degraded_pairs"]

    def get_health_status(self) -> Dict:
        """獲取健康狀態"""
        return {
            "current_pair": self.health_status["current_pair"],
            "consecutive_losses": self.health_status["consecutive_losses"],
            "sharpe_ratio": self.health_status["sharpe_ratio"],
            "cointegration_pvalue": self.health_status["cointegration_pvalue"],
            "last_health_check": self.health_status["last_health_check"].isoformat()
            if self.health_status["last_health_check"]
            else None,
            "strategy_active": self.health_status["strategy_active"],
            "degraded_pairs_count": len(self.health_status["degraded_pairs"]),
            "recent_trades_count": len(self.health_status["recent_trades"]),
        }

    def reset_pair_degradation(self, pair: List[str]):
        """重置交易對降級狀態"""
        if not pair or len(pair) != 2:
            return

        pair_key = self._get_pair_key(pair)
        if pair_key in self.health_status["degraded_pairs"]:
            self.health_status["degraded_pairs"].remove(pair_key)
            self._save_trade_history()

            logger.info(f"重置交易對降級狀態: {pair_key}")

    def _get_pair_key(self, pair: List[str]) -> str:
        """生成交易對的唯一鍵"""
        return f"{pair[0]}-{pair[1]}"


# 全局策略健康監控器實例
_strategy_health_monitor = None


def get_strategy_health_monitor() -> StrategyHealthMonitor:
    """獲取全局策略健康監控器實例"""
    global _strategy_health_monitor
    if _strategy_health_monitor is None:
        _strategy_health_monitor = StrategyHealthMonitor()
    return _strategy_health_monitor


if __name__ == "__main__":
    # 測試策略健康監控器
    print("測試策略健康監控器...")

    monitor = StrategyHealthMonitor()

    # 模擬一些交易記錄
    for _ in range(10):
        pnl = np.random.normal(0, 50)  # 隨機盈虧
        monitor.record_trade()
            {
                "pair": ["BTCUSDT", "ETHUSDT"],
                "pnl": pnl,
                "trade_type": "test",
                "exit_reason": "test",
            }
        )

    # 執行健康檢查
    health_report = monitor.perform_health_check()
    print(f"健康檢查結果: {health_report}")

    # 獲取健康狀態
    status = monitor.get_health_status()
    print(f"健康狀態: {status}")

    print("策略健康監控器測試完成！")
