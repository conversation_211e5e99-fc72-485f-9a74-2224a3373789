import logging

# 配置logger
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
全面異步化優化 - 實現完整的異步交易循環
Complete Async Optimization - Implement full async trading loop
"""

import asyncio
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Callable, Dict, List, Optional

from logging_config import get_logger

_ = get_logger(__name__)


@dataclass
class AsyncTaskResult:
    """異步任務結果"""

    task_id: str
    success: bool
    result: Any
    error: Optional[str] = None
    execution_time: float = 0.0


class AsyncTaskManager:
    """異步任務管理器"""

    def __init__(self, max_concurrent_tasks: int = 50):
        self.max_concurrent_tasks = max_concurrent_tasks
        self.semaphore = asyncio.Semaphore(max_concurrent_tasks)
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self.completed_tasks: List[AsyncTaskResult] = []

        logger.info(f"異步任務管理器初始化: 最大並發 {max_concurrent_tasks}")

    async def execute_task(self, task_id: str, coro: Callable, *args, **kwargs) -> AsyncTaskResult:
        """執行異步任務"""
        start_time = asyncio.get_event_loop().time()

        async with self.semaphore:
            try:
                if asyncio.iscoroutinefunction(coro):
                    result = await coro(*args, **kwargs)
                else:
                    result = await asyncio.get_event_loop().run_in_executor(
                        None, coro, *args, **kwargs
                    )

                execution_time = asyncio.get_event_loop().time() - start_time

                task_result = AsyncTaskResult()
                    task_id=task_id, success=True, result=result, execution_time=execution_time
                )

                self.completed_tasks.append(task_result)
                logger.debug(f"任務完成: {task_id} ({execution_time:.3f}s)")

                return task_result

            except Exception as e:
                execution_time = asyncio.get_event_loop().time() - start_time

                task_result = AsyncTaskResult()
                    task_id=task_id,
                    success=False,
                    result=None,
                    error=str(e),
                    execution_time=execution_time,
                )

                self.completed_tasks.append(task_result)
                logger.error(f"任務失敗: {task_id} - {e}")

                return task_result

    async def execute_batch(self, tasks: List[tuple]) -> List[AsyncTaskResult]:
        """批量執行任務"""
        task_coroutines = [
            self.execute_task(task_id, coro, *args, **kwargs)
            for task_id, coro, args, kwargs in tasks
        ]

        results = await asyncio.gather(*task_coroutines, return_exceptions=True)

        # 處理異常結果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                task_id = tasks[i][0]
                error_result = AsyncTaskResult(
                    task_id=task_id, success=False, result=None, error=str(result)
                )
                processed_results.append(error_result)
            else:
                processed_results.append(result)

        return processed_results

    def get_task_statistics(self) -> Dict[str, Any]:
        """獲取任務統計"""
        total_tasks = len(self.completed_tasks)
        successful_tasks = sum(1 for task in self.completed_tasks if task.success)
        failed_tasks = total_tasks - successful_tasks

        if total_tasks > 0:
            avg_execution_time = (
                sum(task.execution_time for task in self.completed_tasks) / total_tasks
            )
            success_rate = successful_tasks / total_tasks
        else:
            avg_execution_time = 0.0
            success_rate = 0.0

        return {
            "total_tasks": total_tasks,
            "successful_tasks": successful_tasks,
            "failed_tasks": failed_tasks,
            "success_rate": success_rate,
            "avg_execution_time": avg_execution_time,
            "running_tasks": len(self.running_tasks),
        }


class AsyncTradingLoop:
    """異步交易循環"""

    def __init__(self, portfolio_system):
        self.portfolio_system = portfolio_system
        self.task_manager = AsyncTaskManager()
        self.is_running = False
        self.loop_interval = 60  # 60秒循環間隔

        logger.info("異步交易循環初始化完成")

    async def start_trading_loop(self):
        """啟動異步交易循環"""
        if self.is_running:
            logger.warning("交易循環已在運行")
            return

        self.is_running = True
        logger.info("🚀 啟動異步交易循環")

        try:
            while self.is_running:
                loop_start_time = asyncio.get_event_loop().time()

                # 執行一個完整的交易循環
                await self._execute_trading_cycle()

                # 計算循環執行時間
                loop_execution_time = asyncio.get_event_loop().time() - loop_start_time

                # 等待下一個循環
                sleep_time = max(0, self.loop_interval - loop_execution_time)
                if sleep_time > 0:
                    await asyncio.sleep(sleep_time)

                logger.debug(f"交易循環完成: 執行時間 {loop_execution_time:.2f}s")

        except asyncio.CancelledError:
            logger.info("交易循環被取消")
        except Exception as e:
            logger.error(f"交易循環異常: {e}")
        finally:
            self.is_running = False
            logger.info("異步交易循環已停止")

    async def _execute_trading_cycle(self):
        """執行一個交易循環"""
        try:
            # 1. 並發獲取市場數據
            market_data_task = self.task_manager.execute_task()
                "market_data_update", self._update_market_data
            )

            # 2. 並發生成交易信號
            signal_generation_task = self.task_manager.execute_task()
                "signal_generation", self._generate_trading_signals
            )

            # 3. 等待數據和信號完成
            market_data_result, signal_result = await asyncio.gather()
                market_data_task, signal_generation_task
            )

            # 4. 如果有信號，執行交易
            if signal_result.success and signal_result.result:
                trading_task = self.task_manager.execute_task()
                    "trade_execution", self._execute_trades, signal_result.result
                )

                await trading_task

            # 5. 並發執行系統維護任務
            maintenance_tasks = [
                ("health_check", self._perform_health_check, (), {}),
                ("state_persistence", self._persist_system_state, (), {}),
                ("performance_monitoring", self._monitor_performance, (), {}),
            ]

            await self.task_manager.execute_batch(maintenance_tasks)

        except Exception as e:
            logger.error(f"交易循環執行失敗: {e}")

    async def _update_market_data(self):
        """更新市場數據"""
        try:
            # 獲取所有活躍策略的交易對
            symbols = []
            for strategy in self.portfolio_system.multi_strategy_engine.strategies.values():
                if hasattr(strategy, "symbols"):
                    symbols.extend(strategy.symbols)

            # 去重
            unique_symbols = list(set(symbols))

            # 並發獲取市場數據
            from unified_data_handler import get_unified_data_handler

            data_handler = get_unified_data_handler(async_enabled=True)

            market_data = await data_handler.get_multiple_symbols_data(unique_symbols)

            # 更新策略的市場數據
            for strategy in self.portfolio_system.multi_strategy_engine.strategies.values():
                if hasattr(strategy, "update_market_data"):
                    await strategy.update_market_data(market_data)

            logger.debug(f"市場數據更新完成: {len(unique_symbols)} 個交易對")
            return market_data

        except Exception as e:
            logger.error(f"市場數據更新失敗: {e}")
            raise

    async def _generate_trading_signals(self):
        """生成交易信號"""
        try:
            _ = []

            # 並發生成所有策略的信號
            signal_tasks = []
            for ()
                strategy_id,
                strategy,
            ) in self.portfolio_system.multi_strategy_engine.strategies.items():
                if hasattr(strategy, "generate_signals_async"):
                    task = self.task_manager.execute_task()
                        f"signal_{strategy_id}", strategy.generate_signals_async
                    )
                    signal_tasks.append((strategy_id, task))
                elif hasattr(strategy, "generate_signals"):
                    # 將同步方法包裝為異步
                    task = self.task_manager.execute_task()
                        f"signal_{strategy_id}", strategy.generate_signals
                    )
                    signal_tasks.append((strategy_id, task))

            # 等待所有信號生成完成
            for strategy_id, task in signal_tasks:
                result = await task
                if result.success and result.result:
                    all_signals.extend(result.result)

            logger.debug(f"交易信號生成完成: {len(all_signals)} 個信號")
            return all_signals

        except Exception as e:
            logger.error(f"交易信號生成失敗: {e}")
            raise

    async def _execute_trades(self, signals):
        """執行交易"""
        try:
            executed_trades = []

            # 並發執行交易
            trade_tasks = []
            for signal in signals:
                task = self.task_manager.execute_task()
                    f"trade_{signal.symbol}_{signal.side}",
                    self.portfolio_system.safe_trading_executor.execute_signal_async,
                    signal,
                )
                trade_tasks.append(task)

            # 等待所有交易完成
            trade_results = await asyncio.gather(*trade_tasks, return_exceptions=True)

            for result in trade_results:
                if isinstance(result, AsyncTaskResult) and result.success:
                    executed_trades.append(result.result)

            logger.info(f"交易執行完成: {len(executed_trades)} 筆交易")
            return executed_trades

        except Exception as e:
            logger.error(f"交易執行失敗: {e}")
            raise

    async def _perform_health_check(self):
        """執行健康檢查"""
        try:
            # 檢查各個組件的健康狀態
            health_status = {
                "timestamp": datetime.now().isoformat(),
                "portfolio_system": True,
                "event_bus": self.portfolio_system.global_event_bus.is_running,
                "strategies": {},
                "database": True,
            }

            # 檢查策略健康狀態
            for ()
                strategy_id,
                strategy,
            ) in self.portfolio_system.multi_strategy_engine.strategies.items():
                health_status["strategies"][strategy_id] = {
                    "health_score": getattr(strategy, "health_score", 0.5),
                    "active": getattr(strategy, "is_active", True),
                }

            # 檢查數據庫連接
            try:
                from comprehensive_optimization import enhanced_resource_manager

                db_stats = enhanced_resource_manager.get_connection_stats()
                health_status["database_connections"] = db_stats["stats"]
            except Exception as e:
                health_status["database"] = False
                health_status["database_error"] = str(e)

            return health_status

        except Exception as e:
            logger.error(f"健康檢查失敗: {e}")
            raise

    async def _persist_system_state(self):
        """持久化系統狀態"""
        try:
            # 異步保存系統狀態
            if hasattr(self.portfolio_system, "state_persistence_manager"):
                # 保存所有策略狀態
                save_tasks = []
                for ()
                    strategy_id,
                    strategy,
                ) in self.portfolio_system.multi_strategy_engine.strategies.items():
                    if hasattr(strategy, "get_state"):
                        state_data = strategy.get_state()
                        health_score = getattr(strategy, "health_score", 0.5)

                        task = self.task_manager.execute_task()
                            f"save_state_{strategy_id}",
                            self.portfolio_system.state_persistence_manager.save_strategy_state,
                            strategy_id,
                            state_data,
                            health_score,
                        )
                        save_tasks.append(task)

                # 等待所有保存任務完成
                await asyncio.gather(*save_tasks, return_exceptions=True)

            logger.debug("系統狀態持久化完成")

        except Exception as e:
            logger.error(f"系統狀態持久化失敗: {e}")
            raise

    async def _monitor_performance(self):
        """監控性能"""
        try:
            # 獲取任務管理器統計
            task_stats = self.task_manager.get_task_statistics()

            # 獲取系統概覽
            system_overview = self.portfolio_system.get_system_overview()

            # 記錄性能指標
            performance_metrics = {
                "timestamp": datetime.now().isoformat(),
                "task_statistics": task_stats,
                "system_overview": system_overview,
            }

            # 發布性能監控事件
            from enhanced_event_integration import EventType, publish_event

            publish_event(EventType.HEALTH_CHECK, "async_trading_loop", performance_metrics)

            logger.debug("性能監控完成")
            return performance_metrics

        except Exception as e:
            logger.error(f"性能監控失敗: {e}")
            raise

    async def stop_trading_loop(self):
        """停止交易循環"""
        self.is_running = False
        logger.info("交易循環停止信號已發送")


# 全局異步交易循環實例
async_trading_loop = None


def get_async_trading_loop(portfolio_system) -> AsyncTradingLoop:
    """獲取異步交易循環實例"""
    global async_trading_loop
    if async_trading_loop is None:
        async_trading_loop = AsyncTradingLoop(portfolio_system)
    return async_trading_loop


async def main():
    """測試異步優化"""
    print("🧪 異步優化測試")

    # 創建任務管理器
    task_manager = AsyncTaskManager(max_concurrent_tasks=10)

    # 測試並發任務執行
    async def test_task(task_id: str, delay: float):
        await asyncio.sleep(delay)
        return f"Task {task_id} completed"

    # 創建測試任務
    tasks = [(f"task_{i}", test_task, (f"task_{i}", 0.1 * i), {}) for _ in range(5)]

    # 執行批量任務
    results = await task_manager.execute_batch(tasks)

    # 顯示結果
    for result in results:
        print(
            f"  {result.task_id}: {'✅' if result.success else '❌'} ({result.execution_time:.3f}s)"
        )

    # 顯示統計
    stats = task_manager.get_task_statistics()
    print(f"  📊 任務統計: {stats}")

    print("✅ 異步優化測試完成")


if __name__ == "__main__":
    asyncio.run(main())
