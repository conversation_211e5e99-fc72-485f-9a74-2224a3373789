import logging
from typing import Any, Dict, List

# 配置logger
logger = logging.getLogger(__name__)

#!/usr/bin/env python3
"""
智能資金管理系統 - 50倍槓桿但限制實際風險敞口
Smart Capital Management - 50x leverage with controlled risk exposure
"""

from dataclasses import dataclass
from enum import Enum

from logging_config import get_logger

_ = get_logger(__name__)


class RiskLevel(Enum):
    """風險等級"""

    _ = "conservative"  # 5倍等效槓桿
    _ = "moderate"  # 7.5倍等效槓桿
    _ = "aggressive"  # 10倍等效槓桿


@dataclass
class CapitalAllocation:
    """資金配置"""

    total_capital: float  # 總資金
    max_utilization_rate: float  # 最大資金利用率
    effective_leverage: float  # 有效槓桿倍數
    max_position_value: float  # 最大倉位價值
    max_margin_used: float  # 最大保證金使用
    risk_buffer: float  # 風險緩衝


class SmartCapitalManager:
    """智能資金管理器"""

    def __init__(
        self,
        total_capital: float,
        risk_level: RiskLevel = RiskLevel.MODERATE,
        exchange_leverage: int = 50,
    ):
        """
        初始化資金管理器

        Args:
            total_capital: 總資金
            risk_level: 風險等級
            exchange_leverage: 交易所設定的槓桿倍數
        """
        self.total_capital = total_capital
        self.risk_level = risk_level
        self.exchange_leverage = exchange_leverage

        # 根據風險等級設定參數
        self.risk_params = self._get_risk_parameters()

        # 計算資金配置
        self.allocation = self._calculate_allocation()

        logger.info("智能資金管理器初始化完成")
        logger.info(f"總資金: ${total_capital:,.2f}")
        logger.info(f"風險等級: {risk_level.value}")
        logger.info(f"有效槓桿: {self.allocation.effective_leverage}x")
        logger.info(f"最大倉位價值: ${self.allocation.max_position_value:,.2f}")

    def _get_risk_parameters(self) -> Dict[str, float]:
        """獲取風險參數"""
        _ = {
            RiskLevel.CONSERVATIVE: {
                "effective_leverage": 5.0,
                "max_utilization_rate": 0.10,  # 10 % 資金利用率
                "risk_buffer": 0.05,  # 5 % 風險緩衝
                "max_single_position": 0.03,  # 單筆最大3%
                "correlation_limit": 0.8,  # 相關性限制
            },
            RiskLevel.MODERATE: {
                "effective_leverage": 7.5,
                "max_utilization_rate": 0.15,  # 15 % 資金利用率
                "risk_buffer": 0.03,  # 3 % 風險緩衝
                "max_single_position": 0.05,  # 單筆最大5%
                "correlation_limit": 0.7,
            },
            RiskLevel.AGGRESSIVE: {
                "effective_leverage": 10.0,
                "max_utilization_rate": 0.20,  # 20 % 資金利用率
                "risk_buffer": 0.02,  # 2 % 風險緩衝
                "max_single_position": 0.08,  # 單筆最大8%
                "correlation_limit": 0.6,
            },
        }

        return params[self.risk_level]

    def _calculate_allocation(self) -> CapitalAllocation:
        """計算資金配置"""
        params = self.risk_params

        # 計算最大資金利用
        max_capital_used = self.total_capital * params["max_utilization_rate"]

        # 計算有效槓桿
        effective_leverage = params["effective_leverage"]

        # 計算最大倉位價值（考慮有效槓桿）
        max_position_value = max_capital_used * effective_leverage

        # 計算實際需要的保證金（基於交易所槓桿）
        max_margin_used = max_position_value / self.exchange_leverage

        # 風險緩衝
        risk_buffer = self.total_capital * params["risk_buffer"]

        return CapitalAllocation(
            total_capital=self.total_capital,
            max_utilization_rate=params["max_utilization_rate"],
            effective_leverage=effective_leverage,
            max_position_value=max_position_value,
            max_margin_used=max_margin_used,
            risk_buffer=risk_buffer,
        )

    def calculate_position_size(
        self, symbol: str, price: float, confidence: float = 1.0
    ) -> Dict[str, Any]:
        """
        計算倉位大小

        Args:
            symbol: 交易對
            price: 當前價格
            confidence: 信號置信度 (0-1)

        Returns:
            倉位計算結果
        """
        params = self.risk_params

        # 基礎倉位大小（基於單筆最大限制）
        base_position_value = self.total_capital * params["max_single_position"]

        # 根據置信度調整
        adjusted_position_value = base_position_value * confidence

        # 確保不超過最大倉位限制
        max_allowed = self.allocation.max_position_value / 2  # 配對交易需要兩個倉位
        position_value = min(adjusted_position_value, max_allowed)

        # 計算實際數量
        quantity = position_value / price

        # 計算所需保證金
        required_margin = position_value / self.exchange_leverage

        # 計算風險指標
        risk_percentage = (required_margin / self.total_capital) * 100
        effective_leverage_used = position_value / required_margin

        return {
            "symbol": symbol,
            "position_value": position_value,
            "quantity": quantity,
            "required_margin": required_margin,
            "risk_percentage": risk_percentage,
            "effective_leverage_used": effective_leverage_used,
            "confidence": confidence,
            "max_loss_potential": required_margin,  # 最大損失等於保證金
            "recommended": risk_percentage <= (params["max_single_position"] * 100),
        }

    def calculate_pair_position(
        self,
        long_symbol: str,
        long_price: float,
        short_symbol: str,
        short_price: float,
        signal_strength: float = 1.0,
    ) -> Dict[str, Any]:
        """
        計算配對交易倉位

        Args:
            long_symbol: 做多標的
            long_price: 做多價格
            short_symbol: 做空標的
            short_price: 做空價格
            signal_strength: 信號強度 (0-1)

        Returns:
            配對倉位計算結果
        """
        # 計算單邊倉位
        long_position = self.calculate_position_size(long_symbol, long_price, signal_strength)
        short_position = self.calculate_position_size(short_symbol, short_price, signal_strength)

        # 配對交易總風險
        total_margin = long_position["required_margin"] + short_position["required_margin"]
        total_risk_percentage = (total_margin / self.total_capital) * 100

        # 檢查是否超過限制
        max_pair_risk = self.risk_params["max_single_position"] * 2 * 100  # 配對交易可以是單筆的2倍

        return {
            "long_position": long_position,
            "short_position": short_position,
            "total_margin_required": total_margin,
            "total_risk_percentage": total_risk_percentage,
            "max_pair_risk_percentage": max_pair_risk,
            "within_limits": total_risk_percentage <= max_pair_risk,
            "signal_strength": signal_strength,
            "pair_correlation_risk": self._estimate_correlation_risk(long_symbol, short_symbol),
        }

    def _estimate_correlation_risk(self, symbol1: str, symbol2: str) -> float:
        """估算相關性風險"""
        # 這裡可以實現實際的相關性計算
        # 暫時返回預設值
        common_pairs = {
            ("BTC/USDT:USDT", "ETH/USDT:USDT"): 0.7,
            ("ETH/USDT:USDT", "AVAX/USDT:USDT"): 0.6,
            ("ADA/USDT:USDT", "DOT/USDT:USDT"): 0.5,
        }

        pair_key = (symbol1, symbol2)
        reverse_key = (symbol2, symbol1)

        return common_pairs.get(pair_key, common_pairs.get(reverse_key, 0.5))

    def get_current_utilization(self, active_positions: List[Dict]) -> Dict[str, Any]:
        """獲取當前資金利用率"""
        total_margin_used = sum(pos.get("margin_used", 0) for pos in active_positions)
        total_position_value = sum(pos.get("position_value", 0) for pos in active_positions)

        utilization_rate = total_margin_used / self.total_capital
        effective_leverage_current = (
            total_position_value / total_margin_used if total_margin_used > 0 else 0
        )

        return {
            "total_margin_used": total_margin_used,
            "total_position_value": total_position_value,
            "utilization_rate": utilization_rate,
            "utilization_percentage": utilization_rate * 100,
            "effective_leverage_current": effective_leverage_current,
            "remaining_capacity": self.allocation.max_margin_used - total_margin_used,
            "can_open_new_position": total_margin_used < self.allocation.max_margin_used,
        }

    def get_risk_metrics(self) -> Dict[str, Any]:
        """獲取風險指標"""
        return {
            "risk_level": self.risk_level.value,
            "total_capital": self.total_capital,
            "max_utilization_rate": self.allocation.max_utilization_rate,
            "effective_leverage_target": self.allocation.effective_leverage,
            "exchange_leverage": self.exchange_leverage,
            "max_position_value": self.allocation.max_position_value,
            "max_margin_used": self.allocation.max_margin_used,
            "risk_buffer": self.allocation.risk_buffer,
            "max_single_position_pct": self.risk_params["max_single_position"] * 100,
            "liquidation_buffer": (1 / self.exchange_leverage) * 100,  # 強制平倉緩衝
            "effective_liquidation_buffer": (1 / self.allocation.effective_leverage) * 100,
        }

    def validate_new_position(
        self, position_info: Dict, current_positions: List[Dict]
    ) -> Dict[str, Any]:
        """驗證新倉位是否符合風險限制"""
        current_util = self.get_current_utilization(current_positions)

        new_margin = position_info.get("required_margin", 0)
        total_margin_after = current_util["total_margin_used"] + new_margin

        # 檢查各種限制
        checks = {
            "within_margin_limit": total_margin_after <= self.allocation.max_margin_used,
            "within_utilization_limit": (total_margin_after / self.total_capital)
            <= self.allocation.max_utilization_rate,
            "within_single_position_limit": position_info.get("risk_percentage", 0)
            <= (self.risk_params["max_single_position"] * 100),
            "sufficient_buffer": (self.total_capital - total_margin_after)
            >= self.allocation.risk_buffer,
        }

        all_passed = all(checks.values())

        return {
            "approved": all_passed,
            "checks": checks,
            "current_utilization": current_util,
            "new_margin_required": new_margin,
            "total_margin_after": total_margin_after,
            "remaining_capacity_after": self.allocation.max_margin_used - total_margin_after,
        }


def create_capital_manager(config: Dict[str, Any]) -> SmartCapitalManager:
    """創建資金管理器"""
    # 從配置中獲取參數
    total_capital = config.get("total_capital", 10000)
    risk_level_str = config.get("risk_level", "moderate")
    exchange_leverage = config.get("leverage", 50)

    # 轉換風險等級
    risk_level = RiskLevel(risk_level_str.lower())

    return SmartCapitalManager(
        total_capital=total_capital, risk_level=risk_level, exchange_leverage=exchange_leverage
    )


if __name__ == "__main__":
    # 測試資金管理器
    print("🧪 智能資金管理器測試")
    print("=" * 50)

    # 創建管理器
    manager = SmartCapitalManager(
        total_capital=10000, risk_level=RiskLevel.MODERATE, exchange_leverage=50
    )  # $10,000

    # 顯示風險指標
    metrics = manager.get_risk_metrics()
    print("\n📊 風險指標:")
    print(f"總資金: ${metrics['total_capital']:,.2f}")
    print(f"最大資金利用率: {metrics['max_utilization_rate']:.1%}")
    print(f"有效槓桿: {metrics['effective_leverage_target']}x")
    print(f"交易所槓桿: {metrics['exchange_leverage']}x")
    print(f"最大倉位價值: ${metrics['max_position_value']:,.2f}")
    print(f"最大保證金使用: ${metrics['max_margin_used']:,.2f}")

    # 測試配對交易計算
    pair_result = manager.calculate_pair_position(
        "BTC/USDT:USDT", 50000, "ETH/USDT:USDT", 3000, signal_strength=0.8
    )

    print("\n🎯 配對交易示例:")
    print(f"BTC多頭倉位價值: ${pair_result['long_position']['position_value']:,.2f}")
    print(f"ETH空頭倉位價值: ${pair_result['short_position']['position_value']:,.2f}")
    print(f"總保證金需求: ${pair_result['total_margin_required']:,.2f}")
    print(f"總風險百分比: {pair_result['total_risk_percentage']:.2f}%")
    print(f"是否在限制內: {'✅' if pair_result['within_limits'] else '❌'}")
