import logging

# 配置logger
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
數據庫管理器 - 統一管理所有數據存儲
Database Manager - Unified data storage management
"""

import json
import sqlite3
import threading
import time
from contextlib import contextmanager
from datetime import datetime, timedelta
from pathlib import Path
from queue import Queue
from typing import Any, Dict, List, Optional

import pandas as pd

from enhanced_retry_handler import RetryStrategy, enhanced_retry
from logging_config import get_logger

_ = get_logger(__name__)


class DatabaseManager:
    """數據庫管理器"""

    def __init__(self, db_path: str = "data/trading_platform.db"):
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)

        # 批量處理配置
        self.batch_size = 100
        self.batch_queue = Queue()
        self.batch_thread = None
        self.batch_running = False
        self.batch_lock = threading.Lock()

        # 初始化數據庫
        self._initialize_database()

        # 啟動批量處理線程
        self._start_batch_processor()

        logger.info(f"DatabaseManager 初始化完成: {db_path}")

    def _start_batch_processor(self):
        """啟動批量處理線程"""
        self.batch_running = True
        self.batch_thread = threading.Thread(target=self._batch_processor, daemon=True)
        self.batch_thread.start()
        logger.info("批量處理線程已啟動")

    def _batch_processor(self):
        """批量處理線程"""
        batch_operations = []

        while self.batch_running:
            try:
                # 收集批量操作
                while len(batch_operations) < self.batch_size:
                    try:
                        operation = self.batch_queue.get(timeout=1.0)
                        batch_operations.append(operation)
                    except Exception:
                        break

                # 執行批量操作
                if batch_operations:
                    self._execute_batch_operations(batch_operations)
                    batch_operations.clear()

            except Exception as e:
                logger.error(f"批量處理錯誤: {e}")
                time.sleep(1)

    @enhanced_retry(
        max_retries=3,
        base_delay=0.5,
        max_delay=5.0,
        strategy=RetryStrategy.EXPONENTIAL,
        exceptions=(sqlite3.OperationalError, sqlite3.DatabaseError),
    )
    def _execute_batch_operations(self, operations: List[Dict]):
        """執行批量數據庫操作"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                for operation in operations:
                    _ = operation["type"]
                    sql = operation["sql"]
                    params = operation.get("params", ())

                    cursor.execute(sql, params)

                conn.commit()
                logger.debug(f"批量執行 {len(operations)} 個數據庫操作")

        except Exception as e:
            logger.error(f"批量數據庫操作失敗: {e}")
            raise

    @contextmanager
    def get_connection(self):
        """獲取數據庫連接（上下文管理器）"""
        conn = sqlite3.connect(str(self.db_path), timeout=30.0)
        conn.row_factory = sqlite3.Row  # 使結果可以按列名訪問
        # 設置WAL模式提升並發性能
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA synchronous=NORMAL")
        conn.execute("PRAGMA cache_size=10000")
        try:
            yield conn
        finally:
            conn.close()

    def _initialize_database(self):
        """初始化數據庫表結構"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 交易記錄表
                cursor.execute(
                    """
                    CREATE TABLE IF NOT EXISTS trades (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                        pair_key TEXT NOT NULL,
                        base_symbol TEXT NOT NULL,
                        quote_symbol TEXT NOT NULL,
                        trade_type TEXT NOT NULL,
                        entry_price REAL,
                        exit_price REAL,
                        quantity REAL,
                        pnl REAL,
                        pnl_percentage REAL,
                        hold_time_seconds INTEGER,
                        exit_reason TEXT,
                        z_score_entry REAL,
                        z_score_exit REAL,
                        commission REAL,
                        metadata TEXT  -- JSON格式的額外數據
                    )
                    """
                )

                # 配對表現表
                cursor.execute(
                    """
                    CREATE TABLE IF NOT EXISTS pair_performance (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        pair_key TEXT NOT NULL,
                        base_symbol TEXT NOT NULL,
                        quote_symbol TEXT NOT NULL,
                        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                        total_trades INTEGER DEFAULT 0,
                        winning_trades INTEGER DEFAULT 0,
                        losing_trades INTEGER DEFAULT 0,
                        total_pnl REAL DEFAULT 0,
                        win_rate REAL DEFAULT 0,
                        avg_pnl REAL DEFAULT 0,
                        sharpe_ratio REAL DEFAULT 0,
                        max_drawdown REAL DEFAULT 0,
                        correlation REAL,
                        cointegration_p_value REAL,
                        score REAL,
                        is_active BOOLEAN DEFAULT 1,
                        last_trade_time DATETIME,
                        metadata TEXT
                    )
                    """
                )

                # 投資組合狀態表
                cursor.execute(
                    """
                    CREATE TABLE IF NOT EXISTS portfolio_snapshots (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                        total_capital REAL,
                        allocated_capital REAL,
                        available_capital REAL,
                        total_pnl REAL,
                        total_pnl_percentage REAL,
                        active_pairs INTEGER,
                        total_trades INTEGER,
                        portfolio_sharpe REAL,
                        max_drawdown REAL,
                        daily_pnl REAL,
                        metadata TEXT
                    )
                    """
                )

                # 策略健康記錄表
                cursor.execute(
                    """
                    CREATE TABLE IF NOT EXISTS strategy_health (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                        pair_key TEXT NOT NULL,
                        consecutive_losses INTEGER DEFAULT 0,
                        sharpe_ratio REAL DEFAULT 0,
                        cointegration_p_value REAL DEFAULT 1,
                        correlation REAL DEFAULT 0,
                        is_healthy BOOLEAN DEFAULT 1,
                        degradation_reason TEXT,
                        health_score REAL DEFAULT 100,
                        metadata TEXT
                    )
                    """
                )

                # 警報記錄表
                cursor.execute(
                    """
                    CREATE TABLE IF NOT EXISTS alerts (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                        level TEXT NOT NULL,
                        title TEXT NOT NULL,
                        message TEXT NOT NULL,
                        source TEXT,
                        pair_key TEXT,
                        is_read BOOLEAN DEFAULT 0,
                        metadata TEXT
                    )
                    """
                )

                # 系統事件表
                cursor.execute(
                    """
                    CREATE TABLE IF NOT EXISTS system_events (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                        event_type TEXT NOT NULL,
                        event_data TEXT NOT NULL,
                        source TEXT,
                        severity TEXT DEFAULT 'INFO'
                    )
                    """
                )

                # 創建索引
                cursor.execute(
                    "CREATE INDEX IF NOT EXISTS idx_trades_pair_timestamp ON trades(pair_key, timestamp)"
                )
                cursor.execute(
                    "CREATE INDEX IF NOT EXISTS idx_trades_timestamp ON trades(timestamp)"
                )
                cursor.execute(
                    "CREATE INDEX IF NOT EXISTS idx_pair_performance_pair ON pair_performance(pair_key)"
                )
                cursor.execute(
                    "CREATE INDEX IF NOT EXISTS idx_portfolio_timestamp ON portfolio_snapshots(timestamp)"
                )
                cursor.execute(
                    "CREATE INDEX IF NOT EXISTS idx_alerts_timestamp ON alerts(timestamp)"
                )
                cursor.execute(
                    "CREATE INDEX IF NOT EXISTS idx_events_timestamp ON system_events(timestamp)"
                )

                conn.commit()
                logger.info("數據庫表結構初始化完成")

        except Exception as e:
            logger.error(f"初始化數據庫失敗: {e}")
            raise

    def record_trade(self, trade_data: Dict) -> int:
        """記錄交易"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                cursor.execute(
                    """
                    INSERT INTO trades (
                        pair_key, base_symbol, quote_symbol, trade_type,
                        entry_price, exit_price, quantity, pnl, pnl_percentage,
                        hold_time_seconds, exit_reason, z_score_entry, z_score_exit,
                        commission, metadata
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """,
                    (
                        trade_data.get("pair_key"),
                        trade_data.get("base_symbol"),
                        trade_data.get("quote_symbol"),
                        trade_data.get("trade_type"),
                        trade_data.get("entry_price"),
                        trade_data.get("exit_price"),
                        trade_data.get("quantity"),
                        trade_data.get("pnl"),
                        trade_data.get("pnl_percentage"),
                        trade_data.get("hold_time_seconds"),
                        trade_data.get("exit_reason"),
                        trade_data.get("z_score_entry"),
                        trade_data.get("z_score_exit"),
                        trade_data.get("commission"),
                        json.dumps(trade_data.get("metadata", {})),
                    ),
                )

                trade_id = cursor.lastrowid
                conn.commit()

                logger.info(f"交易記錄已保存: ID={trade_id}, 配對={trade_data.get('pair_key')}")
                return trade_id

        except Exception as e:
            logger.error(f"記錄交易失敗: {e}")
            return -1

    def get_trades(
        self,
        pair_key: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: Optional[int] = None,
    ) -> List[Dict]:
        """獲取交易記錄"""
        try:
            with self.get_connection() as conn:
                query = "SELECT * FROM trades WHERE 1=1"
                params = []

                if pair_key:
                    query += " AND pair_key = ?"
                    params.append(pair_key)

                if start_date:
                    query += " AND timestamp >= ?"
                    params.append(start_date.isoformat())

                if end_date:
                    query += " AND timestamp <= ?"
                    params.append(end_date.isoformat())

                query += " ORDER BY timestamp DESC"

                if limit:
                    query += " LIMIT ?"
                    params.append(limit)

                cursor = conn.cursor()
                cursor.execute(query, params)

                trades = []
                for row in cursor.fetchall():
                    trade = dict(row)
                    if trade["metadata"]:
                        trade["metadata"] = json.loads(trade["metadata"])
                    trades.append(trade)

                return trades

        except Exception as e:
            logger.error(f"獲取交易記錄失敗: {e}")
            return []

    def update_pair_performance(self, pair_key: str, performance_data: Dict):
        """更新配對表現"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 檢查是否已存在記錄
                cursor.execute("SELECT id FROM pair_performance WHERE pair_key = ?", (pair_key,))
                existing = cursor.fetchone()

                if existing:
                    # 更新現有記錄
                    cursor.execute(
                        """
                        UPDATE pair_performance SET
                            total_trades = ?, winning_trades = ?, losing_trades = ?,
                            total_pnl = ?, win_rate = ?, avg_pnl = ?, sharpe_ratio = ?,
                            max_drawdown = ?, correlation = ?, cointegration_p_value = ?,
                            score = ?, is_active = ?, last_trade_time = ?, metadata = ?,
                            timestamp = CURRENT_TIMESTAMP
                        WHERE pair_key = ?
                        """,
                        (
                            performance_data.get("total_trades", 0),
                            performance_data.get("winning_trades", 0),
                            performance_data.get("losing_trades", 0),
                            performance_data.get("total_pnl", 0),
                            performance_data.get("win_rate", 0),
                            performance_data.get("avg_pnl", 0),
                            performance_data.get("sharpe_ratio", 0),
                            performance_data.get("max_drawdown", 0),
                            performance_data.get("correlation"),
                            performance_data.get("cointegration_p_value"),
                            performance_data.get("score"),
                            performance_data.get("is_active", True),
                            performance_data.get("last_trade_time"),
                            json.dumps(performance_data.get("metadata", {})),
                            pair_key,
                        ),
                    )
                else:
                    # 插入新記錄
                    symbols = pair_key.split("-")
                    base_symbol = symbols[0] if len(symbols) > 0 else ""
                    quote_symbol = symbols[1] if len(symbols) > 1 else ""

                    cursor.execute(
                        """
                        INSERT INTO pair_performance (
                            pair_key, base_symbol, quote_symbol, total_trades,
                            winning_trades, losing_trades, total_pnl, win_rate,
                            avg_pnl, sharpe_ratio, max_drawdown, correlation,
                            cointegration_p_value, score, is_active, last_trade_time, metadata
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """,
                        (
                            pair_key,
                            base_symbol,
                            quote_symbol,
                            performance_data.get("total_trades", 0),
                            performance_data.get("winning_trades", 0),
                            performance_data.get("losing_trades", 0),
                            performance_data.get("total_pnl", 0),
                            performance_data.get("win_rate", 0),
                            performance_data.get("avg_pnl", 0),
                            performance_data.get("sharpe_ratio", 0),
                            performance_data.get("max_drawdown", 0),
                            performance_data.get("correlation"),
                            performance_data.get("cointegration_p_value"),
                            performance_data.get("score"),
                            performance_data.get("is_active", True),
                            performance_data.get("last_trade_time"),
                            json.dumps(performance_data.get("metadata", {})),
                        ),
                    )

                conn.commit()
                logger.info(f"配對表現已更新: {pair_key}")

        except Exception as e:
            logger.error(f"更新配對表現失敗: {e}")

    def get_pair_performance(self, pair_key: Optional[str] = None) -> List[Dict]:
        """獲取配對表現"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                if pair_key:
                    cursor.execute("SELECT * FROM pair_performance WHERE pair_key = ?", (pair_key,))
                else:
                    cursor.execute("SELECT * FROM pair_performance ORDER BY score DESC")

                performances = []
                for row in cursor.fetchall():
                    perf = dict(row)
                    if perf["metadata"]:
                        perf["metadata"] = json.loads(perf["metadata"])
                    performances.append(perf)

                return performances

        except Exception as e:
            logger.error(f"獲取配對表現失敗: {e}")
            return []

    def record_portfolio_snapshot(self, portfolio_data: Dict) -> int:
        """記錄投資組合快照"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                cursor.execute(
                    """
                    INSERT INTO portfolio_snapshots (
                        total_capital, allocated_capital, available_capital,
                        total_pnl, total_pnl_percentage, active_pairs, total_trades,
                        portfolio_sharpe, max_drawdown, daily_pnl, metadata
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """,
                    (
                        portfolio_data.get("total_capital"),
                        portfolio_data.get("allocated_capital"),
                        portfolio_data.get("available_capital"),
                        portfolio_data.get("total_pnl"),
                        portfolio_data.get("total_pnl_percentage"),
                        portfolio_data.get("active_pairs"),
                        portfolio_data.get("total_trades"),
                        portfolio_data.get("portfolio_sharpe"),
                        portfolio_data.get("max_drawdown"),
                        portfolio_data.get("daily_pnl"),
                        json.dumps(portfolio_data.get("metadata", {})),
                    ),
                )

                snapshot_id = cursor.lastrowid
                conn.commit()

                return snapshot_id

        except Exception as e:
            logger.error(f"記錄投資組合快照失敗: {e}")
            return -1

    def get_portfolio_history(self, days: int = 30) -> pd.DataFrame:
        """獲取投資組合歷史數據"""
        try:
            with self.get_connection() as conn:
                start_date = datetime.now() - timedelta(days=days)

                query = """
                    SELECT * FROM portfolio_snapshots
                    WHERE timestamp >= ?
                    ORDER BY timestamp
                """

                df = pd.read_sql_query(query, conn, params=[start_date.isoformat()])

                if not df.empty:
                    df["timestamp"] = pd.to_datetime(df["timestamp"])

                return df

        except Exception as e:
            logger.error(f"獲取投資組合歷史失敗: {e}")
            return pd.DataFrame()

    def record_alert(self, alert_data: Dict) -> int:
        """記錄警報"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                cursor.execute(
                    """
                    INSERT INTO alerts (level, title, message, source, pair_key, metadata) VALUES (?, ?, ?, ?, ?, ?)
                    """,
                    (
                        alert_data.get("level"),
                        alert_data.get("title"),
                        alert_data.get("message"),
                        alert_data.get("source"),
                        alert_data.get("pair_key"),
                        json.dumps(alert_data.get("metadata", {})),
                    ),
                )

                alert_id = cursor.lastrowid
                conn.commit()

                return alert_id

        except Exception as e:
            logger.error(f"記錄警報失敗: {e}")
            return -1

    def get_recent_alerts(self, limit: int = 100) -> List[Dict]:
        """獲取最近的警報"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                cursor.execute(
                    """
                    SELECT * FROM alerts
                    ORDER BY timestamp DESC
                    LIMIT ?
                    """,
                    (limit,),
                )

                alerts = []
                for row in cursor.fetchall():
                    alert = dict(row)
                    if alert["metadata"]:
                        alert["metadata"] = json.loads(alert["metadata"])
                    alerts.append(alert)

                return alerts

        except Exception as e:
            logger.error(f"獲取警報失敗: {e}")
            return []

    def cleanup_old_data(self, days_to_keep: int = 90):
        """清理舊數據"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cutoff_date = datetime.now() - timedelta(days=days_to_keep)

                # 清理舊的投資組合快照（保留每日最後一條）
                cursor.execute(
                    """
                    DELETE FROM portfolio_snapshots
                    WHERE timestamp < ?
                    AND id NOT IN (SELECT MAX(id)
                        FROM portfolio_snapshots
                        WHERE timestamp < ?
                        GROUP BY DATE(timestamp))
                    """,
                    (cutoff_date.isoformat(), cutoff_date.isoformat()),
                )

                # 清理舊警報
                cursor.execute(
                    """
                    DELETE FROM alerts
                    WHERE timestamp < ? AND level IN ('INFO', 'WARNING')
                    """,
                    (cutoff_date.isoformat(),),
                )

                conn.commit()
                logger.info(f"清理了 {days_to_keep} 天前的舊數據")

        except Exception as e:
            logger.error(f"清理舊數據失敗: {e}")

    def get_database_stats(self) -> Dict:
        """獲取數據庫統計信息"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                stats = {}

                # 各表記錄數
                tables = [
                    "trades",
                    "pair_performance",
                    "portfolio_snapshots",
                    "alerts",
                    "system_events",
                ]
                for table in tables:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    stats[f"{table}_count"] = cursor.fetchone()[0]

                # 數據庫文件大小
                stats["db_size_mb"] = self.db_path.stat().st_size / (1024 * 1024)

                return stats

        except Exception as e:
            logger.error(f"獲取數據庫統計失敗: {e}")
            return {}


# 全局數據庫管理器實例
_database_manager = None


def get_database_manager() -> DatabaseManager:
    """獲取全局數據庫管理器實例"""
    global _database_manager
    if _database_manager is None:
        _database_manager = DatabaseManager()
    return _database_manager


if __name__ == "__main__":
    # 測試數據庫管理器
    print("測試數據庫管理器...")

    db_manager = DatabaseManager("test_trading_platform.db")

    # 測試記錄交易
    trade_data = {
        "pair_key": "BTCUSDT-ETHUSDT",
        "base_symbol": "BTCUSDT",
        "quote_symbol": "ETHUSDT",
        "trade_type": "pair_trading",
        "pnl": 25.5,
        "exit_reason": "take_profit",
    }

    trade_id = db_manager.record_trade(trade_data)
    print(f"記錄交易ID: {trade_id}")

    # 測試獲取交易
    trades = db_manager.get_trades(limit=5)
    print(f"獲取到 {len(trades)} 筆交易記錄")

    # 測試數據庫統計
    stats = db_manager.get_database_stats()
    print(f"數據庫統計: {stats}")

    print("數據庫管理器測試完成！")
