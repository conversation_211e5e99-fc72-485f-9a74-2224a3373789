#!/usr/bin/env python3
"""
配對交易機器人使用示例
"""

import json
import os
import sys

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pair_selection import PairSelectionTool

from backtesting import BacktestEngine
from logging_config import get_logger, setup_logging
from pair_trading_bot import PairTradingBot

# 設置日誌
setup_logging()
logger = get_logger(__name__)


def example_pair_selection():
    """示例：配對篩選"""
    print("=== 配對篩選示例 ===")

    try:
        # 創建配對篩選工具
        pair_tool = PairSelectionTool()

        # 設置較小的資產列表用於演示
        demo_symbols = ["BTCUSDT", "ETHUSDT", "BNBUSDT", "ADAUSDT", "XRPUSDT"]
        pair_tool.symbols = demo_symbols

        print(f"分析資產: {demo_symbols}")

        # 執行分析
        results = pair_tool.run_analysis()

        if results:
            print(f"\n找到 {len(results)} 個配對組合")

            # 顯示前5個結果
            top_pairs = pair_tool.get_top_pairs(5, only_qualified=False)

            print("\n排名前5的配對:")
            for i, pair in enumerate(top_pairs, 1):
                status = "✓" if pair.get("meets_criteria", False) else "✗"
                print(f"{i}. {status} {pair['symbol1']} - {pair['symbol2']}")
                print(f"   相關性: {pair['correlation']:.4f}")
                print(f"   共整合P值: {pair['cointegration_p_value']:.4f}")
                print(f"   評分: {pair['score']:.1f}/100")
                print()

    except Exception as e:
        logger.error(f"配對篩選示例失敗: {e}")


def example_backtest():
    """示例：回測"""
    print("=== 回測示例 ===")

    try:
        # 首先生成示例數據
        generate_demo_data()

        # 創建回測引擎
        backtest_engine = BacktestEngine()

        # 載入數據
        data_path = "data/demo_data.csv"
        if not backtest_engine.load_historical_data(data_path):
            print("載入示例數據失敗")
            return

        print(f"載入數據成功: {data_path}")

        # 執行回測
        results = backtest_engine.run_backtest()

        if results:
            print("\n回測結果:")
            print(f"總回報率: {results['total_return']:.2%}")
            print(f"夏普比率: {results['sharpe_ratio']:.4f}")
            print(f"最大回撤: {results['max_drawdown']:.2%}")
            print(f"勝率: {results['win_rate']:.2%}")
            print(f"總交易次數: {results['total_trades']}")
            print(f"總盈虧: ${results['total_pnl']:.2f}")

            # 生成詳細報告
            report = backtest_engine.generate_report()
            print("\n" + "=" * 50)
            print(report)

    except Exception as e:
        logger.error(f"回測示例失敗: {e}")


def example_live_simulation():
    """示例：實時交易模擬"""
    print("=== 實時交易模擬示例 ===")

    try:
        # 創建交易機器人
        bot = PairTradingBot()

        print("交易機器人初始化完成")
        print(f"交易對: {bot.config['trading_pair']}")
        print(f"時間框架: {bot.config['timeframe']}")

        # 模擬幾次迭代
        print("\n開始模擬交易...")

        for _ in range(5):
            print(f"\n--- 迭代 {i+1} ---")

            # 執行單次迭代
            success = bot.run_single_iteration()

            if success:
                # 獲取狀態報告
                status = bot.get_status_report()

                print(f"交易狀態: {status.get('trading_state', 'unknown')}")
                print(f"信號狀態: {status.get('signal_state', 'unknown')}")
                print(f"當前Z-score: {status.get('current_zscore', 'N/A'):.4f}")

                # 如果有持倉，顯示持倉信息
                position_info = status.get("position_info", {})
                if position_info.get("is_active", False):
                    print(f"持倉方向: {'做多Base' if position_info.get('is_long_base') else '做空Base'}")
                    print(f"當前盈虧: ${position_info.get('current_pnl', 0):.2f}")

                # 顯示統計信息
                stats = status.get("statistics", {})
                if stats.get("total_trades", 0) > 0:
                    print(f"總交易: {stats['total_trades']}, 勝率: {stats['win_rate']:.2%}")
            else:
                print("迭代失敗")

            # 模擬等待
            import time

            time.sleep(1)

        print("\n模擬交易完成")

    except Exception as e:
        logger.error(f"實時交易模擬失敗: {e}")


def generate_demo_data():
    """生成演示數據"""
    try:
        from datetime import datetime, timedelta

        import numpy as np
        import pandas as pd

        # 創建數據目錄
        os.makedirs("data", exist_ok=True)

        # 生成時間序列（30天，每小時一個數據點）
        start_date = datetime.now() - timedelta(days=30)
        dates = pd.date_range(start=start_date, periods=30 * 24, freq="h")

        # 設置隨機種子以獲得可重現的結果
        np.random.seed(42)

        # 生成BTC價格（基準資產）
        btc_returns = np.random.normal(0, 0.02, len(dates))
        btc_prices = [50000]  # 起始價格
        for ret in btc_returns[1:]:
            btc_prices.append(btc_prices[-1] * (1 + ret))

        # 生成ETH價格（與BTC高度相關）
        eth_correlation = 0.85
        eth_returns = []
        for btc_ret in btc_returns:
            correlated_component = eth_correlation * btc_ret
            random_component = (1 - eth_correlation) * np.random.normal(0, 0.025)
            eth_returns.append(correlated_component + random_component)

        eth_prices = [3000]  # 起始價格
        for ret in eth_returns[1:]:
            eth_prices.append(eth_prices[-1] * (1 + ret))

        # 創建DataFrame
        demo_data = pd.DataFrame(
            {
                "base_price": btc_prices,
                "quote_price": eth_prices,
                "base_volume": np.random.uniform(1000, 5000, len(dates)),
                "quote_volume": np.random.uniform(5000, 20000, len(dates)),
            },
            index=dates,
        )

        # 保存數據
        demo_data.to_csv("data/demo_data.csv")
        print("演示數據已生成: data/demo_data.csv")

    except Exception as e:
        logger.error(f"生成演示數據失敗: {e}")


def show_config_example():
    """顯示配置示例"""
    print("=== 配置文件示例 ===")

    example_config = {
        "trading_pair": ["BTCUSDT", "ETHUSDT"],
        "timeframe": "1m",
        "lookback_period": 60,
        "entry_threshold_high": 2.5,
        "entry_threshold_low": -2.5,
        "confirmation_threshold_high": 2.0,
        "confirmation_threshold_low": -2.0,
        "cooldown_period": 15,
        "stop_loss_pct": 0.005,
        "take_profit_target": "zero_crossing",
        "position_size_usd": 1000,
        "exchange": {
            "name": "binance",
            "sandbox": True,
            "api_key": "your_api_key_here",
            "secret": "your_secret_here",
        },
    }

    print("配置文件 (config.json) 示例:")
    print(json.dumps(example_config, indent=2, ensure_ascii=False))

    print("\n重要參數說明:")
    print("- entry_threshold_high/low: Z-score觸發門檻")
    print("- confirmation_threshold_high/low: Z-score確認門檻")
    print("- cooldown_period: 交易後冷卻期")
    print("- stop_loss_pct: 止損百分比")
    print("- position_size_usd: 單邊倉位大小")


def main():
    """主函數"""
    print("配對交易機器人使用示例")
    print("=" * 50)

    while True:
        print("\n請選擇要運行的示例:")
        print("1. 配對篩選示例")
        print("2. 回測示例")
        print("3. 實時交易模擬示例")
        print("4. 顯示配置文件示例")
        print("5. 生成演示數據")
        print("0. 退出")

        choice = input("\n請輸入選項 (0-5): ").strip()

        if choice == "1":
            example_pair_selection()
        elif choice == "2":
            example_backtest()
        elif choice == "3":
            example_live_simulation()
        elif choice == "4":
            show_config_example()
        elif choice == "5":
            generate_demo_data()
        elif choice == "0":
            print("退出程序")
            break
        else:
            print("無效選項，請重新選擇")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n程序被用戶中斷")
    except Exception as e:
        logger.error(f"程序執行失敗: {e}")
        sys.exit(1)
