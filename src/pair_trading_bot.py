"""配對交易機器人主模組 - 實現智能配對交易策略."""
from datetime import datetime
from enum import Enum
from typing import Dict

import numpy as np

from alert_manager import AlertLevel, get_alert_manager
from data_handler import DataHandler
from integrated_trading_executor import IntegratedTradingExecutor
from logging_config import get_logger
from strategy_health_monitor import get_strategy_health_monitor
from unified_pair_selector import UnifiedPairSelector
from utils import get_exchange_instance, load_config

logger = get_logger(__name__)

STRATEGY_DEGRADATION_REASON = "策略健康檢查失敗"


class TradingState(Enum):
    """交易狀態枚舉"""

    SEARCHING = "searching"  # 尋找交易機會
    IN_POSITION = "in_position"  # 持倉中
    COOLDOWN = "cooldown"  # 冷卻期


class SignalState(Enum):
    """信號狀態枚舉"""

    NONE = "none"
    TRIGGERED_HIGH = "triggered_high"  # 觸發上方極端值
    TRIGGERED_LOW = "triggered_low"  # 觸發下方極端值


class TakeProfitTarget(Enum):
    """止盈目標枚舉"""

    ZERO_CROSSING = "zero_crossing"  # Z-score 歸零


class PairTradingBot:
    """進階配對交易機器人"""

    def __init__(self, config_path: str = "config.json"):
        # 載入配置
        self.config = load_config(config_path)

        # 初始化組件
        self.exchange = get_exchange_instance(self.config)
        self.data_handler = DataHandler(self.exchange, self.config)
        self.trading_executor = IntegratedTradingExecutor(self.config)

        # 初始化自適應組件
        self.health_monitor = get_strategy_health_monitor()
        self.pair_selector = UnifiedPairSelector()
        self.alert_manager = get_alert_manager()

        # 策略參數
        self.entry_threshold_high = self.config["entry_threshold_high"]
        self.entry_threshold_low = self.config["entry_threshold_low"]
        self.confirmation_threshold_high = self.config["confirmation_threshold_high"]
        self.confirmation_threshold_low = self.config["confirmation_threshold_low"]
        self.cooldown_period = self.config["cooldown_period"]
        self.stop_loss_pct = self.config["stop_loss_pct"]
        self.take_profit_target = self.config["take_profit_target"]

        # 狀態管理
        self.trading_state = TradingState.SEARCHING
        self.signal_state = SignalState.NONE
        self.cooldown_end_bar = None
        self.current_bar_index = 0
        self._last_trade_notified = False  # 新增：用於追蹤是否已發送交易通知

        # 統計信息
        self.stats = {
            "total_trades": 0,
            "winning_trades": 0,
            "losing_trades": 0,
            "total_pnl": 0.0,
            "max_drawdown": 0.0,
            "current_drawdown": 0.0,
            "peak_equity": 0.0,
        }

        logger.info("PairTradingBot 初始化完成")

    def update_data_and_signals(self) -> bool:
        """更新數據和信號"""
        try:
            # 更新價格數據
            if not self.data_handler.update_data():
                logger.warning("數據更新失敗")
                return False

            self.current_bar_index += 1
            current_zscore = self.data_handler.get_current_zscore()

            if np.isnan(current_zscore):
                logger.warning("Z-score 計算結果為 NaN")
                return False

            logger.debug(f"Bar {self.current_bar_index}: Z-score = {current_zscore:.4f}")
            return True

        except Exception as e:
            logger.error(f"更新數據和信號失敗: {e}")
            return False

    def check_entry_signals(self) -> bool:
        """檢查進場信號"""
        try:
            if self.trading_state != TradingState.SEARCHING:
                return False

            # 檢查是否在冷卻期
            if (
                self.cooldown_end_bar is not None
                and self.current_bar_index <= self.cooldown_end_bar
            ):
                return False

            current_zscore = self.data_handler.get_current_zscore()
            base_price, quote_price = self.data_handler.get_current_prices()

            # 增加信號狀態重置邏輯
            if self.signal_state == SignalState.TRIGGERED_HIGH and current_zscore < 0:
                logger.info(f"Z-score 從上方觸發區直接回歸到0以下 ({current_zscore:.4f})，重置信號狀態")
                self.signal_state = SignalState.NONE
            elif self.signal_state == SignalState.TRIGGERED_LOW and current_zscore > 0:
                logger.info(f"Z-score 從下方觸發區直接回歸到0以上 ({current_zscore:.4f})，重置信號狀態")
                self.signal_state = SignalState.NONE

            # 檢查觸發條件
            if self.signal_state == SignalState.NONE:
                if current_zscore >= self.entry_threshold_high:
                    self.signal_state = SignalState.TRIGGERED_HIGH
                    logger.info(f"觸發上方極端值: Z-score = {current_zscore:.4f}")
                elif current_zscore <= self.entry_threshold_low:
                    self.signal_state = SignalState.TRIGGERED_LOW
                    logger.info(f"觸發下方極端值: Z-score = {current_zscore:.4f}")

            # 檢查確認條件
            elif self.signal_state == SignalState.TRIGGERED_HIGH:
                if current_zscore <= self.confirmation_threshold_high:
                    # 確認信號：做空 Base，做多 Quote
                    logger.info(f"確認做空 Base 信號: Z-score = {current_zscore:.4f}")
                    success = self.trading_executor.enter_short_base_long_quote(
                        base_price, quote_price, self.current_bar_index
                    )
                    if success:
                        self.trading_state = TradingState.IN_POSITION
                        self.signal_state = SignalState.NONE
                        return True
                    else:
                        self.signal_state = SignalState.NONE

            elif self.signal_state == SignalState.TRIGGERED_LOW:
                if current_zscore >= self.confirmation_threshold_low:
                    # 確認信號：做多 Base，做空 Quote
                    logger.info(f"確認做多 Base 信號: Z-score = {current_zscore:.4f}")
                    success = self.trading_executor.enter_long_base_short_quote(
                        base_price, quote_price, self.current_bar_index
                    )
                    if success:
                        self.trading_state = TradingState.IN_POSITION
                        self.signal_state = SignalState.NONE
                        return True
                    else:
                        self.signal_state = SignalState.NONE

            return False

        except Exception as e:
            logger.error(f"檢查進場信號失敗: {e}")
            return False

    def check_exit_signals(self) -> bool:
        """檢查出場信號"""
        try:
            if self.trading_state != TradingState.IN_POSITION:
                return False

            current_zscore = self.data_handler.get_current_zscore()
            base_price, quote_price = self.data_handler.get_current_prices()

            # 檢查止盈條件
            if self.take_profit_target == TakeProfitTarget.ZERO_CROSSING.value:
                if abs(current_zscore) < 0.1:  # Z-score 接近 0
                    logger.info(f"觸發止盈: Z-score 回歸均值 = {current_zscore:.4f}")
                    success = self.trading_executor.exit_position(
                        "take_profit", base_price, quote_price
                    )
                    if success:
                        self._update_stats_on_exit(True)
                        self._enter_cooldown()
                        return True

            # 檢查止損條件
            current_pnl = self.trading_executor.calculate_current_pnl(base_price, quote_price)
            # 使用實際倉位價值而非理論值
            position_value = self.trading_executor.get_actual_position_value()
            # 如果實際倉位價值為0，使用配置中的理論值作為備用，避免除以零錯誤
            if position_value == 0:
                position_value = self.config["position_size_usd"] * 2  # 備用方案
            loss_pct = abs(current_pnl) / position_value

            if current_pnl < 0 and loss_pct >= self.stop_loss_pct:
                logger.info(f"觸發止損: 虧損 {current_pnl:.2f} ({loss_pct:.2%})")
                success = self.trading_executor.exit_position("stop_loss", base_price, quote_price)
                if success:
                    self._update_stats_on_exit(False)
                    self._enter_cooldown()
                    return True

            return False

        except Exception as e:
            logger.error(f"檢查出場信號失敗: {e}")
            return False

    def _update_stats_on_exit(self, is_winning: bool):
        """更新統計信息"""
        try:
            self.stats["total_trades"] += 1

            if is_winning:
                self.stats["winning_trades"] += 1
            else:
                self.stats["losing_trades"] += 1

            # 更新總盈虧
            current_pnl = self.trading_executor.calculate_current_pnl()
            self.stats["total_pnl"] += current_pnl

            # 更新回撤統計
            if self.stats["total_pnl"] > self.stats["peak_equity"]:
                self.stats["peak_equity"] = self.stats["total_pnl"]
                self.stats["current_drawdown"] = 0.0
            else:
                self.stats["current_drawdown"] = self.stats["peak_equity"] - self.stats["total_pnl"]
                if self.stats["current_drawdown"] > self.stats["max_drawdown"]:
                    self.stats["max_drawdown"] = self.stats["current_drawdown"]

            logger.info(
                f"交易統計更新 - 總交易: {self.stats['total_trades']}, "
                f"勝率: {self.get_win_rate():.2%}, 總盈虧: {self.stats['total_pnl']:.2f}"
            )

        except Exception as e:
            logger.error(f"更新統計信息失敗: {e}")

    def _enter_cooldown(self):
        """進入冷卻期"""
        self.trading_state = TradingState.SEARCHING
        self.signal_state = SignalState.NONE
        self.cooldown_end_bar = (
            self.current_bar_index + self.cooldown_period
        )  # 基於當前 Bar 計算冷卻期結束的 Bar 索引
        logger.info(f"進入冷卻期，將持續到 Bar {self.cooldown_end_bar}")

    def run_single_iteration(self) -> bool:
        """執行單次迭代（自適應增強版）"""
        try:
            # 檢查策略健康狀態
            if self.health_monitor.should_check_health():
                self._perform_strategy_health_check()

            # 更新數據
            if not self.update_data_and_signals():
                return False

            # 檢查出場信號（優先級較高）
            if self.check_exit_signals():
                logger.info("執行出場操作")
                # 記錄交易到健康監控器
                self._record_trade_to_health_monitor()

            # 檢查進場信號
            elif self.check_entry_signals():
                logger.info("執行進場操作")

            return True

        except Exception as e:
            logger.error(f"單次迭代執行失敗: {e}")
            return False

    def _perform_strategy_health_check(self):
        """執行策略健康檢查"""
        try:
            logger.info("執行策略健康檢查...")

            # 獲取價格數據進行共整合檢查
            price_data = self.data_handler.price_data

            # 執行健康檢查
            health_report = self.health_monitor.perform_health_check(price_data)

            # 如果策略不健康，觸發自適應調整
            if not health_report.get("is_healthy", True):
                self._trigger_adaptive_adjustment(health_report)

        except Exception as e:
            logger.error(f"策略健康檢查失敗: {e}")

    def _trigger_adaptive_adjustment(self, health_report: Dict):
        """觸發自適應調整"""
        try:
            logger.warning("觸發策略自適應調整...")

            # 如果有活躍持倉，先平倉
            if self.trading_state == TradingState.IN_POSITION:
                logger.info("檢測到策略降級，強制平倉...")
                base_price, quote_price = self.data_handler.get_current_prices()
                self.trading_executor.exit_position("strategy_degradation", base_price, quote_price)
                self.trading_state = TradingState.SEARCHING
                self.signal_state = SignalState.NONE

            # 嘗試切換到更好的交易對
            if self.pair_selector.switch_to_best_pair():
                logger.info("成功切換到新的交易對")

                # 重新初始化數據處理器以適應新交易對
                self._reinitialize_for_new_pair()

                # 發送自適應調整通知
                self.alert_manager.send_alert(
                    AlertLevel.WARNING,
                    "策略自適應調整",
                    "系統已自動調整策略以適應市場變化",
                    {
                        "調整原因": STRATEGY_DEGRADATION_REASON,
                        "新交易對": self.config["trading_pair"],
                        "調整時間": datetime.now().isoformat(),
                    },
                )
            else:
                logger.error("無法找到合適的替代交易對")

                # 發送緊急警報
                self.alert_manager.send_critical_alert(
                    "策略自適應失敗", "系統無法找到合適的替代交易對，需要人工干預", health_report
                )

        except Exception as e:
            logger.error(f"自適應調整失敗: {e}")

    def _reinitialize_for_new_pair(self):
        """為新交易對重新初始化"""
        try:
            # 重新載入配置 (假設 load_config() 能夠獲取最新的配置，例如從文件或遠程服務)
            self.config = load_config()

            # 重新初始化數據處理器
            self.data_handler = DataHandler(self.exchange, self.config)

            # 重置交易狀態
            self.trading_state = TradingState.SEARCHING
            self.signal_state = SignalState.NONE
            self.current_bar_index = 0
            self.cooldown_end_bar = None

            logger.info("已為新交易對重新初始化系統")

        except Exception as e:
            logger.error(f"重新初始化失敗: {e}")

    def _record_trade_to_health_monitor(self):
        """記錄交易到健康監控器"""
        try:
            # 獲取最新的交易歷史
            trade_history = self.trading_executor.get_trade_history()

            if trade_history:
                latest_trade = trade_history[-1]

                # 記錄到健康監控器
                self.health_monitor.record_trade(
                    {
                        "pair": self.config["trading_pair"],
                        "pnl": latest_trade.get("pnl", 0),
                        "trade_type": "pair_trading",
                        "exit_reason": latest_trade.get("exit_reason", "unknown"),
                        "hold_time": latest_trade.get("hold_time", 0),
                    }
                )

                # 記錄到配對選擇器
                self.pair_selector.record_pair_performance(
                    self.config["trading_pair"],
                    {
                        "pnl": latest_trade.get("pnl", 0),
                        "trade_type": "pair_trading",
                        "exit_reason": latest_trade.get("exit_reason", "unknown"),
                    },
                )

        except Exception as e:
            logger.error(f"記錄交易到健康監控器失敗: {e}")

    def get_status_report(self) -> Dict:
        """獲取狀態報告"""
        try:
            current_zscore = self.data_handler.get_current_zscore()
            base_price, quote_price = self.data_handler.get_current_prices()
            position_info = self.trading_executor.get_position_info()

            report = {
                "timestamp": datetime.now().isoformat(),  # 使用 ISO 格式的時間戳，對於精確時間同步可能需要外部機制
                "trading_state": self.trading_state.value,
                "signal_state": self.signal_state.value,
                "current_bar_index": self.current_bar_index,
                "current_zscore": current_zscore,
                "current_prices": {"base": base_price, "quote": quote_price},
                "position_info": position_info,
                "cooldown_remaining": max(0, (self.cooldown_end_bar or 0) - self.current_bar_index),
                "statistics": self.get_statistics(),
            }

            return report

        except Exception as e:
            logger.error(f"生成狀態報告失敗: {e}")
            return {}

    def get_statistics(self) -> Dict:
        """獲取統計信息"""
        stats = self.stats.copy()
        stats["win_rate"] = self.get_win_rate()
        stats["profit_loss_ratio"] = self.get_profit_loss_ratio()
        return stats

    def get_win_rate(self) -> float:
        """計算勝率"""
        if self.stats["total_trades"] == 0:
            return 0.0
        return self.stats["winning_trades"] / self.stats["total_trades"]

    def get_profit_loss_ratio(self) -> float:
        """計算盈虧比"""
        # 使用 TradingExecutor 的詳細交易統計
        trade_stats = self.trading_executor.get_trade_statistics()
        return trade_stats.get("profit_loss_ratio", 0.0)
