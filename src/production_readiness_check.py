#!/usr/bin/env python3
"""
生產環境就緒檢查 - 確保系統準備好進行真實交易
Production Readiness Check - Ensure system is ready for real trading
"""

import os
import sys
from pathlib import Path
from typing import Dict, List, Any
import json

sys.path.append('.')

from config_validation import ConfigV<PERSON><PERSON><PERSON>
from trading_safety_guard import get_safety_guard
from logging_config import setup_logging, get_logger

setup_logging()
logger = get_logger(__name__)


class ProductionReadinessChecker:
    """生產環境就緒檢查器"""
    
    def __init__(self):
        self.checks_passed = 0
        self.checks_failed = 0
        self.warnings = []
        self.errors = []
        self.recommendations = []
    
    def run_comprehensive_check(self) -> Dict[str, Any]:
        """運行全面的生產就緒檢查"""
        print("🔍 生產環境就緒檢查")
        print("=" * 60)
        
        result = {
            'ready_for_production': False,
            'checks': {},
            'summary': {
                'passed': 0,
                'failed': 0,
                'warnings': [],
                'errors': [],
                'recommendations': []
            }
        }
        
        # 執行各項檢查
        checks = [
            ('api_configuration', self._check_api_configuration),
            ('trading_configuration', self._check_trading_configuration),
            ('risk_management', self._check_risk_management),
            ('safety_mechanisms', self._check_safety_mechanisms),
            ('monitoring_setup', self._check_monitoring_setup),
            ('backup_strategy', self._check_backup_strategy),
            ('emergency_procedures', self._check_emergency_procedures)
        ]
        
        for check_name, check_func in checks:
            print(f"\n🔍 檢查: {check_name}")
            try:
                check_result = check_func()
                result['checks'][check_name] = check_result
                
                if check_result['passed']:
                    self.checks_passed += 1
                    print(f"  ✅ 通過")
                else:
                    self.checks_failed += 1
                    print(f"  ❌ 失敗")
                
                # 收集警告和錯誤
                self.warnings.extend(check_result.get('warnings', []))
                self.errors.extend(check_result.get('errors', []))
                self.recommendations.extend(check_result.get('recommendations', []))
                
                # 顯示詳情
                for warning in check_result.get('warnings', []):
                    print(f"    ⚠️ {warning}")
                for error in check_result.get('errors', []):
                    print(f"    ❌ {error}")
                    
            except Exception as e:
                self.checks_failed += 1
                error_msg = f"檢查 {check_name} 失敗: {e}"
                self.errors.append(error_msg)
                print(f"  ❌ 異常: {e}")
        
        # 更新結果
        result['summary']['passed'] = self.checks_passed
        result['summary']['failed'] = self.checks_failed
        result['summary']['warnings'] = self.warnings
        result['summary']['errors'] = self.errors
        result['summary']['recommendations'] = self.recommendations
        
        # 判斷是否準備好生產
        result['ready_for_production'] = (
            self.checks_failed == 0 and 
            len(self.errors) == 0
        )
        
        return result
    
    def _check_api_configuration(self) -> Dict[str, Any]:
        """檢查API配置"""
        result = {'passed': False, 'warnings': [], 'errors': [], 'recommendations': []}
        
        # 檢查API密鑰
        api_key = os.environ.get('TRADING_API_KEY', '')
        secret = os.environ.get('TRADING_SECRET', '')
        
        if not api_key:
            result['errors'].append("TRADING_API_KEY 未設置")
        elif 'test' in api_key.lower() or 'sandbox' in api_key.lower():
            result['warnings'].append("API密鑰看起來像測試密鑰")
        
        if not secret:
            result['errors'].append("TRADING_SECRET 未設置")
        elif 'test' in secret.lower() or 'sandbox' in secret.lower():
            result['warnings'].append("API密鑰看起來像測試密鑰")
        
        # 檢查API密鑰長度（基本驗證）
        if api_key and len(api_key) < 10:
            result['warnings'].append("API密鑰長度可能不正確")
        
        if secret and len(secret) < 10:
            result['warnings'].append("API密鑰長度可能不正確")
        
        # 建議
        result['recommendations'].extend([
            "確保API密鑰只有現貨交易權限",
            "禁用提現和內部轉賬權限",
            "設置IP白名單提高安全性",
            "定期輪換API密鑰"
        ])
        
        result['passed'] = len(result['errors']) == 0
        return result
    
    def _check_trading_configuration(self) -> Dict[str, Any]:
        """檢查交易配置"""
        result = {'passed': False, 'warnings': [], 'errors': [], 'recommendations': []}
        
        try:
            # 載入配置
            config = ConfigValidator.load_and_validate_config('config.json')
            
            # 檢查沙盒模式
            if config.trading.sandbox:
                result['errors'].append("生產環境不能使用沙盒模式")
            
            # 檢查交易對
            if len(config.trading.trading_pairs) == 0:
                result['errors'].append("沒有配置交易對")
            elif len(config.trading.trading_pairs) > 5:
                result['warnings'].append("交易對數量較多，建議先用少量測試")
            
            # 檢查倉位大小
            if config.trading.max_position_size > 0.2:
                result['warnings'].append("最大倉位大小較高，風險較大")
            
            # 檢查止損設置
            if config.trading.stop_loss_pct > 0.1:
                result['warnings'].append("止損百分比較高")
            elif config.trading.stop_loss_pct < 0.01:
                result['warnings'].append("止損百分比較低，可能頻繁觸發")
            
            result['recommendations'].extend([
                "建議先用小倉位測試",
                "設置合理的止損和止盈",
                "監控交易對的流動性"
            ])
            
        except Exception as e:
            result['errors'].append(f"配置載入失敗: {e}")
        
        result['passed'] = len(result['errors']) == 0
        return result
    
    def _check_risk_management(self) -> Dict[str, Any]:
        """檢查風險管理"""
        result = {'passed': False, 'warnings': [], 'errors': [], 'recommendations': []}
        
        # 檢查資金管理
        result['recommendations'].extend([
            "只使用您能承受損失的資金",
            "設置每日最大損失限額",
            "定期檢查和調整風險參數",
            "保持資金分散，不要全部投入"
        ])
        
        # 檢查監控設置
        telegram_token = os.environ.get('MONITOR_TELEGRAM_BOT_TOKEN', '')
        if not telegram_token:
            result['warnings'].append("未設置Telegram警報，建議啟用實時監控")
        
        result['passed'] = True  # 風險管理主要是建議
        return result
    
    def _check_safety_mechanisms(self) -> Dict[str, Any]:
        """檢查安全機制"""
        result = {'passed': False, 'warnings': [], 'errors': [], 'recommendations': []}
        
        # 檢查安全防護
        safety_guard = get_safety_guard()
        
        # 檢查確認文件
        confirmation_file = Path('.trading_confirmation')
        if not confirmation_file.exists():
            result['errors'].append("未找到交易確認文件，真實交易將被拒絕")
            result['recommendations'].append("運行: echo 'CONFIRMED' > .trading_confirmation")
        
        result['recommendations'].extend([
            "設置緊急停止機制",
            "準備手動干預程序",
            "定期備份交易數據"
        ])
        
        result['passed'] = len(result['errors']) == 0
        return result
    
    def _check_monitoring_setup(self) -> Dict[str, Any]:
        """檢查監控設置"""
        result = {'passed': False, 'warnings': [], 'errors': [], 'recommendations': []}
        
        # 檢查日誌目錄
        logs_dir = Path('logs')
        if not logs_dir.exists():
            result['warnings'].append("日誌目錄不存在")
        
        # 檢查數據目錄
        data_dir = Path('data')
        if not data_dir.exists():
            result['warnings'].append("數據目錄不存在")
        
        result['recommendations'].extend([
            "設置日誌輪轉避免磁盤滿",
            "配置系統監控和警報",
            "準備性能監控儀表板"
        ])
        
        result['passed'] = True
        return result
    
    def _check_backup_strategy(self) -> Dict[str, Any]:
        """檢查備份策略"""
        result = {'passed': False, 'warnings': [], 'errors': [], 'recommendations': []}
        
        result['recommendations'].extend([
            "定期備份配置文件",
            "備份交易歷史數據",
            "準備災難恢復計劃",
            "測試備份恢復流程"
        ])
        
        result['passed'] = True
        return result
    
    def _check_emergency_procedures(self) -> Dict[str, Any]:
        """檢查緊急程序"""
        result = {'passed': False, 'warnings': [], 'errors': [], 'recommendations': []}
        
        result['recommendations'].extend([
            "準備緊急停止腳本",
            "了解如何手動平倉",
            "準備聯繫方式和支持渠道",
            "制定異常情況處理流程"
        ])
        
        result['passed'] = True
        return result


def main():
    """主函數"""
    checker = ProductionReadinessChecker()
    result = checker.run_comprehensive_check()
    
    print("\n" + "=" * 60)
    print("📊 檢查結果總結")
    print("=" * 60)
    
    print(f"通過檢查: {result['summary']['passed']}")
    print(f"失敗檢查: {result['summary']['failed']}")
    print(f"準備就緒: {'✅ 是' if result['ready_for_production'] else '❌ 否'}")
    
    if result['summary']['warnings']:
        print(f"\n⚠️ 警告 ({len(result['summary']['warnings'])}):")
        for warning in result['summary']['warnings']:
            print(f"  - {warning}")
    
    if result['summary']['errors']:
        print(f"\n❌ 錯誤 ({len(result['summary']['errors'])}):")
        for error in result['summary']['errors']:
            print(f"  - {error}")
    
    if result['summary']['recommendations']:
        print(f"\n💡 建議 ({len(result['summary']['recommendations'])}):")
        for rec in result['summary']['recommendations'][:10]:  # 只顯示前10個
            print(f"  - {rec}")
    
    print("\n" + "=" * 60)
    if result['ready_for_production']:
        print("🎉 系統已準備好進行生產交易！")
        print("⚠️ 請確保您已充分理解風險並做好準備")
    else:
        print("🚨 系統尚未準備好進行生產交易")
        print("請解決上述錯誤後再次檢查")
    
    return 0 if result['ready_for_production'] else 1


if __name__ == "__main__":
    sys.exit(main())
