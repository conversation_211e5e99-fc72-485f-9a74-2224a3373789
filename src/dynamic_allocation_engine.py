#!/usr/bin/env python3
"""
動態資金分配引擎 - 基於策略實時表現的智能資金分配
Dynamic Allocation Engine - Intelligent capital allocation based on real-time strategy performance
"""

import asyncio
from dataclasses import dataclass

import numpy as np
import pandas as pd

from global_event_bus import Event, EventType, get_global_event_bus, publish_event
from logging_config import get_logger
from portfolio_manager import PortfolioManager

logger = get_logger(__name__)


@dataclass
class StrategyPerformance:
    """策略績效指標"""

    strategy_id: str
    health_score: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    total_trades: int
    total_pnl: float
    volatility: float
    momentum_score: float
    risk_adjusted_return: float
    last_updated: datetime


class DynamicAllocationEngine:
    """動態資金分配引擎"""

    def __init__(self, portfolio_manager: PortfolioManager):
        self.portfolio_manager = portfolio_manager
        self.event_bus = get_global_event_bus()
        self.event_integrator = get_enhanced_event_integrator()

        # 績效追蹤
        self.strategy_performances: Dict[str, StrategyPerformance] = {}
        self.performance_history: Dict[str, List[float]] = {}

        # 分配參數
        self.rebalance_threshold = 0.05  # 5 % 變化觸發重新分配
        self.min_observation_period = 10  # 最少觀察期（天）
        self.max_allocation_change = 0.1  # 單次最大分配變化10%

        # 風險控制
        self.max_strategy_allocation = 0.4  # 單策略最大40%
        self.min_strategy_allocation = 0.05  # 單策略最小5%
        self.correlation_penalty_threshold = 0.7

        # 設置事件監聽
        self._setup_event_listeners()

        logger.info("動態資金分配引擎初始化完成")

    def _setup_event_listeners(self):
        """設置事件監聽"""
        self.event_bus.subscribe(
            [EventType.ORDER_FILLED, EventType.STRATEGY_HEALTH_CHANGED], self._on_performance_event
        )

    def _on_performance_event(self, event: Event):
        """處理績效相關事件"""
        try:
            if event.event_type == EventType.ORDER_FILLED:
                strategy_id = event.data.get("strategy_id", "unknown")
                pnl = event.data.get("pnl", 0.0)
                self._update_trade_performance(strategy_id, pnl)

            elif event.event_type == EventType.STRATEGY_HEALTH_CHANGED:
                strategy_id = event.data.get("strategy_id", "unknown")
                health_score = event.data.get("health_score", 0.5)
                self._update_health_score(strategy_id, health_score)

        except Exception as e:
            logger.error(f"處理績效事件失敗: {e}")

    def _update_trade_performance(self, strategy_id: str, pnl: float):
        """更新交易績效"""
        if strategy_id not in self.performance_history:
            self.performance_history[strategy_id] = []

        # 將PnL轉換為收益率（假設基準資金）
        base_capital = 10000  # 基準資金
        return_rate = pnl / base_capital

        self.performance_history[strategy_id].append(return_rate)

        # 保持歷史長度
        max_history = 252  # 一年
        if len(self.performance_history[strategy_id]) > max_history:
            self.performance_history[strategy_id] = self.performance_history[strategy_id][
                -max_history:
            ]

        # 更新績效指標
        self._calculate_strategy_performance(strategy_id)

    def _update_health_score(self, strategy_id: str, health_score: float):
        """更新健康分數"""
        if strategy_id in self.strategy_performances:
            self.strategy_performances[strategy_id].health_score = health_score
            self.strategy_performances[strategy_id].last_updated = datetime.now()
        else:
            # 創建新的績效記錄
            self.strategy_performances[strategy_id] = StrategyPerformance(
                strategy_id=strategy_id,
                health_score=health_score,
                sharpe_ratio=0.0,
                max_drawdown=0.0,
                win_rate=0.0,
                total_trades=0,
                total_pnl=0.0,
                volatility=0.0,
                momentum_score=0.0,
                risk_adjusted_return=0.0,
                last_updated=datetime.now(),
            )

    def _calculate_strategy_performance(self, strategy_id: str):
        """計算策略績效指標"""
        try:
            if strategy_id not in self.performance_history:
                return

            returns = self.performance_history[strategy_id]
            if len(returns) < 5:  # 數據不足
                return

            # 計算基本指標
            returns_array = np.array(returns)

            # 夏普比率
            mean_return = np.mean(returns_array)
            volatility = np.std(returns_array)
            sharpe_ratio = mean_return / volatility if volatility > 0 else 0

            # 最大回撤
            cumulative_returns = np.cumprod(1 + returns_array)
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdown = (cumulative_returns - running_max) / running_max
            max_drawdown = abs(np.min(drawdown))

            # 勝率
            win_rate = len([r for r in returns if r > 0]) / len(returns)

            # 總PnL
            total_pnl = sum(returns) * 10000  # 轉換回絕對值

            # 動量分數（最近表現趨勢）
            if len(returns) >= 10:
                recent_returns = returns[-10:]
                momentum_score = np.mean(recent_returns)
            else:
                momentum_score = mean_return

            # 風險調整收益
            risk_adjusted_return = mean_return / max(max_drawdown, 0.01)

            # 更新績效記錄
            if strategy_id not in self.strategy_performances:
                self.strategy_performances[strategy_id] = StrategyPerformance(
                    strategy_id=strategy_id,
                    health_score=0.5,
                    sharpe_ratio=sharpe_ratio,
                    max_drawdown=max_drawdown,
                    win_rate=win_rate,
                    total_trades=len(returns),
                    total_pnl=total_pnl,
                    volatility=volatility,
                    momentum_score=momentum_score,
                    risk_adjusted_return=risk_adjusted_return,
                    last_updated=datetime.now(),
                )
            else:
                perf = self.strategy_performances[strategy_id]
                perf.sharpe_ratio = sharpe_ratio
                perf.max_drawdown = max_drawdown
                perf.win_rate = win_rate
                perf.total_trades = len(returns)
                perf.total_pnl = total_pnl
                perf.volatility = volatility
                perf.momentum_score = momentum_score
                perf.risk_adjusted_return = risk_adjusted_return
                perf.last_updated = datetime.now()

            logger.debug(f"策略績效更新: {strategy_id} - 夏普: {sharpe_ratio:.2f}, 勝率: {win_rate:.2%}")

        except Exception as e:
            logger.error(f"計算策略績效失敗 {strategy_id}: {e}")

    def calculate_dynamic_allocation(self) -> Dict[str, float]:
        """計算動態資金分配"""
        try:
            if not self.strategy_performances:
                logger.warning("無策略績效數據，使用平均分配")
                return {}

            # 計算綜合分數
            strategy_scores = {}

            for strategy_id, perf in self.strategy_performances.items():
                # 檢查數據充分性
                if perf.total_trades < self.min_observation_period:
                    # 數據不足，使用健康分數
                    strategy_scores[strategy_id] = perf.health_score
                    continue

                # 多因子評分模型
                health_weight = 0.3
                sharpe_weight = 0.25
                momentum_weight = 0.2
                win_rate_weight = 0.15
                risk_adj_weight = 0.1

                # 歸一化各指標到0-1範圍
                normalized_sharpe = max(0, min(1, (perf.sharpe_ratio + 2) / 4))  # -2到2映射到0-1
                normalized_momentum = max(
                    0, min(1, (perf.momentum_score + 0.1) / 0.2)
                )  # -0.1到0.1映射到0-1
                normalized_win_rate = perf.win_rate
                normalized_risk_adj = max(0, min(1, perf.risk_adjusted_return / 10))  # 0到10映射到0-1

                # 計算綜合分數
                composite_score = (
                    perf.health_score * health_weight
                    + normalized_sharpe * sharpe_weight
                    + normalized_momentum * momentum_weight
                    + normalized_win_rate * win_rate_weight
                    + normalized_risk_adj * risk_adj_weight
                )

                # 應用懲罰因子
                penalty = self._calculate_penalty_factors(strategy_id, perf)
                final_score = composite_score * (1 - penalty)

                strategy_scores[strategy_id] = max(final_score, 0.1)  # 最低0.1

                logger.debug(
                    f"策略評分: {strategy_id} = {final_score:.3f} "
                    f"(健康:{perf.health_score:.2f}, 夏普:{normalized_sharpe:.2f}, "
                    f"動量:{normalized_momentum:.2f}, 勝率:{normalized_win_rate:.2f})"
                )

            # 歸一化分配
            return self._normalize_allocations(strategy_scores)

        except Exception as e:
            logger.error(f"計算動態分配失敗: {e}")
            return {}

    def _calculate_penalty_factors(self, strategy_id: str, perf: StrategyPerformance) -> float:
        """計算懲罰因子"""
        penalty = 0.0

        # 最大回撤懲罰
        if perf.max_drawdown > 0.15:  # 超過15 % 回撤
            penalty += (perf.max_drawdown - 0.15) * 2

        # 波動性懲罰
        if perf.volatility > 0.05:  # 超過5 % 日波動
            penalty += (perf.volatility - 0.05) * 1

        # 交易次數懲罰（過度交易）
        if perf.total_trades > 100:  # 超過100筆交易
            penalty += 0.1

        return min(penalty, 0.5)  # 最大懲罰50%

    def _normalize_allocations(self, strategy_scores: Dict[str, float]) -> Dict[str, float]:
        """歸一化分配"""
        if not strategy_scores:
            return {}

        total_score = sum(strategy_scores.values())
        if total_score == 0:
            # 平均分配
            equal_allocation = 1.0 / len(strategy_scores)
            return {sid: equal_allocation for sid in strategy_scores.keys()}

        # 計算原始分配
        raw_allocations = {sid: score / total_score for sid, score in strategy_scores.items()}

        # 應用分配限制
        target_allocations = {}
        for strategy_id, raw_allocation in raw_allocations.items():
            target_allocation = max(
                min(raw_allocation, self.max_strategy_allocation), self.min_strategy_allocation
            )
            target_allocations[strategy_id] = target_allocation

        # 重新歸一化
        total_allocation = sum(target_allocations.values())
        if total_allocation > 0:
            for strategy_id in target_allocations:
                target_allocations[strategy_id] /= total_allocation

        return target_allocations

    async def execute_dynamic_rebalancing(self) -> Dict[str, Any]:
        """執行動態重新平衡"""
        try:
            logger.info("開始動態資金重新分配")

            # 計算新的分配
            new_allocations = self.calculate_dynamic_allocation()

            if not new_allocations:
                logger.warning("無法計算新分配，跳過重新平衡")
                return {"rebalanced": False, "reason": "no_allocations"}

            # 獲取當前分配
            current_allocations = {}
            for strategy_id, allocation in self.portfolio_manager.strategy_allocations.items():
                current_allocations[strategy_id] = allocation.current_allocation

            # 檢查是否需要重新平衡
            significant_changes = []
            for strategy_id, new_allocation in new_allocations.items():
                current_allocation = current_allocations.get(strategy_id, 0.0)
                change = abs(new_allocation - current_allocation)

                if change > self.rebalance_threshold:
                    # 限制單次變化幅度
                    if change > self.max_allocation_change:
                        direction = 1 if new_allocation > current_allocation else -1
                        new_allocation = current_allocation + (
                            self.max_allocation_change * direction
                        )
                        new_allocations[strategy_id] = new_allocation

                    significant_changes.append(
                        {
                            "strategy_id": strategy_id,
                            "old_allocation": current_allocation,
                            "new_allocation": new_allocation,
                            "change": new_allocation - current_allocation,
                        }
                    )

            if not significant_changes:
                logger.info("分配變化不顯著，無需重新平衡")
                return {"rebalanced": False, "reason": "no_significant_changes"}

            # 執行重新平衡
            for change in significant_changes:
                strategy_id = change["strategy_id"]
                new_allocation = change["new_allocation"]

                if strategy_id in self.portfolio_manager.strategy_allocations:
                    allocation = self.portfolio_manager.strategy_allocations[strategy_id]
                    allocation.target_allocation = new_allocation
                    allocation.current_allocation = new_allocation
                    allocation.allocated_capital = (
                        self.portfolio_manager.total_capital * new_allocation
                    )
                    allocation.last_updated = datetime.now()

            # 發布重新平衡事件
            publish_event(
                EventType.PORTFOLIO_REBALANCE,
                "dynamic_allocation_engine",
                {
                    "actions": significant_changes,
                    "total_change": sum(abs(c["change"]) for c in significant_changes),
                    "timestamp": datetime.now().isoformat(),
                    "reason": "dynamic_performance_based",
                },
            )

            logger.info(f"動態重新平衡完成: {len(significant_changes)} 個調整")

            return {
                "rebalanced": True,
                "actions": significant_changes,
                "total_change": sum(abs(c["change"]) for c in significant_changes),
            }

        except Exception as e:
            logger.error(f"動態重新平衡失敗: {e}")
            return {"rebalanced": False, "reason": f"error: {e}"}

    def get_performance_summary(self) -> Dict[str, Any]:
        """獲取績效摘要"""
        summary = {
            "total_strategies": len(self.strategy_performances),
            "strategies_with_data": len(
                [
                    p
                    for p in self.strategy_performances.values()
                    if p.total_trades >= self.min_observation_period
                ]
            ),
            "average_health_score": np.mean(
                [p.health_score for p in self.strategy_performances.values()]
            )
            if self.strategy_performances
            else 0.0,
            "best_strategy": None,
            "worst_strategy": None,
            "strategy_details": {},
        }

        if self.strategy_performances:
            # 找出最佳和最差策略
            best_strategy = max(
                self.strategy_performances.values(), key=lambda p: p.health_score * p.sharpe_ratio
            )
            worst_strategy = min(
                self.strategy_performances.values(), key=lambda p: p.health_score * p.sharpe_ratio
            )

            summary["best_strategy"] = best_strategy.strategy_id
            summary["worst_strategy"] = worst_strategy.strategy_id

            # 策略詳情
            for strategy_id, perf in self.strategy_performances.items():
                summary["strategy_details"][strategy_id] = {
                    "health_score": perf.health_score,
                    "sharpe_ratio": perf.sharpe_ratio,
                    "max_drawdown": perf.max_drawdown,
                    "win_rate": perf.win_rate,
                    "total_trades": perf.total_trades,
                    "total_pnl": perf.total_pnl,
                }

        return summary


if __name__ == "__main__":
    # 測試動態分配引擎
    print("🧪 動態資金分配引擎測試")

    # 這裡需要實際的PortfolioManager實例來測試
    print("✅ 動態分配引擎模塊載入成功")
