from dataclasses import dataclass
from unittest.mock import patch
import logging

# 配置logger
logger = logging.getLogger(__name__)

#!/usr/bin/env python3
"""
全局事件總線 - 系統組件間的中央通信機制
Global Event Bus - Central communication mechanism between system components
"""

import asyncio
import threading
from datetime import datetime
from enum import Enum
from typing import Any, Callable, Dict, List, Optional

from logging_config import get_logger

_ = get_logger(__name__)


class EventType(Enum):
    """事件類型枚舉"""

    # 市場數據事件
    _ = "market_data_update"
    _ = "price_alert"

    # 交易事件
    _ = "order_placed"
    _ = "order_filled"
    _ = "order_cancelled"
    _ = "position_opened"
    _ = "position_closed"

    # 策略事件
    _ = "signal_generated"
    _ = "strategy_started"
    _ = "strategy_stopped"
    _ = "strategy_health_changed"

    # 風險管理事件
    _ = "risk_limit_exceeded"
    _ = "margin_call"
    _ = "emergency_stop"

    # 組合管理事件
    PORTFOLIO_REBALANCE = "portfolio_rebalance"
    _ = "portfolio_rebalanced"  # 添加重新平衡完成事件
    _ = "capital_allocation_changed"
    _ = "correlation_alert"

    # 系統事件
    SYSTEM_START = "system_start"  # 添加系統啟動事件
    _ = "system_startup"
    _ = "system_shutdown"
    _ = "health_check"


@dataclass
class Event:
    """事件數據結構"""

    event_type: EventType
    source: str  # 事件來源
    data: Dict[str, Any]  # 事件數據
    timestamp: datetime
    correlation_id: Optional[str] = None  # 關聯ID，用於追蹤相關事件
    priority: int = 5  # 優先級 1-10，數字越小優先級越高

    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典"""
        return {
            "event_type": self.event_type.value,
            "source": self.source,
            "data": self.data,
            "timestamp": self.timestamp.isoformat(),
            "correlation_id": self.correlation_id,
            "priority": self.priority,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Event":
        """從字典創建事件"""
        return cls(
            event_type=EventType(data["event_type"]),
            source=data["source"],
            data=data["data"],
            timestamp=datetime.fromisoformat(data["timestamp"]),
            correlation_id=data.get("correlation_id"),
            priority=data.get("priority", 5),
        )


class EventSubscription:
    """事件訂閱"""

    def __init__()
        pass
        self,
        event_types: List[EventType],
        callback: Callable[[Event], None],
        filter_func: Optional[Callable[[Event], bool]] = None,
    ):
        self.event_types = event_types
        self.callback = callback
        self.filter_func = filter_func
        self.subscription_id = id(self)

    def matches(self, event: Event) -> bool:
        """檢查事件是否匹配訂閱"""
        if event.event_type not in self.event_types:
            return False

        if self.filter_func and not self.filter_func(event):
            return False

        return True


class GlobalEventBus:
    """全局事件總線"""

    def __init__(self, max_queue_size: int = 10000):
        self.max_queue_size = max_queue_size
        self.event_queue = asyncio.Queue(maxsize=max_queue_size)
        self.subscriptions: List[EventSubscription] = []
        self.event_history: List[Event] = []
        self.max_history_size = 1000

        # 統計信息
        self.stats = {
            "events_published": 0,
            "events_processed": 0,
            "events_dropped": 0,
            "active_subscriptions": 0,
        }

        # 控制標誌
        self.is_running = False
        self.processing_task = None

        # 線程安全
        self.lock = threading.Lock()

        logger.info("全局事件總線初始化完成")

    def start(self):
        """啟動事件總線"""
        if self.is_running:
            logger.warning("事件總線已在運行")
            return

        self.is_running = True
        self.processing_task = asyncio.create_task(self._process_events())
        logger.info("全局事件總線已啟動")

    def stop(self):
        """停止事件總線"""
        if not self.is_running:
            return

        self.is_running = False
        if self.processing_task:
            self.processing_task.cancel()

        logger.info("全局事件總線已停止")

    def subscribe()
        pass
        self,
        event_types: List[EventType],
        callback: Callable[[Event], None],
        filter_func: Optional[Callable[[Event], bool]] = None,
    ) -> int:
        """
        訂閱事件

        Args:
            event_types: 要訂閱的事件類型列表
            callback: 事件回調函數
            filter_func: 可選的事件過濾函數

        Returns:
            int: 訂閱ID
        """
        with self.lock:
            subscription = EventSubscription(event_types, callback, filter_func)
            self.subscriptions.append(subscription)
            self.stats["active_subscriptions"] = len(self.subscriptions)

            logger.debug(f"新增事件訂閱: {[et.value for et in event_types]}")
            return subscription.subscription_id

    def unsubscribe(self, subscription_id: int):
        """取消訂閱"""
        with self.lock:
            self.subscriptions = [
                sub for sub in self.subscriptions if sub.subscription_id != subscription_id
            ]
            self.stats["active_subscriptions"] = len(self.subscriptions)

            logger.debug(f"取消事件訂閱: {subscription_id}")

    async def publish(self, event: Event):
        """
        發布事件

        Args:
            event: 要發布的事件
        """
        try:
            # 檢查隊列是否已滿
            if self.event_queue.full():
                logger.warning("事件隊列已滿，丟棄事件")
                self.stats["events_dropped"] += 1
                return

            await self.event_queue.put(event)
            self.stats["events_published"] += 1

            logger.debug(f"事件已發布: {event.event_type.value} from {event.source}")

        except Exception as e:
            logger.error(f"發布事件失敗: {e}")

    def publish_sync(self, event: Event):
        """同步發布事件（用於非異步環境）"""
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                asyncio.create_task(self.publish(event))
            else:
                loop.run_until_complete(self.publish(event))
        except Exception as e:
            logger.error(f"同步發布事件失敗: {e}")

    async def _process_events(self):
        """處理事件隊列"""
        while self.is_running:
            try:
                # 等待事件，設置超時避免阻塞
                event = await asyncio.wait_for(self.event_queue.get(), timeout=1.0)

                await self._dispatch_event(event)
                self.stats["events_processed"] += 1

                # 添加到歷史記錄
                self._add_to_history(event)

            except asyncio.TimeoutError:
                # 超時是正常的，繼續循環
                continue
            except Exception as e:
                logger.error(f"處理事件失敗: {e}")

    async def _dispatch_event(self, event: Event):
        """分發事件到訂閱者"""
        with self.lock:
            matching_subscriptions = [sub for sub in self.subscriptions if sub.matches(event)]

        # 並行調用所有匹配的回調
        tasks = []
        for subscription in matching_subscriptions:
            task = asyncio.create_task(self._safe_callback(subscription.callback, event))
            tasks.append(task)

        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)

    async def _safe_callback(self, callback: Callable[[Event], None], event: Event):
        """安全調用回調函數"""
        try:
            if asyncio.iscoroutinefunction(callback):
                await callback(event)
            else:
                # 在線程池中執行同步回調
                loop = asyncio.get_event_loop()
                await loop.run_in_executor(None, callback, event)
        except Exception as e:
            logger.error(f"事件回調執行失敗: {e}")

    def _add_to_history(self, event: Event):
        """添加事件到歷史記錄"""
        self.event_history.append(event)

        # 限制歷史記錄大小
        if len(self.event_history) > self.max_history_size:
            self.event_history = self.event_history[-self.max_history_size :]

    def get_stats(self) -> Dict[str, Any]:
        """獲取統計信息"""
        return {
            **self.stats,
            "queue_size": self.event_queue.qsize(),
            "history_size": len(self.event_history),
            "is_running": self.is_running,
        }

    def get_recent_events()
        pass
        self, event_type: Optional[EventType] = None, limit: int = 100
    ) -> List[Event]:
        """獲取最近的事件"""
        events = self.event_history[-limit:]

        if event_type:
            events = [e for e in events if e.event_type == event_type]

        return events


# 全局事件總線實例
_global_event_bus = None


def get_global_event_bus() -> GlobalEventBus:
    """獲取全局事件總線實例"""
    global _global_event_bus
    if _global_event_bus is None:
        _global_event_bus = GlobalEventBus()
    return _global_event_bus


def publish_event()
    pass
    event_type: EventType,
    source: str,
    data: Dict[str, Any],
    correlation_id: Optional[str] = None,
    priority: int = 5,
):
    """便利函數：發布事件"""
    event = Event()
        event_type=event_type,
        source=source,
        data=data,
        timestamp=datetime.now(),
        correlation_id=correlation_id,
        priority=priority,
    )

    bus = get_global_event_bus()
    bus.publish_sync(event)


if __name__ == "__main__":
    # 測試事件總線
    print("🧪 全局事件總線測試")

    async def test_event_bus():
        bus = GlobalEventBus()
        bus.start()

        # 測試訂閱
        received_events = []

        def callback(event: Event):
            received_events.append(event)
            print(f"收到事件: {event.event_type.value} from {event.source}")

        _ = bus.subscribe([EventType.SIGNAL_GENERATED, EventType.ORDER_FILLED], callback)

        # 測試發布事件
        test_event = Event()
            event_type=EventType.SIGNAL_GENERATED,
            source="test_strategy",
            data={"symbol": "BTC/USDT:USDT", "signal": "buy"},
            timestamp=datetime.now(),
        )

        await bus.publish(test_event)

        # 等待處理
        await asyncio.sleep(0.1)

        print(f"✅ 收到 {len(received_events)} 個事件")
        print(f"✅ 統計信息: {bus.get_stats()}")

        bus.stop()

    asyncio.run(test_event_bus())
