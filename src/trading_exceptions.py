#!/usr/bin/env python3
"""
交易異常體系 - 統一的異常處理
Trading Exceptions - Unified exception handling system
"""

from datetime import datetime
from typing import Any, Dict, Optional


class TradingException(Exception):
    """交易系統基礎異常"""

    def __init__(self, message: str, context: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.message = message
        self.context = context or {}
        self.timestamp = datetime.now()


class ConfigError(TradingException):
    """配置錯誤"""

    pass


class DataError(TradingException):
    """數據錯誤"""

    pass


class ExecutionError(TradingException):
    """交易執行錯誤"""

    pass


class PartialFillError(ExecutionError):
    """部分成交錯誤 - 配對交易中的單邊風險"""

    def __init__()
        self,
        message: str,
        successful_order: Dict[str, Any],
        failed_order_info: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(message, context)
        self.successful_order = successful_order
        self.failed_order_info = failed_order_info
        self.requires_compensation = True


class NetworkError(TradingException):
    """網絡錯誤"""

    pass


class InsufficientFundsError(ExecutionError):
    """資金不足錯誤"""

    pass


class RiskLimitExceededError(TradingException):
    """風險限制超出錯誤"""

    pass
