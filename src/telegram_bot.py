#!/usr/bin/env python3
"""
Telegram 機器人控制器 - 遠程控制交易機器人
Telegram Bot Controller - Remote control for trading bot
"""

import os
import json
import threading
import time
from typing import Dict, Optional
import requests
from dynamic_config import get_dynamic_config_manager
from alert_manager import get_alert_manager
from logging_config import get_logger

logger = get_logger(__name__)


class TelegramBotController:
    """Telegram 機器人控制器"""
    
    def __init__(self, bot_token: str = None, authorized_users: list = None):
        self.bot_token = bot_token or os.getenv('TELEGRAM_BOT_TOKEN')
        self.authorized_users = authorized_users or []
        self.last_update_id = 0
        self.is_running = False
        self.polling_thread = None
        
        # 載入授權用戶列表
        self._load_authorized_users()
        
        # 命令處理器映射
        self.command_handlers = {
            '/start': self._handle_start,
            '/help': self._handle_help,
            '/status': self._handle_status,
            '/config': self._handle_config,
            '/set': self._handle_set_parameter,
            '/get': self._handle_get_parameter,
            '/reload': self._handle_reload_config,
            '/panic': self._handle_panic,
            '/health': self._handle_health,
            '/stats': self._handle_stats
        }
        
        logger.info("TelegramBotController 初始化完成")
    
    def _load_authorized_users(self):
        """載入授權用戶列表"""
        try:
            # 從環境變量載入
            env_users = os.getenv('TELEGRAM_AUTHORIZED_USERS', '')
            if env_users:
                self.authorized_users.extend([int(uid.strip()) for uid in env_users.split(',') if uid.strip()])
            
            # 從配置文件載入
            config_manager = get_dynamic_config_manager()
            config_users = config_manager.get_value('telegram.authorized_users', [])
            self.authorized_users.extend(config_users)
            
            # 去重
            self.authorized_users = list(set(self.authorized_users))
            
            logger.info(f"已載入 {len(self.authorized_users)} 個授權用戶")
            
        except Exception as e:
            logger.error(f"載入授權用戶失敗: {e}")
    
    def _is_authorized(self, user_id: int) -> bool:
        """檢查用戶是否有權限"""
        return user_id in self.authorized_users
    
    def _send_message(self, chat_id: int, text: str, parse_mode: str = 'Markdown') -> bool:
        """發送消息"""
        try:
            if not self.bot_token:
                logger.warning("Telegram bot token 未設置")
                return False
            
            url = f"https://api.telegram.org/bot{self.bot_token}/sendMessage"
            
            payload = {
                'chat_id': chat_id,
                'text': text,
                'parse_mode': parse_mode,
                'disable_web_page_preview': True
            }
            
            response = requests.post(url, json=payload, timeout=10)
            
            if response.status_code == 200:
                return True
            else:
                logger.warning(f"發送 Telegram 消息失敗: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"發送 Telegram 消息失敗: {e}")
            return False
    
    def _get_updates(self) -> list:
        """獲取更新"""
        try:
            if not self.bot_token:
                return []
            
            url = f"https://api.telegram.org/bot{self.bot_token}/getUpdates"
            
            params = {
                'offset': self.last_update_id + 1,
                'timeout': 10
            }
            
            response = requests.get(url, params=params, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                return data.get('result', [])
            else:
                logger.warning(f"獲取 Telegram 更新失敗: {response.status_code}")
                return []
                
        except Exception as e:
            logger.error(f"獲取 Telegram 更新失敗: {e}")
            return []
    
    def _process_update(self, update: Dict):
        """處理更新"""
        try:
            if 'message' not in update:
                return
            
            message = update['message']
            chat_id = message['chat']['id']
            user_id = message['from']['id']
            text = message.get('text', '')
            
            # 檢查授權
            if not self._is_authorized(user_id):
                self._send_message(chat_id, "❌ 您沒有權限使用此機器人")
                logger.warning(f"未授權用戶嘗試訪問: {user_id}")
                return
            
            # 解析命令
            if text.startswith('/'):
                parts = text.split()
                command = parts[0]
                args = parts[1:] if len(parts) > 1 else []
                
                # 執行命令
                if command in self.command_handlers:
                    self.command_handlers[command](chat_id, args)
                else:
                    self._send_message(chat_id, f"❌ 未知命令: {command}\n使用 /help 查看可用命令")
            
        except Exception as e:
            logger.error(f"處理 Telegram 更新失敗: {e}")
    
    def _handle_start(self, chat_id: int, args: list):
        """處理 /start 命令"""
        message = """
🤖 **配對交易機器人控制台**

歡迎使用 Telegram 遠程控制功能！

使用 /help 查看所有可用命令。
        """
        self._send_message(chat_id, message)
    
    def _handle_help(self, chat_id: int, args: list):
        """處理 /help 命令"""
        message = """
📋 **可用命令列表**

🔍 **查詢命令:**
• `/status` - 查看機器人狀態
• `/health` - 查看健康狀態
• `/stats` - 查看統計信息
• `/config` - 查看當前配置

⚙️ **配置命令:**
• `/get <參數>` - 獲取配置參數
• `/set <參數> <值>` - 設置配置參數
• `/reload` - 重新載入配置

🚨 **控制命令:**
• `/panic` - 緊急停止所有交易

💡 **示例:**
• `/get trading_pair`
• `/set entry_threshold_high 2.8`
        """
        self._send_message(chat_id, message)
    
    def _handle_status(self, chat_id: int, args: list):
        """處理 /status 命令"""
        try:
            # 獲取機器人狀態（這裡需要與主機器人集成）
            alert_manager = get_alert_manager()
            health_status = alert_manager.get_health_status()
            
            message = f"""
📊 **機器人狀態報告**

🕐 **運行時間:** {health_status.get('uptime_human', 'N/A')}
📈 **狀態:** {health_status.get('status', 'unknown')}
🔔 **總警報數:** {health_status.get('total_alerts', 0)}

💰 **最後交易:**
{self._format_last_trade(health_status.get('last_trade'))}

📍 **當前持倉:**
{self._format_current_position(health_status.get('current_position'))}
            """
            
            self._send_message(chat_id, message)
            
        except Exception as e:
            self._send_message(chat_id, f"❌ 獲取狀態失敗: {e}")
    
    def _handle_config(self, chat_id: int, args: list):
        """處理 /config 命令"""
        try:
            config_manager = get_dynamic_config_manager()
            config = config_manager.get_config()
            
            # 隱藏敏感信息
            safe_config = self._sanitize_config(config)
            
            message = f"""
⚙️ **當前配置**

```json
{json.dumps(safe_config, indent=2, ensure_ascii=False)}
```
            """
            
            self._send_message(chat_id, message)
            
        except Exception as e:
            self._send_message(chat_id, f"❌ 獲取配置失敗: {e}")
    
    def _handle_get_parameter(self, chat_id: int, args: list):
        """處理 /get 命令"""
        if not args:
            self._send_message(chat_id, "❌ 請指定參數名稱\n示例: `/get trading_pair`")
            return
        
        try:
            config_manager = get_dynamic_config_manager()
            key = args[0]
            value = config_manager.get_value(key)
            
            message = f"""
📋 **配置參數**

**參數:** `{key}`
**值:** `{value}`
            """
            
            self._send_message(chat_id, message)
            
        except Exception as e:
            self._send_message(chat_id, f"❌ 獲取參數失敗: {e}")
    
    def _handle_set_parameter(self, chat_id: int, args: list):
        """處理 /set 命令"""
        if len(args) < 2:
            self._send_message(chat_id, "❌ 請指定參數名稱和值\n示例: `/set entry_threshold_high 2.8`")
            return
        
        try:
            config_manager = get_dynamic_config_manager()
            key = args[0]
            value_str = ' '.join(args[1:])
            
            # 嘗試轉換數據類型
            try:
                if value_str.lower() in ['true', 'false']:
                    value = value_str.lower() == 'true'
                elif '.' in value_str:
                    value = float(value_str)
                elif value_str.isdigit():
                    value = int(value_str)
                else:
                    value = value_str
            except:
                value = value_str
            
            success = config_manager.set_value(key, value)
            
            if success:
                message = f"✅ 參數已更新\n**{key}** = `{value}`"
            else:
                message = f"❌ 參數更新失敗: {key}"
            
            self._send_message(chat_id, message)
            
        except Exception as e:
            self._send_message(chat_id, f"❌ 設置參數失敗: {e}")
    
    def _handle_reload_config(self, chat_id: int, args: list):
        """處理 /reload 命令"""
        try:
            config_manager = get_dynamic_config_manager()
            result = config_manager.execute_remote_command('reload_config')
            
            if result['status'] == 'success':
                message = "✅ 配置重新載入成功"
            else:
                message = f"❌ 配置重新載入失敗: {result['message']}"
            
            self._send_message(chat_id, message)
            
        except Exception as e:
            self._send_message(chat_id, f"❌ 重新載入配置失敗: {e}")
    
    def _handle_panic(self, chat_id: int, args: list):
        """處理 /panic 命令"""
        try:
            # 發送緊急停止警報
            alert_manager = get_alert_manager()
            alert_manager.send_critical_alert(
                "緊急停止指令",
                "收到 Telegram 緊急停止指令",
                {"來源": "Telegram", "用戶": chat_id}
            )
            
            message = """
🚨 **緊急停止指令已發送**

⚠️ 所有交易活動將被暫停
📧 緊急警報已發送給管理員
🔄 請等待系統響應
            """
            
            self._send_message(chat_id, message)
            
        except Exception as e:
            self._send_message(chat_id, f"❌ 緊急停止失敗: {e}")
    
    def _handle_health(self, chat_id: int, args: list):
        """處理 /health 命令"""
        try:
            alert_manager = get_alert_manager()
            health_status = alert_manager.get_health_status()
            
            status_emoji = "✅" if health_status.get('status') == 'ok' else "❌"
            
            message = f"""
{status_emoji} **健康檢查報告**

**狀態:** {health_status.get('status', 'unknown')}
**最後更新:** {health_status.get('last_update', 'N/A')}
**運行時間:** {health_status.get('uptime_human', 'N/A')}
            """
            
            self._send_message(chat_id, message)
            
        except Exception as e:
            self._send_message(chat_id, f"❌ 獲取健康狀態失敗: {e}")
    
    def _handle_stats(self, chat_id: int, args: list):
        """處理 /stats 命令"""
        # 這裡需要與主機器人集成獲取統計信息
        message = """
📊 **統計信息**

🔄 此功能正在開發中...
請使用 `/status` 查看基本狀態信息
        """
        self._send_message(chat_id, message)
    
    def _format_last_trade(self, last_trade: Optional[Dict]) -> str:
        """格式化最後交易信息"""
        if not last_trade:
            return "無最近交易記錄"
        
        return f"""
• **類型:** {last_trade.get('type', 'N/A')}
• **時間:** {last_trade.get('time', 'N/A')}
• **盈虧:** ${last_trade.get('pnl', 0):.2f}
        """
    
    def _format_current_position(self, position: Optional[Dict]) -> str:
        """格式化當前持倉信息"""
        if not position:
            return "無當前持倉"
        
        return f"""
• **狀態:** {position.get('status', 'N/A')}
• **類型:** {position.get('type', 'N/A')}
        """
    
    def _sanitize_config(self, config: Dict) -> Dict:
        """清理配置中的敏感信息"""
        safe_config = config.copy()
        
        # 隱藏敏感字段
        sensitive_fields = ['api_key', 'secret', 'password', 'token']
        
        def hide_sensitive(obj, path=""):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    if any(field in key.lower() for field in sensitive_fields):
                        obj[key] = "***隱藏***"
                    else:
                        hide_sensitive(value, f"{path}.{key}" if path else key)
        
        hide_sensitive(safe_config)
        return safe_config
    
    def start_polling(self):
        """開始輪詢"""
        if self.is_running:
            logger.warning("Telegram bot 已在運行")
            return
        
        if not self.bot_token:
            logger.warning("Telegram bot token 未設置，無法啟動")
            return
        
        self.is_running = True
        
        def polling_loop():
            logger.info("Telegram bot 輪詢已啟動")
            
            while self.is_running:
                try:
                    updates = self._get_updates()
                    
                    for update in updates:
                        self._process_update(update)
                        self.last_update_id = update['update_id']
                    
                    time.sleep(1)
                    
                except Exception as e:
                    logger.error(f"Telegram bot 輪詢錯誤: {e}")
                    time.sleep(5)
            
            logger.info("Telegram bot 輪詢已停止")
        
        self.polling_thread = threading.Thread(target=polling_loop, daemon=True)
        self.polling_thread.start()
    
    def stop_polling(self):
        """停止輪詢"""
        self.is_running = False
        if self.polling_thread:
            self.polling_thread.join(timeout=5)
        logger.info("Telegram bot 已停止")


# 全局 Telegram 機器人實例
_telegram_bot = None

def get_telegram_bot() -> Optional[TelegramBotController]:
    """獲取全局 Telegram 機器人實例"""
    global _telegram_bot
    if _telegram_bot is None:
        bot_token = os.getenv('TELEGRAM_BOT_TOKEN')
        if bot_token:
            _telegram_bot = TelegramBotController(bot_token)
        else:
            logger.warning("TELEGRAM_BOT_TOKEN 未設置，Telegram 控制功能不可用")
    return _telegram_bot


if __name__ == "__main__":
    # 測試 Telegram 機器人
    print("測試 Telegram 機器人...")
    
    bot = TelegramBotController()
    
    if bot.bot_token:
        print("啟動 Telegram 機器人輪詢...")
        bot.start_polling()
        
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n停止 Telegram 機器人...")
            bot.stop_polling()
    else:
        print("請設置 TELEGRAM_BOT_TOKEN 環境變量")
    
    print("Telegram 機器人測試完成！")
