#!/usr/bin/env python3
"""
改進的回測腳本，使用優化後的組件
"""

import json
import os
from datetime import datetime
from typing import Dict

import numpy as np
import pandas as pd
from trading_executor import TradingExecutor

from data_handler import DataHandler
from logging_config import get_logger, setup_logging
from pair_trading_bot import PairTradingBot, SignalState, TradingState
from utils import load_config

# 設置日誌
setup_logging()
logger = get_logger(__name__)


class ImprovedBacktestEngine:
    """改進的回測引擎"""

    def __init__(self, config_path: str = "config.json"):
        self.config = load_config(config_path)
        self.backtest_config = self.config.get("backtesting", {})

        # 初始化組件（模擬模式）
        self.mock_exchange = MockExchange()
        self.data_handler = DataHandler(self.mock_exchange, self.config, mode="BACKTEST")
        self.trading_executor = TradingExecutor(self.mock_exchange, self.config)

        # 創建機器人實例
        self.bot = None

        # 回測結果
        self.results = {"trades": [], "equity_curve": [], "performance_metrics": {}}

        logger.info("改進的回測引擎初始化完成")

    def load_data(self, data_path: str) -> bool:
        """載入歷史數據"""
        try:
            if data_path.endswith(".csv"):
                data = pd.read_csv(data_path, index_col=0, parse_dates=True)
            else:
                logger.error(f"不支持的數據格式: {data_path}")
                return False

            # 驗證數據格式
            required_columns = ["base_price", "quote_price"]
            if not all(col in data.columns for col in required_columns):
                logger.error(f"數據缺少必要列: {required_columns}")
                return False

            # 如果沒有成交量數據，生成模擬數據
            if "base_volume" not in data.columns:
                data["base_volume"] = np.random.uniform(1000, 5000, len(data))
            if "quote_volume" not in data.columns:
                data["quote_volume"] = np.random.uniform(5000, 20000, len(data))

            # 載入到數據處理器
            success = self.data_handler.load_backtest_data(data)
            if success:
                logger.info(f"歷史數據載入成功: {len(data)} 條記錄")

            return success

        except Exception as e:
            logger.error(f"載入歷史數據失敗: {e}")
            return False

    def run_backtest(self) -> Dict:
        """執行回測"""
        try:
            logger.info("開始執行改進的回測...")

            # 創建機器人實例，使用已配置的組件
            self.bot = PairTradingBot.__new__(PairTradingBot)
            self.bot.config = self.config
            self.bot.data_handler = self.data_handler
            self.bot.trading_executor = self.trading_executor

            # 初始化機器人狀態
            self.bot.entry_threshold_high = self.config["entry_threshold_high"]
            self.bot.entry_threshold_low = self.config["entry_threshold_low"]
            self.bot.confirmation_threshold_high = self.config["confirmation_threshold_high"]
            self.bot.confirmation_threshold_low = self.config["confirmation_threshold_low"]
            self.bot.cooldown_period = self.config["cooldown_period"]
            self.bot.stop_loss_pct = self.config["stop_loss_pct"]
            self.bot.take_profit_target = self.config["take_profit_target"]

            self.bot.trading_state = TradingState.SEARCHING
            self.bot.signal_state = SignalState.NONE
            self.bot.cooldown_end_bar = None
            self.bot.current_bar_index = 0

            self.bot.stats = {
                "total_trades": 0,
                "winning_trades": 0,
                "losing_trades": 0,
                "total_pnl": 0.0,
                "max_drawdown": 0.0,
                "current_drawdown": 0.0,
                "peak_equity": 0.0,
            }

            # 初始資金
            initial_capital = self.backtest_config.get("initial_capital", 10000)
            current_equity = initial_capital

            # 執行回測循環
            bar_count = 0
            while self.data_handler.has_more_data():
                try:
                    # 執行單次迭代
                    success = self.bot.run_single_iteration()

                    if success:
                        # 記錄權益曲線
                        current_pnl = self.trading_executor.calculate_current_pnl()
                        current_equity = (
                            initial_capital
                            + self.trading_executor.get_trade_statistics()["total_pnl"]
                            + current_pnl
                        )

                        equity_record = {
                            "bar_index": bar_count,
                            "timestamp": datetime.now(),  # 在實際應用中應該使用數據的時間戳
                            "equity": current_equity,
                            "zscore": self.data_handler.get_current_zscore(),
                            "trading_state": self.bot.trading_state.value,
                            "position_active": self.trading_executor.is_position_active(),
                        }
                        self.results["equity_curve"].append(equity_record)

                        bar_count += 1

                        if bar_count % 100 == 0:
                            logger.info(f"回測進度: {bar_count} 根K線，當前權益: ${current_equity:.2f}")

                    else:
                        logger.warning(f"第 {bar_count} 根K線處理失敗")
                        break

                except Exception as e:
                    logger.error(f"回測循環中發生錯誤: {e}")
                    break

            # 獲取最終交易統計
            final_stats = self.trading_executor.get_trade_statistics()
            self.results["trades"] = self.trading_executor.get_trade_history()

            # 計算績效指標
            self.results["performance_metrics"] = self._calculate_performance_metrics(
                initial_capital, current_equity, final_stats
            )

            logger.info(f"回測完成，共處理 {bar_count} 根K線，執行 {final_stats['total_trades']} 筆交易")
            return self.results["performance_metrics"]

        except Exception as e:
            logger.error(f"回測執行失敗: {e}")
            return {}

    def _calculate_performance_metrics(
        self, initial_capital: float, final_equity: float, trade_stats: Dict
    ) -> Dict:
        """計算績效指標"""
        try:
            if not self.results["equity_curve"]:
                return {}

            equity_df = pd.DataFrame(self.results["equity_curve"])

            # 基本指標
            total_return = (final_equity - initial_capital) / initial_capital

            # 計算回撤
            equity_df["peak"] = equity_df["equity"].cummax()
            equity_df["drawdown"] = (equity_df["equity"] - equity_df["peak"]) / equity_df["peak"]
            max_drawdown = abs(equity_df["drawdown"].min())

            # 計算夏普比率
            equity_df["returns"] = equity_df["equity"].pct_change()
            returns_std = equity_df["returns"].std()
            sharpe_ratio = (
                (equity_df["returns"].mean() / returns_std * np.sqrt(252)) if returns_std > 0 else 0
            )

            # 平均持倉時間（以K線數計算）
            avg_hold_time = (
                np.mean([t.get("hold_time", 0) for t in self.results["trades"]])
                if self.results["trades"]
                else 0
            )

            metrics = {
                "initial_capital": initial_capital,
                "final_equity": final_equity,
                "total_return": total_return,
                "total_pnl": trade_stats["total_pnl"],
                "total_trades": trade_stats["total_trades"],
                "winning_trades": trade_stats["winning_trades"],
                "losing_trades": trade_stats["losing_trades"],
                "win_rate": trade_stats["win_rate"],
                "avg_win": trade_stats["avg_win"],
                "avg_loss": trade_stats["avg_loss"],
                "profit_loss_ratio": trade_stats["profit_loss_ratio"],
                "max_drawdown": max_drawdown,
                "sharpe_ratio": sharpe_ratio,
                "avg_hold_time": avg_hold_time,
            }

            return metrics

        except Exception as e:
            logger.error(f"計算績效指標失敗: {e}")
            return {}

    def generate_report(self) -> str:
        """生成回測報告"""
        try:
            metrics = self.results["performance_metrics"]
            if not metrics:
                return "無可用的績效指標"

            report = """
=== 改進版配對交易回測報告 ===
生成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

策略參數:
- 交易對: {self.config['trading_pair']}
- 進場門檻: ±{self.config['entry_threshold_high']}
- 確認門檻: ±{self.config['confirmation_threshold_high']}
- 冷卻期: {self.config['cooldown_period']} 根K線
- 止損比例: {self.config['stop_loss_pct']:.2%}

績效指標:
- 初始資金: ${metrics['initial_capital']:,.2f}
- 最終權益: ${metrics['final_equity']:,.2f}
- 總回報率: {metrics['total_return']:.2%}
- 總盈虧: ${metrics['total_pnl']:,.2f}
- 夏普比率: {metrics['sharpe_ratio']:.4f}
- 最大回撤: {metrics['max_drawdown']:.2%}

交易統計:
- 總交易次數: {metrics['total_trades']}
- 獲利交易: {metrics['winning_trades']}
- 虧損交易: {metrics['losing_trades']}
- 勝率: {metrics['win_rate']:.2%}
- 平均獲利: ${metrics['avg_win']:.2f}
- 平均虧損: ${metrics['avg_loss']:.2f}
- 盈虧比: {metrics['profit_loss_ratio']:.2f}
- 平均持倉時間: {metrics['avg_hold_time']:.1f} 秒

改進功能驗證:
- ✓ 信號狀態重置邏輯
- ✓ 實際倉位價值計算
- ✓ 精確的盈虧比計算
- ✓ 詳細的交易歷史記錄
"""

            return report

        except Exception as e:
            logger.error(f"生成回測報告失敗: {e}")
            return ""

    def save_results(self, results_dir: str = "improved_backtest_results"):
        """保存回測結果"""
        try:
            os.makedirs(results_dir, exist_ok=True)
            timestamp = datetime.now().strftime("%Y % m%d_ % H%M % S")

            # 保存完整結果
            results_file = os.path.join(results_dir, f"backtest_results_{timestamp}.json")
            with open(results_file, "w", encoding="utf-8") as f:
                json.dump(self.results, f, indent=2, default=str, ensure_ascii=False)

            # 保存報告
            report_file = os.path.join(results_dir, f"backtest_report_{timestamp}.txt")
            report = self.generate_report()
            with open(report_file, "w", encoding="utf-8") as f:
                f.write(report)

            logger.info(f"改進版回測結果已保存到: {results_dir}")

        except Exception as e:
            logger.error(f"保存回測結果失敗: {e}")


class MockExchange:
    """模擬交易所，用於回測"""

    def __init__(self):
        self.commission = 0.001
        self.slippage = 0.0005

    def create_market_order(self, symbol: str, side: str, amount: float):
        """模擬市價單"""
        return {
            "id": f"mock_order_{datetime.now().timestamp()}",
            "symbol": symbol,
            "side": side,
            "amount": amount,
            "status": "closed",
        }

    def fetch_ticker(self, symbol: str):
        """模擬獲取行情"""
        # 在實際回測中，這應該返回當前時間點的價格
        return {"last": 50000}  # 簡化實現


def main():
    """主函數"""
    try:
        # 創建改進的回測引擎
        backtest_engine = ImprovedBacktestEngine()

        # 生成測試數據（如果不存在）
        data_path = "data/demo_data.csv"
        if not os.path.exists(data_path):
            print("生成測試數據...")
            from example_usage import generate_demo_data

            generate_demo_data()

        # 載入數據
        if not backtest_engine.load_data(data_path):
            print("載入數據失敗")
            return

        # 執行回測
        print("執行改進版回測...")
        results = backtest_engine.run_backtest()

        if results:
            # 顯示報告
            report = backtest_engine.generate_report()
            print(report)

            # 保存結果
            backtest_engine.save_results()

            print("改進版回測完成！")
        else:
            print("回測執行失敗")

    except Exception as e:
        logger.error(f"主程序執行失敗: {e}")


if __name__ == "__main__":
    main()
