import json
from datetime import datetime
from typing import Dict

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd

from logging_config import get_logger
from utils import calculate_log_spread, calculate_zscore, load_config

logger = get_logger(__name__)


class BacktestEngine:
    """回測引擎"""

    def __init__(self, config_path: str = "config.json"):
        self.config = load_config(config_path)
        self.backtest_config = self.config.get("backtesting", {})

        # 策略參數
        self.entry_threshold_high = self.config["entry_threshold_high"]
        self.entry_threshold_low = self.config["entry_threshold_low"]
        self.confirmation_threshold_high = self.config["confirmation_threshold_high"]
        self.confirmation_threshold_low = self.config["confirmation_threshold_low"]
        self.cooldown_period = self.config["cooldown_period"]
        self.stop_loss_pct = self.config["stop_loss_pct"]
        self.take_profit_target = self.config["take_profit_target"]
        self.lookback_period = self.config["lookback_period"]
        self.position_size_usd = self.config["position_size_usd"]

        # 回測參數
        self.initial_capital = self.backtest_config.get("initial_capital", 10000)
        self.commission = self.backtest_config.get("commission", 0.001)
        self.slippage = self.backtest_config.get("slippage", 0.0005)

        # 數據和結果
        self.price_data = None
        self.trades = []
        self.equity_curve = []
        self.performance_metrics = {}

        logger.info("BacktestEngine 初始化完成")

    def load_historical_data(self, data_path: str) -> bool:
        """載入歷史數據"""
        try:
            if data_path.endswith(".csv"):
                self.price_data = pd.read_csv(data_path, index_col=0, parse_dates=True)
            else:
                logger.error(f"不支持的數據格式: {data_path}")
                return False

            # 驗證數據格式
            required_columns = ["base_price", "quote_price"]
            if not all(col in self.price_data.columns for col in required_columns):
                logger.error(f"數據缺少必要列: {required_columns}")
                return False

            logger.info(f"歷史數據載入成功: {len(self.price_data)} 條記錄")
            return True

        except Exception as e:
            logger.error(f"載入歷史數據失敗: {e}")
            return False

    def prepare_data(self):
        """準備回測數據"""
        try:
            # 計算價差
            self.price_data["spread"] = self.price_data.apply(
                lambda row: calculate_log_spread(row["base_price"], row["quote_price"]), axis=1
            )

            # 計算 Z-score
            self.price_data["zscore"] = np.nan
            for _ in range(len(self.price_data)):
                if i >= self.lookback_period - 1:
                    spread_window = self.price_data["spread"].iloc[: i + 1]
                    zscore = calculate_zscore(spread_window, self.lookback_period)
                    self.price_data.loc[self.price_data.index[i], "zscore"] = zscore

            logger.info("回測數據準備完成")

        except Exception as e:
            logger.error(f"準備回測數據失敗: {e}")
            raise

    def run_backtest(self) -> Dict:
        """執行回測"""
        try:
            if self.price_data is None:
                raise ValueError("請先載入歷史數據")

            self.prepare_data()

            # 初始化狀態
            current_capital = self.initial_capital
            position = None  # None, 'long_base', 'short_base'
            entry_bar = None
            cooldown_end = None
            signal_triggered = None  # None, 'high', 'low'

            # 遍歷每個時間點
            for i, (timestamp, row) in enumerate(self.price_data.iterrows()):
                if np.isnan(row["zscore"]):
                    continue

                current_zscore = row["zscore"]
                base_price = row["base_price"]
                quote_price = row["quote_price"]

                # 檢查出場條件
                if position is not None:
                    exit_reason = None

                    # 止盈條件
                    if self.take_profit_target == "zero_crossing" and abs(current_zscore) < 0.1:
                        exit_reason = "take_profit"

                    # 止損條件
                    if exit_reason is None:
                        current_pnl = self._calculate_position_pnl(
                            position, entry_bar, i, base_price, quote_price
                        )
                        position_value = self.position_size_usd * 2
                        if (
                            current_pnl < 0
                            and abs(current_pnl) / position_value >= self.stop_loss_pct
                        ):
                            exit_reason = "stop_loss"

                    # 執行出場
                    if exit_reason:
                        pnl = self._execute_exit(position, entry_bar, i, exit_reason)
                        current_capital += pnl
                        position = None
                        entry_bar = None
                        cooldown_end = i + self.cooldown_period
                        signal_triggered = None

                # 檢查進場條件
                elif position is None and (cooldown_end is None or i > cooldown_end):
                    # 檢查觸發條件
                    if signal_triggered is None:
                        if current_zscore >= self.entry_threshold_high:
                            signal_triggered = "high"
                        elif current_zscore <= self.entry_threshold_low:
                            signal_triggered = "low"

                    # 檢查確認條件
                    elif (
                        signal_triggered == "high"
                        and current_zscore <= self.confirmation_threshold_high
                    ):
                        position = "short_base"
                        entry_bar = i
                        signal_triggered = None
                    elif (
                        signal_triggered == "low"
                        and current_zscore >= self.confirmation_threshold_low
                    ):
                        position = "long_base"
                        entry_bar = i
                        signal_triggered = None

                # 記錄權益曲線
                portfolio_value = current_capital
                if position is not None:
                    unrealized_pnl = self._calculate_position_pnl(
                        position, entry_bar, i, base_price, quote_price
                    )
                    portfolio_value += unrealized_pnl

                self.equity_curve.append(
                    {
                        "timestamp": timestamp,
                        "portfolio_value": portfolio_value,
                        "position": position,
                        "zscore": current_zscore,
                    }
                )

            # 計算績效指標
            self.performance_metrics = self._calculate_performance_metrics()

            logger.info("回測執行完成")
            return self.performance_metrics

        except Exception as e:
            logger.error(f"回測執行失敗: {e}")
            raise

    def _calculate_position_pnl(
        self,
        position: str,
        entry_idx: int,
        current_idx: int,
        current_base_price: float,
        current_quote_price: float,
    ) -> float:
        """計算持倉盈虧"""
        try:
            entry_data = self.price_data.iloc[entry_idx]
            entry_base_price = entry_data["base_price"]
            entry_quote_price = entry_data["quote_price"]

            # 計算倉位大小
            base_position_size = self.position_size_usd / entry_base_price
            quote_position_size = self.position_size_usd / entry_quote_price

            if position == "long_base":
                # 做多 base，做空 quote
                base_pnl = (current_base_price - entry_base_price) * base_position_size
                quote_pnl = (entry_quote_price - current_quote_price) * quote_position_size
            else:  # short_base
                # 做空 base，做多 quote
                base_pnl = (entry_base_price - current_base_price) * base_position_size
                quote_pnl = (current_quote_price - entry_quote_price) * quote_position_size

            # 扣除手續費和滑價
            total_cost = (
                entry_base_price * base_position_size + entry_quote_price * quote_position_size
            ) * (self.commission + self.slippage)

            return base_pnl + quote_pnl - total_cost

        except Exception as e:
            logger.error(f"計算持倉盈虧失敗: {e}")
            return 0.0

    def _execute_exit(
        self, position: str, entry_idx: int, exit_idx: int, exit_reason: str
    ) -> float:
        """執行出場並記錄交易"""
        try:
            entry_data = self.price_data.iloc[entry_idx]
            exit_data = self.price_data.iloc[exit_idx]

            pnl = self._calculate_position_pnl(
                position, entry_idx, exit_idx, exit_data["base_price"], exit_data["quote_price"]
            )

            # 記錄交易
            trade = {
                "entry_time": self.price_data.index[entry_idx],
                "exit_time": self.price_data.index[exit_idx],
                "position": position,
                "entry_base_price": entry_data["base_price"],
                "entry_quote_price": entry_data["quote_price"],
                "exit_base_price": exit_data["base_price"],
                "exit_quote_price": exit_data["quote_price"],
                "entry_zscore": entry_data["zscore"],
                "exit_zscore": exit_data["zscore"],
                "pnl": pnl,
                "exit_reason": exit_reason,
                "hold_bars": exit_idx - entry_idx,
            }

            self.trades.append(trade)
            return pnl

        except Exception as e:
            logger.error(f"執行出場失敗: {e}")
            return 0.0

    def _calculate_performance_metrics(self) -> Dict:
        """計算績效指標"""
        try:
            if not self.trades:
                return {}

            # 基本統計
            total_trades = len(self.trades)
            winning_trades = sum(1 for trade in self.trades if trade["pnl"] > 0)
            losing_trades = total_trades - winning_trades

            total_pnl = sum(trade["pnl"] for trade in self.trades)
            win_rate = winning_trades / total_trades if total_trades > 0 else 0

            # 盈虧比
            avg_win = (
                np.mean([trade["pnl"] for trade in self.trades if trade["pnl"] > 0])
                if winning_trades > 0
                else 0
            )
            avg_loss = (
                np.mean([abs(trade["pnl"]) for trade in self.trades if trade["pnl"] < 0])
                if losing_trades > 0
                else 1
            )
            profit_loss_ratio = avg_win / avg_loss if avg_loss > 0 else 0

            # 權益曲線分析
            equity_df = pd.DataFrame(self.equity_curve)
            equity_df.set_index("timestamp", inplace=True)

            # 總回報率
            initial_value = self.initial_capital
            final_value = equity_df["portfolio_value"].iloc[-1]
            total_return = (final_value - initial_value) / initial_value

            # 最大回撤
            equity_df["peak"] = equity_df["portfolio_value"].cummax()
            equity_df["drawdown"] = (equity_df["portfolio_value"] - equity_df["peak"]) / equity_df[
                "peak"
            ]
            max_drawdown = equity_df["drawdown"].min()

            # 夏普比率（假設無風險利率為0）
            equity_df["returns"] = equity_df["portfolio_value"].pct_change()
            sharpe_ratio = (
                equity_df["returns"].mean() / equity_df["returns"].std() * np.sqrt(252)
                if equity_df["returns"].std() > 0
                else 0
            )

            # 平均持倉時間
            avg_hold_time = np.mean([trade["hold_bars"] for trade in self.trades])

            metrics = {
                "total_return": total_return,
                "total_trades": total_trades,
                "winning_trades": winning_trades,
                "losing_trades": losing_trades,
                "win_rate": win_rate,
                "profit_loss_ratio": profit_loss_ratio,
                "total_pnl": total_pnl,
                "max_drawdown": abs(max_drawdown),
                "sharpe_ratio": sharpe_ratio,
                "avg_hold_time": avg_hold_time,
                "final_portfolio_value": final_value,
            }

            return metrics

        except Exception as e:
            logger.error(f"計算績效指標失敗: {e}")
            return {}

    def generate_report(self, save_path: str = None) -> str:
        """生成回測報告"""
        try:
            if not self.performance_metrics:
                return "請先執行回測"

            report = """
=== 配對交易回測報告 ===
生成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

策略參數:
- 交易對: {self.config['trading_pair']}
- 時間框架: {self.config['timeframe']}
- 回看週期: {self.lookback_period}
- 進場門檻: {self.entry_threshold_high} / {self.entry_threshold_low}
- 確認門檻: {self.confirmation_threshold_high} / {self.confirmation_threshold_low}
- 冷卻期: {self.cooldown_period}
- 止損比例: {self.stop_loss_pct:.2%}

績效指標:
- 總回報率: {self.performance_metrics['total_return']:.2%}
- 夏普比率: {self.performance_metrics['sharpe_ratio']:.4f}
- 最大回撤: {self.performance_metrics['max_drawdown']:.2%}
- 勝率: {self.performance_metrics['win_rate']:.2%}
- 盈虧比: {self.performance_metrics['profit_loss_ratio']:.2f}
- 總交易次數: {self.performance_metrics['total_trades']}
- 平均持倉時間: {self.performance_metrics['avg_hold_time']:.1f} 根K線
- 總盈虧: ${self.performance_metrics['total_pnl']:.2f}
- 最終資產: ${self.performance_metrics['final_portfolio_value']:.2f}
"""

            if save_path:
                with open(save_path, "w", encoding="utf-8") as f:
                    f.write(report)
                logger.info(f"回測報告已保存: {save_path}")

            return report

        except Exception as e:
            logger.error(f"生成回測報告失敗: {e}")
            return ""

    def plot_results(self, save_path: str = None):
        """繪製回測結果圖表"""
        try:
            if not self.equity_curve:
                logger.warning("無權益曲線數據可繪製")
                return

            equity_df = pd.DataFrame(self.equity_curve)
            equity_df.set_index("timestamp", inplace=True)

            fig, axes = plt.subplots(3, 1, figsize=(15, 12))

            # 權益曲線
            axes[0].plot(equity_df.index, equity_df["portfolio_value"])
            axes[0].set_title("權益曲線")
            axes[0].set_ylabel("資產價值 ($)")
            axes[0].grid(True)

            # Z-score 和交易點
            axes[1].plot(equity_df.index, equity_df["zscore"], label="Z-score", alpha=0.7)
            axes[1].axhline(
                y=self.entry_threshold_high, color="r", linestyle="--", alpha=0.5, label="進場門檻"
            )
            axes[1].axhline(y=self.entry_threshold_low, color="r", linestyle="--", alpha=0.5)
            axes[1].axhline(
                y=self.confirmation_threshold_high,
                color="orange",
                linestyle="--",
                alpha=0.5,
                label="確認門檻",
            )
            axes[1].axhline(
                y=self.confirmation_threshold_low, color="orange", linestyle="--", alpha=0.5
            )
            axes[1].axhline(y=0, color="black", linestyle="-", alpha=0.3)

            # 標記交易點
            for trade in self.trades:
                entry_time = trade["entry_time"]
                exit_time = trade["exit_time"]
                color = "green" if trade["pnl"] > 0 else "red"
                axes[1].scatter(entry_time, trade["entry_zscore"], color=color, marker="^", s=50)
                axes[1].scatter(exit_time, trade["exit_zscore"], color=color, marker="v", s=50)

            axes[1].set_title("Z-score 和交易信號")
            axes[1].set_ylabel("Z-score")
            axes[1].legend()
            axes[1].grid(True)

            # 回撤
            equity_df["peak"] = equity_df["portfolio_value"].cummax()
            equity_df["drawdown"] = (equity_df["portfolio_value"] - equity_df["peak"]) / equity_df[
                "peak"
            ]
            axes[2].fill_between(equity_df.index, equity_df["drawdown"], 0, alpha=0.3, color="red")
            axes[2].set_title("回撤")
            axes[2].set_ylabel("回撤 (%)")
            axes[2].set_xlabel("時間")
            axes[2].grid(True)

            plt.tight_layout()

            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches="tight")
                logger.info(f"回測圖表已保存: {save_path}")

            plt.show()

        except Exception as e:
            logger.error(f"繪製回測結果失敗: {e}")

    def save_results(self, results_dir: str = "backtest_results"):
        """保存回測結果"""
        try:
            from pathlib import Path

            results_path = Path(results_dir)
            results_path.mkdir(exist_ok=True)
            timestamp = datetime.now().strftime("%Y % m%d_ % H%M % S")

            # 保存交易記錄
            trades_file = results_path / f"trades_{timestamp}.json"
            with open(trades_file, "w", encoding="utf-8") as f:
                json.dump(self.trades, f, indent=2, default=str, ensure_ascii=False)

            # 保存權益曲線
            equity_file = results_path / f"equity_curve_{timestamp}.csv"
            equity_df = pd.DataFrame(self.equity_curve)
            equity_df.to_csv(equity_file, index=False)

            # 保存績效指標
            metrics_file = results_path / f"metrics_{timestamp}.json"
            with open(metrics_file, "w", encoding="utf-8") as f:
                json.dump(self.performance_metrics, f, indent=2, ensure_ascii=False)

            # 保存報告
            report_file = results_path / f"report_{timestamp}.txt"
            report = self.generate_report()
            with open(report_file, "w", encoding="utf-8") as f:
                f.write(report)

            # 保存圖表
            chart_file = results_path / f"charts_{timestamp}.png"
            self.plot_results(str(chart_file))

            logger.info(f"回測結果已保存到: {results_path}")

        except Exception as e:
            logger.error(f"保存回測結果失敗: {e}")
