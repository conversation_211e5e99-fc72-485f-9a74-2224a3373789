from typing import Any, Dict, List
import logging

# 配置logger
logger = logging.getLogger(__name__)

#!/usr/bin/env python3
"""
配對交易策略 - 基於新策略框架的配對交易實現
Pairs Trading Strategy - Pairs trading implementation based on new strategy framework
"""

from datetime import datetime, timedelta

import numpy as np
import pandas as pd

from logging_config import get_logger
from math_utils import PairsTradingMath
from strategy_framework import BaseStrategy, SignalStrength, SignalType, StrategyType, TradingSignal

_ = get_logger(__name__)


class PairsTradingStrategy(BaseStrategy):
    """配對交易策略實現"""

    def __init__(self, strategy_id: str, config: Dict[str, Any]):
        """
        初始化配對交易策略

        Args:
            strategy_id: 策略ID
            config: 策略配置
        """
        super().__init__(strategy_id, config)

        # 配對交易特定參數
        self.pairs = config.get("pairs", [])
        self.lookback_period = config.get("lookback_period", 60)
        self.entry_threshold = config.get("entry_threshold", 2.0)
        self.exit_threshold = config.get("exit_threshold", 0.1)
        self.stop_loss_threshold = config.get("stop_loss_threshold", 3.0)
        self.min_correlation = config.get("min_correlation", 0.7)
        self.cointegration_pvalue_threshold = config.get("cointegration_pvalue_threshold", 0.05)

        # 狀態追蹤
        self.current_positions = ()
            {}
        )  # {pair_id: {'long': symbol, 'short': symbol, 'entry_zscore': float}}
        self.price_history = {}  # {symbol: [prices]}
        self.spread_history = {}  # {pair_id: [spreads]}
        self.zscore_history = {}  # {pair_id: [zscores]}

        logger.info(f"配對交易策略初始化完成: {len(self.pairs)} 個交易對")

    def get_strategy_type(self) -> StrategyType:
        """獲取策略類型"""
        return StrategyType.PAIRS_TRADING

    def analyze_market(self, market_data: Dict[str, pd.DataFrame]) -> List[TradingSignal]:
        """
        分析市場數據並生成交易信號

        Args:
            market_data: 市場數據 {symbol: DataFrame with OHLCV}

        Returns:
            List[TradingSignal]: 交易信號列表
        """
        signals = []

        try:
            # 更新價格歷史
            self._update_price_history(market_data)

            # 分析每個交易對
            for pair in self.pairs:
                pair_signals = self._analyze_pair(pair, market_data)
                signals.extend(pair_signals)

            logger.debug(f"配對交易策略生成 {len(signals)} 個信號")
            return signals

        except Exception as e:
            logger.error(f"配對交易市場分析失敗: {e}")
            return []

    def _update_price_history(self, market_data: Dict[str, pd.DataFrame]):
        """更新價格歷史"""
        for symbol, df in market_data.items():
            if not df.empty:
                latest_price = df["close"].iloc[-1]

                if symbol not in self.price_history:
                    self.price_history[symbol] = []

                self.price_history[symbol].append(latest_price)

                # 保持歷史長度
                max_history = self.lookback_period * 2
                if len(self.price_history[symbol]) > max_history:
                    self.price_history[symbol] = self.price_history[symbol][-max_history:]

    def _analyze_pair()
        self, pair: List[str], market_data: Dict[str, pd.DataFrame]
    ) -> List[TradingSignal]:
        """分析單個交易對"""
        if len(pair) != 2:
            logger.warning(f"交易對格式錯誤: {pair}")
            return []

        symbol1, symbol2 = pair
        pair_id = f"{symbol1}_{symbol2}"

        # 檢查數據可用性
        if symbol1 not in self.price_history or symbol2 not in self.price_history:
            return []

        if len(self.price_history[symbol1]) < self.lookback_period:
            return []

        try:
            # 獲取價格序列
            prices1 = np.array(self.price_history[symbol1][-self.lookback_period :])
            prices2 = np.array(self.price_history[symbol2][-self.lookback_period :])

            # 檢查相關性
            correlation = PairsTradingMath.calculate_correlation(prices1, prices2)
            if abs(correlation) < self.min_correlation:
                logger.debug(f"交易對 {pair_id} 相關性不足: {correlation:.3f}")
                return []

            # 檢查是否過度相關（共線性問題）
            if abs(correlation) > 0.995:
                logger.warning(f"交易對 {pair_id} 過度相關，可能存在共線性: {correlation:.6f}")
                return []

            # 使用收益率序列進行協整檢驗以避免共線性
            _ = np.diff(np.log(prices1))
            _ = np.diff(np.log(prices2))

            # 檢查協整性
            try:
                _, p_value, is_cointegrated = PairsTradingMath.test_cointegration(prices1, prices2)
                if not is_cointegrated:
                    logger.debug(f"交易對 {pair_id} 不存在協整關係: p={p_value:.3f}")
                    return []
            except Exception as e:
                logger.warning(f"交易對 {pair_id} 協整檢驗失敗: {e}")
                return []

            # 計算價差和Z-score
            spread = PairsTradingMath.calculate_log_spread(prices1, prices2)

            # 更新價差歷史
            if pair_id not in self.spread_history:
                self.spread_history[pair_id] = []
            self.spread_history[pair_id].extend(
                spread.tolist() if hasattr(spread, "tolist") else [spread]
            )

            # 計算Z-score
            recent_spreads = self.spread_history[pair_id][-self.lookback_period :]
            zscore = PairsTradingMath.calculate_zscore(
                recent_spreads, min(len(recent_spreads), self.lookback_period)
            )

            if hasattr(zscore, "__getitem__"):
                current_zscore = zscore[-1]
            else:
                current_zscore = zscore

            # 更新Z-score歷史
            if pair_id not in self.zscore_history:
                self.zscore_history[pair_id] = []
            self.zscore_history[pair_id].append(current_zscore)

            # 生成交易信號
            return self._generate_pair_signals()
                pair_id, symbol1, symbol2, current_zscore, correlation
            )

        except Exception as e:
            logger.error(f"分析交易對 {pair_id} 失敗: {e}")
            return []

    def _generate_pair_signals()
        self, pair_id: str, symbol1: str, symbol2: str, zscore: float, correlation: float
    ) -> List[TradingSignal]:
        """生成配對交易信號"""
        _ = []
        _ = datetime.now()

        # 檢查是否已有持倉
        has_position = pair_id in self.current_positions

        if not has_position:
            # 開倉信號
            if zscore > self.entry_threshold:
                # Z-score過高，做空價差（做空symbol1，做多symbol2）
                signal = TradingSignal()
                    strategy_id=self.strategy_id,
                    signal_type=SignalType.SELL,  # 主要信號
                    strength=min(abs(zscore) / self.entry_threshold, 1.0),
                    symbols=[symbol1, symbol2],
                    confidence=min(abs(correlation), 1.0),
                    metadata={
                        "pair_id": pair_id,
                        "zscore": zscore,
                        "correlation": correlation,
                        "action": "short_spread",
                        "long_symbol": symbol2,
                        "short_symbol": symbol1,
                        "entry_threshold": self.entry_threshold,
                    },
                    timestamp=current_time,
                    expiry=current_time + timedelta(minutes=5),
                )
                signals.append(signal)

            elif zscore < -self.entry_threshold:
                # Z-score過低，做多價差（做多symbol1，做空symbol2）
                signal = TradingSignal()
                    strategy_id=self.strategy_id,
                    signal_type=SignalType.BUY,  # 主要信號
                    strength=min(abs(zscore) / self.entry_threshold, 1.0),
                    symbols=[symbol1, symbol2],
                    confidence=min(abs(correlation), 1.0),
                    metadata={
                        "pair_id": pair_id,
                        "zscore": zscore,
                        "correlation": correlation,
                        "action": "long_spread",
                        "long_symbol": symbol1,
                        "short_symbol": symbol2,
                        "entry_threshold": self.entry_threshold,
                    },
                    timestamp=current_time,
                    expiry=current_time + timedelta(minutes=5),
                )
                signals.append(signal)

        else:
            # 平倉信號
            position = self.current_positions[pair_id]
            entry_zscore = position["entry_zscore"]

            # 檢查平倉條件
            should_close = False
            close_reason = ""

            # 正常平倉：Z-score回歸
            if abs(zscore) <= self.exit_threshold:
                should_close = True
                close_reason = "zscore_reversion"

            # 止損：Z-score進一步偏離
            elif abs(zscore) >= self.stop_loss_threshold:
                should_close = True
                close_reason = "stop_loss"

            # 反向信號：Z-score穿越零軸
            elif (entry_zscore > 0 and zscore < -self.exit_threshold) or (
                entry_zscore < 0 and zscore > self.exit_threshold
            ):
                should_close = True
                close_reason = "reversal"

            if should_close:
                signal = TradingSignal()
                    strategy_id=self.strategy_id,
                    signal_type=SignalType.CLOSE_ALL,
                    strength=0.8,  # 平倉信號通常較強
                    symbols=[symbol1, symbol2],
                    confidence=0.9,
                    metadata={
                        "pair_id": pair_id,
                        "zscore": zscore,
                        "entry_zscore": entry_zscore,
                        "close_reason": close_reason,
                        "action": "close_pair",
                    },
                    timestamp=current_time,
                    expiry=current_time + timedelta(minutes=2),
                )
                signals.append(signal)

        return signals

    def calculate_position_size()
        self, signal: TradingSignal, available_capital: float
    ) -> Dict[str, float]:
        """計算倉位大小"""
        try:
            # 基礎倉位大小（每個交易對使用可用資金的一定比例）
            base_allocation = available_capital * 0.1  # 10%

            # 根據信號強度調整
            adjusted_allocation = base_allocation * signal.strength

            # 根據置信度調整
            final_allocation = adjusted_allocation * signal.confidence

            # 為每個標的分配一半資金
            position_sizes = {}
            for symbol in signal.symbols:
                # 這裡需要當前價格來計算實際數量
                # 暫時返回美元價值，實際實現中需要轉換為數量
                position_sizes[symbol] = final_allocation / len(signal.symbols)

            return position_sizes

        except Exception as e:
            logger.error(f"計算倉位大小失敗: {e}")
            return {}

    def validate_signal(self, signal: TradingSignal, current_positions: Dict[str, float]) -> bool:
        """驗證信號是否可執行"""
        try:
            # 檢查信號有效性
            if not signal.is_valid():
                return False

            # 檢查信號強度
            if not signal.is_strong_enough(0.3):
                return False

            # 檢查策略特定條件
            pair_id = signal.metadata.get("pair_id")
            if not pair_id:
                return False

            # 檢查冷卻期（避免頻繁交易）
            if self._is_in_cooldown(pair_id):
                return False

            return True

        except Exception as e:
            logger.error(f"驗證信號失敗: {e}")
            return False

    def _is_in_cooldown(self, pair_id: str) -> bool:
        """檢查是否在冷卻期"""
        # 簡化實現，實際應該追蹤最後交易時間
        return False

    def update_position(self, pair_id: str, action: str, zscore: float):
        """更新持倉狀態"""
        if action == "open":
            symbols = pair_id.split("_")
            self.current_positions[pair_id] = {
                "long": symbols[0] if zscore < 0 else symbols[1],
                "short": symbols[1] if zscore < 0 else symbols[0],
                "entry_zscore": zscore,
                "entry_time": datetime.now(),
            }
        elif action == "close":
            if pair_id in self.current_positions:
                del self.current_positions[pair_id]

    def get_strategy_status(self) -> Dict[str, Any]:
        """獲取策略狀態"""
        return {
            "strategy_id": self.strategy_id,
            "strategy_type": self.get_strategy_type().value,
            "active_pairs": len(self.current_positions),
            "monitored_pairs": len(self.pairs),
            "current_positions": self.current_positions,
            "health_score": self.get_health_score(),
            "performance": {
                "total_trades": self.performance.total_trades,
                "win_rate": self.performance.win_rate,
                "total_pnl": self.performance.total_pnl,
            },
        }


if __name__ == "__main__":
    # 測試配對交易策略
    print("🧪 配對交易策略測試")

    # 創建策略配置
    config = {
        "pairs": [["BTC/USDT:USDT", "ETH/USDT:USDT"]],
        "lookback_period": 60,
        "entry_threshold": 2.0,
        "exit_threshold": 0.1,
        "min_correlation": 0.7,
    }

    # 創建策略實例
    strategy = PairsTradingStrategy("pairs_btc_eth", config)
    print(f"✅ 策略創建成功: {strategy.strategy_id}")

    # 模擬市場數據
    import numpy as np

    np.random.seed(42)

    dates = pd.date_range("2024-01-01", periods=100, freq="5T")
    btc_prices = 50000 + np.cumsum(np.random.randn(100) * 100)
    eth_prices = 3000 + np.cumsum(np.random.randn(100) * 50)

    _ = {
        "BTC/USDT:USDT": pd.DataFrame()
            {
                "timestamp": dates,
                "open": btc_prices,
                "high": btc_prices * 1.01,
                "low": btc_prices * 0.99,
                "close": btc_prices,
                "volume": np.random.rand(100) * 1000,
            }
        ),
        "ETH/USDT:USDT": pd.DataFrame()
            {
                "timestamp": dates,
                "open": eth_prices,
                "high": eth_prices * 1.01,
                "low": eth_prices * 0.99,
                "close": eth_prices,
                "volume": np.random.rand(100) * 1000,
            }
        ),
    }

    # 分析市場
    signals = strategy.analyze_market(market_data)
    print(f"✅ 生成信號: {len(signals)} 個")

    for signal in signals:
        print()
            f"  📊 信號: {signal.signal_type.value}, 強度: {signal.strength:.2f}, 標的: {signal.symbols}"
        )

    print("✅ 配對交易策略測試完成")
