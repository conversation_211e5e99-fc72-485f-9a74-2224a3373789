#!/usr/bin/env python3
"""
智能槓桿演示 - 展示50倍槓桿但限制實際風險的效果
Smart Leverage Demo - Demonstrate 50x leverage with controlled risk
"""

import sys
from typing import Dict, Any

sys.path.append('.')

from smart_capital_management import SmartCapitalManager, RiskLevel
from logging_config import setup_logging, get_logger

setup_logging()
logger = get_logger(__name__)


def demo_smart_leverage():
    """演示智能槓桿系統"""
    print("🎯 智能槓桿系統演示")
    print("=" * 60)
    print("策略: 50倍槓桿設定，但限制實際風險敞口到5-10倍等效槓桿")
    print("=" * 60)
    
    # 創建不同風險等級的管理器
    scenarios = [
        ("保守型", RiskLevel.CONSERVATIVE, 10000),
        ("穩健型", RiskLevel.MODERATE, 10000),
        ("積極型", RiskLevel.AGGRESSIVE, 10000)
    ]
    
    for scenario_name, risk_level, capital in scenarios:
        print(f"\n📊 {scenario_name}策略 (總資金: ${capital:,})")
        print("-" * 40)
        
        # 創建管理器
        manager = SmartCapitalManager(
            total_capital=capital,
            risk_level=risk_level,
            exchange_leverage=50
        )
        
        # 獲取風險指標
        metrics = manager.get_risk_metrics()
        
        print(f"🔧 配置參數:")
        print(f"  交易所槓桿: {metrics['exchange_leverage']}x")
        print(f"  有效槓桿目標: {metrics['effective_leverage_target']}x")
        print(f"  最大資金利用率: {metrics['max_utilization_rate']:.1%}")
        print(f"  最大單筆倉位: {metrics['max_single_position_pct']:.1f}%")
        
        print(f"\n💰 資金配置:")
        print(f"  總資金: ${metrics['total_capital']:,}")
        print(f"  最大可用保證金: ${metrics['max_margin_used']:,}")
        print(f"  最大倉位價值: ${metrics['max_position_value']:,}")
        print(f"  風險緩衝: ${metrics['risk_buffer']:,}")
        
        # 演示配對交易
        print(f"\n🎯 配對交易示例:")
        pair_result = manager.calculate_pair_position(
            'BTC/USDT:USDT', 50000,
            'ETH/USDT:USDT', 3000,
            signal_strength=0.8
        )
        
        print(f"  BTC多頭: ${pair_result['long_position']['position_value']:,.0f} "
              f"(保證金: ${pair_result['long_position']['required_margin']:.0f})")
        print(f"  ETH空頭: ${pair_result['short_position']['position_value']:,.0f} "
              f"(保證金: ${pair_result['short_position']['required_margin']:.0f})")
        print(f"  總風險: {pair_result['total_risk_percentage']:.2f}% of capital")
        print(f"  實際槓桿: {pair_result['long_position']['effective_leverage_used']:.1f}x")
        print(f"  風險狀態: {'✅ 安全' if pair_result['within_limits'] else '❌ 超限'}")


def compare_traditional_vs_smart():
    """對比傳統槓桿 vs 智能槓桿"""
    print(f"\n🔄 傳統槓桿 vs 智能槓桿對比")
    print("=" * 60)
    
    capital = 10000
    position_value = 5000  # $5000倉位
    
    print(f"假設場景: ${capital:,}資金，開${position_value:,}倉位")
    print("-" * 40)
    
    # 傳統50倍槓桿
    traditional_margin = position_value / 50
    traditional_risk = (traditional_margin / capital) * 100
    traditional_liquidation = 1/50 * 100  # 2%
    
    print(f"📈 傳統50倍槓桿:")
    print(f"  所需保證金: ${traditional_margin:.0f}")
    print(f"  資金風險: {traditional_risk:.2f}%")
    print(f"  強制平倉距離: {traditional_liquidation:.1f}%")
    print(f"  風險等級: 🚨 極高")
    
    # 智能槓桿系統
    manager = SmartCapitalManager(capital, RiskLevel.MODERATE, 50)
    smart_result = manager.calculate_position_size('BTC/USDT:USDT', 50000, 1.0)
    
    print(f"\n🧠 智能槓桿系統:")
    print(f"  建議倉位: ${smart_result['position_value']:.0f}")
    print(f"  所需保證金: ${smart_result['required_margin']:.0f}")
    print(f"  資金風險: {smart_result['risk_percentage']:.2f}%")
    print(f"  實際槓桿: {smart_result['effective_leverage_used']:.1f}x")
    print(f"  風險等級: ✅ 可控")
    
    # 風險對比
    print(f"\n⚖️ 風險對比:")
    risk_reduction = (traditional_risk - smart_result['risk_percentage']) / traditional_risk * 100
    print(f"  風險降低: {risk_reduction:.1f}%")
    print(f"  槓桿降低: {50 / smart_result['effective_leverage_used']:.1f}倍")
    print(f"  安全提升: 🚨 極高風險 → ✅ 可控風險")


def demo_position_scaling():
    """演示倉位縮放"""
    print(f"\n📏 智能倉位縮放演示")
    print("=" * 60)
    
    manager = SmartCapitalManager(10000, RiskLevel.MODERATE, 50)
    
    # 不同信號強度的倉位計算
    signal_strengths = [0.5, 0.7, 0.9, 1.0]
    
    print(f"BTC價格: $50,000")
    print(f"信號強度對倉位大小的影響:")
    print("-" * 40)
    
    for strength in signal_strengths:
        result = manager.calculate_position_size('BTC/USDT:USDT', 50000, strength)
        
        print(f"信號強度 {strength:.1f}: "
              f"倉位 ${result['position_value']:,.0f}, "
              f"風險 {result['risk_percentage']:.2f}%, "
              f"槓桿 {result['effective_leverage_used']:.1f}x")


def demo_risk_scenarios():
    """演示風險場景"""
    print(f"\n⚠️ 風險場景分析")
    print("=" * 60)
    
    manager = SmartCapitalManager(10000, RiskLevel.MODERATE, 50)
    
    scenarios = [
        ("正常市場", 0.02, "穩定交易環境"),
        ("高波動", 0.05, "市場波動加劇"),
        ("極端波動", 0.10, "黑天鵝事件"),
    ]
    
    for scenario_name, volatility, description in scenarios:
        print(f"\n📊 {scenario_name} ({description}):")
        
        # 根據波動性調整倉位
        volatility_factor = max(0.5, 1 - volatility * 5)  # 波動越高，倉位越小
        
        result = manager.calculate_position_size(
            'BTC/USDT:USDT', 50000, volatility_factor
        )
        
        print(f"  建議倉位: ${result['position_value']:,.0f}")
        print(f"  風險敞口: {result['risk_percentage']:.2f}%")
        print(f"  最大損失: ${result['max_loss_potential']:,.0f}")
        print(f"  調整因子: {volatility_factor:.2f}")


def main():
    """主函數"""
    try:
        # 基本演示
        demo_smart_leverage()
        
        # 對比演示
        compare_traditional_vs_smart()
        
        # 倉位縮放演示
        demo_position_scaling()
        
        # 風險場景演示
        demo_risk_scenarios()
        
        print(f"\n" + "=" * 60)
        print("🎉 智能槓桿系統演示完成")
        print("=" * 60)
        print("✅ 優勢總結:")
        print("  • 50倍槓桿提高資金效率")
        print("  • 智能限制實際風險敞口到5-10倍")
        print("  • 動態倉位調整")
        print("  • 多重風險控制")
        print("  • 適應不同市場環境")
        
        print(f"\n💡 使用建議:")
        print("  1. 從保守型開始測試")
        print("  2. 根據經驗調整風險等級")
        print("  3. 密切監控實際槓桿使用")
        print("  4. 定期檢查風險指標")
        
    except Exception as e:
        logger.error(f"演示失敗: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
