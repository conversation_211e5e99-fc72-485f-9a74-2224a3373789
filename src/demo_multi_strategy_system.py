#!/usr/bin/env python3
"""
多策略系統演示 - 展示完整的多策略交易系統
Multi-Strategy System Demo - Demonstrate complete multi-strategy trading system
"""

import asyncio
import time
from datetime import datetime
from typing import Dict, Any

from strategy_framework import StrategyType
from pairs_trading_strategy import PairsTradingStrategy
from trend_following_strategy import TrendFollowingStrategy
from multi_strategy_engine import MultiStrategyEngine, EngineConfig
from exchange_factory import ExchangeFactory
from smart_capital_management import SmartCapitalManager, RiskLevel
from logging_config import setup_logging, get_logger

setup_logging()
logger = get_logger(__name__)


class MultiStrategyDemo:
    """多策略系統演示"""
    
    def __init__(self):
        self.engine = None
        self.demo_duration = 60  # 演示時長（秒）
    
    def setup_system(self):
        """設置系統"""
        print("🔧 設置多策略交易系統...")
        
        # 1. 創建交易所網關（模擬模式）
        exchange_gateway = ExchangeFactory.create_exchange_gateway({}, mock_mode=True)
        print("  ✅ 交易所網關創建完成")
        
        # 2. 創建資金管理器
        capital_manager = SmartCapitalManager(
            total_capital=100000,  # $100,000
            risk_level=RiskLevel.MODERATE,
            exchange_leverage=50
        )
        print("  ✅ 資金管理器創建完成")
        
        # 3. 創建引擎配置
        engine_config = EngineConfig(
            max_concurrent_strategies=5,
            signal_aggregation_window=10,
            position_update_interval=30,
            health_check_interval=60,
            max_daily_trades=100
        )
        print("  ✅ 引擎配置創建完成")
        
        # 4. 創建多策略引擎
        self.engine = MultiStrategyEngine(
            exchange_gateway=exchange_gateway,
            capital_manager=capital_manager,
            config=engine_config
        )
        print("  ✅ 多策略引擎創建完成")
        
        return True
    
    def add_strategies(self):
        """添加策略"""
        print("\n📊 添加交易策略...")
        
        strategies = []
        
        # 1. 配對交易策略 - BTC/ETH
        pairs_config_1 = {
            'pairs': [['BTC/USDT:USDT', 'ETH/USDT:USDT']],
            'lookback_period': 60,
            'entry_threshold': 2.0,
            'exit_threshold': 0.1,
            'min_correlation': 0.7
        }
        pairs_strategy_1 = PairsTradingStrategy("pairs_btc_eth", pairs_config_1)
        strategies.append(pairs_strategy_1)
        
        # 2. 配對交易策略 - ETH/AVAX
        pairs_config_2 = {
            'pairs': [['ETH/USDT:USDT', 'AVAX/USDT:USDT']],
            'lookback_period': 60,
            'entry_threshold': 2.5,
            'exit_threshold': 0.1,
            'min_correlation': 0.6
        }
        pairs_strategy_2 = PairsTradingStrategy("pairs_eth_avax", pairs_config_2)
        strategies.append(pairs_strategy_2)
        
        # 3. 趨勢跟蹤策略 - BTC
        trend_config_1 = {
            'symbols': ['BTC/USDT:USDT'],
            'fast_ma_period': 10,
            'slow_ma_period': 30,
            'rsi_period': 14,
            'min_trend_strength': 0.01
        }
        trend_strategy_1 = TrendFollowingStrategy("trend_btc", trend_config_1)
        strategies.append(trend_strategy_1)
        
        # 4. 趨勢跟蹤策略 - ETH
        trend_config_2 = {
            'symbols': ['ETH/USDT:USDT'],
            'fast_ma_period': 8,
            'slow_ma_period': 25,
            'rsi_period': 14,
            'min_trend_strength': 0.015
        }
        trend_strategy_2 = TrendFollowingStrategy("trend_eth", trend_config_2)
        strategies.append(trend_strategy_2)
        
        # 5. 趨勢跟蹤策略 - AVAX
        trend_config_3 = {
            'symbols': ['AVAX/USDT:USDT'],
            'fast_ma_period': 12,
            'slow_ma_period': 35,
            'rsi_period': 14,
            'min_trend_strength': 0.02
        }
        trend_strategy_3 = TrendFollowingStrategy("trend_avax", trend_config_3)
        strategies.append(trend_strategy_3)
        
        # 添加所有策略到引擎
        for strategy in strategies:
            self.engine.add_strategy(strategy)
            print(f"  ✅ {strategy.get_strategy_type().value} 策略: {strategy.strategy_id}")
        
        print(f"\n📈 總計添加 {len(strategies)} 個策略")
        return strategies
    
    def display_system_overview(self):
        """顯示系統概覽"""
        print("\n" + "="*80)
        print("🎯 多策略交易系統概覽")
        print("="*80)
        
        status = self.engine.get_engine_status()
        
        print(f"💰 總資金: $100,000")
        print(f"🔧 活躍策略: {status['active_strategies']} / {status['total_strategies']}")
        print(f"📊 當前持倉: {status['current_positions']}")
        print(f"📈 今日交易: {status['daily_trades']}")
        
        print(f"\n📋 策略詳情:")
        for strategy_id, perf in status['strategy_performance'].items():
            print(f"  🎯 {strategy_id}:")
            print(f"    類型: {perf['type']}")
            print(f"    狀態: {'🟢 活躍' if perf['is_active'] else '🔴 暫停'}")
            print(f"    交易次數: {perf['total_trades']}")
            print(f"    勝率: {perf['win_rate']:.1%}")
            print(f"    總盈虧: ${perf['total_pnl']:.2f}")
            print(f"    健康分數: {perf['health_score']:.2f}")
    
    async def run_demo(self):
        """運行演示"""
        print(f"\n🚀 啟動多策略系統演示 (運行 {self.demo_duration} 秒)...")
        
        # 啟動引擎
        self.engine.start()
        
        start_time = time.time()
        last_status_time = start_time
        
        try:
            while time.time() - start_time < self.demo_duration:
                current_time = time.time()
                
                # 每10秒顯示一次狀態
                if current_time - last_status_time >= 10:
                    elapsed = int(current_time - start_time)
                    remaining = self.demo_duration - elapsed
                    
                    print(f"\n⏰ 運行時間: {elapsed}s / {self.demo_duration}s (剩餘: {remaining}s)")
                    
                    # 獲取並顯示實時狀態
                    status = self.engine.get_engine_status()
                    print(f"📊 實時狀態: {status['daily_trades']} 筆交易, "
                          f"{status['active_strategies']} 個活躍策略")
                    
                    # 顯示策略績效變化
                    for strategy_id, perf in status['strategy_performance'].items():
                        if perf['total_trades'] > 0:
                            print(f"  📈 {strategy_id}: "
                                  f"{perf['total_trades']} 筆交易, "
                                  f"勝率 {perf['win_rate']:.1%}, "
                                  f"PnL ${perf['total_pnl']:.2f}")
                    
                    last_status_time = current_time
                
                await asyncio.sleep(1)
        
        finally:
            # 停止引擎
            self.engine.stop()
            print("\n🛑 多策略引擎已停止")
    
    def display_final_results(self):
        """顯示最終結果"""
        print("\n" + "="*80)
        print("📊 演示結果總結")
        print("="*80)
        
        status = self.engine.get_engine_status()
        
        print(f"🎯 系統表現:")
        print(f"  總交易次數: {status['daily_trades']}")
        print(f"  活躍策略數: {status['active_strategies']}")
        
        total_pnl = 0
        total_trades = 0
        winning_strategies = 0
        
        print(f"\n📈 策略表現詳情:")
        for strategy_id, perf in status['strategy_performance'].items():
            total_pnl += perf['total_pnl']
            total_trades += perf['total_trades']
            
            if perf['total_pnl'] > 0:
                winning_strategies += 1
            
            status_icon = "🟢" if perf['is_active'] else "🔴"
            pnl_icon = "📈" if perf['total_pnl'] > 0 else "📉" if perf['total_pnl'] < 0 else "➖"
            
            print(f"  {status_icon} {strategy_id} ({perf['type']}):")
            print(f"    {pnl_icon} PnL: ${perf['total_pnl']:.2f}")
            print(f"    📊 交易: {perf['total_trades']} 筆")
            print(f"    🎯 勝率: {perf['win_rate']:.1%}")
            print(f"    ❤️ 健康: {perf['health_score']:.2f}")
        
        print(f"\n🏆 總體統計:")
        print(f"  💰 總盈虧: ${total_pnl:.2f}")
        print(f"  📊 總交易: {total_trades} 筆")
        print(f"  🎯 盈利策略: {winning_strategies} / {len(status['strategy_performance'])}")
        
        if total_trades > 0:
            avg_pnl = total_pnl / total_trades
            print(f"  📈 平均每筆: ${avg_pnl:.2f}")
        
        # 評價系統表現
        if total_pnl > 0:
            print(f"\n🎉 系統表現: 盈利 (${total_pnl:.2f})")
        elif total_pnl < 0:
            print(f"\n⚠️ 系統表現: 虧損 (${total_pnl:.2f})")
        else:
            print(f"\n➖ 系統表現: 持平")


async def main():
    """主函數"""
    print("🎯 多策略交易系統演示")
    print("="*80)
    
    demo = MultiStrategyDemo()
    
    try:
        # 1. 設置系統
        if not demo.setup_system():
            print("❌ 系統設置失敗")
            return
        
        # 2. 添加策略
        strategies = demo.add_strategies()
        
        # 3. 顯示系統概覽
        demo.display_system_overview()
        
        # 4. 運行演示
        await demo.run_demo()
        
        # 5. 顯示最終結果
        demo.display_final_results()
        
        print(f"\n🎉 多策略系統演示完成！")
        print("="*80)
        
    except KeyboardInterrupt:
        print("\n⚠️ 演示被用戶中斷")
    except Exception as e:
        print(f"\n❌ 演示過程中發生錯誤: {e}")
        logger.error(f"演示失敗: {e}")


if __name__ == "__main__":
    asyncio.run(main())
