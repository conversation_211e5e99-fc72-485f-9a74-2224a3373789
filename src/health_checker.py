#!/usr/bin/env python3
"""
系統健康檢查器 - 全面的系統健康狀態檢查
System Health Checker - Comprehensive system health status check
"""

import asyncio
import psutil
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List
import json

from logging_config import get_logger

logger = get_logger(__name__)


class SystemHealthChecker:
    """系統健康檢查器"""
    
    def __init__(self):
        self.start_time = datetime.now()
        self.checks_performed = 0
        self.failed_checks = 0
        
    async def comprehensive_check(self) -> Dict[str, Any]:
        """執行全面健康檢查"""
        logger.info("開始執行全面系統健康檢查...")
        
        health_results = {
            'timestamp': datetime.now().isoformat(),
            'overall_healthy': True,
            'checks': {},
            'summary': {
                'total_checks': 0,
                'passed_checks': 0,
                'failed_checks': 0,
                'warnings': []
            }
        }
        
        # 執行各項檢查
        checks = [
            ('system_resources', self._check_system_resources),
            ('disk_space', self._check_disk_space),
            ('memory_usage', self._check_memory_usage),
            ('cpu_usage', self._check_cpu_usage),
            ('file_permissions', self._check_file_permissions),
            ('dependencies', self._check_dependencies),
            ('configuration', self._check_configuration),
            ('network_connectivity', self._check_network_connectivity)
        ]
        
        for check_name, check_func in checks:
            try:
                logger.debug(f"執行檢查: {check_name}")
                check_result = await check_func()
                health_results['checks'][check_name] = check_result
                
                self.checks_performed += 1
                
                if not check_result.get('healthy', True):
                    health_results['overall_healthy'] = False
                    self.failed_checks += 1
                
                # 收集警告
                if check_result.get('warnings'):
                    health_results['summary']['warnings'].extend(check_result['warnings'])
                
            except Exception as e:
                logger.error(f"健康檢查 {check_name} 失敗: {e}")
                health_results['checks'][check_name] = {
                    'healthy': False,
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                }
                health_results['overall_healthy'] = False
                self.failed_checks += 1
        
        # 更新摘要
        health_results['summary']['total_checks'] = self.checks_performed
        health_results['summary']['passed_checks'] = self.checks_performed - self.failed_checks
        health_results['summary']['failed_checks'] = self.failed_checks
        
        # 計算健康分數
        if self.checks_performed > 0:
            health_score = (self.checks_performed - self.failed_checks) / self.checks_performed * 100
            health_results['health_score'] = round(health_score, 1)
        else:
            health_results['health_score'] = 0
        
        logger.info(f"健康檢查完成: {health_results['health_score']}% "
                   f"({health_results['summary']['passed_checks']}/{health_results['summary']['total_checks']})")
        
        return health_results
    
    async def _check_system_resources(self) -> Dict[str, Any]:
        """檢查系統資源"""
        try:
            # CPU信息
            cpu_count = psutil.cpu_count()
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 內存信息
            memory = psutil.virtual_memory()
            
            # 負載平均值（如果可用）
            try:
                load_avg = psutil.getloadavg()
            except:
                load_avg = [0, 0, 0]
            
            warnings = []
            healthy = True
            
            # 檢查CPU使用率
            if cpu_percent > 90:
                warnings.append(f"CPU使用率過高: {cpu_percent}%")
                healthy = False
            elif cpu_percent > 70:
                warnings.append(f"CPU使用率較高: {cpu_percent}%")
            
            # 檢查內存使用率
            if memory.percent > 90:
                warnings.append(f"內存使用率過高: {memory.percent}%")
                healthy = False
            elif memory.percent > 70:
                warnings.append(f"內存使用率較高: {memory.percent}%")
            
            return {
                'healthy': healthy,
                'warnings': warnings,
                'details': {
                    'cpu_count': cpu_count,
                    'cpu_percent': cpu_percent,
                    'memory_total_gb': round(memory.total / 1024**3, 2),
                    'memory_used_gb': round(memory.used / 1024**3, 2),
                    'memory_percent': memory.percent,
                    'load_average': load_avg
                },
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'healthy': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def _check_disk_space(self) -> Dict[str, Any]:
        """檢查磁盤空間"""
        try:
            disk_usage = psutil.disk_usage('.')
            
            free_space_gb = disk_usage.free / 1024**3
            total_space_gb = disk_usage.total / 1024**3
            used_percent = (disk_usage.used / disk_usage.total) * 100
            
            warnings = []
            healthy = True
            
            if used_percent > 95:
                warnings.append(f"磁盤空間嚴重不足: {used_percent:.1f}% 已使用")
                healthy = False
            elif used_percent > 85:
                warnings.append(f"磁盤空間不足: {used_percent:.1f}% 已使用")
            elif free_space_gb < 1:
                warnings.append(f"可用磁盤空間不足1GB: {free_space_gb:.2f}GB")
            
            return {
                'healthy': healthy,
                'warnings': warnings,
                'details': {
                    'total_gb': round(total_space_gb, 2),
                    'used_gb': round(disk_usage.used / 1024**3, 2),
                    'free_gb': round(free_space_gb, 2),
                    'used_percent': round(used_percent, 1)
                },
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'healthy': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def _check_memory_usage(self) -> Dict[str, Any]:
        """檢查內存使用情況"""
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            
            rss_mb = memory_info.rss / 1024 / 1024
            vms_mb = memory_info.vms / 1024 / 1024
            
            warnings = []
            healthy = True
            
            if rss_mb > 1000:  # 1GB
                warnings.append(f"進程內存使用過高: {rss_mb:.1f}MB")
                if rss_mb > 2000:  # 2GB
                    healthy = False
            
            return {
                'healthy': healthy,
                'warnings': warnings,
                'details': {
                    'rss_mb': round(rss_mb, 1),
                    'vms_mb': round(vms_mb, 1),
                    'percent': round(process.memory_percent(), 2)
                },
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'healthy': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def _check_cpu_usage(self) -> Dict[str, Any]:
        """檢查CPU使用情況"""
        try:
            process = psutil.Process()
            cpu_percent = process.cpu_percent()
            
            warnings = []
            healthy = True
            
            if cpu_percent > 80:
                warnings.append(f"進程CPU使用率過高: {cpu_percent}%")
                if cpu_percent > 95:
                    healthy = False
            
            return {
                'healthy': healthy,
                'warnings': warnings,
                'details': {
                    'cpu_percent': cpu_percent,
                    'num_threads': process.num_threads()
                },
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'healthy': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def _check_file_permissions(self) -> Dict[str, Any]:
        """檢查文件權限"""
        try:
            important_paths = [
                'config.json',
                '.env',
                'data/',
                'logs/'
            ]
            
            warnings = []
            healthy = True
            accessible_files = []
            
            for path_str in important_paths:
                path = Path(path_str)
                
                if path.exists():
                    try:
                        if path.is_file():
                            # 檢查文件讀寫權限
                            readable = path.is_file() and path.stat().st_mode & 0o444
                            writable = path.is_file() and path.stat().st_mode & 0o200
                            accessible_files.append({
                                'path': str(path),
                                'readable': bool(readable),
                                'writable': bool(writable)
                            })
                        elif path.is_dir():
                            # 檢查目錄權限
                            accessible_files.append({
                                'path': str(path),
                                'exists': True,
                                'is_directory': True
                            })
                    except Exception as e:
                        warnings.append(f"無法檢查 {path} 的權限: {e}")
                else:
                    if path_str in ['data/', 'logs/']:
                        # 嘗試創建目錄
                        try:
                            path.mkdir(exist_ok=True)
                            accessible_files.append({
                                'path': str(path),
                                'created': True
                            })
                        except Exception as e:
                            warnings.append(f"無法創建目錄 {path}: {e}")
                            healthy = False
            
            return {
                'healthy': healthy,
                'warnings': warnings,
                'details': {
                    'accessible_files': accessible_files
                },
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'healthy': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def _check_dependencies(self) -> Dict[str, Any]:
        """檢查依賴項"""
        try:
            required_modules = [
                'pandas',
                'numpy',
                'ccxt',
                'psutil',
                'flask',
                'redis'
            ]
            
            available_modules = []
            missing_modules = []
            
            for module in required_modules:
                try:
                    __import__(module)
                    available_modules.append(module)
                except ImportError:
                    missing_modules.append(module)
            
            warnings = []
            healthy = len(missing_modules) == 0
            
            if missing_modules:
                warnings.append(f"缺少依賴模組: {', '.join(missing_modules)}")
            
            return {
                'healthy': healthy,
                'warnings': warnings,
                'details': {
                    'available_modules': available_modules,
                    'missing_modules': missing_modules,
                    'total_required': len(required_modules)
                },
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'healthy': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def _check_configuration(self) -> Dict[str, Any]:
        """檢查配置"""
        try:
            from config_validation import ConfigValidator
            
            warnings = []
            healthy = True
            
            # 檢查配置文件
            config_file = Path('config.json')
            env_file = Path('.env')
            
            if not config_file.exists():
                warnings.append("config.json 文件不存在")
                healthy = False
            
            if not env_file.exists():
                warnings.append(".env 文件不存在")
                healthy = False
            
            # 嘗試載入配置
            config_valid = False
            try:
                config = ConfigValidator.load_and_validate_config()
                config_valid = True
            except Exception as e:
                warnings.append(f"配置驗證失敗: {e}")
                healthy = False
            
            return {
                'healthy': healthy,
                'warnings': warnings,
                'details': {
                    'config_file_exists': config_file.exists(),
                    'env_file_exists': env_file.exists(),
                    'config_valid': config_valid
                },
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'healthy': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def _check_network_connectivity(self) -> Dict[str, Any]:
        """檢查網絡連接"""
        try:
            import socket
            
            # 檢查基本網絡連接
            test_hosts = [
                ('*******', 53),  # Google DNS
                ('*******', 53),  # Cloudflare DNS
            ]
            
            connectivity_results = []
            warnings = []
            healthy = True
            
            for host, port in test_hosts:
                try:
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(5)
                    result = sock.connect_ex((host, port))
                    sock.close()
                    
                    is_connected = result == 0
                    connectivity_results.append({
                        'host': host,
                        'port': port,
                        'connected': is_connected
                    })
                    
                    if not is_connected:
                        warnings.append(f"無法連接到 {host}:{port}")
                        
                except Exception as e:
                    connectivity_results.append({
                        'host': host,
                        'port': port,
                        'connected': False,
                        'error': str(e)
                    })
                    warnings.append(f"網絡測試失敗 {host}:{port}: {e}")
            
            # 如果所有連接都失敗，標記為不健康
            connected_count = sum(1 for r in connectivity_results if r.get('connected', False))
            if connected_count == 0:
                healthy = False
            
            return {
                'healthy': healthy,
                'warnings': warnings,
                'details': {
                    'connectivity_results': connectivity_results,
                    'connected_hosts': connected_count,
                    'total_hosts': len(test_hosts)
                },
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'healthy': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }


if __name__ == "__main__":
    async def main():
        checker = SystemHealthChecker()
        results = await checker.comprehensive_check()
        print(json.dumps(results, indent=2, default=str))
    
    asyncio.run(main())
