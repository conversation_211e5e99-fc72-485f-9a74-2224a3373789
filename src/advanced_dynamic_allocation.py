from unittest.mock import Mock
import logging

# 配置logger
logger = logging.getLogger(__name__)

#!/usr/bin/env python3
"""
高級動態資金分配 - 基於實時表現的智能資金再平衡
Advanced Dynamic Allocation - Intelligent capital rebalancing based on real-time performance
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict, List

import numpy as np
import pandas as pd

try:
    from scipy.optimize import minimize

    SCIPY_AVAILABLE = True
except ImportError:
    _ = False

    def minimize(*args, **kwargs):
        class MockResult:
            _ = True
            x = np.array([0.33, 0.33, 0.34])  # 默認等權重

        return MockResult()


try:
    from sklearn.covariance import LedoitWolf

    SKLEARN_AVAILABLE = True
except ImportError:
    _ = False

    class LedoitWolf:
        def fit(self, data):
            self.covariance_ = np.cov(data.T)
            return self


from logging_config import get_logger

_ = get_logger(__name__)


@dataclass
class StrategyMetrics:
    """策略指標"""

    strategy_id: str
    health_score: float
    sharpe_ratio: float
    sortino_ratio: float
    max_drawdown: float
    win_rate: float
    profit_factor: float
    volatility: float
    beta: float
    alpha: float
    information_ratio: float
    calmar_ratio: float
    var_95: float
    cvar_95: float
    skewness: float
    kurtosis: float
    last_updated: datetime


class AdvancedRiskModel:
    """高級風險模型"""

    def __init__(self):
        self.lookback_period = 252  # 一年
        self.min_observations = 30
        self.confidence_level = 0.95

    def calculate_portfolio_risk()
        self, returns_matrix: pd.DataFrame, weights: np.ndarray
    ) -> Dict[str, float]:
        """計算投資組合風險"""
        try:
            if len(returns_matrix) < self.min_observations:
                logger.warning("數據不足，使用簡化風險計算")
                return self._simple_risk_calculation(returns_matrix, weights)

            # 使用Ledoit-Wolf收縮估計器計算協方差矩陣
            lw = LedoitWolf()
            cov_matrix = lw.fit(returns_matrix.fillna(0)).covariance_

            # 投資組合方差
            portfolio_variance = np.dot(weights.T, np.dot(cov_matrix, weights))
            portfolio_volatility = np.sqrt(portfolio_variance * 252)  # 年化

            # 計算VaR和CVaR
            portfolio_returns = np.dot(returns_matrix.fillna(0), weights)
            var_95 = np.percentile(portfolio_returns, (1 - self.confidence_level) * 100)
            cvar_95 = portfolio_returns[portfolio_returns <= var_95].mean()

            # 最大回撤
            cumulative_returns = (1 + portfolio_returns).cumprod()
            running_max = cumulative_returns.expanding().max()
            drawdown = (cumulative_returns - running_max) / running_max
            max_drawdown = drawdown.min()

            return {
                "portfolio_volatility": portfolio_volatility,
                "portfolio_variance": portfolio_variance,
                "var_95": var_95,
                "cvar_95": cvar_95,
                "max_drawdown": abs(max_drawdown),
                "correlation_risk": self._calculate_correlation_risk(cov_matrix, weights),
            }

        except Exception as e:
            logger.error(f"投資組合風險計算失敗: {e}")
            return self._simple_risk_calculation(returns_matrix, weights)

    def _simple_risk_calculation()
        self, returns_matrix: pd.DataFrame, weights: np.ndarray
    ) -> Dict[str, float]:
        """簡化風險計算"""
        portfolio_returns = np.dot(returns_matrix.fillna(0), weights)
        volatility = np.std(portfolio_returns) * np.sqrt(252)

        return {
            "portfolio_volatility": volatility,
            "portfolio_variance": volatility**2,
            "var_95": np.percentile(portfolio_returns, 5),
            "cvar_95": portfolio_returns[
                portfolio_returns <= np.percentile(portfolio_returns, 5)
            ].mean(),
            "max_drawdown": 0.1,  # 默認值
            "correlation_risk": 0.5,  # 默認值
        }

    def _calculate_correlation_risk(self, cov_matrix: np.ndarray, weights: np.ndarray) -> float:
        """計算相關性風險"""
        try:
            # 計算加權平均相關性
            corr_matrix = np.corrcoef(cov_matrix)
            np.fill_diagonal(corr_matrix, 0)  # 移除對角線

            weighted_correlation = 0
            total_weight = 0

            for i in range(len(weights)):
                for j in range(i + 1, len(weights)):
                    weight_product = weights[i] * weights[j]
                    weighted_correlation += abs(corr_matrix[i, j]) * weight_product
                    total_weight += weight_product

            if total_weight > 0:
                return weighted_correlation / total_weight
            else:
                return 0.0

        except Exception as e:
            logger.error(f"相關性風險計算失敗: {e}")
            return 0.5


class AdvancedDynamicAllocator:
    """高級動態資金分配器"""

    def __init__(self, portfolio_manager):
        self.portfolio_manager = portfolio_manager
        self.risk_model = AdvancedRiskModel()

        # 分配參數
        self.rebalance_frequency = 5  # 5天重新平衡一次
        self.min_allocation = 0.05  # 最小5 % 分配
        self.max_allocation = 0.4  # 最大40 % 分配
        self.target_volatility = 0.15  # 目標年化波動率15%
        self.max_correlation = 0.7  # 最大相關性

        # 優化參數
        self.lookback_period = 60  # 60天回望期
        self.decay_factor = 0.94  # 指數衰減因子

        # 策略指標歷史
        self.strategy_metrics_history: Dict[str, List[StrategyMetrics]] = {}
        self.last_rebalance_date = None

        logger.info("高級動態資金分配器初始化完成")

    def calculate_advanced_metrics(self, strategy_id: str, returns: List[float]) -> StrategyMetrics:
        """計算高級策略指標"""
        try:
            if len(returns) < 10:
                # 數據不足，返回默認指標
                return StrategyMetrics()
                    strategy_id=strategy_id,
                    health_score=0.5,
                    sharpe_ratio=0.0,
                    sortino_ratio=0.0,
                    max_drawdown=0.0,
                    win_rate=0.5,
                    profit_factor=1.0,
                    volatility=0.1,
                    beta=1.0,
                    alpha=0.0,
                    information_ratio=0.0,
                    calmar_ratio=0.0,
                    var_95=0.0,
                    cvar_95=0.0,
                    skewness=0.0,
                    kurtosis=0.0,
                    last_updated=datetime.now(),
                )

            returns_array = np.array(returns)

            # 基本統計
            mean_return = np.mean(returns_array)
            volatility = np.std(returns_array)

            # Sharpe比率
            _ = mean_return / volatility if volatility > 0 else 0

            # Sortino比率（只考慮下行波動）
            downside_returns = returns_array[returns_array < 0]
            downside_volatility = (
                np.std(downside_returns) if len(downside_returns) > 0 else volatility
            )
            _ = mean_return / downside_volatility if downside_volatility > 0 else 0

            # 最大回撤
            cumulative_returns = np.cumprod(1 + returns_array)
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdown = (cumulative_returns - running_max) / running_max
            max_drawdown = abs(np.min(drawdown))

            # 勝率
            _ = len(returns_array[returns_array > 0]) / len(returns_array)

            # 盈虧比
            winning_returns = returns_array[returns_array > 0]
            losing_returns = returns_array[returns_array < 0]
            avg_win = np.mean(winning_returns) if len(winning_returns) > 0 else 0
            avg_loss = abs(np.mean(losing_returns)) if len(losing_returns) > 0 else 1
            profit_factor = avg_win / avg_loss if avg_loss > 0 else 1

            # VaR和CVaR
            var_95 = np.percentile(returns_array, 5)
            _ = np.mean(returns_array[returns_array <= var_95])

            # Calmar比率
            _ = mean_return / max_drawdown if max_drawdown > 0 else 0

            # 偏度和峰度
            skewness = self._calculate_skewness(returns_array)
            kurtosis = self._calculate_kurtosis(returns_array)

            # 計算健康分數（綜合指標）
            health_score = self._calculate_health_score()
                sharpe_ratio,
                sortino_ratio,
                max_drawdown,
                win_rate,
                profit_factor,
                skewness,
                kurtosis,
            )

            return StrategyMetrics()
                strategy_id=strategy_id,
                health_score=health_score,
                sharpe_ratio=sharpe_ratio,
                sortino_ratio=sortino_ratio,
                max_drawdown=max_drawdown,
                win_rate=win_rate,
                profit_factor=profit_factor,
                volatility=volatility,
                beta=1.0,  # 需要基準數據計算
                alpha=0.0,  # 需要基準數據計算
                information_ratio=sharpe_ratio,  # 簡化
                calmar_ratio=calmar_ratio,
                var_95=var_95,
                cvar_95=cvar_95,
                skewness=skewness,
                kurtosis=kurtosis,
                last_updated=datetime.now(),
            )

        except Exception as e:
            logger.error(f"計算策略指標失敗 {strategy_id}: {e}")
            return StrategyMetrics()
                strategy_id=strategy_id,
                health_score=0.3,
                sharpe_ratio=0.0,
                sortino_ratio=0.0,
                max_drawdown=0.2,
                win_rate=0.4,
                profit_factor=0.8,
                volatility=0.2,
                beta=1.0,
                alpha=0.0,
                information_ratio=0.0,
                calmar_ratio=0.0,
                var_95=-0.05,
                cvar_95=-0.08,
                skewness=0.0,
                kurtosis=0.0,
                last_updated=datetime.now(),
            )

    def _calculate_skewness(self, returns: np.ndarray) -> float:
        """計算偏度"""
        try:
            mean = np.mean(returns)
            std = np.std(returns)
            if std == 0:
                return 0.0
            return np.mean(((returns - mean) / std) ** 3)
        except (ZeroDivisionError, ValueError):
            return 0.0

    def _calculate_kurtosis(self, returns: np.ndarray) -> float:
        """計算峰度"""
        try:
            mean = np.mean(returns)
            std = np.std(returns)
            if std == 0:
                return 0.0
            return np.mean(((returns - mean) / std) ** 4) - 3
        except (ZeroDivisionError, ValueError):
            return 0.0

    def _calculate_health_score()
        self,
        sharpe: float,
        sortino: float,
        max_dd: float,
        win_rate: float,
        profit_factor: float,
        skewness: float,
        kurtosis: float,
    ) -> float:
        """計算綜合健康分數"""
        try:
            # 歸一化各指標
            sharpe_score = min(max(sharpe / 2, 0), 1)  # Sharpe > 2 為滿分
            sortino_score = min(max(sortino / 2, 0), 1)
            drawdown_score = max(0, 1 - max_dd / 0.2)  # 20 % 回撤為0分
            win_rate_score = win_rate
            profit_factor_score = min(max((profit_factor - 1) / 2, 0), 1)  # PF > 3 為滿分

            # 偏度獎勵（正偏度好）
            skewness_score = min(max(skewness / 2 + 0.5, 0), 1)

            # 峰度懲罰（高峰度不好）
            kurtosis_score = max(0, 1 - abs(kurtosis) / 5)

            # 加權平均
            weights = [0.25, 0.2, 0.2, 0.15, 0.1, 0.05, 0.05]
            scores = [
                sharpe_score,
                sortino_score,
                drawdown_score,
                win_rate_score,
                profit_factor_score,
                skewness_score,
                kurtosis_score,
            ]

            health_score = sum(w * s for w, s in zip(weights, scores))

            return min(max(health_score, 0), 1)

        except Exception as e:
            logger.error(f"健康分數計算失敗: {e}")
            return 0.5

    def optimize_allocation()
        self, strategy_metrics: Dict[str, StrategyMetrics], returns_matrix: pd.DataFrame
    ) -> Dict[str, float]:
        """優化資金分配"""
        try:
            strategy_ids = list(strategy_metrics.keys())
            n_strategies = len(strategy_ids)

            if n_strategies == 0:
                return {}

            # 構建目標函數
            def objective(weights):
                # 風險調整收益最大化
                expected_returns = np.array()
                    [
                        strategy_metrics[sid].sharpe_ratio * strategy_metrics[sid].health_score
                        for sid in strategy_ids
                    ]
                )

                portfolio_return = np.dot(weights, expected_returns)

                # 計算風險
                risk_metrics = self.risk_model.calculate_portfolio_risk(returns_matrix, weights)
                portfolio_risk = risk_metrics["portfolio_volatility"]

                # 目標：最大化風險調整收益
                return -(portfolio_return / max(portfolio_risk, 0.01))

            # 約束條件
            constraints = [
                {"type": "eq", "fun": lambda w: np.sum(w) - 1},  # 權重和為1
            ]

            # 邊界條件
            bounds = [(self.min_allocation, self.max_allocation) for _ in range(n_strategies)]

            # 初始權重（等權重）
            initial_weights = np.array([1.0 / n_strategies] * n_strategies)

            # 優化
            result = minimize()
                objective,
                initial_weights,
                method="SLSQP",
                bounds=bounds,
                constraints=constraints,
                options={"maxiter": 1000},
            )

            if result.success:
                optimized_weights = result.x

                # 應用額外約束
                optimized_weights = self._apply_additional_constraints()
                    optimized_weights, strategy_metrics, strategy_ids
                )

                # 重新歸一化
                optimized_weights = optimized_weights / np.sum(optimized_weights)

                allocation = {
                    strategy_ids[i]: float(optimized_weights[i]) for i in range(n_strategies)
                }

                logger.info(f"資金分配優化完成: {allocation}")
                return allocation
            else:
                logger.warning("優化失敗，使用基於健康分數的分配")
                return self._fallback_allocation(strategy_metrics)

        except Exception as e:
            logger.error(f"資金分配優化失敗: {e}")
            return self._fallback_allocation(strategy_metrics)

    def _apply_additional_constraints()
        self,
        weights: np.ndarray,
        strategy_metrics: Dict[str, StrategyMetrics],
        strategy_ids: List[str],
    ) -> np.ndarray:
        """應用額外約束"""
        try:
            adjusted_weights = weights.copy()

            # 健康分數過低的策略減少分配
            for i, strategy_id in enumerate(strategy_ids):
                health_score = strategy_metrics[strategy_id].health_score
                if health_score < 0.3:
                    adjusted_weights[i] *= 0.5  # 減半
                elif health_score < 0.5:
                    adjusted_weights[i] *= 0.8  # 減少20%

            # 最大回撤過大的策略減少分配
            for i, strategy_id in enumerate(strategy_ids):
                max_drawdown = strategy_metrics[strategy_id].max_drawdown
                if max_drawdown > 0.2:  # 超過20 % 回撤
                    adjusted_weights[i] *= 0.6

            return adjusted_weights

        except Exception as e:
            logger.error(f"應用額外約束失敗: {e}")
            return weights

    def _fallback_allocation()
        self, strategy_metrics: Dict[str, StrategyMetrics]
    ) -> Dict[str, float]:
        """備用分配方案（基於健康分數）"""
        try:
            # 基於健康分數的簡單分配
            health_scores = {
                sid: max(metrics.health_score, 0.1)  # 最低0.1
                for sid, metrics in strategy_metrics.items()
            }

            total_score = sum(health_scores.values())

            allocation = {sid: score / total_score for sid, score in health_scores.items()}

            # 應用最小/最大分配限制
            for sid in allocation:
                allocation[sid] = max(
                    min(allocation[sid], self.max_allocation), self.min_allocation
                )

            # 重新歸一化
            total_allocation = sum(allocation.values())
            for sid in allocation:
                allocation[sid] /= total_allocation

            return allocation

        except Exception as e:
            logger.error(f"備用分配方案失敗: {e}")
            # 返回等權重分配
            n_strategies = len(strategy_metrics)
            return {sid: 1.0 / n_strategies for sid in strategy_metrics.keys()}

    async def execute_rebalancing(self) -> Dict[str, Any]:
        """執行重新平衡"""
        try:
            # 檢查是否需要重新平衡
            if not self._should_rebalance():
                return {"rebalanced": False, "reason": "not_due"}

            # 獲取策略績效數據
            strategy_metrics = {}
            returns_data = {}

            for strategy_id in self.portfolio_manager.strategy_allocations.keys():
                # 獲取策略收益率歷史
                returns = self._get_strategy_returns(strategy_id)
                if returns:
                    metrics = self.calculate_advanced_metrics(strategy_id, returns)
                    strategy_metrics[strategy_id] = metrics
                    returns_data[strategy_id] = returns

            if not strategy_metrics:
                return {"rebalanced": False, "reason": "no_data"}

            # 構建收益率矩陣
            returns_matrix = pd.DataFrame(returns_data).fillna(0)

            # 優化分配
            new_allocation = self.optimize_allocation(strategy_metrics, returns_matrix)

            # 執行重新平衡
            rebalance_result = await self._apply_new_allocation(new_allocation)

            # 更新最後重新平衡日期
            self.last_rebalance_date = datetime.now()

            logger.info(f"高級動態重新平衡完成: {rebalance_result}")

            return {
                "rebalanced": True,
                "new_allocation": new_allocation,
                "strategy_metrics": {
                    sid: metrics.__dict__ for sid, metrics in strategy_metrics.items()
                },
                "rebalance_result": rebalance_result,
            }

        except Exception as e:
            logger.error(f"高級動態重新平衡失敗: {e}")
            return {"rebalanced": False, "reason": f"error: {e}"}

    def _should_rebalance(self) -> bool:
        """檢查是否應該重新平衡"""
        if self.last_rebalance_date is None:
            return True

        days_since_last = (datetime.now() - self.last_rebalance_date).days
        return days_since_last >= self.rebalance_frequency

    def _get_strategy_returns(self, strategy_id: str) -> List[float]:
        """獲取策略收益率"""
        try:
            # 這裡應該從持久化管理器獲取歷史收益率
            # 暫時返回模擬數據
            np.random.seed(hash(strategy_id) % 2**32)
            returns = np.random.normal(0.001, 0.02, self.lookback_period).tolist()
            return returns
        except Exception as e:
            logger.error(f"獲取策略收益率失敗 {strategy_id}: {e}")
            return []

    async def _apply_new_allocation(self, new_allocation: Dict[str, float]) -> Dict[str, Any]:
        """應用新的資金分配"""
        try:
            changes = []

            for strategy_id, new_weight in new_allocation.items():
                if strategy_id in self.portfolio_manager.strategy_allocations:
                    allocation = self.portfolio_manager.strategy_allocations[strategy_id]
                    old_weight = allocation.current_allocation

                    if abs(new_weight - old_weight) > 0.01:  # 1 % 變化閾值
                        allocation.target_allocation = new_weight
                        allocation.current_allocation = new_weight
                        allocation.allocated_capital = ()
                            self.portfolio_manager.total_capital * new_weight
                        )
                        allocation.last_updated = datetime.now()

                        changes.append()
                            {
                                "strategy_id": strategy_id,
                                "old_allocation": old_weight,
                                "new_allocation": new_weight,
                                "change": new_weight - old_weight,
                                "new_capital": allocation.allocated_capital,
                            }
                        )

            return {"changes": changes, "total_changes": len(changes)}

        except Exception as e:
            logger.error(f"應用新分配失敗: {e}")
            return {"changes": [], "total_changes": 0}


if __name__ == "__main__":
    print("🧪 高級動態資金分配測試")

    # 創建測試數據
    _ = {
        "strategy_1": np.random.normal(0.002, 0.015, 60).tolist(),
        "strategy_2": np.random.normal(0.001, 0.020, 60).tolist(),
        "strategy_3": np.random.normal(-0.001, 0.025, 60).tolist(),
    }

    # 創建分配器（需要portfolio_manager實例）
    # allocator = AdvancedDynamicAllocator(portfolio_manager)

    print("✅ 高級動態資金分配模塊載入成功")
