import logging

# 配置logger
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
統一客戶端管理器 - 基於您的建議實現統一的異步客戶端實例管理
Unified Client Manager - Unified async client instance management based on your recommendations
"""

import asyncio
import weakref
from contextlib import asynccontextmanager
from typing import Any, Dict, Optional

import aiohttp
import ccxt.pro as ccxtpro

from graceful_shutdown import ShutdownComponent
from logging_config import get_logger

_ = get_logger(__name__)


class UnifiedClientManager(ShutdownComponent):
    """統一客戶端管理器 - 管理所有異步客戶端實例"""

    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}

        # 客戶端實例
        self._http_session: Optional[aiohttp.ClientSession] = None
        self._exchange_instances: Dict[str, ccxtpro.Exchange] = {}

        # 配置參數
        self.http_timeout = self.config.get("http_timeout", 30)
        self.http_connector_limit = self.config.get("http_connector_limit", 100)
        self.exchange_config = self.config.get("exchanges", {})

        # 客戶端引用計數（用於安全關閉）
        self._http_session_refs = weakref.WeakSet()
        self._exchange_refs: Dict[str, weakref.WeakSet] = {}

        logger.info("統一客戶端管理器初始化完成")

    @property
    def component_name(self) -> str:
        return "UnifiedClientManager"

    async def get_http_session(self) -> aiohttp.ClientSession:
        """獲取共享的 HTTP 會話"""
        if self._http_session is None or self._http_session.closed:
            await self._create_http_session()

        # 添加到引用集合
        self._http_session_refs.add(self._http_session)
        return self._http_session

    async def _create_http_session(self):
        """創建 HTTP 會話"""
        connector = aiohttp.TCPConnector()
            limit=self.http_connector_limit,
            limit_per_host=20,
            ttl_dns_cache=300,
            use_dns_cache=True,
            keepalive_timeout=30,
            enable_cleanup_closed=True,
        )

        timeout = aiohttp.ClientTimeout(total=self.http_timeout)

        self._http_session = aiohttp.ClientSession()
            connector=connector,
            timeout=timeout,
            headers={
                "User-Agent": "IntelligentTradingSystem/1.0",
                "Accept": "application/json",
                "Accept-Encoding": "gzip, deflate",
            },
        )

        logger.info(f"創建 HTTP 會話，連接限制: {self.http_connector_limit}")

    async def get_exchange_instance()
        self, exchange_name: str, exchange_config: Dict[str, Any] = None
    ) -> ccxtpro.Exchange:
        """獲取交易所實例"""
        if exchange_name not in self._exchange_instances:
            await self._create_exchange_instance(exchange_name, exchange_config)

        exchange = self._exchange_instances[exchange_name]

        # 添加到引用集合
        if exchange_name not in self._exchange_refs:
            self._exchange_refs[exchange_name] = weakref.WeakSet()
        self._exchange_refs[exchange_name].add(exchange)

        return exchange

    async def _create_exchange_instance()
        self, exchange_name: str, exchange_config: Dict[str, Any] = None
    ):
        """創建交易所實例"""
        try:
            # 合併配置
            config = self.exchange_config.get(exchange_name, {})
            if exchange_config:
                config.update(exchange_config)

            # 設置默認配置
            default_config = {
                "enableRateLimit": True,
                "rateLimit": 100,
                "timeout": 30000,
                "verbose": False,
                "sandbox": config.get("sandbox", True),  # 默認使用沙盒
            }

            # 合併默認配置
            final_config = {**default_config, **config}

            # 創建交易所實例
            exchange_class = getattr(ccxtpro, exchange_name)
            exchange = exchange_class(final_config)

            # 測試連接
            await exchange.load_markets()

            self._exchange_instances[exchange_name] = exchange

            logger.info(f"創建交易所實例: {exchange_name} ({'沙盒' if final_config['sandbox'] else '生產'})")

        except Exception as e:
            logger.error(f"創建交易所實例失敗 {exchange_name}: {e}")
            raise

    @asynccontextmanager
    async def http_session_context(self):
        """HTTP 會話上下文管理器"""
        session = await self.get_http_session()
        try:
            yield session
        finally:
            # 會話由管理器統一管理，這裡不關閉
            pass

    @asynccontextmanager
    async def exchange_context(self, exchange_name: str, exchange_config: Dict[str, Any] = None):
        """交易所實例上下文管理器"""
        exchange = await self.get_exchange_instance(exchange_name, exchange_config)
        try:
            yield exchange
        finally:
            # 交易所實例由管理器統一管理，這裡不關閉
            pass

    async def close_http_session(self):
        """關閉 HTTP 會話"""
        if self._http_session and not self._http_session.closed:
            logger.info("關閉 HTTP 會話...")
            await self._http_session.close()

            # 等待連接器關閉
            if hasattr(self._http_session, "connector") and self._http_session.connector:
                await self._http_session.connector.close()

            self._http_session = None
            logger.info("HTTP 會話已關閉")

    async def close_exchange_instance(self, exchange_name: str):
        """關閉特定交易所實例"""
        if exchange_name in self._exchange_instances:
            exchange = self._exchange_instances[exchange_name]

            try:
                logger.info(f"關閉交易所實例: {exchange_name}")
                await exchange.close()
                del self._exchange_instances[exchange_name]

                # 清理引用
                if exchange_name in self._exchange_refs:
                    del self._exchange_refs[exchange_name]

                logger.info(f"交易所實例已關閉: {exchange_name}")

            except Exception as e:
                logger.error(f"關閉交易所實例失敗 {exchange_name}: {e}")

    async def close_all_exchanges(self):
        """關閉所有交易所實例"""
        exchange_names = list(self._exchange_instances.keys())

        for exchange_name in exchange_names:
            await self.close_exchange_instance(exchange_name)

    async def shutdown(self) -> bool:
        """執行優雅停機"""
        try:
            logger.info("開始關閉統一客戶端管理器...")

            # 關閉所有交易所實例
            await self.close_all_exchanges()

            # 關閉 HTTP 會話
            await self.close_http_session()

            logger.info("統一客戶端管理器關閉完成")
            return True

        except Exception as e:
            logger.error(f"統一客戶端管理器關閉失敗: {e}")
            return False

    def get_client_stats(self) -> Dict[str, Any]:
        """獲取客戶端統計信息"""
        return {
            "http_session": {
                "active": self._http_session is not None and not self._http_session.closed,
                "references": len(self._http_session_refs),
            },
            "exchanges": {
                "active_instances": list(self._exchange_instances.keys()),
                "total_instances": len(self._exchange_instances),
                "references": {name: len(refs) for name, refs in self._exchange_refs.items()},
            },
        }


# 全局客戶端管理器實例
_client_manager: Optional[UnifiedClientManager] = None


def get_client_manager(config: Dict[str, Any] = None) -> UnifiedClientManager:
    """獲取全局客戶端管理器實例"""
    global _client_manager
    if _client_manager is None:
        _client_manager = UnifiedClientManager(config)

        # 自動註冊到停機管理器
        try:
            from graceful_shutdown import get_shutdown_manager

            get_shutdown_manager().register_component(_client_manager)
        except ImportError:
            logger.warning("無法註冊到停機管理器")

    return _client_manager


# 便利函數
async def get_shared_http_session() -> aiohttp.ClientSession:
    """獲取共享的 HTTP 會話"""
    manager = get_client_manager()
    return await manager.get_http_session()


async def get_shared_exchange()
    exchange_name: str, config: Dict[str, Any] = None
) -> ccxtpro.Exchange:
    """獲取共享的交易所實例"""
    manager = get_client_manager()
    return await manager.get_exchange_instance(exchange_name, config)


# 上下文管理器便利函數
@asynccontextmanager
async def shared_http_session():
    """共享 HTTP 會話上下文管理器"""
    manager = get_client_manager()
    async with manager.http_session_context() as session:
        yield session


@asynccontextmanager
async def shared_exchange(exchange_name: str, config: Dict[str, Any] = None):
    """共享交易所實例上下文管理器"""
    manager = get_client_manager()
    async with manager.exchange_context(exchange_name, config) as exchange:
        yield exchange


async def main():
    """測試統一客戶端管理器"""
    print("🧪 測試統一客戶端管理器")

    # 創建客戶端管理器
    config = {
        "http_timeout": 30,
        "http_connector_limit": 50,
        "exchanges": {"gateio": {"sandbox": True, "apiKey": "test_key", "secret": "test_secret"}},
    }

    manager = get_client_manager(config)

    try:
        # 測試 HTTP 會話
        print("測試 HTTP 會話...")
        async with shared_http_session() as session:
            print(f"  ✅ HTTP 會話創建成功: {type(session)}")

        # 測試交易所實例
        print("測試交易所實例...")
        try:
            async with shared_exchange("gateio") as exchange:
                print(f"  ✅ 交易所實例創建成功: {exchange.name}")
                markets = await exchange.load_markets()
                print(f"  📊 載入市場數據: {len(markets)} 個交易對")
        except Exception as e:
            print(f"  ⚠️ 交易所測試失敗 (可能需要真實API密鑰): {e}")

        # 獲取統計信息
        stats = manager.get_client_stats()
        print(f"  📊 客戶端統計: {stats}")

    finally:
        # 測試優雅關閉
        print("測試優雅關閉...")
        success = await manager.shutdown()
        print(f"  {'✅' if success else '❌'} 優雅關閉: {success}")

    print("✅ 統一客戶端管理器測試完成")


if __name__ == "__main__":
    asyncio.run(main())
