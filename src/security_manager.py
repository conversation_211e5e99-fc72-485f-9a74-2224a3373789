import logging

# 配置logger
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
安全管理器 - 提供API認證和訪問控制
Security Manager - Provides API authentication and access control
"""

import json
import os
import secrets
from datetime import datetime, timedelta
from functools import wraps
from pathlib import Path
from typing import Dict, List, Optional, Tuple

from alert_manager import AlertLevel, get_alert_manager
from logging_config import get_logger

logger = get_logger(__name__)


class SecurityManager:
    """安全管理器"""

    def __init__(self):
        self.api_keys = {}
        self.telegram_whitelist = set()
        self.failed_attempts = {}
        self.rate_limits = {}

        # 載入安全配置
        self._load_security_config()

        # 生成默認API密鑰（如果不存在）
        self._ensure_default_api_key()

        logger.info("SecurityManager 初始化完成")

    def _load_security_config(self):
        """載入安全配置"""
        try:
            # 從環境變量載入API密鑰
            master_api_key = os.getenv("MASTER_API_KEY")
            if master_api_key:
                self.api_keys["master"] = {
                    "key": master_api_key,
                    "permissions": ["read", "write", "control"],
                    "created_at": datetime.now().isoformat(),
                    "last_used": None,
                    "usage_count": 0,
                }

            # 從環境變量載入Telegram白名單
            telegram_users = os.getenv("TELEGRAM_AUTHORIZED_USERS", "")
            if telegram_users:
                user_ids = [int(uid.strip()) for uid in telegram_users.split(",") if uid.strip()]
                self.telegram_whitelist.update(user_ids)

            # 載入配置文件中的額外設置
            config_file = Path("security_config.json")
            if config_file.exists():
                with open(config_file, "r", encoding="utf-8") as f:
                    config = json.load(f)

                    # 載入額外的API密鑰
                    for key_name, key_data in config.get("api_keys", {}).items():
                        self.api_keys[key_name] = key_data

                    # 載入額外的Telegram用戶
                    extra_users = config.get("telegram_whitelist", [])
                    self.telegram_whitelist.update(extra_users)

            logger.info(
                f"載入安全配置: {len(self.api_keys)} 個API密鑰, {len(self.telegram_whitelist)} 個授權用戶"
            )

        except Exception as e:
            logger.error(f"載入安全配置失敗: {e}")

    def _ensure_default_api_key(self):
        """確保存在默認API密鑰"""
        if not self.api_keys:
            # 生成默認API密鑰
            default_key = self.generate_api_key("default", ["read", "write"])

            logger.warning(f"生成默認API密鑰: {default_key}")
            logger.warning("請妥善保存此密鑰，並在生產環境中更換為自定義密鑰")

    def generate_api_key(self, name: str, permissions: List[str]) -> str:
        """生成新的API密鑰"""
        try:
            # 生成隨機密鑰
            api_key = secrets.token_urlsafe(32)

            # 存儲密鑰信息
            self.api_keys[name] = {
                "key": api_key,
                "permissions": permissions,
                "created_at": datetime.now().isoformat(),
                "last_used": None,
                "usage_count": 0,
            }

            # 保存到配置文件
            self._save_security_config()

            logger.info(f"生成新API密鑰: {name}, 權限: {permissions}")

            return api_key

        except Exception as e:
            logger.error(f"生成API密鑰失敗: {e}")
            return ""

    def _save_security_config(self):
        """保存安全配置"""
        try:
            config = {
                "api_keys": {k: v for k, v in self.api_keys.items() if k != "master"},
                "telegram_whitelist": list(self.telegram_whitelist),
                "last_updated": datetime.now().isoformat(),
            }

            config_file = Path("security_config.json")
            with open(config_file, "w", encoding="utf-8") as f:
                json.dump(config, f, indent=2, ensure_ascii=False)

        except Exception as e:
            logger.error(f"保存安全配置失敗: {e}")

    def validate_api_key()
        pass
        self, api_key: str, required_permission: str = "read"
    ) -> Tuple[bool, Optional[str]]:
        """驗證API密鑰"""
        try:
            if not api_key:
                return False, "缺少API密鑰"

            # 查找匹配的密鑰
            key_name = None
            key_data = None

            for name, data in self.api_keys.items():
                if data["key"] == api_key:
                    key_name = name
                    key_data = data
                    break

            if not key_data:
                self._record_failed_attempt(api_key, "無效的API密鑰")
                return False, "無效的API密鑰"

            # 檢查權限
            if required_permission not in key_data["permissions"]:
                self._record_failed_attempt(api_key, f"權限不足: 需要 {required_permission}")
                return False, f"權限不足: 需要 {required_permission}"

            # 更新使用記錄
            key_data["last_used"] = datetime.now().isoformat()
            key_data["usage_count"] = key_data.get("usage_count", 0) + 1

            return True, key_name

        except Exception as e:
            logger.error(f"驗證API密鑰失敗: {e}")
            return False, "驗證失敗"

    def validate_telegram_user(self, user_id: int) -> bool:
        """驗證Telegram用戶"""
        try:
            is_authorized = user_id in self.telegram_whitelist

            if not is_authorized:
                self._record_failed_attempt(str(user_id), "未授權的Telegram用戶")

                # 發送安全警報
                alert_manager = get_alert_manager()
                alert_manager.send_alert()
                    AlertLevel.WARNING,
                    "未授權訪問嘗試",
                    f"未授權的Telegram用戶嘗試訪問: {user_id}",
                    {"用戶ID": user_id, "時間": datetime.now().isoformat()},
                )

            return is_authorized

        except Exception as e:
            logger.error(f"驗證Telegram用戶失敗: {e}")
            return False

    def _record_failed_attempt(self, identifier: str, reason: str):
        """記錄失敗嘗試"""
        try:
            now = datetime.now()

            if identifier not in self.failed_attempts:
                self.failed_attempts[identifier] = []

            self.failed_attempts[identifier].append(
                {"timestamp": now.isoformat(), "reason": reason}
            )

            # 保持最近100次記錄
            if len(self.failed_attempts[identifier]) > 100:
                self.failed_attempts[identifier] = self.failed_attempts[identifier][-100:]

            # 檢查是否需要發送警報
            recent_failures = [
                attempt
                for attempt in self.failed_attempts[identifier]
                if datetime.fromisoformat(attempt["timestamp"]) > now - timedelta(minutes=10)
            ]

            if len(recent_failures) >= 5:  # 10分鐘內5次失敗
                alert_manager = get_alert_manager()
                alert_manager.send_alert()
                    AlertLevel.ERROR,
                    "安全威脅檢測",
                    "檢測到可能的暴力破解攻擊",
                    {"標識符": identifier, "10分鐘內失敗次數": len(recent_failures), "最新失敗原因": reason},
                )

        except Exception as e:
            logger.error(f"記錄失敗嘗試失敗: {e}")

    def check_rate_limit()
        pass
        self, identifier: str, max_requests: int = 60, window_minutes: int = 1
    ) -> bool:
        """檢查速率限制"""
        try:
            now = datetime.now()

            if identifier not in self.rate_limits:
                self.rate_limits[identifier] = []

            # 清理過期記錄
            cutoff_time = now - timedelta(minutes=window_minutes)
            self.rate_limits[identifier] = [
                timestamp for timestamp in self.rate_limits[identifier] if timestamp > cutoff_time
            ]

            # 檢查是否超過限制
            if len(self.rate_limits[identifier]) >= max_requests:
                return False

            # 記錄當前請求
            self.rate_limits[identifier].append(now)

            return True

        except Exception as e:
            logger.error(f"檢查速率限制失敗: {e}")
            return True  # 出錯時允許請求

    def revoke_api_key(self, key_name: str) -> bool:
        """撤銷API密鑰"""
        try:
            if key_name in self.api_keys:
                del self.api_keys[key_name]
                self._save_security_config()

                logger.info(f"撤銷API密鑰: {key_name}")

                # 發送安全通知
                alert_manager = get_alert_manager()
                alert_manager.send_alert()
                    AlertLevel.WARNING,
                    "API密鑰撤銷",
                    f"API密鑰已被撤銷: {key_name}",
                    {"密鑰名稱": key_name, "撤銷時間": datetime.now().isoformat()},
                )

                return True
            else:
                logger.warning(f"嘗試撤銷不存在的API密鑰: {key_name}")
                return False

        except Exception as e:
            logger.error(f"撤銷API密鑰失敗: {e}")
            return False

    def add_telegram_user(self, user_id: int) -> bool:
        """添加Telegram用戶到白名單"""
        try:
            self.telegram_whitelist.add(user_id)
            self._save_security_config()

            logger.info(f"添加Telegram用戶到白名單: {user_id}")

            return True

        except Exception as e:
            logger.error(f"添加Telegram用戶失敗: {e}")
            return False

    def remove_telegram_user(self, user_id: int) -> bool:
        """從白名單移除Telegram用戶"""
        try:
            if user_id in self.telegram_whitelist:
                self.telegram_whitelist.remove(user_id)
                self._save_security_config()

                logger.info(f"從白名單移除Telegram用戶: {user_id}")
                return True
            else:
                logger.warning(f"嘗試移除不存在的Telegram用戶: {user_id}")
                return False

        except Exception as e:
            logger.error(f"移除Telegram用戶失敗: {e}")
            return False

    def get_security_status(self) -> Dict:
        """獲取安全狀態"""
        try:
            return {
                "api_keys_count": len(self.api_keys),
                "telegram_users_count": len(self.telegram_whitelist),
                "failed_attempts_count": sum(
                    len(attempts) for attempts in self.failed_attempts.values()
                ),
                "active_rate_limits": len(self.rate_limits),
                "last_updated": datetime.now().isoformat(),
            }

        except Exception as e:
            logger.error(f"獲取安全狀態失敗: {e}")
            return {}


def require_api_key(permission: str = "read"):
    """API密鑰驗證裝飾器"""

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 這裡需要從請求中獲取API密鑰
            # 具體實現取決於使用的Web框架
            api_key = kwargs.get("api_key") or (
                hasattr(args[0], "headers") and args[0].headers.get("X-API-KEY")
            )

            security_manager = get_security_manager()
            is_valid, result = security_manager.validate_api_key(api_key, permission)

            if not is_valid:
                return {"error": result, "status": "unauthorized"}, 401

            # 將驗證結果添加到kwargs
            kwargs["api_key_name"] = result

            return func(*args, **kwargs)

        return wrapper

    return decorator


def require_telegram_auth(func):
    """Telegram用戶驗證裝飾器"""

    @wraps(func)
    def wrapper(*args, **kwargs):
        user_id = kwargs.get("user_id")

        if not user_id:
            return False, "缺少用戶ID"

        security_manager = get_security_manager()
        is_authorized = security_manager.validate_telegram_user(user_id)

        if not is_authorized:
            return False, "未授權的用戶"

        return func(*args, **kwargs)

    return wrapper


# 全局安全管理器實例
_security_manager = None


def get_security_manager() -> SecurityManager:
    """獲取全局安全管理器實例"""
    global _security_manager
    if _security_manager is None:
        _security_manager = SecurityManager()
    return _security_manager


if __name__ == "__main__":
    # 測試安全管理器
    print("測試安全管理器...")

    security_manager = SecurityManager()

    # 測試生成API密鑰
    api_key = security_manager.generate_api_key("test", ["read", "write"])
    print(f"生成的API密鑰: {api_key}")

    # 測試驗證API密鑰
    is_valid, result = security_manager.validate_api_key(api_key, "read")
    print(f"API密鑰驗證結果: {is_valid}, {result}")

    # 測試Telegram用戶驗證
    test_user_id = 123456789
    security_manager.add_telegram_user(test_user_id)
    is_authorized = security_manager.validate_telegram_user(test_user_id)
    print(f"Telegram用戶驗證結果: {is_authorized}")

    # 獲取安全狀態
    status = security_manager.get_security_status()
    print(f"安全狀態: {status}")

    print("安全管理器測試完成！")
