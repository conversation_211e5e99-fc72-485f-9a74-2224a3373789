#!/usr/bin/env python3
"""
統一配置管理系統 - 基於 Pydantic 的分層配置管理
Unified Configuration Management System - Hierarchical config management with Pydantic
"""

import os
import json
import yaml
from typing import Dict, Any, Optional, List, Union
from pathlib import Path
from enum import Enum

from pydantic import BaseModel, Field, field_validator, model_validator, ConfigDict
from pydantic_settings import BaseSettings

from logging_config import get_logger

logger = get_logger(__name__)


class Environment(str, Enum):
    """環境類型"""
    DEVELOPMENT = "development"
    TESTING = "testing"
    PRODUCTION = "production"
    BITMART = "bitmart"
    GATEIO = "gateio"


class ExchangeType(str, Enum):
    """交易所類型"""
    BINANCE = "binance"
    GATEIO = "gateio"
    BITMART = "bitmart"
    OKX = "okx"


class TradingMode(str, Enum):
    """交易模式"""
    SPOT = "spot"
    FUTURES = "futures"
    MARGIN = "margin"


class RiskLevel(str, Enum):
    """風險等級"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    EXTREME = "extreme"


class ExchangeConfig(BaseModel):
    """交易所配置"""
    name: ExchangeType
    api_key: str = Field(..., description="API密鑰")
    api_secret: str = Field(..., description="API密鑰")
    passphrase: Optional[str] = Field(None, description="API密碼短語（OKX需要）")
    sandbox: bool = Field(False, description="是否使用沙盒環境")
    trading_mode: TradingMode = Field(TradingMode.SPOT, description="交易模式")
    
    # 交易參數
    leverage: float = Field(1.0, ge=1.0, le=100.0, description="槓桿倍數")
    margin_mode: str = Field("cross", description="保證金模式")
    
    # 限制參數
    rate_limit: int = Field(1200, description="API請求限制（每分鐘）")
    timeout: int = Field(30000, description="請求超時時間（毫秒）")
    
    @field_validator('api_key', 'api_secret')
    @classmethod
    def validate_credentials(cls, v):
        if not v or len(v) < 10:
            raise ValueError('API憑證長度不足')
        return v


class TradingConfig(BaseModel):
    """交易配置"""
    # 基本參數
    position_size_usd: float = Field(100.0, gt=0, description="倉位大小（USD）")
    max_position_size: float = Field(0.1, ge=0.01, le=1.0, description="最大倉位比例")
    risk_level: RiskLevel = Field(RiskLevel.MEDIUM, description="風險等級")
    
    # 止損止盈
    stop_loss_pct: float = Field(0.02, ge=0.001, le=0.1, description="止損百分比")
    take_profit_pct: float = Field(0.04, ge=0.001, le=0.2, description="止盈百分比")
    
    # 交易對配置
    trading_pair: Optional[str] = Field(None, description="交易對")
    symbol_a: Optional[str] = Field(None, description="交易對A")
    symbol_b: Optional[str] = Field(None, description="交易對B")
    
    # 策略參數
    zscore_entry: float = Field(2.0, ge=1.0, le=5.0, description="Z分數入場閾值")
    zscore_exit: float = Field(0.5, ge=0.1, le=2.0, description="Z分數出場閾值")
    lookback_period: int = Field(100, ge=20, le=500, description="回望期間")
    
    @field_validator('take_profit_pct')
    @classmethod
    def validate_take_profit(cls, v, info):
        if info.data and 'stop_loss_pct' in info.data:
            if v <= info.data['stop_loss_pct']:
                raise ValueError('止盈百分比必須大於止損百分比')
        return v
    
    @field_validator('zscore_exit')
    @classmethod
    def validate_zscore_exit(cls, v, info):
        if info.data and 'zscore_entry' in info.data:
            if v >= info.data['zscore_entry']:
                raise ValueError('Z分數出場閾值必須小於入場閾值')
        return v


class PairSelectionConfig(BaseModel):
    """配對選擇配置"""
    symbols: List[str] = Field(default_factory=list, description="候選交易對列表")
    lookback_days: int = Field(90, ge=30, le=365, description="歷史數據天數")
    min_correlation: float = Field(0.7, ge=0.5, le=0.95, description="最小相關性")
    max_cointegration_pvalue: float = Field(0.05, ge=0.01, le=0.1, description="最大協整p值")
    max_pairs: int = Field(10, ge=1, le=50, description="最大配對數量")


class BacktestConfig(BaseModel):
    """回測配置"""
    start_date: str = Field("2023-01-01", description="開始日期")
    end_date: str = Field("2024-01-01", description="結束日期")
    initial_capital: float = Field(10000.0, gt=0, description="初始資金")
    commission: float = Field(0.001, ge=0, le=0.01, description="手續費率")
    slippage: float = Field(0.0005, ge=0, le=0.01, description="滑點")


class MonitoringConfig(BaseModel):
    """監控配置"""
    health_check_interval: int = Field(60, ge=10, le=300, description="健康檢查間隔（秒）")
    alert_enabled: bool = Field(True, description="是否啟用警報")
    telegram_enabled: bool = Field(False, description="是否啟用Telegram通知")
    telegram_token: Optional[str] = Field(None, description="Telegram機器人Token")
    telegram_chat_id: Optional[str] = Field(None, description="Telegram聊天ID")
    
    # 日誌配置
    log_level: str = Field("INFO", description="日誌級別")
    log_file: str = Field("logs/trading.log", description="日誌文件路徑")
    max_log_size: int = Field(10, description="最大日誌文件大小（MB）")


class UnifiedConfig(BaseSettings):
    """統一配置模型"""
    # 環境配置
    environment: Environment = Field(Environment.DEVELOPMENT, description="運行環境")
    debug: bool = Field(False, description="調試模式")
    
    # 交易所配置
    exchange: ExchangeConfig
    
    # 交易配置
    trading: TradingConfig = Field(default_factory=TradingConfig)
    
    # 配對選擇配置
    pair_selection: PairSelectionConfig = Field(default_factory=PairSelectionConfig)
    
    # 回測配置
    backtest: BacktestConfig = Field(default_factory=BacktestConfig)
    
    # 監控配置
    monitoring: MonitoringConfig = Field(default_factory=MonitoringConfig)
    
    # 高級配置
    advanced: Dict[str, Any] = Field(default_factory=dict, description="高級配置選項")
    
    @model_validator(mode='after')
    def validate_config_consistency(self):
        """驗證配置一致性"""
        # 檢查交易對配置
        if self.trading.trading_pair:
            if not (self.trading.symbol_a and self.trading.symbol_b):
                # 從trading_pair解析symbol_a和symbol_b
                if '/' in self.trading.trading_pair:
                    symbols = self.trading.trading_pair.split('/')
                    if len(symbols) == 2:
                        self.trading.symbol_a = symbols[0] + '/USDT'
                        self.trading.symbol_b = symbols[1] + '/USDT'
        
        # 檢查風險等級與槓桿的一致性
        if self.trading.risk_level == RiskLevel.LOW and self.exchange.leverage > 5:
            logger.warning("低風險等級建議使用較低槓桿")
        elif self.trading.risk_level == RiskLevel.EXTREME and self.exchange.leverage < 10:
            logger.warning("極高風險等級通常使用較高槓桿")
        
        # 檢查Telegram配置
        if self.monitoring.telegram_enabled:
            if not self.monitoring.telegram_token or not self.monitoring.telegram_chat_id:
                raise ValueError("啟用Telegram通知需要提供token和chat_id")
        
        return self
    
    model_config = ConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="allow"
    )


class UnifiedConfigManager:
    """統一配置管理器"""
    
    def __init__(self, base_config_path: str = "config.json"):
        self.base_config_path = Path(base_config_path)
        self.config_cache: Dict[str, UnifiedConfig] = {}
        self.environment = os.getenv("TRADING_ENV", Environment.DEVELOPMENT.value)
        
        logger.info(f"配置管理器初始化，環境: {self.environment}")
    
    def load_config(self, environment: Optional[str] = None) -> UnifiedConfig:
        """
        載入配置
        
        Args:
            environment: 環境名稱，如果為None則使用默認環境
            
        Returns:
            統一配置對象
        """
        env = environment or self.environment
        
        # 檢查緩存
        if env in self.config_cache:
            return self.config_cache[env]
        
        try:
            # 載入基礎配置
            base_config = self._load_base_config()
            
            # 載入環境特定配置
            env_config = self._load_environment_config(env)
            
            # 合併配置
            merged_config = self._merge_configs(base_config, env_config)
            
            # 創建Pydantic模型
            config = UnifiedConfig(**merged_config)
            
            # 緩存配置
            self.config_cache[env] = config
            
            logger.info(f"配置載入成功: {env}")
            return config
            
        except Exception as e:
            logger.error(f"載入配置失敗: {e}")
            raise
    
    def _load_base_config(self) -> Dict[str, Any]:
        """載入基礎配置"""
        if not self.base_config_path.exists():
            logger.warning(f"基礎配置文件不存在: {self.base_config_path}")
            return {}
        
        try:
            with open(self.base_config_path, 'r', encoding='utf-8') as f:
                if self.base_config_path.suffix.lower() == '.yaml':
                    return yaml.safe_load(f)
                else:
                    return json.load(f)
        except Exception as e:
            logger.error(f"載入基礎配置失敗: {e}")
            return {}
    
    def _load_environment_config(self, environment: str) -> Dict[str, Any]:
        """載入環境特定配置"""
        env_config_path = self.base_config_path.parent / f"config_{environment}.json"
        
        if not env_config_path.exists():
            logger.debug(f"環境配置文件不存在: {env_config_path}")
            return {}
        
        try:
            with open(env_config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"載入環境配置失敗: {e}")
            return {}
    
    def _merge_configs(self, base_config: Dict[str, Any], 
                      env_config: Dict[str, Any]) -> Dict[str, Any]:
        """合併配置"""
        merged = base_config.copy()
        
        def deep_merge(target: Dict, source: Dict):
            for key, value in source.items():
                if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                    deep_merge(target[key], value)
                else:
                    target[key] = value
        
        deep_merge(merged, env_config)
        return merged
    
    def save_config(self, config: UnifiedConfig, environment: str):
        """保存配置"""
        try:
            config_path = self.base_config_path.parent / f"config_{environment}.json"
            
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config.model_dump(), f, indent=2, ensure_ascii=False)
            
            # 更新緩存
            self.config_cache[environment] = config
            
            logger.info(f"配置保存成功: {config_path}")
            
        except Exception as e:
            logger.error(f"保存配置失敗: {e}")
            raise
    
    def validate_config(self, config_dict: Dict[str, Any]) -> Dict[str, Any]:
        """驗證配置"""
        try:
            config = UnifiedConfig(**config_dict)
            return {
                'is_valid': True,
                'config': config,
                'errors': []
            }
        except Exception as e:
            return {
                'is_valid': False,
                'config': None,
                'errors': [str(e)]
            }
    
    def get_config_template(self, environment: Environment = Environment.DEVELOPMENT) -> Dict[str, Any]:
        """獲取配置模板"""
        template = {
            "environment": environment.value,
            "debug": environment == Environment.DEVELOPMENT,
            "exchange": {
                "name": "gateio",
                "api_key": "your_api_key_here",
                "api_secret": "your_api_secret_here",
                "sandbox": environment != Environment.PRODUCTION,
                "trading_mode": "futures",
                "leverage": 10.0,
                "margin_mode": "cross"
            },
            "trading": {
                "position_size_usd": 100.0,
                "max_position_size": 0.1,
                "risk_level": "medium",
                "stop_loss_pct": 0.02,
                "take_profit_pct": 0.04,
                "zscore_entry": 2.0,
                "zscore_exit": 0.5,
                "lookback_period": 100
            },
            "pair_selection": {
                "symbols": ["BTC/USDT", "ETH/USDT", "BNB/USDT"],
                "lookback_days": 90,
                "min_correlation": 0.7,
                "max_cointegration_pvalue": 0.05,
                "max_pairs": 10
            },
            "monitoring": {
                "health_check_interval": 60,
                "alert_enabled": True,
                "telegram_enabled": False,
                "log_level": "INFO"
            }
        }
        
        return template
    
    def clear_cache(self):
        """清除配置緩存"""
        self.config_cache.clear()
        logger.info("配置緩存已清除")


# 全局配置管理器實例
_config_manager: Optional[UnifiedConfigManager] = None


def get_unified_config_manager(base_config_path: str = "config.json") -> UnifiedConfigManager:
    """獲取統一配置管理器實例"""
    global _config_manager
    if _config_manager is None:
        _config_manager = UnifiedConfigManager(base_config_path)
    return _config_manager


def load_unified_config(environment: Optional[str] = None) -> UnifiedConfig:
    """載入統一配置的便利函數"""
    manager = get_unified_config_manager()
    return manager.load_config(environment)


async def main():
    """測試統一配置管理器"""
    print("🧪 測試統一配置管理器")
    
    try:
        # 創建配置管理器
        manager = get_unified_config_manager()
        
        # 生成配置模板
        template = manager.get_config_template(Environment.DEVELOPMENT)
        print(f"  ✅ 生成配置模板: {len(template)} 個配置項")
        
        # 驗證配置
        validation = manager.validate_config(template)
        print(f"  {'✅' if validation['is_valid'] else '❌'} 配置驗證: {validation['is_valid']}")
        
        if validation['is_valid']:
            config = validation['config']
            print(f"    環境: {config.environment}")
            print(f"    交易所: {config.exchange.name}")
            print(f"    風險等級: {config.trading.risk_level}")
            print(f"    槓桿: {config.exchange.leverage}x")
        else:
            print(f"    錯誤: {validation['errors']}")
        
    except Exception as e:
        print(f"  ❌ 測試失敗: {e}")
    
    print("✅ 統一配置管理器測試完成")


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
