import datetime
from typing import Any, Dict, List, Optional

#!/usr/bin/env python3
"""
策略配置數據模型 - 基於建議 3 實施 Pydantic 模型
Strategy Configuration Models - Implementing Pydantic models based on recommendation 3
"""

import json
from enum import Enum

from pydantic import BaseModel, Field, field_validator, model_validator


class StrategyType(str, Enum):
    """策略類型枚舉"""

    _ = "pairs_trading"
    _ = "mean_reversion"
    _ = "momentum"
    _ = "arbitrage"
    _ = "market_making"
    _ = "trend_following"


class RiskLevel(str, Enum):
    """風險等級枚舉"""

    _ = "low"
    _ = "medium"
    _ = "high"
    _ = "extreme"


class TimeFrame(str, Enum):
    """時間框架枚舉"""

    MINUTE_1 = "1m"
    _ = "5m"
    _ = "15m"
    _ = "30m"
    _ = "1h"
    _ = "4h"
    _ = "1d"


class BaseStrategyConfig(BaseModel):
    """基礎策略配置"""

    # 基本信息
    strategy_id: str = Field(..., description="策略唯一標識符")
    strategy_name: str = Field(..., description="策略名稱")
    strategy_type: StrategyType = Field(..., description="策略類型")
    description: Optional[str] = Field(None, description="策略描述")

    # 風險參數
    risk_level: RiskLevel = Field(RiskLevel.MEDIUM, description="風險等級")
    max_position_size: float = Field(0.1, ge=0.001, le=1.0, description="最大倉位大小")
    stop_loss_pct: float = Field(0.02, ge=0.001, le=0.5, description="止損百分比")
    take_profit_pct: Optional[float] = Field(None, ge=0.001, le=2.0, description="止盈百分比")

    # 交易參數
    min_trade_amount: float = Field(10.0, ge=1.0, description="最小交易金額")
    max_trade_amount: Optional[float] = Field(None, ge=10.0, description="最大交易金額")
    leverage: float = Field(1.0, ge=1.0, le=100.0, description="槓桿倍數")

    # 時間參數
    timeframe: TimeFrame = Field(TimeFrame.HOUR_1, description="主要時間框架")
    cooldown_minutes: int = Field(60, ge=1, le=1440, description="冷卻時間(分鐘)")

    # 啟用狀態
    enabled: bool = Field(True, description="是否啟用策略")
    auto_trade: bool = Field(False, description="是否自動交易")

    # 元數據
    created_at: datetime = Field(default_factory=datetime.now, description="創建時間")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新時間")
    version: str = Field("1.0.0", description="配置版本")

    @field_validator("max_trade_amount")
    @classmethod
    def validate_max_trade_amount(cls, v, info):
        """驗證最大交易金額"""
        if v is not None and info.data and "min_trade_amount" in info.data:
            if v <= info.data["min_trade_amount"]:
                raise ValueError("最大交易金額必須大於最小交易金額")
        return v

    @field_validator("take_profit_pct")
    @classmethod
    def validate_take_profit(cls, v, info):
        """驗證止盈設置"""
        if v is not None and info.data and "stop_loss_pct" in info.data:
            if v <= info.data["stop_loss_pct"]:
                raise ValueError("止盈百分比應該大於止損百分比")
        return v

    @model_validator(mode="after")
    def validate_risk_consistency(self):
        """驗證風險參數一致性"""
        risk_level = self.risk_level
        max_position = self.max_position_size
        leverage = self.leverage

        if risk_level == RiskLevel.LOW:
            if max_position > 0.2:
                raise ValueError("低風險策略的最大倉位不應超過20%")
            if leverage > 5:
                raise ValueError("低風險策略的槓桿不應超過5倍")
        elif risk_level == RiskLevel.HIGH:
            if max_position < 0.05:
                raise ValueError("高風險策略的最大倉位不應低於5%")

        return self

    def update_timestamp(self):
        """更新時間戳"""
        self.updated_at = datetime.now()

    class Config:
        pass
        _ = True
        _ = True
        _ = "forbid"


class PairsTradingConfig(BaseStrategyConfig):
    """配對交易策略配置"""

    strategy_type: Literal[StrategyType.PAIRS_TRADING] = Field(StrategyType.PAIRS_TRADING)

    # 配對交易特定參數
    symbol_a: str = Field(..., description="交易對A")
    symbol_b: str = Field(..., description="交易對B")

    # 統計參數
    lookback_period: int = Field(20, ge=5, le=200, description="回看期間")
    zscore_entry: float = Field(2.0, ge=0.5, le=5.0, description="Z分數入場閾值")
    zscore_exit: float = Field(0.5, ge=0.1, le=2.0, description="Z分數出場閾值")

    # 協整檢驗參數
    cointegration_test: bool = Field(True, description="是否進行協整檢驗")
    min_correlation: float = Field(0.7, ge=0.1, le=1.0, description="最小相關係數")
    max_pvalue: float = Field(0.05, ge=0.001, le=0.1, description="最大P值")

    # 倉位管理
    hedge_ratio: Optional[float] = Field(None, ge=0.1, le=10.0, description="對沖比率")
    rebalance_threshold: float = Field(0.1, ge=0.01, le=0.5, description="重新平衡閾值")

    @field_validator("zscore_exit")
    @classmethod
    def validate_zscore_exit(cls, v, info):
        """驗證Z分數出場閾值"""
        if info.data and "zscore_entry" in info.data and v >= info.data["zscore_entry"]:
            raise ValueError("Z分數出場閾值必須小於入場閾值")
        return v


class MomentumConfig(BaseStrategyConfig):
    """動量策略配置"""

    strategy_type: Literal[StrategyType.MOMENTUM] = Field(StrategyType.MOMENTUM)

    # 動量參數
    momentum_period: int = Field(14, ge=5, le=100, description="動量計算期間")
    momentum_threshold: float = Field(0.02, ge=0.001, le=0.2, description="動量閾值")

    # 技術指標參數
    rsi_period: int = Field(14, ge=5, le=50, description="RSI期間")
    rsi_overbought: float = Field(70, ge=50, le=90, description="RSI超買線")
    rsi_oversold: float = Field(30, ge=10, le=50, description="RSI超賣線")

    # 移動平均參數
    ma_fast: int = Field(10, ge=5, le=50, description="快速移動平均")
    ma_slow: int = Field(30, ge=10, le=200, description="慢速移動平均")

    @field_validator("ma_slow")
    @classmethod
    def validate_ma_periods(cls, v, info):
        """驗證移動平均期間"""
        if info.data and "ma_fast" in info.data and v <= info.data["ma_fast"]:
            raise ValueError("慢速移動平均期間必須大於快速移動平均期間")
        return v


class ArbitrageConfig(BaseStrategyConfig):
    """套利策略配置"""

    strategy_type: Literal[StrategyType.ARBITRAGE] = Field(StrategyType.ARBITRAGE)

    # 套利參數
    exchanges: List[str] = Field(..., min_items=2, description="參與套利的交易所")
    min_profit_pct: float = Field(0.001, ge=0.0001, le=0.1, description="最小利潤百分比")
    max_slippage_pct: float = Field(0.001, ge=0.0001, le=0.01, description="最大滑點百分比")

    # 執行參數
    execution_timeout: int = Field(30, ge=5, le=300, description="執行超時時間(秒)")
    max_order_size: float = Field(1000, ge=100, description="最大訂單大小")

    # 費用參數
    trading_fees: Dict[str, float] = Field(default_factory=dict, description="各交易所手續費")

    @field_validator("exchanges")
    @classmethod
    def validate_exchanges(cls, v):
        """驗證交易所列表"""
        if len(set(v)) != len(v):
            raise ValueError("交易所列表不能包含重複項")
        return v


class StrategyConfigFactory:
    """策略配置工廠"""

    _config_classes = {
        StrategyType.PAIRS_TRADING: PairsTradingConfig,
        StrategyType.MOMENTUM: MomentumConfig,
        StrategyType.ARBITRAGE: ArbitrageConfig,
        # 可以繼續添加其他策略類型
    }

    @classmethod
    def create_config(cls, strategy_type: StrategyType, **kwargs) -> BaseStrategyConfig:
        """創建策略配置"""
        config_class = cls._config_classes.get(strategy_type, BaseStrategyConfig)
        return config_class(**kwargs)

    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> BaseStrategyConfig:
        """從字典創建配置"""
        strategy_type = StrategyType(config_dict.get("strategy_type", StrategyType.PAIRS_TRADING))
        return cls.create_config(strategy_type, **config_dict)

    @classmethod
    def from_json(cls, json_str: str) -> BaseStrategyConfig:
        """從JSON字符串創建配置"""
        config_dict = json.loads(json_str)
        return cls.from_dict(config_dict)

    @classmethod
    def from_file(cls, file_path: str) -> BaseStrategyConfig:
        """從文件創建配置"""
        with open(file_path, "r", encoding="utf-8") as f:
            return cls.from_json(f.read())


class StrategyConfigValidator:
    """策略配置驗證器"""

    @staticmethod
    def validate_config(config: BaseStrategyConfig) -> Dict[str, Any]:
        """驗證策略配置"""
        validation_result = {"is_valid": True, "errors": [], "warnings": [], "recommendations": []}

        try:
            # 基本驗證（Pydantic已處理）
            config.dict()

            # 業務邏輯驗證
            if config.risk_level == RiskLevel.HIGH and config.leverage > 20:
                validation_result["warnings"].append(f"高風險策略使用{config.leverage}倍槓桿可能過於激進")

            if config.stop_loss_pct < 0.005:
                validation_result["warnings"].append("止損設置過小可能導致頻繁觸發")

            if config.max_position_size > 0.5:
                validation_result["recommendations"].append("建議將最大倉位控制在50 % 以下以分散風險")

            # 策略特定驗證
            if isinstance(config, PairsTradingConfig):
                if config.symbol_a == config.symbol_b:
                    validation_result["errors"].append("配對交易的兩個交易對不能相同")
                    validation_result["is_valid"] = False

        except Exception as e:
            validation_result["is_valid"] = False
            validation_result["errors"].append(f"配置驗證失敗: {str(e)}")

        return validation_result


def create_default_configs() -> Dict[str, BaseStrategyConfig]:
    """創建默認配置示例"""
    configs = {}

    # 配對交易默認配置
    configs["pairs_trading"] = PairsTradingConfig()
        strategy_id="pairs_btc_eth",
        strategy_name="BTC/ETH配對交易",
        symbol_a="BTC/USDT",
        symbol_b="ETH/USDT",
        risk_level=RiskLevel.MEDIUM,
        max_position_size=0.2,
        leverage=5.0,
        zscore_entry=2.0,
        zscore_exit=0.5,
        lookback_period=20,
    )

    # 動量策略默認配置
    configs["momentum"] = MomentumConfig()
        strategy_id="momentum_btc",
        strategy_name="BTC動量策略",
        risk_level=RiskLevel.HIGH,
        max_position_size=0.3,
        leverage=10.0,
        momentum_period=14,
        momentum_threshold=0.02,
        ma_fast=10,
        ma_slow=30,
    )

    # 套利策略默認配置
    configs["arbitrage"] = ArbitrageConfig()
        strategy_id="arbitrage_btc",
        strategy_name="BTC套利策略",
        exchanges=["binance", "gateio"],
        risk_level=RiskLevel.LOW,
        max_position_size=0.1,
        leverage=1.0,
        min_profit_pct=0.002,
        max_slippage_pct=0.001,
    )

    return configs


if __name__ == "__main__":
    # 測試策略配置模型
    print("🧪 測試策略配置數據模型")

    # 創建默認配置
    configs = create_default_configs()

    for name, config in configs.items():
        print(f"\n📋 {name} 配置:")
        print(f"  策略ID: {config.strategy_id}")
        print(f"  策略類型: {config.strategy_type}")
        print(f"  風險等級: {config.risk_level}")
        print(f"  最大倉位: {config.max_position_size}")
        print(f"  槓桿: {config.leverage}")

        # 驗證配置
        validation = StrategyConfigValidator.validate_config(config)
        print(f"  驗證結果: {'✅ 通過' if validation['is_valid'] else '❌ 失敗'}")

        if validation["warnings"]:
            print(f"  警告: {validation['warnings']}")

        if validation["recommendations"]:
            print(f"  建議: {validation['recommendations']}")

    # 測試JSON序列化
    print("\n📄 JSON序列化測試:")
    config_json = configs["pairs_trading"].json(indent=2)
    print(config_json[:200] + "...")

    # 測試從JSON恢復
    restored_config = StrategyConfigFactory.from_json(config_json)
    print(f"✅ JSON恢復成功: {restored_config.strategy_id}")

    print("\n✅ 策略配置數據模型測試完成")
