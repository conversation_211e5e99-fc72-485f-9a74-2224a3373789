from typing import Any, Callable, Dict, Optional
import logging

# 配置logger
logger = logging.getLogger(__name__)

#!/usr/bin/env python3
"""
多執行器管理器 - 基於建議 7 實施 CPU/IO 任務分離
Multi-Executor Manager - CPU/IO task separation based on recommendation 7
"""

import asyncio
import functools
import multiprocessing
import time
from dataclasses import dataclass
from enum import Enum

import psutil

from graceful_shutdown import ShutdownComponent
from logging_config import get_logger

_ = get_logger(__name__)


class TaskType(str, Enum):
    """任務類型枚舉"""

    _ = "cpu_bound"  # CPU密集型任務
    _ = "io_bound"  # IO密集型任務
    _ = "mixed"  # 混合型任務
    _ = "network"  # 網絡任務
    _ = "database"  # 數據庫任務


class TaskPriority(int, Enum):
    """任務優先級枚舉"""

    _ = 1
    _ = 2
    _ = 3
    _ = 4


@dataclass
class TaskMetrics:
    """任務執行指標"""

    task_id: str
    task_type: TaskType
    priority: TaskPriority
    submitted_at: float
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    execution_time: Optional[float] = None
    success: Optional[bool] = None
    error: Optional[str] = None

    @property
    def queue_time(self) -> Optional[float]:
        """排隊時間"""
        if self.started_at is None:
            return None
        return self.started_at - self.submitted_at

    @property
    def total_time(self) -> Optional[float]:
        """總時間"""
        if self.completed_at is None:
            return None
        return self.completed_at - self.submitted_at


class MultiExecutorManager(ShutdownComponent):
    """多執行器管理器"""

    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}

        # 執行器配置
        self.cpu_workers = self.config.get("cpu_workers", multiprocessing.cpu_count())
        self.io_workers = self.config.get("io_workers", min(32, (multiprocessing.cpu_count() or 1) + 4))
        self.network_workers = self.config.get("network_workers", 20)
        self.database_workers = self.config.get("database_workers", 10)

        # 創建執行器
        self.executors: Dict[TaskType, concurrent.futures.Executor] = {}
        self._create_executors()

        # 任務隊列和指標
        self.task_queues: Dict[TaskType, asyncio.PriorityQueue] = {}
        self.task_metrics: Dict[str, TaskMetrics] = {}
        self.running_tasks: Dict[str, asyncio.Task] = {}

        # 統計信息
        self.stats = {
            "total_submitted": 0,
            "total_completed": 0,
            "total_failed": 0,
            "by_type": {
                task_type: {"submitted": 0, "completed": 0, "failed": 0} for task_type in TaskType
            },
            "avg_execution_time": {},
            "avg_queue_time": {},
        }

        # 監控和調優
        self.adaptive_enabled = self.config.get("adaptive_enabled", True)
        self.monitor_interval = self.config.get("monitor_interval", 30)
        self._monitor_task: Optional[asyncio.Task] = None

        # 初始化任務隊列
        self._init_task_queues()

        logger.info(f"多執行器管理器初始化完成: CPU={self.cpu_workers}, IO={self.io_workers}")

    @property
    def component_name(self) -> str:
        return "MultiExecutorManager"

    def _create_executors(self):
        """創建執行器"""
        # CPU密集型任務執行器 - 使用進程池
        self.executors[TaskType.CPU_BOUND] = concurrent.futures.ProcessPoolExecutor(max_workers=self.cpu_workers, mp_context=multiprocessing.get_context("spawn")  # 更安全的上下文)

        # IO密集型任務執行器 - 使用線程池
        self.executors[TaskType.IO_BOUND] = concurrent.futures.ThreadPoolExecutor(
            max_workers=self.io_workers, thread_name_prefix="IOWorker")

        # 網絡任務執行器 - 專用線程池
        self.executors[TaskType.NETWORK] = concurrent.futures.ThreadPoolExecutor(max_workers=self.network_workers, thread_name_prefix="NetworkWorker")

        # 數據庫任務執行器 - 專用線程池
        self.executors[TaskType.DATABASE] = concurrent.futures.ThreadPoolExecutor(max_workers=self.database_workers, thread_name_prefix="DatabaseWorker")

        # 混合型任務使用IO執行器
        self.executors[TaskType.MIXED] = self.executors[TaskType.IO_BOUND]

        logger.info(f"執行器創建完成: {list(self.executors.keys())}")

    def _init_task_queues(self):
        """初始化任務隊列"""
        for task_type in TaskType:
            self.task_queues[task_type] = asyncio.PriorityQueue()

    async def start(self):
        """啟動執行器管理器"""
        # 啟動監控任務
        if self.adaptive_enabled:
            self._monitor_task = asyncio.create_task(self._monitor_loop())

        logger.info("多執行器管理器已啟動")

    async def submit_task(self,)
        func: Callable,
        task_type: TaskType,
        priority: TaskPriority = TaskPriority.NORMAL,
        timeout: Optional[float] = None,
        *args,
        **kwargs,) -> Any:
        """提交任務"""
        task_id = f"{task_type.value}_{int(time.time() * 1000000)}"

        # 創建任務指標
        metrics = TaskMetrics(task_id=task_id, task_type=task_type, priority=priority, submitted_at=time.time())
        self.task_metrics[task_id] = metrics

        # 更新統計
        self.stats["total_submitted"] += 1
        self.stats["by_type"][task_type]["submitted"] += 1

        try:
            # 選擇執行器
            executor = self.executors[task_type]

            # 記錄開始時間
            metrics.started_at = time.time()

            # 提交任務
            loop = asyncio.get_event_loop()

            if timeout:
                future = asyncio.wait_for(loop.run_in_executor(executor, func, *args, **kwargs), timeout=timeout)
            else:
                future = loop.run_in_executor(executor, func, *args, **kwargs)

            # 執行任務
            result = await future

            # 記錄完成
            metrics.completed_at = time.time()
            metrics.execution_time = metrics.completed_at - metrics.started_at
            metrics.success = True

            # 更新統計
            self.stats["total_completed"] += 1
            self.stats["by_type"][task_type]["completed"] += 1
            self._update_avg_times(task_type, metrics)

            logger.debug(f"任務完成: {task_id} ({metrics.execution_time:.3f}s)")
            return result

        except Exception as e:
            # 記錄失敗
            metrics.completed_at = time.time()
            metrics.success = False
            metrics.error = str(e)

            # 更新統計
            self.stats["total_failed"] += 1
            self.stats["by_type"][task_type]["failed"] += 1

            logger.error(f"任務失敗: {task_id} - {e}")
            raise
        finally:
            # 清理任務指標（保留最近1000個）
            if len(self.task_metrics) > 1000:
                old_tasks = sorted(self.task_metrics.items(), key=lambda x: x[1].submitted_at)[:-500
                ]
                for old_task_id, _ in old_tasks:
                    del self.task_metrics[old_task_id]

    def _update_avg_times(self, task_type: TaskType, metrics: TaskMetrics):
        """更新平均時間統計"""
        if task_type not in self.stats["avg_execution_time"]:
            self.stats["avg_execution_time"][task_type] = []
            self.stats["avg_queue_time"][task_type] = []

        # 保留最近100個樣本
        exec_times = self.stats["avg_execution_time"][task_type]
        queue_times = self.stats["avg_queue_time"][task_type]

        if metrics.execution_time:
            exec_times.append(metrics.execution_time)
            if len(exec_times) > 100:
                exec_times.pop(0)

        if metrics.queue_time:
            queue_times.append(metrics.queue_time)
            if len(queue_times) > 100:
                queue_times.pop(0)

    async def submit_cpu_task(self, func: Callable, *args, **kwargs) -> Any:
        """提交CPU密集型任務"""
        return await self.submit_task(func, TaskType.CPU_BOUND, *args, **kwargs)

    async def submit_io_task(self, func: Callable, *args, **kwargs) -> Any:
        """提交IO密集型任務"""
        return await self.submit_task(func, TaskType.IO_BOUND, *args, **kwargs)

    async def submit_network_task(self, func: Callable, *args, **kwargs) -> Any:
        """提交網絡任務"""
        return await self.submit_task(func, TaskType.NETWORK, *args, **kwargs)

    async def submit_database_task(self, func: Callable, *args, **kwargs) -> Any:
        """提交數據庫任務"""
        return await self.submit_task(func, TaskType.DATABASE, *args, **kwargs)

    async def _monitor_loop(self):
        """監控循環"""
        while True:
            try:
                await asyncio.sleep(self.monitor_interval)
                await self._adaptive_adjustment()

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"執行器監控異常: {e}")

    async def _adaptive_adjustment(self):
        """自適應調整"""
        try:
            # 獲取系統資源使用情況
            cpu_percent = psutil.cpu_percent(interval=1)
            memory_percent = psutil.virtual_memory().percent

            # 分析任務執行情況
            for task_type in TaskType:
                if task_type in self.stats["avg_execution_time"]:
                    exec_times = self.stats["avg_execution_time"][task_type]
                    queue_times = self.stats["avg_queue_time"][task_type]

                    if exec_times and queue_times:
                        avg_exec = sum(exec_times) / len(exec_times)
                        avg_queue = sum(queue_times) / len(queue_times)

                        # 如果排隊時間過長，考慮調整執行器大小
                        if avg_queue > avg_exec * 2:  # 排隊時間超過執行時間2倍
                            await self._suggest_executor_adjustment(task_type, "increase")
                        elif avg_queue < avg_exec * 0.1:  # 排隊時間很短
                            await self._suggest_executor_adjustment(task_type, "decrease")

            logger.debug(f"系統監控: CPU={cpu_percent}%, 內存={memory_percent}%")

        except Exception as e:
            logger.error(f"自適應調整失敗: {e}")

    async def _suggest_executor_adjustment(self, task_type: TaskType, action: str):
        """建議執行器調整"""
        current_workers = self._get_executor_size(task_type)

        if action == "increase" and current_workers < 50:  # 最大限制
            suggested = min(current_workers + 2, 50)
            logger.info(f"建議增加 {task_type.value} 執行器: {current_workers} -> {suggested}")
        elif action == "decrease" and current_workers > 2:  # 最小限制
            suggested = max(current_workers - 1, 2)
            logger.info(f"建議減少 {task_type.value} 執行器: {current_workers} -> {suggested}")

    def _get_executor_size(self, task_type: TaskType) -> int:
        """獲取執行器大小"""
        executor = self.executors[task_type]
        if hasattr(executor, "_max_workers"):
            return executor._max_workers
        return 0

    def get_executor_stats(self) -> Dict[str, Any]:
        """獲取執行器統計"""
        executor_info = {}

        for task_type, executor in self.executors.items():
            if hasattr(executor, "_threads"):  # ThreadPoolExecutor
                active_threads = len([t for t in executor._threads if t.is_alive()])
                executor_info[task_type.value] = {
                    "type": "ThreadPoolExecutor",
                    "max_workers": executor._max_workers,
                    "active_threads": active_threads,
                }
            elif hasattr(executor, "_processes"):  # ProcessPoolExecutor
                executor_info[task_type.value] = {
                    "type": "ProcessPoolExecutor",
                    "max_workers": executor._max_workers,
                    "active_processes": len(executor._processes),
                }

        # 計算平均時間
        avg_times = {}
        for task_type in TaskType:
            if task_type in self.stats["avg_execution_time"]:
                exec_times = self.stats["avg_execution_time"][task_type]
                queue_times = self.stats["avg_queue_time"][task_type]

                avg_times[task_type.value] = {
                    "avg_execution_time": sum(exec_times) / len(exec_times) if exec_times else 0,
                    "avg_queue_time": sum(queue_times) / len(queue_times) if queue_times else 0,
                }

        return {
            "executors": executor_info,
            "stats": self.stats,
            "avg_times": avg_times,
            "active_tasks": len(self.running_tasks),
            "total_metrics": len(self.task_metrics),
        }

    async def shutdown(self) -> bool:
        """關閉執行器管理器"""
        try:
            logger.info("關閉多執行器管理器...")

            # 停止監控任務
            if self._monitor_task:
                self._monitor_task.cancel()
                try:
                    await self._monitor_task
                except asyncio.CancelledError:
                    pass

            # 關閉所有執行器
            for task_type, executor in self.executors.items():
                logger.info(f"關閉 {task_type.value} 執行器...")
                executor.shutdown(wait=True)

            # 清理任務指標
            self.task_metrics.clear()
            self.running_tasks.clear()

            logger.info("多執行器管理器關閉完成")
            return True

        except Exception as e:
            logger.error(f"關閉多執行器管理器失敗: {e}")
            return False


# 全局執行器管理器實例
_executor_manager: Optional[MultiExecutorManager] = None


def get_multi_executor_manager(config: Dict[str, Any] = None) -> MultiExecutorManager:
    """獲取全局執行器管理器實例"""
    global _executor_manager
    if _executor_manager is None:
        _executor_manager = MultiExecutorManager(config)

        # 自動註冊到停機管理器
        try:
            from graceful_shutdown import get_shutdown_manager

            get_shutdown_manager().register_component(_executor_manager)
        except ImportError:
            logger.warning("無法註冊到停機管理器")

    return _executor_manager


# 便利函數
async def submit_cpu_task(func: Callable, *args, **kwargs) -> Any:
    """提交CPU密集型任務的便利函數"""
    manager = get_multi_executor_manager()
    return await manager.submit_cpu_task(func, *args, **kwargs)


async def submit_io_task(func: Callable, *args, **kwargs) -> Any:
    """提交IO密集型任務的便利函數"""
    manager = get_multi_executor_manager()
    return await manager.submit_io_task(func, *args, **kwargs)


async def submit_network_task(func: Callable, *args, **kwargs) -> Any:
    """提交網絡任務的便利函數"""
    manager = get_multi_executor_manager()
    return await manager.submit_network_task(func, *args, **kwargs)


async def submit_database_task(func: Callable, *args, **kwargs) -> Any:
    """提交數據庫任務的便利函數"""
    manager = get_multi_executor_manager()
    return await manager.submit_database_task(func, *args, **kwargs)


# 裝飾器
def cpu_task(func: Callable) -> Callable:
    """CPU任務裝飾器"""

    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        return await submit_cpu_task(func, *args, **kwargs)

    return wrapper


def io_task(func: Callable) -> Callable:
    """IO任務裝飾器"""

    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        return await submit_io_task(func, *args, **kwargs)

    return wrapper


def network_task(func: Callable) -> Callable:
    """網絡任務裝飾器"""

    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        return await submit_network_task(func, *args, **kwargs)

    return wrapper


def database_task(func: Callable) -> Callable:
    """數據庫任務裝飾器"""

    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        return await submit_database_task(func, *args, **kwargs)

    return wrapper


async def main():
    """測試多執行器管理器"""
    print("🧪 測試多執行器管理器")

    # 創建執行器管理器
    config = {
        "cpu_workers": 2,
        "io_workers": 4,
        "network_workers": 3,
        "database_workers": 2,
        "adaptive_enabled": True,
        "monitor_interval": 5,
    }

    executor_manager = get_multi_executor_manager(config)

    try:
        # 啟動管理器
        await executor_manager.start()

        # 測試CPU密集型任務
        print("測試CPU密集型任務...")

        def cpu_intensive_task(n):
            """CPU密集型任務示例"""
            result = 0
            for _ in range(n):
                result += i**2
            return result

        start_time = time.time()
        result = await executor_manager.submit_cpu_task(cpu_intensive_task, 1000000)
        cpu_time = time.time() - start_time
        print(f"  ✅ CPU任務完成: 結果={result}, 耗時={cpu_time:.3f}s")

        # 測試IO密集型任務
        print("測試IO密集型任務...")

        def io_intensive_task():
            """IO密集型任務示例"""
            import time

            time.sleep(0.1)  # 模擬IO等待
            return "IO任務完成"

        start_time = time.time()
        result = await executor_manager.submit_io_task(io_intensive_task)
        io_time = time.time() - start_time
        print(f"  ✅ IO任務完成: 結果={result}, 耗時={io_time:.3f}s")

        # 測試並發執行
        print("測試並發執行...")

        tasks = []
        for _ in range(5):
            tasks.append(executor_manager.submit_cpu_task(cpu_intensive_task, 100000))
            tasks.append(executor_manager.submit_io_task(io_intensive_task))

        start_time = time.time()
        results = await asyncio.gather(*tasks)
        concurrent_time = time.time() - start_time
        print(f"  ✅ 並發執行完成: {len(results)}個任務, 耗時={concurrent_time:.3f}s")

        # 獲取統計信息
        stats = executor_manager.get_executor_stats()
        print("  📊 執行器統計:")
        print(f"    總提交: {stats['stats']['total_submitted']}")
        print(f"    總完成: {stats['stats']['total_completed']}")
        print(f"    總失敗: {stats['stats']['total_failed']}")

    finally:
        # 測試關閉
        success = await executor_manager.shutdown()
        print(f"  {'✅' if success else '❌'} 關閉結果: {success}")

    print("✅ 多執行器管理器測試完成")


if __name__ == "__main__":
    asyncio.run(main())
