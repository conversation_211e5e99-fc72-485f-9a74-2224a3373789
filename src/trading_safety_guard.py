from typing import Any, Dict
import logging

# 配置logger
logger = logging.getLogger(__name__)

#!/usr/bin/env python3
"""
交易安全防護系統 - 防止意外的真實交易
Trading Safety Guard - Prevent accidental real trading
"""

import os
from datetime import datetime
from enum import Enum

from logging_config import get_logger

_ = get_logger(__name__)


class TradingMode(Enum):
    """交易模式"""

    _ = "sandbox"
    _ = "paper"
    _ = "live"


class SafetyLevel(Enum):
    """安全級別"""

    _ = "safe"  # 沙盒或紙上交易
    _ = "warning"  # 即將進入真實交易
    _ = "danger"  # 真實交易模式


class TradingSafetyGuard:
    """交易安全防護器"""

    def __init__(self):
        self.safety_checks_enabled = True
        self.confirmation_required = True
        self.max_daily_loss = 1000  # 最大日損失（美元）
        self.max_position_size = 10000  # 最大倉位（美元）
        self.daily_loss_tracker = 0
        self.last_reset_date = datetime.now().date()

        logger.info("交易安全防護系統已啟動")

    def validate_trading_environment(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """驗證交易環境安全性"""
        _ = {
            "safe_to_trade": False,
            "trading_mode": TradingMode.SANDBOX,
            "safety_level": SafetyLevel.SAFE,
            "warnings": [],
            "errors": [],
            "recommendations": [],
        }

        try:
            # 檢查交易所配置
            exchange_config = config.get("exchange", {})
            sandbox_mode = exchange_config.get("sandbox", True)

            # 檢查API密鑰
            api_key = os.environ.get("TRADING_API_KEY", "")
            secret = os.environ.get("TRADING_SECRET", "")

            # 檢查環境變量
            _ = os.environ.get("ENVIRONMENT", "development")

            # 1. 檢查沙盒模式
            if sandbox_mode:
                validation_result["trading_mode"] = TradingMode.SANDBOX
                validation_result["safety_level"] = SafetyLevel.SAFE
                validation_result["safe_to_trade"] = True
                logger.info("✅ 沙盒模式：安全的測試環境")
            else:
                validation_result["trading_mode"] = TradingMode.LIVE
                validation_result["safety_level"] = SafetyLevel.DANGER
                validation_result["warnings"].append("⚠️ 真實交易模式：將使用真實資金")

            # 2. 檢查API密鑰
            if self._is_test_api_key(api_key, secret):
                validation_result["recommendations"].append("使用測試API密鑰，安全")
            elif not api_key or not secret:
                validation_result["errors"].append("❌ API密鑰未設置")
            else:
                if not sandbox_mode:
                    validation_result["warnings"].append("⚠️ 使用真實API密鑰且非沙盒模式")

            # 3. 檢查環境設置
            if environment == "production" and sandbox_mode:
                validation_result["warnings"].append("⚠️ 生產環境但使用沙盒模式")
            elif environment == "development" and not sandbox_mode:
                validation_result["warnings"].append("⚠️ 開發環境但使用真實交易模式")

            # 4. 安全性評估
            if validation_result["errors"]:
                validation_result["safe_to_trade"] = False
            elif validation_result["trading_mode"] == TradingMode.LIVE:
                # 真實交易需要額外確認
                validation_result["safe_to_trade"] = self._require_live_trading_confirmation()

            # 5. 生成建議
            self._generate_safety_recommendations(validation_result)

            return validation_result

        except Exception as e:
            logger.error(f"交易環境驗證失敗: {e}")
            validation_result["errors"].append(f"驗證失敗: {e}")
            return validation_result

    def _is_test_api_key(self, api_key: str, secret: str) -> bool:
        """檢查是否為測試API密鑰"""
        test_indicators = [
            "test",
            "sandbox",
            "demo",
            "fake",
            "mock",
            "your_api_key",
            "your_secret",
            "example",
        ]

        api_key_lower = api_key.lower()
        secret_lower = secret.lower()

        for indicator in test_indicators:
            if indicator in api_key_lower or indicator in secret_lower:
                return True

        return False

    def _require_live_trading_confirmation(self) -> bool:
        """要求真實交易確認"""
        if not self.confirmation_required:
            return True

        logger.warning("🚨 檢測到真實交易模式！")
        logger.warning("這將使用真實資金進行交易，可能導致資金損失！")

        # 檢查高槓桿風險
        leverage = os.environ.get("TRADING_LEVERAGE", "1")
        margin_mode = os.environ.get("TRADING_MARGIN_MODE", "isolated")

        if int(leverage) >= 10:
            logger.critical("🚨🚨🚨 檢測到高槓桿交易！")
            logger.critical(f"槓桿倍數: {leverage}x")
            logger.critical(f"保證金模式: {margin_mode}")
            logger.critical("高槓桿交易風險極大，可能導致快速且巨大的損失！")

            # 高槓桿需要額外確認
            high_leverage_confirmation = ".high_leverage_confirmation"
            if not os.path.exists(high_leverage_confirmation):
                logger.error("❌ 高槓桿交易需要額外確認")
                logger.error("請先運行風險檢查：")
                logger.error()
                    "python3 high_leverage_risk_checker.py --config config.json --acknowledge-risks"
                )
                logger.error()
                    f"然後創建確認文件：echo 'HIGH_LEVERAGE_CONFIRMED' > {high_leverage_confirmation}"
                )
                return False

        # 在生產環境中，這裡應該實現更嚴格的確認機制
        # 例如：要求輸入特定的確認碼、多重簽名等

        confirmation_file = ".trading_confirmation"
        if os.path.exists(confirmation_file):
            logger.info("找到交易確認文件，允許真實交易")
            return True
        else:
            logger.error("❌ 未找到交易確認文件，拒絕真實交易")
            logger.error("如需進行真實交易，請創建確認文件：")
            logger.error(f"echo 'CONFIRMED' > {confirmation_file}")
            return False

    def _generate_safety_recommendations(self, validation_result: Dict[str, Any]):
        """生成安全建議"""
        recommendations = validation_result["recommendations"]

        if validation_result["trading_mode"] == TradingMode.LIVE:
            recommendations.extend()
                ["🔒 建議先在沙盒環境充分測試", "💰 建議設置較小的倉位大小", "📊 建議先進行回測驗證策略", "⏰ 建議設置交易時間限制", "🛑 建議設置緊急停止機制"]
            )

        if validation_result["safety_level"] == SafetyLevel.DANGER:
            recommendations.extend()
                ["⚠️ 強烈建議使用風險管理工具", "📱 建議啟用實時監控和警報", "💾 建議定期備份交易數據", "🔄 建議設置自動止損機制"]
            )

    def check_position_limits(self, position_size_usd: float) -> bool:
        """檢查倉位限制"""
        if position_size_usd > self.max_position_size:
            logger.error(f"❌ 倉位大小超出限制: {position_size_usd} > {self.max_position_size}")
            return False

        return True

    def check_daily_loss_limit(self, current_loss: float) -> bool:
        """檢查日損失限制"""
        # 重置日計數器
        today = datetime.now().date()
        if today != self.last_reset_date:
            self.daily_loss_tracker = 0
            self.last_reset_date = today

        total_loss = self.daily_loss_tracker + abs(current_loss)

        if total_loss > self.max_daily_loss:
            logger.error(f"❌ 日損失超出限制: {total_loss} > {self.max_daily_loss}")
            return False

        # 更新損失追蹤
        if current_loss < 0:
            self.daily_loss_tracker += abs(current_loss)

        return True

    def create_trading_confirmation_file(self):
        """創建交易確認文件（僅用於測試）"""
        confirmation_file = ".trading_confirmation"

        with open(confirmation_file, "w") as f:
            f.write("TRADING_CONFIRMED\n")
            f.write(f"TIMESTAMP: {datetime.now().isoformat()}\n")
            f.write("WARNING: This enables real trading with real money!\n")

        logger.warning(f"⚠️ 已創建交易確認文件: {confirmation_file}")
        logger.warning("這將允許系統進行真實交易！")

    def remove_trading_confirmation_file(self):
        """移除交易確認文件"""
        confirmation_file = ".trading_confirmation"

        if os.path.exists(confirmation_file):
            os.remove(confirmation_file)
            logger.info(f"✅ 已移除交易確認文件: {confirmation_file}")
        else:
            logger.info("交易確認文件不存在")

    def get_safety_status(self) -> Dict[str, Any]:
        """獲取安全狀態"""
        return {
            "safety_checks_enabled": self.safety_checks_enabled,
            "confirmation_required": self.confirmation_required,
            "max_daily_loss": self.max_daily_loss,
            "max_position_size": self.max_position_size,
            "daily_loss_tracker": self.daily_loss_tracker,
            "last_reset_date": self.last_reset_date.isoformat(),
            "confirmation_file_exists": os.path.exists(".trading_confirmation"),
        }


# 全局安全防護實例
_safety_guard = None


def get_safety_guard() -> TradingSafetyGuard:
    """獲取全局安全防護實例"""
    global _safety_guard
    if _safety_guard is None:
        _safety_guard = TradingSafetyGuard()
    return _safety_guard


def validate_trading_safety(config: Dict[str, Any]) -> Dict[str, Any]:
    """驗證交易安全性的便利函數"""
    safety_guard = get_safety_guard()
    return safety_guard.validate_trading_environment(config)


if __name__ == "__main__":
    # 測試安全防護系統
    test_config = {"exchange": {"name": "binance", "sandbox": True}}

    safety_guard = get_safety_guard()
    result = safety_guard.validate_trading_environment(test_config)

    print("=== 交易安全驗證結果 ===")
    print(f"安全交易: {result['safe_to_trade']}")
    print(f"交易模式: {result['trading_mode'].value}")
    print(f"安全級別: {result['safety_level'].value}")

    if result["warnings"]:
        print("\n警告:")
        for warning in result["warnings"]:
            print(f"  {warning}")

    if result["errors"]:
        print("\n錯誤:")
        for error in result["errors"]:
            print(f"  {error}")

    if result["recommendations"]:
        print("\n建議:")
        for rec in result["recommendations"]:
            print(f"  {rec}")
