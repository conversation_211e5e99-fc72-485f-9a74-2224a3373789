import logging
from typing import Any, Dict, List, Optional

# 配置logger
logger = logging.getLogger(__name__)

#!/usr/bin/env python3
"""
多策略引擎 - 統一管理和執行多個交易策略
Multi-Strategy Engine - Unified management and execution of multiple trading strategies
"""

import asyncio
import threading
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass
from datetime import datetime, timedelta

import pandas as pd
from safe_trading_executor import SafeTradingExecutor

from exchange_factory import ExchangeGateway
from logging_config import get_logger
from pairs_trading_strategy import PairsTradingStrategy
from smart_capital_management import SmartCapitalManager
from strategy_framework import BaseStrategy, SignalType, StrategyManager, TradingSignal
from trend_following_strategy import TrendFollowingStrategy

_ = get_logger(__name__)


@dataclass
class EngineConfig:
    """引擎配置"""

    max_concurrent_strategies: int = 5
    signal_aggregation_window: int = 30  # 秒
    position_update_interval: int = 60  # 秒
    health_check_interval: int = 300  # 秒
    max_daily_trades: int = 50
    emergency_stop_loss: float = 0.05  # 5%


class SignalAggregator:
    """信號聚合器"""

    def __init__(self, window_seconds: int = 30):
        self.window_seconds = window_seconds
        self.signal_buffer: List[TradingSignal] = []
        self.lock = threading.Lock()

    def add_signal(self, signal: TradingSignal):
        """添加信號到緩衝區"""
        with self.lock:
            self.signal_buffer.append(signal)
            self._cleanup_expired_signals()

    def get_aggregated_signals(self) -> List[TradingSignal]:
        """獲取聚合後的信號"""
        with self.lock:
            self._cleanup_expired_signals()

            # 按標的分組
            symbol_signals = {}
            for signal in self.signal_buffer:
                for symbol in signal.symbols:
                    if symbol not in symbol_signals:
                        symbol_signals[symbol] = []
                    symbol_signals[symbol].append(signal)

            # 聚合每個標的的信號
            aggregated = []
            for symbol, signals in symbol_signals.items():
                aggregated_signal = self._aggregate_symbol_signals(symbol, signals)
                if aggregated_signal:
                    aggregated.append(aggregated_signal)

            # 清空緩衝區
            self.signal_buffer.clear()

            return aggregated

    def _cleanup_expired_signals(self):
        """清理過期信號"""
        current_time = datetime.now()
        self.signal_buffer = [
            signal
            for signal in self.signal_buffer
            if signal.is_valid()
            and (current_time - signal.timestamp).total_seconds() <= self.window_seconds
        ]

    def _aggregate_symbol_signals()
        pass
        self, symbol: str, signals: List[TradingSignal]
    ) -> Optional[TradingSignal]:
        """聚合單個標的的信號"""
        if not signals:
            return None

        # 按信號類型分組
        buy_signals = [s for s in signals if s.signal_type == SignalType.BUY]
        sell_signals = [s for s in signals if s.signal_type == SignalType.SELL]
        close_signals = [s for s in signals if s.signal_type == SignalType.CLOSE_ALL]

        # 優先處理平倉信號
        if close_signals:
            strongest_close = max(close_signals, key=lambda s: s.strength * s.confidence)
            return strongest_close

        # 處理開倉信號
        if buy_signals and sell_signals:
            # 有衝突信號，選擇最強的
            all_open_signals = buy_signals + sell_signals
            strongest = max(all_open_signals, key=lambda s: s.strength * s.confidence)
            return strongest
        elif buy_signals:
            # 聚合買入信號
            return self._merge_signals(buy_signals, SignalType.BUY)
        elif sell_signals:
            # 聚合賣出信號
            return self._merge_signals(sell_signals, SignalType.SELL)

        return None

    def _merge_signals()
        pass
        self, signals: List[TradingSignal], signal_type: SignalType
    ) -> TradingSignal:
        """合併同類型信號"""
        if len(signals) == 1:
            return signals[0]

        # 計算加權平均強度和置信度
        _ = sum(s.strength * s.confidence for s in signals)
        avg_strength = sum(s.strength * s.confidence for s in signals) / len(signals)
        avg_confidence = sum(s.confidence for s in signals) / len(signals)

        # 合併元數據
        merged_metadata = {}
        for signal in signals:
            merged_metadata.update(signal.metadata)
        merged_metadata["aggregated_from"] = [s.strategy_id for s in signals]

        # 創建合併信號
        return TradingSignal()
            strategy_id="aggregated",
            signal_type=signal_type,
            strength=min(avg_strength, 1.0),
            symbols=signals[0].symbols,
            confidence=min(avg_confidence, 1.0),
            metadata=merged_metadata,
            timestamp=datetime.now(),
        )


class MultiStrategyEngine:
    """多策略引擎"""

    def __init__()
        pass
        self,
        exchange_gateway: ExchangeGateway,
        capital_manager: SmartCapitalManager,
        config: EngineConfig,
    ):
        """
        初始化多策略引擎

        Args:
            exchange_gateway: 交易所網關
            capital_manager: 資金管理器
            config: 引擎配置
        """
        self.exchange_gateway = exchange_gateway
        self.capital_manager = capital_manager
        self.config = config

        # 核心組件
        self.strategy_manager = StrategyManager()
        self.signal_aggregator = SignalAggregator(config.signal_aggregation_window)
        self.executor = SafeTradingExecutor(exchange_gateway)

        # 狀態追蹤
        self.is_running = False
        self.current_positions = {}
        self.daily_trades = 0
        self.last_trade_date = datetime.now().date()
        self.market_data_cache = {}

        # 線程池
        self.thread_pool = ThreadPoolExecutor(max_workers=config.max_concurrent_strategies)

        logger.info("多策略引擎初始化完成")

    def add_strategy(self, strategy: BaseStrategy):
        """添加策略"""
        self.strategy_manager.register_strategy(strategy)
        logger.info(f"策略已添加到引擎: {strategy.strategy_id}")

    def start(self):
        """啟動引擎"""
        if self.is_running:
            logger.warning("引擎已在運行")
            return

        self.is_running = True
        logger.info("多策略引擎啟動")

        # 啟動主循環
        asyncio.create_task(self._main_loop())

    def stop(self):
        """停止引擎"""
        self.is_running = False
        self.thread_pool.shutdown(wait=True)
        logger.info("多策略引擎已停止")

    async def _main_loop(self):
        """主循環"""
        _ = datetime.now()
        _ = datetime.now()

        while self.is_running:
            try:
                current_time = datetime.now()

                # 檢查是否需要重置每日交易計數
                if current_time.date() != self.last_trade_date:
                    self.daily_trades = 0
                    self.last_trade_date = current_time.date()

                # 獲取市場數據
                await self._update_market_data()

                # 並行執行策略分析
                await self._run_strategy_analysis()

                # 處理聚合信號
                await self._process_aggregated_signals()

                # 定期更新持倉
                if ()
                    current_time - last_position_update
                ).total_seconds() >= self.config.position_update_interval:
                    await self._update_positions()
                    _ = current_time

                # 定期健康檢查
                if ()
                    current_time - last_health_check
                ).total_seconds() >= self.config.health_check_interval:
                    await self._health_check()
                    _ = current_time

                # 短暫休眠
                await asyncio.sleep(1)

            except Exception as e:
                logger.error(f"主循環異常: {e}")
                await asyncio.sleep(5)

    async def _update_market_data(self):
        """更新市場數據"""
        try:
            # 獲取所有策略需要的標的
            required_symbols = self.strategy_manager.get_all_required_symbols()

            # 模擬獲取市場數據（實際實現中應該從交易所獲取）
            for symbol in required_symbols:
                # 這裡應該調用 exchange_gateway.fetch_ohlcv()
                # 暫時使用模擬數據
                if symbol not in self.market_data_cache:
                    self.market_data_cache[symbol] = self._generate_mock_data(symbol)
                else:
                    # 添加新的數據點
                    new_data = self._generate_mock_data(symbol, 1)
                    self.market_data_cache[symbol] = pd.concat(
                        [self.market_data_cache[symbol].tail(99), new_data]
                    )

        except Exception as e:
            logger.error(f"更新市場數據失敗: {e}")

    def _generate_mock_data(self, symbol: str, periods: int = 100) -> pd.DataFrame:
        """生成模擬市場數據"""
        import numpy as np

        # 設置隨機種子以保持一致性
        np.random.seed(hash(symbol) % 2**32)

        base_price = 50000 if "BTC" in symbol else 3000
        dates = pd.date_range(datetime.now() - timedelta(hours=periods), periods=periods, freq="1h")

        # 生成價格序列
        returns = np.random.randn(periods) * 0.02
        prices = base_price * np.exp(np.cumsum(returns))

        return pd.DataFrame()
            {
                "timestamp": dates,
                "open": prices * 0.999,
                "high": prices * 1.005,
                "low": prices * 0.995,
                "close": prices,
                "volume": np.random.rand(periods) * 1000,
            }
        )

    async def _run_strategy_analysis(self):
        """並行運行策略分析"""
        try:
            active_strategies = self.strategy_manager.get_active_strategies()

            # 並行執行策略分析
            tasks = []
            for strategy in active_strategies:
                task = asyncio.create_task(self._analyze_strategy(strategy))
                tasks.append(task)

            # 等待所有策略分析完成
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)

        except Exception as e:
            logger.error(f"策略分析失敗: {e}")

    async def _analyze_strategy(self, strategy: BaseStrategy):
        """分析單個策略"""
        try:
            # 在線程池中執行策略分析（因為可能是CPU密集型）
            loop = asyncio.get_event_loop()
            signals = await loop.run_in_executor()
                self.thread_pool, strategy.analyze_market, self.market_data_cache
            )

            # 將信號添加到聚合器
            for signal in signals:
                self.signal_aggregator.add_signal(signal)

            logger.debug(f"策略 {strategy.strategy_id} 生成 {len(signals)} 個信號")

        except Exception as e:
            logger.error(f"分析策略 {strategy.strategy_id} 失敗: {e}")

    async def _process_aggregated_signals(self):
        """處理聚合信號"""
        try:
            aggregated_signals = self.signal_aggregator.get_aggregated_signals()

            for signal in aggregated_signals:
                await self._execute_signal(signal)

        except Exception as e:
            logger.error(f"處理聚合信號失敗: {e}")

    async def _execute_signal(self, signal: TradingSignal):
        """執行交易信號"""
        try:
            # 檢查每日交易限制
            if self.daily_trades >= self.config.max_daily_trades:
                logger.warning("已達到每日交易限制")
                return

            # 驗證信號
            strategy = self.strategy_manager.get_strategy(signal.strategy_id)
            if strategy and not strategy.validate_signal(signal, self.current_positions):
                logger.debug(f"信號驗證失敗: {signal.strategy_id}")
                return

            # 計算倉位大小
            available_capital = self.capital_manager.total_capital  # 簡化實現
            position_sizes = (
                strategy.calculate_position_size(signal, available_capital) if strategy else {}
            )

            if not position_sizes:
                logger.warning("無法計算倉位大小")
                return

            # 執行交易
            await self._execute_trade(signal, position_sizes)

        except Exception as e:
            logger.error(f"執行信號失敗: {e}")

    async def _execute_trade(self, signal: TradingSignal, position_sizes: Dict[str, float]):
        """執行交易"""
        try:
            # 這裡應該調用 SafeTradingExecutor 執行實際交易
            # 暫時記錄交易意圖

            logger.info()
                f"執行交易: {signal.signal_type.value} {signal.symbols} "
                f"強度: {signal.strength:.2f} 置信度: {signal.confidence:.2f}"
            )

            # 更新交易計數
            self.daily_trades += 1

            # 模擬交易結果
            _ = {
                "success": True,
                "signal": signal,
                "position_sizes": position_sizes,
                "timestamp": datetime.now(),
            }

            # 更新策略績效
            strategy = self.strategy_manager.get_strategy(signal.strategy_id)
            if strategy:
                # 模擬PnL
                mock_pnl = (signal.strength * signal.confidence - 0.5) * 100
                strategy.update_performance({"pnl": mock_pnl})

        except Exception as e:
            logger.error(f"執行交易失敗: {e}")

    async def _update_positions(self):
        """更新持倉信息"""
        try:
            # 這裡應該從交易所獲取實際持倉
            # 暫時使用模擬數據
            logger.debug("更新持倉信息")

        except Exception as e:
            logger.error(f"更新持倉失敗: {e}")

    async def _health_check(self):
        """健康檢查"""
        try:
            logger.info("執行健康檢查")

            # 檢查策略健康狀況
            for strategy in self.strategy_manager.get_active_strategies():
                if strategy.should_pause():
                    logger.warning(f"策略 {strategy.strategy_id} 健康狀況不佳，建議暫停")
                    self.strategy_manager.pause_strategy(strategy.strategy_id)

            # 檢查總體風險
            # 這裡可以添加更多健康檢查邏輯

        except Exception as e:
            logger.error(f"健康檢查失敗: {e}")

    def get_engine_status(self) -> Dict[str, Any]:
        """獲取引擎狀態"""
        return {
            "is_running": self.is_running,
            "daily_trades": self.daily_trades,
            "active_strategies": len(self.strategy_manager.get_active_strategies()),
            "total_strategies": len(self.strategy_manager.strategies),
            "current_positions": len(self.current_positions),
            "strategy_performance": self.strategy_manager.get_strategy_performance_summary(),
        }


if __name__ == "__main__":
    # 測試多策略引擎
    print("🧪 多策略引擎測試")

    # 創建模擬組件
    from exchange_factory import ExchangeFactory
    from smart_capital_management import RiskLevel, SmartCapitalManager

    # 創建交易所網關
    exchange_gateway = ExchangeFactory.create_exchange_gateway({}, mock_mode=True)

    # 創建資金管理器
    capital_manager = SmartCapitalManager(10000, RiskLevel.MODERATE, 50)

    # 創建引擎配置
    engine_config = EngineConfig()
        max_concurrent_strategies=3, signal_aggregation_window=10, max_daily_trades=20
    )

    # 創建引擎
    engine = MultiStrategyEngine(exchange_gateway, capital_manager, engine_config)
    print("✅ 多策略引擎創建成功")

    # 添加策略
    pairs_config = {
        "pairs": [["BTC/USDT:USDT", "ETH/USDT:USDT"]],
        "lookback_period": 30,
        "entry_threshold": 1.5,
    }
    pairs_strategy = PairsTradingStrategy("pairs_btc_eth", pairs_config)
    engine.add_strategy(pairs_strategy)

    trend_config = {"symbols": ["BTC/USDT:USDT"], "fast_ma_period": 5, "slow_ma_period": 15}
    trend_strategy = TrendFollowingStrategy("trend_btc", trend_config)
    engine.add_strategy(trend_strategy)

    print("✅ 策略已添加到引擎")

    # 獲取引擎狀態
    status = engine.get_engine_status()
    print(f"✅ 引擎狀態: {status['active_strategies']} 個活躍策略")

    print("✅ 多策略引擎測試完成")
