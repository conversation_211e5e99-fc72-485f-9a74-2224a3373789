import logging
from typing import Dict

# 配置logger
logger = logging.getLogger(__name__)

#!/usr/bin/env python3
"""
驗證所有改進功能的腳本
"""

import json
import os
import sys
from datetime import datetime

import numpy as np
import pandas as pd

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from trading_executor import TradingExecutor

from data_handler import DataHandler
from logging_config import get_logger, setup_logging
from pair_trading_bot import PairTradingBot, SignalState, TradingState

# 設置日誌
setup_logging()  # 修復不完整調用
logger = get_logger(__name__)


class ImprovementValidator:
    """改進功能驗證器"""

    def __init__(self):
        self.test_results = {}
        logger.info("改進功能驗證器初始化完成")

    def validate_signal_state_reset(self) -> bool:
        """驗證信號狀態重置邏輯"""
        try:
            logger.info("驗證信號狀態重置邏輯...")

            # 創建模擬配置
            config = {
                "trading_pair": ["BTCUSDT", "ETHUSDT"],
                "timeframe": "1m",
                "lookback_period": 20,
                "entry_threshold_high": 2.0,
                "entry_threshold_low": -2.0,
                "confirmation_threshold_high": 1.5,
                "confirmation_threshold_low": -1.5,
                "cooldown_period": 5,
                "stop_loss_pct": 0.01,
                "take_profit_target": "zero_crossing",
                "position_size_usd": 1000,
                "exchange": {"name": "binance", "sandbox": True},
            }

            # 創建機器人實例
            bot = PairTradingBot.__new__(PairTradingBot)
            bot.config = config
            bot.entry_threshold_high = 2.0
            bot.entry_threshold_low = -2.0
            bot.confirmation_threshold_high = 1.5
            bot.confirmation_threshold_low = -1.5
            bot.trading_state = TradingState.SEARCHING
            bot.signal_state = SignalState.NONE
            bot.cooldown_end_bar = None
            bot.current_bar_index = 0

            # 模擬數據處理器
            from unittest.mock import Mock

            bot.data_handler = Mock()
            bot.data_handler.get_current_prices.return_value = (50000, 3000)

            # 測試1: 從上方觸發區重置
            bot.signal_state = SignalState.TRIGGERED_HIGH
            bot.data_handler.get_current_zscore.return_value = -0.5  # 跨越到負值

            # 調用檢查進場信號的方法（需要模擬部分邏輯）
            current_zscore = bot.data_handler.get_current_zscore()

            # 手動執行重置邏輯
            if bot.signal_state == SignalState.TRIGGERED_HIGH and current_zscore < 0:
                bot.signal_state = SignalState.NONE
                reset_success_1 = True
            else:
                reset_success_1 = False

            # 測試2: 從下方觸發區重置
            bot.signal_state = SignalState.TRIGGERED_LOW
            bot.data_handler.get_current_zscore.return_value = 0.5  # 跨越到正值

            current_zscore = bot.data_handler.get_current_zscore()

            if bot.signal_state == SignalState.TRIGGERED_LOW and current_zscore > 0:
                bot.signal_state = SignalState.NONE
                reset_success_2 = True
            else:
                reset_success_2 = False

            success = reset_success_1 and reset_success_2
            self.test_results["signal_state_reset"] = success

            if success:
                logger.info("✓ 信號狀態重置邏輯驗證通過")
            else:
                logger.error("✗ 信號狀態重置邏輯驗證失敗")

            return success

        except Exception as e:
            logger.error(f"驗證信號狀態重置邏輯失敗: {e}")
            self.test_results["signal_state_reset"] = False
            return False

    def validate_position_value_calculation(self) -> bool:
        """驗證實際倉位價值計算"""
        try:
            logger.info("驗證實際倉位價值計算...")

            from unittest.mock import Mock

            config = {"trading_pair": ["BTCUSDT", "ETHUSDT"], "position_size_usd": 1000}

            mock_exchange = Mock()
            mock_exchange.create_market_order.return_value = {"id": "test_order"}

            executor = TradingExecutor(mock_exchange, config)

            # 執行進場
            base_price = 50000
            quote_price = 3000
            success = executor.enter_long_base_short_quote(base_price, quote_price, 1)

            if success:
                # 檢查實際倉位價值是否正確計算
                expected_base_value = base_price * (1000 / base_price)  # 1000
                expected_quote_value = quote_price * (1000 / quote_price)  # 1000
                expected_total = expected_base_value + expected_quote_value  # 2000

                actual_value = executor.get_actual_position_value()

                # 允許小的浮點誤差
                value_correct = abs(actual_value - expected_total) < 0.01

                self.test_results["position_value_calculation"] = value_correct

                if value_correct:
                    logger.info(f"✓ 實際倉位價值計算正確: {actual_value:.2f} (期望: {expected_total:.2f})")
                else:
                    logger.error(f"✗ 實際倉位價值計算錯誤: {actual_value:.2f} (期望: {expected_total:.2f})")

                return value_correct
            else:
                logger.error("✗ 進場操作失敗")
                self.test_results["position_value_calculation"] = False
                return False

        except Exception as e:
            logger.error(f"驗證實際倉位價值計算失敗: {e}")
            self.test_results["position_value_calculation"] = False
            return False

    def validate_trade_history_recording(self) -> bool:
        """驗證交易歷史記錄"""
        try:
            logger.info("驗證交易歷史記錄...")

            from unittest.mock import Mock

            config = {"trading_pair": ["BTCUSDT", "ETHUSDT"], "position_size_usd": 1000}

            mock_exchange = Mock()
            mock_exchange.create_market_order.return_value = {"id": "test_order"}
            mock_exchange.fetch_ticker.return_value = {"last": 50000}

            executor = TradingExecutor(mock_exchange, config)

            # 執行進場
            executor.enter_long_base_short_quote(50000, 3000, 1)

            # 執行出場
            executor.exit_position("take_profit", 51000, 3100)

            # 檢查交易歷史
            history = executor.get_trade_history()

            if len(history) == 1:
                trade = history[0]
                required_fields = [
                    "entry_time",
                    "exit_time",
                    "pnl",
                    "exit_reason",
                    "is_long_base",
                    "hold_time",
                ]

                fields_present = all(field in trade for field in required_fields)

                if fields_present:
                    logger.info("✓ 交易歷史記錄格式正確")

                    # 檢查統計計算
                    stats = executor.get_trade_statistics()
                    stats_correct = ()
                        stats["total_trades"] == 1
                        and stats["winning_trades"] == 1
                        and stats["total_pnl"] > 0
                    )

                    if stats_correct:
                        logger.info("✓ 交易統計計算正確")
                        self.test_results["trade_history_recording"] = True
                        return True
                    else:
                        logger.error("✗ 交易統計計算錯誤")
                        self.test_results["trade_history_recording"] = False
                        return False
                else:
                    logger.error("✗ 交易歷史記錄缺少必要字段")
                    self.test_results["trade_history_recording"] = False
                    return False
            else:
                logger.error(f"✗ 交易歷史記錄數量錯誤: {len(history)} (期望: 1)")
                self.test_results["trade_history_recording"] = False
                return False

        except Exception as e:
            logger.error(f"驗證交易歷史記錄失敗: {e}")
            self.test_results["trade_history_recording"] = False
            return False

    def validate_backtest_mode(self) -> bool:
        """驗證回測模式"""
        try:
            logger.info("驗證回測模式...")

            from unittest.mock import Mock

            config = {
                "trading_pair": ["BTCUSDT", "ETHUSDT"],
                "timeframe": "1h",
                "lookback_period": 20,
            }

            mock_exchange = Mock()

            # 創建回測模式的數據處理器
            data_handler = DataHandler(mock_exchange, config, mode="BACKTEST")

            # 創建測試數據
            dates = pd.date_range(start="2024-01-01", periods=50, freq="h")
            test_data = pd.DataFrame()
                {
                    "base_price": np.random.uniform(49000, 51000, 50),
                    "quote_price": np.random.uniform(2900, 3100, 50),
                    "base_volume": np.random.uniform(1000, 5000, 50),
                    "quote_volume": np.random.uniform(5000, 20000, 50),
                },
                index=dates,
            )

            # 載入回測數據
            load_success = data_handler.load_backtest_data(test_data)

            if load_success:
                # 測試數據更新
                update_count = 0
                while data_handler.has_more_data() and update_count < 10:
                    update_success = data_handler.update_data()
                    if update_success:
                        update_count += 1
                    else:
                        break

                if update_count > 0:
                    logger.info(f"✓ 回測模式數據更新成功，處理了 {update_count} 次更新")
                    self.test_results["backtest_mode"] = True
                    return True
                else:
                    logger.error("✗ 回測模式數據更新失敗")
                    self.test_results["backtest_mode"] = False
                    return False
            else:
                logger.error("✗ 回測數據載入失敗")
                self.test_results["backtest_mode"] = False
                return False

        except Exception as e:
            logger.error(f"驗證回測模式失敗: {e}")
            self.test_results["backtest_mode"] = False
            return False

    def run_all_validations(self) -> Dict:
        """運行所有驗證測試"""
        logger.info("開始運行所有改進功能驗證...")

        validations = [
            ("信號狀態重置", self.validate_signal_state_reset),
            ("實際倉位價值計算", self.validate_position_value_calculation),
            ("交易歷史記錄", self.validate_trade_history_recording),
            ("回測模式", self.validate_backtest_mode),
        ]

        results = {}
        passed = 0
        total = len(validations)

        for name, validation_func in validations:
            logger.info(f"\n--- 驗證 {name} ---")
            try:
                success = validation_func()
                results[name] = success
                if success:
                    passed += 1
                    logger.info(f"✓ {name} 驗證通過")
                else:
                    logger.error(f"✗ {name} 驗證失敗")
            except Exception as e:
                logger.error(f"✗ {name} 驗證過程中發生錯誤: {e}")
                results[name] = False

        # 生成總結報告
        logger.info("\n=== 驗證總結 ===")
        logger.info(f"通過: {passed}/{total}")
        logger.info(f"成功率: {passed/total*100:.1f}%")

        for name, success in results.items():
            status = "✓" if success else "✗"
            logger.info(f"{status} {name}")

        return results

    def save_validation_report(self, results: Dict, filename: str = "validation_report.json"):
        """保存驗證報告"""
        try:
            report = {
                "timestamp": datetime.now().isoformat(),
                "results": results,
                "summary": {
                    "total_tests": len(results),
                    "passed_tests": sum(results.values()),
                    "success_rate": sum(results.values()) / len(results) if results else 0,
                },
            }

            with open(filename, "w", encoding="utf-8") as f:
                json.dump(report, f, indent=2, ensure_ascii=False)

            logger.info(f"驗證報告已保存: {filename}")

        except Exception as e:
            logger.error(f"保存驗證報告失敗: {e}")


def main():
    """主函數"""
    try:
        validator = ImprovementValidator()
        results = validator.run_all_validations()
        validator.save_validation_report(results)

        # 返回適當的退出碼
        if all(results.values()):
            logger.info("所有改進功能驗證通過！")
            return 0
        else:
            logger.error("部分改進功能驗證失敗")
            return 1

    except Exception as e:
        logger.error(f"驗證過程失敗: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
