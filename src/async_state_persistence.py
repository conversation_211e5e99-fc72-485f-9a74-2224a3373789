#!/usr/bin/env python3
"""
異步狀態持久化系統 - 基於建議 13 實施寫入隊列模式
Async State Persistence System - Write queue pattern based on recommendation 13
"""

import asyncio
import gzip
import json
import pickle
import time
from datetime import datetime
from enum import Enum

import aiosqlite

from async_resource_manager import get_async_resource_manager
from global_event_bus import Event, EventType, get_global_event_bus
from graceful_shutdown import ShutdownComponent
from logging_config import get_logger

logger = get_logger(__name__)


class PersistenceOperation(str, Enum):
    """持久化操作類型"""

    INSERT = "insert"
    UPDATE = "update"
    DELETE = "delete"
    BATCH_INSERT = "batch_insert"
    BATCH_UPDATE = "batch_update"


class SerializationFormat(str, Enum):
    """序列化格式"""

    JSON = "json"
    PICKLE = "pickle"
    COMPRESSED_PICKLE = "compressed_pickle"


@dataclass
class PersistenceTask:
    """持久化任務"""

    operation: PersistenceOperation
    table: str
    data: Any
    task_id: str
    priority: int = 1
    created_at: float = None
    retry_count: int = 0
    max_retries: int = 3
    callback: Optional[Callable] = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = time.time()

    def __lt__(self, other):
        """用於優先級隊列排序"""
        return self.priority > other.priority  # 高優先級先執行


class AsyncStatePersistenceManager(ShutdownComponent):
    """異步狀態持久化管理器"""

    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}

        # 數據庫配置
        self.db_path = self.config.get("db_path", "async_state.db")
        self.batch_size = self.config.get("batch_size", 100)
        self.flush_interval = self.config.get("flush_interval", 5.0)  # 秒
        self.max_queue_size = self.config.get("max_queue_size", 10000)

        # 寫入隊列
        self.write_queue = asyncio.PriorityQueue(maxsize=self.max_queue_size)
        self.batch_buffer: List[PersistenceTask] = []

        # 任務管理
        self._writer_task: Optional[asyncio.Task] = None
        self._flush_task: Optional[asyncio.Task] = None
        self.is_running = False

        # 統計信息
        self.stats = {
            "total_tasks": 0,
            "completed_tasks": 0,
            "failed_tasks": 0,
            "batch_operations": 0,
            "queue_size": 0,
            "avg_write_time": 0.0,
        }

        # 資源管理器
        self.resource_manager = get_async_resource_manager()
        self.event_bus = get_global_event_bus()

        # 序列化器
        self.serializers = {
            SerializationFormat.JSON: self._json_serializer,
            SerializationFormat.PICKLE: self._pickle_serializer,
            SerializationFormat.COMPRESSED_PICKLE: self._compressed_pickle_serializer,
        }

        logger.info(f"異步狀態持久化管理器初始化完成: {self.db_path}")

    @property
    def component_name(self) -> str:
        return "AsyncStatePersistenceManager"

    async def start(self):
        """啟動持久化管理器"""
        if self.is_running:
            logger.warning("持久化管理器已在運行")
            return

        try:
            # 初始化數據庫
            await self._init_database()

            # 啟動寫入任務
            self._writer_task = asyncio.create_task(self._writer_loop())
            self._flush_task = asyncio.create_task(self._flush_loop())

            # 註冊事件監聽器
            await self._register_event_listeners()

            self.is_running = True
            logger.info("異步狀態持久化管理器已啟動")

        except Exception as e:
            logger.error(f"啟動持久化管理器失敗: {e}")
            raise

    async def _init_database(self):
        """初始化數據庫"""
        async with self.resource_manager.get_db_connection(self.db_path) as conn:
            # 創建基本表結構
            await conn.execute(
                """
                CREATE TABLE IF NOT EXISTS strategy_states (
                    strategy_id TEXT PRIMARY KEY,
                    state_data TEXT NOT NULL,
                    serialization_format TEXT DEFAULT 'json',
                    created_at REAL NOT NULL,
                    updated_at REAL NOT NULL
                )
            """
            )

            await conn.execute(
                """
                CREATE TABLE IF NOT EXISTS portfolio_allocations (
                    allocation_id TEXT PRIMARY KEY,
                    strategy_id TEXT NOT NULL,
                    allocation_data TEXT NOT NULL,
                    serialization_format TEXT DEFAULT 'json',
                    created_at REAL NOT NULL,
                    updated_at REAL NOT NULL
                )
            """
            )

            await conn.execute(
                """
                CREATE TABLE IF NOT EXISTS trade_records (
                    trade_id TEXT PRIMARY KEY,
                    strategy_id TEXT NOT NULL,
                    trade_data TEXT NOT NULL,
                    serialization_format TEXT DEFAULT 'json',
                    created_at REAL NOT NULL
                )
            """
            )

            await conn.execute(
                """
                CREATE TABLE IF NOT EXISTS system_snapshots (
                    snapshot_id TEXT PRIMARY KEY,
                    snapshot_data TEXT NOT NULL,
                    serialization_format TEXT DEFAULT 'compressed_pickle',
                    created_at REAL NOT NULL
                )
            """
            )

            # 創建索引
            await conn.execute(
                "CREATE INDEX IF NOT EXISTS idx_strategy_states_updated ON strategy_states(updated_at)"
            )
            await conn.execute(
                "CREATE INDEX IF NOT EXISTS idx_portfolio_strategy ON portfolio_allocations(strategy_id)"
            )
            await conn.execute(
                "CREATE INDEX IF NOT EXISTS idx_trade_strategy ON trade_records(strategy_id)"
            )
            await conn.execute(
                "CREATE INDEX IF NOT EXISTS idx_snapshots_created ON system_snapshots(created_at)"
            )

            await conn.commit()

        logger.info("數據庫初始化完成")

    async def _register_event_listeners(self):
        """註冊事件監聽器"""
        # 監聽策略狀態變化
        self.event_bus.subscribe(
            [EventType.STRATEGY_STATE_CHANGED], self._on_strategy_state_changed
        )

        # 監聽投組分配變化
        self.event_bus.subscribe([EventType.PORTFOLIO_REBALANCED], self._on_portfolio_rebalanced)

        # 監聽交易記錄
        self.event_bus.subscribe([EventType.ORDER_FILLED], self._on_trade_executed)

    async def save_strategy_state(
        self,
        strategy_id: str,
        state_data: Dict[str, Any],
        format: SerializationFormat = SerializationFormat.JSON,
        priority: int = 2,
    ) -> bool:
        """保存策略狀態"""
        return await self._queue_task(
            operation=PersistenceOperation.UPDATE,
            table="strategy_states",
            data={
                "strategy_id": strategy_id,
                "state_data": state_data,
                "serialization_format": format.value,
                "updated_at": time.time(),
            },
            priority=priority,
        )

    async def save_portfolio_allocation(
        self,
        allocation_id: str,
        strategy_id: str,
        allocation_data: Dict[str, Any],
        format: SerializationFormat = SerializationFormat.JSON,
        priority: int = 2,
    ) -> bool:
        """保存投組分配"""
        return await self._queue_task(
            operation=PersistenceOperation.UPDATE,
            table="portfolio_allocations",
            data={
                "allocation_id": allocation_id,
                "strategy_id": strategy_id,
                "allocation_data": allocation_data,
                "serialization_format": format.value,
                "updated_at": time.time(),
            },
            priority=priority,
        )

    async def save_trade_record(
        self,
        trade_id: str,
        strategy_id: str,
        trade_data: Dict[str, Any],
        format: SerializationFormat = SerializationFormat.JSON,
        priority: int = 3,
    ) -> bool:
        """保存交易記錄"""
        return await self._queue_task(
            operation=PersistenceOperation.INSERT,
            table="trade_records",
            data={
                "trade_id": trade_id,
                "strategy_id": strategy_id,
                "trade_data": trade_data,
                "serialization_format": format.value,
                "created_at": time.time(),
            },
            priority=priority,
        )

    async def save_system_snapshot(
        self,
        snapshot_id: str,
        snapshot_data: Any,
        format: SerializationFormat = SerializationFormat.COMPRESSED_PICKLE,
        priority: int = 1,
    ) -> bool:
        """保存系統快照"""
        return await self._queue_task(
            operation=PersistenceOperation.INSERT,
            table="system_snapshots",
            data={
                "snapshot_id": snapshot_id,
                "snapshot_data": snapshot_data,
                "serialization_format": format.value,
                "created_at": time.time(),
            },
            priority=priority,
        )

    async def _queue_task(
        self, operation: PersistenceOperation, table: str, data: Any, priority: int = 1
    ) -> bool:
        """將任務加入隊列"""
        try:
            task_id = f"{table}_{int(time.time() * 1000000)}"

            task = PersistenceTask(
                operation=operation, table=table, data=data, task_id=task_id, priority=priority
            )

            # 檢查隊列是否已滿
            if self.write_queue.full():
                logger.warning("寫入隊列已滿，丟棄低優先級任務")
                return False

            await self.write_queue.put(task)
            self.stats["total_tasks"] += 1
            self.stats["queue_size"] = self.write_queue.qsize()

            return True

        except Exception as e:
            logger.error(f"任務入隊失敗: {e}")
            return False

    async def _writer_loop(self):
        """寫入循環"""
        while self.is_running:
            try:
                # 從隊列獲取任務
                try:
                    task = await asyncio.wait_for(self.write_queue.get(), timeout=1.0)
                except asyncio.TimeoutError:
                    continue

                # 添加到批處理緩衝區
                self.batch_buffer.append(task)

                # 如果緩衝區滿了，立即處理
                if len(self.batch_buffer) >= self.batch_size:
                    await self._process_batch()

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"寫入循環異常: {e}")
                await asyncio.sleep(1)

    async def _flush_loop(self):
        """定期刷新循環"""
        while self.is_running:
            try:
                await asyncio.sleep(self.flush_interval)

                if self.batch_buffer:
                    await self._process_batch()

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"刷新循環異常: {e}")

    async def _process_batch(self):
        """處理批次任務"""
        if not self.batch_buffer:
            return

        start_time = time.time()
        batch = self.batch_buffer.copy()
        self.batch_buffer.clear()

        try:
            async with self.resource_manager.get_db_connection(self.db_path) as conn:
                # 按表分組任務
                tasks_by_table = {}
                for task in batch:
                    if task.table not in tasks_by_table:
                        tasks_by_table[task.table] = []
                    tasks_by_table[task.table].append(task)

                # 處理每個表的任務
                for table, tasks in tasks_by_table.items():
                    await self._process_table_tasks(conn, table, tasks)

                await conn.commit()

                # 更新統計
                self.stats["completed_tasks"] += len(batch)
                self.stats["batch_operations"] += 1

                execution_time = time.time() - start_time
                self.stats["avg_write_time"] = (
                    self.stats["avg_write_time"] * (self.stats["batch_operations"] - 1)
                    + execution_time
                ) / self.stats["batch_operations"]

                logger.debug(f"批次處理完成: {len(batch)}個任務, 耗時{execution_time:.3f}s")

        except Exception as e:
            logger.error(f"批次處理失敗: {e}")
            self.stats["failed_tasks"] += len(batch)

            # 重新入隊失敗的任務（有重試限制）
            for task in batch:
                if task.retry_count < task.max_retries:
                    task.retry_count += 1
                    await self.write_queue.put(task)

    async def _process_table_tasks(
        self, conn: aiosqlite.Connection, table: str, tasks: List[PersistenceTask]
    ):
        """處理特定表的任務"""
        try:
            if table == "strategy_states":
                await self._process_strategy_states(conn, tasks)
            elif table == "portfolio_allocations":
                await self._process_portfolio_allocations(conn, tasks)
            elif table == "trade_records":
                await self._process_trade_records(conn, tasks)
            elif table == "system_snapshots":
                await self._process_system_snapshots(conn, tasks)
            else:
                logger.warning(f"未知表類型: {table}")

        except Exception as e:
            logger.error(f"處理表任務失敗 {table}: {e}")
            raise

    async def _process_strategy_states(
        self, conn: aiosqlite.Connection, tasks: List[PersistenceTask]
    ):
        """處理策略狀態任務"""
        for task in tasks:
            data = task.data
            serialized_data = await self._serialize_data(
                data["state_data"], SerializationFormat(data["serialization_format"])
            )

            await conn.execute(
                """
                INSERT OR REPLACE INTO strategy_states
                (strategy_id, state_data, serialization_format, created_at, updated_at)
                VALUES (?, ?, ?, COALESCE((SELECT created_at FROM strategy_states WHERE strategy_id = ?), ?), ?)
            """,
                (
                    data["strategy_id"],
                    serialized_data,
                    data["serialization_format"],
                    data["strategy_id"],
                    data["updated_at"],
                    data["updated_at"],
                ),
            )

    async def _process_portfolio_allocations(
        self, conn: aiosqlite.Connection, tasks: List[PersistenceTask]
    ):
        """處理投組分配任務"""
        for task in tasks:
            data = task.data
            serialized_data = await self._serialize_data(
                data["allocation_data"], SerializationFormat(data["serialization_format"])
            )

            await conn.execute(
                """
                INSERT OR REPLACE INTO portfolio_allocations
                (allocation_id, strategy_id, allocation_data, serialization_format, created_at, updated_at)
                VALUES (?, ?, ?, ?, COALESCE((SELECT created_at FROM portfolio_allocations WHERE allocation_id = ?), ?), ?)
            """,
                (
                    data["allocation_id"],
                    data["strategy_id"],
                    serialized_data,
                    data["serialization_format"],
                    data["allocation_id"],
                    data["updated_at"],
                    data["updated_at"],
                ),
            )

    async def _process_trade_records(
        self, conn: aiosqlite.Connection, tasks: List[PersistenceTask]
    ):
        """處理交易記錄任務"""
        for task in tasks:
            data = task.data
            serialized_data = await self._serialize_data(
                data["trade_data"], SerializationFormat(data["serialization_format"])
            )

            await conn.execute(
                """
                INSERT OR IGNORE INTO trade_records
                (trade_id, strategy_id, trade_data, serialization_format, created_at)
                VALUES (?, ?, ?, ?, ?)
            """,
                (
                    data["trade_id"],
                    data["strategy_id"],
                    serialized_data,
                    data["serialization_format"],
                    data["created_at"],
                ),
            )

    async def _process_system_snapshots(
        self, conn: aiosqlite.Connection, tasks: List[PersistenceTask]
    ):
        """處理系統快照任務"""
        for task in tasks:
            data = task.data
            serialized_data = await self._serialize_data(
                data["snapshot_data"], SerializationFormat(data["serialization_format"])
            )

            await conn.execute(
                """
                INSERT OR REPLACE INTO system_snapshots
                (snapshot_id, snapshot_data, serialization_format, created_at)
                VALUES (?, ?, ?, ?)
            """,
                (
                    data["snapshot_id"],
                    serialized_data,
                    data["serialization_format"],
                    data["created_at"],
                ),
            )

    async def _serialize_data(self, data: Any, format: SerializationFormat) -> str:
        """序列化數據"""
        serializer = self.serializers.get(format, self._json_serializer)
        return await serializer(data)

    async def _json_serializer(self, data: Any) -> str:
        """JSON序列化器"""
        return json.dumps(data, ensure_ascii=False, default=str)

    async def _pickle_serializer(self, data: Any) -> str:
        """Pickle序列化器"""
        import base64

        pickled = pickle.dumps(data)
        return base64.b64encode(pickled).decode("ascii")

    async def _compressed_pickle_serializer(self, data: Any) -> str:
        """壓縮Pickle序列化器"""
        import base64

        pickled = pickle.dumps(data)
        compressed = gzip.compress(pickled)
        return base64.b64encode(compressed).decode("ascii")

    async def _on_strategy_state_changed(self, event: Event):
        """處理策略狀態變化事件"""
        try:
            strategy_id = event.data.get("strategy_id")
            state_data = event.data.get("state_data")

            if strategy_id and state_data:
                await self.save_strategy_state(strategy_id, state_data)

        except Exception as e:
            logger.error(f"處理策略狀態變化事件失敗: {e}")

    async def _on_portfolio_rebalanced(self, event: Event):
        """處理投組重新平衡事件"""
        try:
            changes = event.data.get("changes", [])

            for change in changes:
                strategy_id = change.get("strategy_id")
                if strategy_id:
                    allocation_id = f"{strategy_id}_{int(time.time())}"
                    await self.save_portfolio_allocation(allocation_id, strategy_id, change)

        except Exception as e:
            logger.error(f"處理投組重新平衡事件失敗: {e}")

    async def _on_trade_executed(self, event: Event):
        """處理交易執行事件"""
        try:
            strategy_id = event.data.get("strategy_id")
            trade_data = event.data

            if strategy_id:
                trade_id = f"{strategy_id}_{int(time.time() * 1000000)}"
                await self.save_trade_record(trade_id, strategy_id, trade_data)

        except Exception as e:
            logger.error(f"處理交易執行事件失敗: {e}")

    async def flush_all(self):
        """強制刷新所有待處理任務"""
        if self.batch_buffer:
            await self._process_batch()

        # 等待隊列清空
        while not self.write_queue.empty():
            await asyncio.sleep(0.1)

    def get_persistence_stats(self) -> Dict[str, Any]:
        """獲取持久化統計"""
        self.stats["queue_size"] = self.write_queue.qsize()
        self.stats["buffer_size"] = len(self.batch_buffer)

        return self.stats.copy()

    async def shutdown(self) -> bool:
        """關閉持久化管理器"""
        try:
            logger.info("關閉異步狀態持久化管理器...")

            self.is_running = False

            # 刷新所有待處理任務
            await self.flush_all()

            # 停止任務
            if self._writer_task:
                self._writer_task.cancel()
                try:
                    await self._writer_task
                except asyncio.CancelledError:
                    pass

            if self._flush_task:
                self._flush_task.cancel()
                try:
                    await self._flush_task
                except asyncio.CancelledError:
                    pass

            logger.info("異步狀態持久化管理器關閉完成")
            return True

        except Exception as e:
            logger.error(f"關閉異步狀態持久化管理器失敗: {e}")
            return False


# 全局持久化管理器實例
_persistence_manager: Optional[AsyncStatePersistenceManager] = None


def get_async_persistence_manager(config: Dict[str, Any] = None) -> AsyncStatePersistenceManager:
    """獲取全局異步持久化管理器實例"""
    global _persistence_manager
    if _persistence_manager is None:
        _persistence_manager = AsyncStatePersistenceManager(config)

        # 自動註冊到停機管理器
        try:
            from graceful_shutdown import get_shutdown_manager

            get_shutdown_manager().register_component(_persistence_manager)
        except ImportError:
            logger.warning("無法註冊到停機管理器")

    return _persistence_manager


async def main():
    """測試異步狀態持久化系統"""
    print("🧪 測試異步狀態持久化系統")

    # 創建持久化管理器
    config = {
        "db_path": "test_async_persistence.db",
        "batch_size": 5,
        "flush_interval": 2.0,
        "max_queue_size": 100,
    }

    persistence_manager = get_async_persistence_manager(config)

    try:
        # 啟動管理器
        await persistence_manager.start()

        # 測試策略狀態保存
        print("測試策略狀態保存...")
        for i in range(10):
            await persistence_manager.save_strategy_state(
                f"strategy_{i}", {"position": i * 100, "pnl": i * 10.5, "status": "active"}
            )

        # 測試交易記錄保存
        print("測試交易記錄保存...")
        for i in range(5):
            await persistence_manager.save_trade_record(
                f"trade_{i}",
                f"strategy_{i % 3}",
                {"symbol": "BTC/USDT", "side": "buy", "amount": i * 0.1, "price": 50000 + i * 100},
            )

        # 測試系統快照保存
        print("測試系統快照保存...")
        snapshot_data = {
            "timestamp": datetime.now().isoformat(),
            "strategies": ["strategy_0", "strategy_1", "strategy_2"],
            "total_capital": 100000,
            "system_health": "good",
        }
        await persistence_manager.save_system_snapshot(
            f"snapshot_{int(time.time())}", snapshot_data
        )

        # 等待批次處理
        print("等待批次處理...")
        await asyncio.sleep(3)

        # 強制刷新
        await persistence_manager.flush_all()

        # 獲取統計信息
        stats = persistence_manager.get_persistence_stats()
        print("  📊 持久化統計:")
        print(f"    總任務: {stats['total_tasks']}")
        print(f"    完成任務: {stats['completed_tasks']}")
        print(f"    失敗任務: {stats['failed_tasks']}")
        print(f"    批次操作: {stats['batch_operations']}")
        print(f"    平均寫入時間: {stats['avg_write_time']:.3f}s")

    finally:
        # 測試關閉
        success = await persistence_manager.shutdown()
        print(f"  {'✅' if success else '❌'} 關閉結果: {success}")

        # 清理測試文件
        import os

        if os.path.exists(config["db_path"]):
            os.remove(config["db_path"])

    print("✅ 異步狀態持久化系統測試完成")


if __name__ == "__main__":
    asyncio.run(main())
