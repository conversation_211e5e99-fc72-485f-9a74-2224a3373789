#!/usr/bin/env python3
"""
快速修復最終問題 - 修復剩餘的微調項目
Quick Fix Final Issues - Fix remaining fine-tuning items
"""

import re
from pathlib import Path

def fix_sqlite_connections():
    """修復所有SQLite連接以使用統一參數"""
    print("🔧 修復SQLite數據庫連接...")
    
    file_path = Path("state_persistence_manager.py")
    
    if not file_path.exists():
        print("❌ state_persistence_manager.py 文件不存在")
        return
    
    # 讀取文件內容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替換所有不帶參數的sqlite3.connect
    pattern = r'sqlite3\.connect\(self\.db_path\)(?!\s*,)'
    replacement = 'sqlite3.connect(self.db_path, detect_types=sqlite3.PARSE_DECLTYPES)'
    
    new_content = re.sub(pattern, replacement, content)
    
    # 檢查是否有修改
    if new_content != content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        print("✅ SQLite連接已修復")
    else:
        print("✅ SQLite連接已是最新狀態")

def create_final_validation_script():
    """創建最終驗證腳本"""
    print("🔧 創建最終驗證腳本...")
    
    script_content = '''#!/usr/bin/env python3
"""
最終完美驗證 - 驗證所有問題已修復
Final Perfect Validation - Validate all issues are fixed
"""

import asyncio
import warnings
from datetime import datetime

# 捕獲所有警告
warnings.filterwarnings('error')

from enhanced_pair_selection import EnhancedPairSelector
from state_persistence_manager import StatePersistenceManager
from intelligent_portfolio_system import IntelligentPortfolioSystem
from logging_config import setup_logging, get_logger

setup_logging()
logger = get_logger(__name__)

async def run_perfect_validation():
    """運行完美驗證"""
    print("🎯 最終完美驗證")
    print("=" * 60)
    
    validation_results = {}
    
    # 1. 驗證增強配對選擇
    print("\\n1. 🔍 驗證增強配對選擇...")
    try:
        selector = EnhancedPairSelector()
        
        # 測試方法是否存在
        if hasattr(selector, 'enhanced_pair_selection'):
            import numpy as np
            np.random.seed(42)
            
            # 測試正常數據
            prices1 = np.cumsum(np.random.randn(100)) + 100
            prices2 = prices1 * 0.8 + np.cumsum(np.random.randn(100) * 0.5) + 80
            
            result = selector.enhanced_pair_selection(prices1, prices2)
            validation_results['enhanced_pair_selection'] = 'PASSED'
            print(f"  ✅ 增強配對選擇: {result}")
        else:
            validation_results['enhanced_pair_selection'] = 'FAILED'
            print("  ❌ 方法不存在")
            
    except Exception as e:
        validation_results['enhanced_pair_selection'] = 'ERROR'
        print(f"  ❌ 測試失敗: {e}")
    
    # 2. 驗證SQLite修復
    print("\\n2. 🔍 驗證SQLite修復...")
    try:
        persistence_manager = StatePersistenceManager("perfect_test.db")
        
        # 測試保存和載入
        test_state = {'test': 'data', 'timestamp': datetime.now().isoformat()}
        persistence_manager.save_strategy_state("test_strategy", test_state, 0.8)
        loaded_state = persistence_manager.load_strategy_state("test_strategy")
        
        if loaded_state and loaded_state.get('test') == 'data':
            validation_results['sqlite_fix'] = 'PASSED'
            print("  ✅ SQLite數據庫正常工作")
        else:
            validation_results['sqlite_fix'] = 'FAILED'
            print("  ❌ 數據完整性問題")
        
        # 清理測試文件
        import os
        if os.path.exists("perfect_test.db"):
            os.remove("perfect_test.db")
            
    except Exception as e:
        validation_results['sqlite_fix'] = 'ERROR'
        print(f"  ❌ 測試失敗: {e}")
    
    # 3. 驗證完整系統
    print("\\n3. 🔍 驗證完整系統...")
    try:
        system = IntelligentPortfolioSystem(total_capital=50000)
        await system.start_system()
        
        # 短暫運行
        await asyncio.sleep(1)
        
        overview = system.get_system_overview()
        await system.stop_system()
        
        if overview['system_stats']['total_strategies'] > 0:
            validation_results['complete_system'] = 'PASSED'
            print(f"  ✅ 系統運行正常: {overview['system_stats']['total_strategies']} 個策略")
        else:
            validation_results['complete_system'] = 'FAILED'
            print("  ❌ 系統運行異常")
            
    except Exception as e:
        validation_results['complete_system'] = 'ERROR'
        print(f"  ❌ 測試失敗: {e}")
    
    # 4. 生成最終報告
    print("\\n4. 📊 生成最終報告...")
    
    passed_tests = sum(1 for result in validation_results.values() if result == 'PASSED')
    total_tests = len(validation_results)
    success_rate = passed_tests / total_tests * 100 if total_tests > 0 else 0
    
    print(f"\\n🎉 最終驗證完成！")
    print(f"📊 通過率: {success_rate:.1f}% ({passed_tests}/{total_tests})")
    
    if success_rate == 100:
        print("🏆 系統達到完美狀態！")
    elif success_rate >= 90:
        print("✅ 系統狀態優秀！")
    else:
        print("⚠️ 系統需要進一步優化")
    
    return validation_results

if __name__ == "__main__":
    asyncio.run(run_perfect_validation())
'''
    
    with open("perfect_validation.py", 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print("✅ 最終驗證腳本已創建: perfect_validation.py")

def main():
    """主函數"""
    print("🎯 快速修復最終問題")
    print("=" * 50)
    
    # 1. 修復SQLite連接
    fix_sqlite_connections()
    
    # 2. 創建最終驗證腳本
    create_final_validation_script()
    
    print("\\n🎉 所有問題修復完成！")
    print("請運行: python3 perfect_validation.py")

if __name__ == "__main__":
    main()
