#!/usr/bin/env python3
"""
配置加載器 - 專門負責配置載入和環境變量處理
Config Loader - Dedicated configuration loading and environment variable handling
"""

import os
import json
from pathlib import Path
from typing import Dict, Any, Optional

from trading_exceptions import ConfigError
from logging_config import get_logger

logger = get_logger(__name__)


class ConfigLoader:
    """配置加載器"""
    
    @staticmethod
    def load_env_file(env_file: str = ".env") -> Dict[str, str]:
        """
        載入環境變量文件
        
        Args:
            env_file: 環境變量文件路徑
            
        Returns:
            Dict: 環境變量字典
        """
        env_vars = {}
        env_path = Path(env_file)
        
        if not env_path.exists():
            logger.warning(f"環境變量文件不存在: {env_file}")
            return env_vars
        
        try:
            logger.info(f"載入環境變量文件: {env_file}")
            
            with open(env_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    
                    # 跳過空行和註釋
                    if not line or line.startswith('#'):
                        continue
                    
                    # 解析 KEY=VALUE 格式
                    if '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip()
                        
                        # 移除行尾註釋
                        if '#' in value:
                            value = value.split('#')[0].strip()
                        
                        # 移除引號
                        if value.startswith('"') and value.endswith('"'):
                            value = value[1:-1]
                        elif value.startswith("'") and value.endswith("'"):
                            value = value[1:-1]
                        
                        env_vars[key] = value
                        
                        # 同時設置到系統環境變量
                        os.environ[key] = value
                        
                    else:
                        logger.warning(f"環境變量文件第{line_num}行格式錯誤: {line}")
            
            logger.info(f"成功載入 {len(env_vars)} 個環境變量")
            return env_vars
            
        except Exception as e:
            logger.error(f"載入環境變量文件失敗: {e}")
            raise ConfigError(f"無法載入環境變量文件 {env_file}: {e}")
    
    @staticmethod
    def load_json_config(config_file: str) -> Dict[str, Any]:
        """
        載入JSON配置文件
        
        Args:
            config_file: JSON配置文件路徑
            
        Returns:
            Dict: 配置字典
        """
        config_path = Path(config_file)
        
        if not config_path.exists():
            raise ConfigError(f"配置文件不存在: {config_file}")
        
        try:
            logger.info(f"載入JSON配置文件: {config_file}")
            
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            logger.info(f"成功載入配置文件: {config_file}")
            return config
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON配置文件格式錯誤: {e}")
            raise ConfigError(f"配置文件格式錯誤 {config_file}: {e}")
        
        except Exception as e:
            logger.error(f"載入配置文件失敗: {e}")
            raise ConfigError(f"無法載入配置文件 {config_file}: {e}")
    
    @staticmethod
    def merge_configs(json_config: Dict[str, Any], 
                     env_vars: Dict[str, str]) -> Dict[str, Any]:
        """
        合併JSON配置和環境變量
        
        Args:
            json_config: JSON配置
            env_vars: 環境變量
            
        Returns:
            Dict: 合併後的配置
        """
        merged_config = json_config.copy()
        
        # 環境變量映射規則
        env_mappings = {
            # 交易所配置
            'TRADING_API_KEY': ['api_key'],
            'TRADING_SECRET': ['secret'],
            'TRADING_PASSPHRASE': ['passphrase'],
            'TRADING_EXCHANGE': ['exchange', 'name'],
            'TRADING_SANDBOX': ['exchange', 'sandbox'],
            
            # 交易模式配置
            'TRADING_TRADING_MODE': ['trading_mode'],
            'TRADING_FUTURES_TYPE': ['futures_type'],
            'TRADING_LEVERAGE': ['leverage'],
            'TRADING_MARGIN_MODE': ['margin_mode'],
            
            # 監控配置
            'MONITOR_TELEGRAM_BOT_TOKEN': ['telegram_bot_token'],
            'MONITOR_TELEGRAM_CHAT_ID': ['telegram_chat_id'],
            
            # 環境配置
            'ENVIRONMENT': ['environment'],
        }
        
        # 應用環境變量覆蓋
        for env_key, config_path in env_mappings.items():
            if env_key in env_vars:
                value = env_vars[env_key]
                
                # 類型轉換
                if env_key in ['TRADING_SANDBOX']:
                    value = value.lower() in ('true', '1', 'yes', 'on')
                elif env_key in ['TRADING_LEVERAGE']:
                    try:
                        value = int(value)
                    except ValueError:
                        logger.warning(f"環境變量 {env_key} 值無效: {value}")
                        continue
                
                # 設置嵌套配置
                ConfigLoader._set_nested_config(merged_config, config_path, value)
        
        return merged_config
    
    @staticmethod
    def _set_nested_config(config: Dict[str, Any], path: list, value: Any):
        """設置嵌套配置值"""
        current = config
        
        # 導航到最後一級的父級
        for key in path[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        
        # 設置最終值
        current[path[-1]] = value
    
    @staticmethod
    def load_complete_config(config_file: str = "config.json", 
                           env_file: str = ".env") -> Dict[str, Any]:
        """
        載入完整配置（JSON + 環境變量）
        
        Args:
            config_file: JSON配置文件路徑
            env_file: 環境變量文件路徑
            
        Returns:
            Dict: 完整配置
        """
        try:
            # 載入環境變量
            env_vars = ConfigLoader.load_env_file(env_file)
            
            # 載入JSON配置
            json_config = ConfigLoader.load_json_config(config_file)
            
            # 合併配置
            complete_config = ConfigLoader.merge_configs(json_config, env_vars)
            
            logger.info("完整配置載入成功")
            return complete_config
            
        except Exception as e:
            logger.error(f"載入完整配置失敗: {e}")
            raise
    
    @staticmethod
    def validate_required_config(config: Dict[str, Any]) -> Dict[str, Any]:
        """
        驗證必需的配置項
        
        Args:
            config: 配置字典
            
        Returns:
            Dict: 驗證結果
        """
        result = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        # 必需的配置項
        required_fields = [
            (['api_key'], "API密鑰"),
            (['secret'], "API密鑰"),
            (['exchange', 'name'], "交易所名稱"),
        ]
        
        for field_path, field_name in required_fields:
            if not ConfigLoader._get_nested_config(config, field_path):
                result['errors'].append(f"{field_name}未設置")
                result['valid'] = False
        
        # 可選但重要的配置項
        optional_fields = [
            (['trading_mode'], "交易模式"),
            (['leverage'], "槓桿倍數"),
            (['margin_mode'], "保證金模式"),
        ]
        
        for field_path, field_name in optional_fields:
            if not ConfigLoader._get_nested_config(config, field_path):
                result['warnings'].append(f"{field_name}未設置，將使用默認值")
        
        return result
    
    @staticmethod
    def _get_nested_config(config: Dict[str, Any], path: list) -> Any:
        """獲取嵌套配置值"""
        current = config
        
        try:
            for key in path:
                current = current[key]
            return current
        except (KeyError, TypeError):
            return None
    
    @staticmethod
    def create_env_template(output_file: str = ".env.example"):
        """創建環境變量模板文件"""
        template = """# 配對交易機器人環境變量配置
# Pair Trading Bot Environment Configuration

# 交易所 API 配置 / Exchange API Configuration (必需)
TRADING_API_KEY=your_api_key_here
TRADING_SECRET=your_secret_here
TRADING_PASSPHRASE=  # 某些交易所需要

# 交易模式配置 / Trading Mode Configuration
TRADING_TRADING_MODE=futures
TRADING_FUTURES_TYPE=linear
TRADING_LEVERAGE=50
TRADING_MARGIN_MODE=cross

# Telegram警報配置 / Telegram Alert Configuration (可選)
MONITOR_TELEGRAM_BOT_TOKEN=
MONITOR_TELEGRAM_CHAT_ID=

# 環境配置 / Environment Configuration
ENVIRONMENT=development  # development, staging, production

# 注意事項 / Important Notes:
# 1. 請勿將真實 API 密鑰提交到版本控制
# 2. 複製此文件為 .env 並填入真實值
# 3. 生產環境請使用專用 API 密鑰並限制權限
"""
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(template.strip())
            logger.info(f"環境變量模板已創建: {output_file}")
        except Exception as e:
            logger.error(f"創建環境變量模板失敗: {e}")


if __name__ == "__main__":
    # 測試配置加載器
    print("🧪 配置加載器測試")
    
    try:
        # 測試環境變量載入
        env_vars = ConfigLoader.load_env_file(".env")
        print(f"✅ 環境變量載入成功: {len(env_vars)} 個變量")
        
        # 測試JSON配置載入
        json_config = ConfigLoader.load_json_config("config.json")
        print(f"✅ JSON配置載入成功")
        
        # 測試完整配置載入
        complete_config = ConfigLoader.load_complete_config()
        print(f"✅ 完整配置載入成功")
        
        # 測試配置驗證
        validation = ConfigLoader.validate_required_config(complete_config)
        print(f"✅ 配置驗證: {validation}")
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
