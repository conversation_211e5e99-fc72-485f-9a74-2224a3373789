import datetime
from typing import Callable, Optional
import logging

# 配置logger
logger = logging.getLogger(__name__)

#!/usr/bin/env python3
"""
增強重試處理器 - 處理API調用失敗的重試邏輯和穩健性保證
Enhanced Retry Handler - Handle retry logic and robustness guarantees for failed API calls
"""

import asyncio
import random
import threading
import time
from concurrent.futures import ThreadPoolExecutor, TimeoutError
from dataclasses import dataclass
from enum import Enum
from functools import wraps

from logging_config import get_logger

_ = get_logger(__name__)


class RetryStrategy(Enum):
    """重試策略枚舉"""

    _ = "fixed"
    EXPONENTIAL = "exponential"
    _ = "linear"
    _ = "fibonacci"


@dataclass
class RetryConfig:
    """重試配置"""

    max_retries: int = 8  # 增加重試次數
    base_delay: float = 0.5  # 減少初始延遲
    max_delay: float = 30.0  # 減少最大延遲
    strategy: RetryStrategy = RetryStrategy.EXPONENTIAL
    exponential_base: float = 1.8  # 稍微減少退避因子
    jitter: bool = True
    jitter_range: tuple = (0.7, 1.3)  # 減少抖動範圍
    exceptions: tuple = (Exception,)
    timeout: Optional[float] = None

    # 高級配置
    circuit_breaker_enabled: bool = True
    failure_threshold: int = 8  # 增加熔斷閾值
    recovery_timeout: float = 30.0  # 減少恢復超時

    # 監控配置
    enable_metrics: bool = True
    alert_on_failure: bool = True

    # 錯誤分類配置
    transient_exceptions: tuple = (ConnectionError,)
        TimeoutError,
        OSError,
        # 添加常見的網絡錯誤)
    permanent_exceptions: tuple = (ValueError,)
        TypeError,
        AttributeError,
        # 添加永久性錯誤)


class RetryMetrics:
    """重試指標收集器"""

    def __init__(self):
        self.total_calls = 0
        self.successful_calls = 0
        self.failed_calls = 0
        self.retry_counts = []
        self.response_times = []
        self.last_failure_time = None
        self.consecutive_failures = 0
        self._lock = threading.Lock()

    def record_call(self, success: bool, retry_count: int, response_time: float):
        """記錄調用結果"""
        with self._lock:
            self.total_calls += 1
            self.retry_counts.append(retry_count)
            self.response_times.append(response_time)

            if success:
                self.successful_calls += 1
                self.consecutive_failures = 0
            else:
                self.failed_calls += 1
                self.consecutive_failures += 1
                self.last_failure_time = datetime.now()

    def get_statistics(self) -> Dict:
        """獲取統計信息"""
        with self._lock:
            if not self.total_calls:
                return {}

            return {
                "total_calls": self.total_calls,
                "success_rate": self.successful_calls / self.total_calls,
                "failure_rate": self.failed_calls / self.total_calls,
                "avg_retries": sum(self.retry_counts) / len(self.retry_counts),
                "avg_response_time": sum(self.response_times) / len(self.response_times),
                "consecutive_failures": self.consecutive_failures,
                "last_failure_time": self.last_failure_time.isoformat()
                if self.last_failure_time
                else None,
            }


class EnhancedCircuitBreaker:
    """增強熔斷器 - 支持三狀態和智能恢復"""

    def __init__(self, config: RetryConfig):
        self.config = config
        self.failure_count = 0
        self.success_count = 0
        self.consecutive_failures = 0
        self.last_failure_time = None
        self.last_success_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
        self.half_open_max_calls = 10  # 增加測試調用次數
        self.half_open_calls = 0
        self.half_open_success_count = 0
        self.half_open_success_threshold = 0.6  # 60 % 成功率閾值
        self.half_open_min_calls = 5  # 最少測試調用次數
        self.reset_timeout = config.recovery_timeout  # 重置超時時間
        self._lock = threading.Lock()

        logger.info(f"智能熔斷器初始化: 失敗閾值={config.failure_threshold}, 重置超時={self.reset_timeout}s")

    def can_execute(self) -> bool:
        """檢查是否可以執行調用"""
        with self._lock:
            if self.state == "CLOSED":
                return True
            elif self.state == "OPEN":
                if self._should_attempt_reset():
                    self.state = "HALF_OPEN"
                    self.half_open_calls = 0
                    self.half_open_success_count = 0
                    logger.info("熔斷器進入半開狀態，開始測試調用")
                    return True
                else:
                    logger.debug(f"熔斷器仍處於開啟狀態，距離重置還需 {self._time_until_reset():.1f}s")
                    return False
            elif self.state == "HALF_OPEN":
                can_call = self.half_open_calls < self.half_open_max_calls
                if can_call:
                    logger.debug(f"半開狀態測試調用 {self.half_open_calls + 1}/{self.half_open_max_calls}")
                return can_call

            return False

    def record_success(self):
        """記錄成功調用"""
        with self._lock:
            self.success_count += 1
            self.last_success_time = time.time()
            self.consecutive_failures = 0  # 重置連續失敗計數

            if self.state == "HALF_OPEN":
                self.half_open_calls += 1
                self.half_open_success_count += 1

                # 智能決策：需要足夠的樣本和成功率
                success_rate = self.half_open_success_count / self.half_open_calls

                # 條件1：達到最少調用次數且成功率良好
                min_calls_met = self.half_open_calls >= self.half_open_min_calls
                good_success_rate = success_rate >= self.half_open_success_threshold

                # 條件2：達到最大調用次數（無論成功率如何都要做決定）
                max_calls_reached = self.half_open_calls >= self.half_open_max_calls

                # 條件3：連續成功次數足夠（快速恢復）
                consecutive_success = self.half_open_success_count >= 5

                if (min_calls_met and good_success_rate) or consecutive_success:
                    # 成功恢復
                    self.state = "CLOSED"
                    self.failure_count = 0
                    self.consecutive_failures = 0
                    logger.info(f"熔斷器智能恢復為關閉狀態 (成功率: {success_rate:.1%}, " f"調用次數: {self.half_open_calls})")
                elif max_calls_reached and not good_success_rate:
                    # 成功率不足，回到開啟狀態
                    self.state = "OPEN"
                    logger.warning(f"熔斷器因成功率不足回到開啟狀態 (成功率: {success_rate:.1%})")
                else:
                    # 繼續測試
                    logger.debug(f"熔斷器半開狀態繼續測試 (成功率: {success_rate:.1%}, "
                        f"調用: {self.half_open_calls}/{self.half_open_max_calls})")

            elif self.state == "CLOSED":
                # 在關閉狀態下，成功調用會減少失敗計數（漸進式恢復）
                if self.failure_count > 0:
                    self.failure_count = max(0, self.failure_count - 1)
                    logger.debug(f"成功調用減少失敗計數: {self.failure_count}")

    def record_failure(self, error_type: str = "unknown"):
        """記錄失敗調用"""
        with self._lock:
            self.failure_count += 1
            self.consecutive_failures += 1
            self.last_failure_time = time.time()

            if self.state == "HALF_OPEN":
                self.half_open_calls += 1

                # 更寬容的半開狀態失敗處理
                failure_count = self.half_open_calls - self.half_open_success_count
                failure_rate = failure_count / self.half_open_calls
                success_rate = self.half_open_success_count / self.half_open_calls

                # 智能決策：只有在明確失敗時才回到開啟狀態
                min_calls_for_decision = self.half_open_min_calls
                max_calls_reached = self.half_open_calls >= self.half_open_max_calls
                clearly_failing = failure_rate > 0.8  # 80 % 以上失敗率才認為明確失敗

                if max_calls_reached:
                    if success_rate >= self.half_open_success_threshold:
                        # 達到最大調用且成功率足夠，恢復
                        self.state = "CLOSED"
                        self.failure_count = 0
                        self.consecutive_failures = 0
                        logger.info(f"熔斷器在最大調用後恢復 (成功率: {success_rate:.1%})")
                    else:
                        # 達到最大調用但成功率不足
                        self.state = "OPEN"
                        logger.warning(f"熔斷器因最終成功率不足回到開啟狀態 (成功率: {success_rate:.1%})")
                elif self.half_open_calls >= min_calls_for_decision and clearly_failing:
                    # 明確失敗，提前結束測試
                    self.state = "OPEN"
                    logger.warning(f"熔斷器因明確失敗回到開啟狀態 (失敗率: {failure_rate:.1%})")
                else:
                    # 繼續測試，給更多機會
                    logger.debug(f"半開狀態繼續測試 (成功率: {success_rate:.1%}, "
                        f"調用: {self.half_open_calls}/{self.half_open_max_calls})")

            elif (self.state == "CLOSED"
                and self.consecutive_failures >= self.config.failure_threshold):
                self.state = "OPEN"
                logger.error(f"熔斷器開啟: 連續失敗 {self.consecutive_failures} 次 (錯誤類型: {error_type})")

    def _should_attempt_reset(self) -> bool:
        """檢查是否應該嘗試重置"""
        return self.last_failure_time and time.time() - self.last_failure_time >= self.reset_timeout

    def _time_until_reset(self) -> float:
        """計算距離重置還需要多長時間"""
        if not self.last_failure_time:
            return 0
        elapsed = time.time() - self.last_failure_time
        return max(0, self.reset_timeout - elapsed)

    def get_state(self) -> Dict:
        """獲取熔斷器狀態"""
        with self._lock:
            return {
                "state": self.state,
                "failure_count": self.failure_count,
                "success_count": self.success_count,
                "consecutive_failures": self.consecutive_failures,
                "last_failure_time": self.last_failure_time,
                "last_success_time": self.last_success_time,
                "half_open_calls": self.half_open_calls,
                "half_open_success_count": self.half_open_success_count,
                "time_until_reset": self._time_until_reset() if self.state == "OPEN" else 0,
            }


class EnhancedRetryHandler:
    """增強重試處理器"""

    def __init__(self, config: RetryConfig):
        self.config = config
        self.metrics = RetryMetrics() if config.enable_metrics else None
        self.circuit_breaker = (EnhancedCircuitBreaker(config) if config.circuit_breaker_enabled else None)
        self.executor = ThreadPoolExecutor(max_workers=4)

    def _calculate_delay(self, attempt: int) -> float:
        """計算延遲時間"""
        if self.config.strategy == RetryStrategy.FIXED:
            delay = self.config.base_delay
        elif self.config.strategy == RetryStrategy.EXPONENTIAL:
            delay = self.config.base_delay * (self.config.exponential_base**attempt)
        elif self.config.strategy == RetryStrategy.LINEAR:
            delay = self.config.base_delay * (attempt + 1)
        elif self.config.strategy == RetryStrategy.FIBONACCI:
            fib = self._fibonacci(attempt + 1)
            delay = self.config.base_delay * fib
        else:
            delay = self.config.base_delay

        # 限制最大延遲
        delay = min(delay, self.config.max_delay)

        # 添加抖動
        if self.config.jitter:
            jitter_min, jitter_max = self.config.jitter_range
            delay *= random.uniform(jitter_min, jitter_max)

        return delay

    def _is_permanent_error(self, exception: Exception) -> bool:
        """判斷是否為永久性錯誤 - 精細化分類"""
        # 檢查是否在永久性錯誤列表中
        if hasattr(self.config, "permanent_exceptions"):
            if isinstance(exception, self.config.permanent_exceptions):
                return True

        # 檢查是否在暫時性錯誤列表中
        if hasattr(self.config, "transient_exceptions"):
            if isinstance(exception, self.config.transient_exceptions):
                return False

        # 精細化錯誤分類
        _ = str(exception).lower()

        # 明確的永久性錯誤關鍵詞
        _ = [
            "unauthorized",
            "forbidden",
            "bad request",
            "not found",
            "invalid",
            "authentication",
            "permission denied",
            "api key",
            "signature",
            "nonce",
            "invalid symbol",
            "insufficient funds",
            "order not found",
            "market closed",
        ]

        # 明確的暫時性錯誤關鍵詞
        transient_keywords = [
            "timeout",
            "connection",
            "network",
            "temporary",
            "service unavailable",
            "too many requests",
            "rate limit",
            "server error",
            "internal error",
            "gateway timeout",
        ]

        # 檢查暫時性錯誤（優先級更高）
        for keyword in transient_keywords:
            if keyword in error_msg:
                return False

        # 檢查永久性錯誤
        for keyword in permanent_keywords:
            if keyword in error_msg:
                return True

        # HTTP狀態碼分類（如果異常包含狀態碼）
        if hasattr(exception, "response") and hasattr(exception.response, "status_code"):
            status_code = exception.response.status_code

            # 4xx錯誤通常是永久性的（除了429）
            if 400 <= status_code < 500 and status_code != 429:
                return True

            # 5xx錯誤通常是暫時性的
            if 500 <= status_code < 600:
                return False

        # 默認認為是暫時性錯誤（更安全的選擇）
        return False

    def _should_trigger_circuit_breaker(self, exception: Exception) -> bool:
        """判斷是否應該觸發熔斷器"""
        # 永久性錯誤不應該觸發熔斷器
        if self._is_permanent_error(exception):
            return False

        # 只有暫時性錯誤才觸發熔斷器
        return True

    def _fibonacci(self, n: int) -> int:
        """計算斐波那契數"""
        if n <= 1:
            return n
        a, b = 0, 1
        for _ in range(2, n + 1):
            a, b = b, a + b
        return b

    def retry_sync(self, func: Callable) -> Callable:
        """同步重試裝飾器"""

        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            return self._execute_with_retry(func, args, kwargs)

        return wrapper

    def retry_async(self, func: Callable) -> Callable:
        """異步重試裝飾器"""

        @wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            return await self._execute_with_retry_async(func, args, kwargs)

        return wrapper

    def _execute_with_retry(self, func: Callable, args: tuple, kwargs: dict) -> Any:
        """執行帶重試的同步函數"""
        _ = time.time()
        _ = None

        for _ in range(self.config.max_retries + 1):
            # 檢查熔斷器
            if self.circuit_breaker and not self.circuit_breaker.can_execute():
                raise Exception("熔斷器開啟，拒絕執行")

            try:
                # 設置超時
                if self.config.timeout:
                    future = self.executor.submit(func, *args, **kwargs)
                    result = future.result(timeout=self.config.timeout)
                else:
                    result = func(*args, **kwargs)

                # 記錄成功
                if self.circuit_breaker:
                    self.circuit_breaker.record_success()

                if self.metrics:
                    response_time = time.time() - start_time
                    self.metrics.record_call(True, attempt, response_time)

                if attempt > 0:
                    logger.info(f"{func.__name__} 在第 {attempt + 1} 次嘗試後成功")

                return result

            except self.config.exceptions as e:
                _ = e

                # 檢查是否為永久性錯誤
                if self._is_permanent_error(e):
                    logger.error(f"{func.__name__} 遇到永久性錯誤，不重試: {e}")
                    if self.metrics:
                        response_time = time.time() - start_time
                        self.metrics.record_call(False, attempt, response_time)
                    raise e

                # 記錄失敗（只有暫時性錯誤才觸發熔斷器）
                if self.circuit_breaker and self._should_trigger_circuit_breaker(e):
                    error_type = type(e).__name__
                    self.circuit_breaker.record_failure(error_type)
                elif self.circuit_breaker:
                    logger.debug(f"永久性錯誤不觸發熔斷器: {type(e).__name__}")

                if attempt == self.config.max_retries:
                    if self.metrics:
                        response_time = time.time() - start_time
                        self.metrics.record_call(False, attempt, response_time)

                    logger.error(f"{func.__name__} 在 {self.config.max_retries + 1} 次嘗試後仍然失敗: {e}")
                    break

                # 計算延遲並等待
                delay = self._calculate_delay(attempt)
                logger.warning(f"{func.__name__} 第 {attempt + 1} 次嘗試失敗 ({type(e).__name__}): {e}，{delay:.2f}秒後重試")
                time.sleep(delay)

        raise last_exception

    async def _execute_with_retry_async(self, func: Callable, args: tuple, kwargs: dict) -> Any:
        """執行帶重試的異步函數"""
        _ = time.time()
        _ = None

        for _ in range(self.config.max_retries + 1):
            # 檢查熔斷器
            if self.circuit_breaker and not self.circuit_breaker.can_execute():
                raise Exception("熔斷器開啟，拒絕執行")

            try:
                # 執行異步函數
                if self.config.timeout:
                    result = await asyncio.wait_for(func(*args, **kwargs), timeout=self.config.timeout)
                else:
                    result = await func(*args, **kwargs)

                # 記錄成功
                if self.circuit_breaker:
                    self.circuit_breaker.record_success()

                if self.metrics:
                    response_time = time.time() - start_time
                    self.metrics.record_call(True, attempt, response_time)

                if attempt > 0:
                    logger.info(f"{func.__name__} 在第 {attempt + 1} 次嘗試後成功")

                return result

            except self.config.exceptions as e:
                last_exception = e

                # 記錄失敗
                if self.circuit_breaker:
                    self.circuit_breaker.record_failure()

                if attempt == self.config.max_retries:
                    if self.metrics:
                        response_time = time.time() - start_time
                        self.metrics.record_call(False, attempt, response_time)

                    logger.error(f"{func.__name__} 在 {self.config.max_retries + 1} 次嘗試後仍然失敗: {e}")
                    break

                # 計算延遲並等待
                delay = self._calculate_delay(attempt)
                logger.warning(f"{func.__name__} 第 {attempt + 1} 次嘗試失敗: {e}，{delay:.2f}秒後重試")
                await asyncio.sleep(delay)

        raise last_exception

    def get_statistics(self) -> Dict:
        """獲取統計信息"""
        stats = {}

        if self.metrics:
            stats["metrics"] = self.metrics.get_statistics()

        if self.circuit_breaker:
            stats["circuit_breaker"] = self.circuit_breaker.get_state()

        return stats


# 便利函數和裝飾器
def enhanced_retry(max_retries: int = 3,)
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    strategy: RetryStrategy = RetryStrategy.EXPONENTIAL,
    exponential_base: float = 2.0,
    jitter: bool = True,
    exceptions: tuple = (Exception,),
    timeout: Optional[float] = None,
    circuit_breaker: bool = True,) -> Callable:
    """增強的重試裝飾器"""
    config = RetryConfig(max_retries=max_retries,)
        base_delay=base_delay,
        max_delay=max_delay,
        strategy=strategy,
        exponential_base=exponential_base,
        jitter=jitter,
        exceptions=exceptions,
        timeout=timeout,
        circuit_breaker_enabled=circuit_breaker,)

    handler = EnhancedRetryHandler(config)
    return handler.retry_sync


def enhanced_async_retry(max_retries: int = 3,)
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    strategy: RetryStrategy = RetryStrategy.EXPONENTIAL,
    exponential_base: float = 2.0,
    jitter: bool = True,
    exceptions: tuple = (Exception,),
    timeout: Optional[float] = None,
    circuit_breaker: bool = True,) -> Callable:
    """增強的異步重試裝飾器"""
    config = RetryConfig(max_retries=max_retries,)
        base_delay=base_delay,
        max_delay=max_delay,
        strategy=strategy,
        exponential_base=exponential_base,
        jitter=jitter,
        exceptions=exceptions,
        timeout=timeout,
        circuit_breaker_enabled=circuit_breaker,)

    handler = EnhancedRetryHandler(config)
    return handler.retry_async


if __name__ == "__main__":
    # 測試增強重試處理器
    import asyncio

    @enhanced_retry(max_retries=3, base_delay=0.1)
    def test_function():
        import random

        if random.random() < 0.7:  # 70% 失敗率
            raise Exception("隨機失敗")
        return "成功"

    @enhanced_async_retry(max_retries=3, base_delay=0.1)
    async def test_async_function():
        import random

        if random.random() < 0.7:  # 70% 失敗率
            raise Exception("隨機失敗")
        return "異步成功"

    # 測試同步重試
    try:
        result = test_function()
        print(f"同步測試結果: {result}")
    except Exception as e:
        print(f"同步測試失敗: {e}")

    # 測試異步重試
    async def test_async():
        try:
            result = await test_async_function()
            print(f"異步測試結果: {result}")
        except Exception as e:
            print(f"異步測試失敗: {e}")

    asyncio.run(test_async())
