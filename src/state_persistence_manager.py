#!/usr/bin/env python3
"""
狀態持久化管理器 - 系統狀態的持久化與恢復
State Persistence Manager - System state persistence and recovery
"""

import json
import pickle
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from pathlib import Path
import threading
import asyncio

# 修復 Python 3.12+ SQLite datetime 適配器問題
sqlite3.register_adapter(datetime, lambda dt: dt.isoformat())
sqlite3.register_converter("TIMESTAMP", lambda b: datetime.fromisoformat(b.decode()))

from resource_manager import get_db_connection, get_resource_stats
from global_event_bus import get_global_event_bus, Event, EventType, publish_event
from logging_config import get_logger

logger = get_logger(__name__)


class StatePersistenceManager:
    """狀態持久化管理器"""
    
    def __init__(self, db_path: str = "trading_system_state.db"):
        """
        初始化狀態持久化管理器
        
        Args:
            db_path: 數據庫文件路徑
        """
        self.db_path = db_path
        self.lock = threading.Lock()
        self.auto_save_interval = 300  # 5分鐘自動保存
        self.is_running = False
        self.save_task = None
        
        # 事件總線
        self.event_bus = get_global_event_bus()
        
        # 初始化數據庫
        self._init_database()
        
        # 設置事件監聽
        self._setup_event_subscriptions()
        
        logger.info(f"狀態持久化管理器初始化完成: {db_path}")
    
    def _init_database(self):
        """初始化數據庫表結構"""
        try:
            with get_db_connection(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 策略狀態表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS strategy_states (
                        strategy_id TEXT PRIMARY KEY,
                        state_data TEXT NOT NULL,
                        health_score REAL DEFAULT 0.5,
                        last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # 組合分配表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS portfolio_allocations (
                        strategy_id TEXT PRIMARY KEY,
                        allocated_capital REAL NOT NULL,
                        target_allocation REAL NOT NULL,
                        current_allocation REAL NOT NULL,
                        health_score REAL DEFAULT 0.5,
                        last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # 交易記錄表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS trade_records (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        strategy_id TEXT NOT NULL,
                        symbol TEXT NOT NULL,
                        side TEXT NOT NULL,
                        amount REAL NOT NULL,
                        price REAL,
                        pnl REAL,
                        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        metadata TEXT
                    )
                ''')
                
                # 系統狀態表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS system_states (
                        key TEXT PRIMARY KEY,
                        value TEXT NOT NULL,
                        last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # 績效歷史表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS performance_history (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        strategy_id TEXT NOT NULL,
                        daily_return REAL NOT NULL,
                        cumulative_return REAL,
                        date DATE NOT NULL,
                        UNIQUE(strategy_id, date)
                    )
                ''')
                
                conn.commit()
                logger.info("數據庫表結構初始化完成")
                
        except Exception as e:
            logger.error(f"數據庫初始化失敗: {e}")
            raise
    
    def _setup_event_subscriptions(self):
        """設置事件訂閱"""
        # 訂閱需要持久化的事件
        self.event_bus.subscribe(
            [
                EventType.ORDER_FILLED,
                EventType.PORTFOLIO_REBALANCE,
                EventType.STRATEGY_HEALTH_CHANGED
            ],
            self._on_persistent_event
        )
    
    def start_auto_save(self):
        """啟動自動保存"""
        if self.is_running:
            return
        
        self.is_running = True
        self.save_task = asyncio.create_task(self._auto_save_loop())
        logger.info("自動保存已啟動")
    
    def stop_auto_save(self):
        """停止自動保存"""
        self.is_running = False
        if self.save_task:
            self.save_task.cancel()
        logger.info("自動保存已停止")
    
    async def _auto_save_loop(self):
        """自動保存循環"""
        while self.is_running:
            try:
                await asyncio.sleep(self.auto_save_interval)
                
                # 發布自動保存事件
                publish_event(
                    EventType.SYSTEM_STARTUP,  # 重用事件類型
                    "state_persistence_manager",
                    {"action": "auto_save", "timestamp": datetime.now().isoformat()}
                )
                
                logger.debug("執行自動保存")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"自動保存循環異常: {e}")
    
    def save_strategy_state(self, strategy_id: str, state_data: Dict[str, Any], 
                           health_score: float = 0.5):
        """
        保存策略狀態
        
        Args:
            strategy_id: 策略ID
            state_data: 策略狀態數據
            health_score: 健康分數
        """
        try:
            with self.lock:
                with get_db_connection(self.db_path) as conn:
                    cursor = conn.cursor()
                    
                    cursor.execute('''
                        INSERT OR REPLACE INTO strategy_states 
                        (strategy_id, state_data, health_score, last_updated)
                        VALUES (?, ?, ?, ?)
                    ''', (
                        strategy_id,
                        json.dumps(state_data),
                        health_score,
                        datetime.now()
                    ))
                    
                    conn.commit()
                    
            logger.debug(f"策略狀態已保存: {strategy_id}")
            
        except Exception as e:
            logger.error(f"保存策略狀態失敗 {strategy_id}: {e}")
    
    def load_strategy_state(self, strategy_id: str) -> Optional[Dict[str, Any]]:
        """
        載入策略狀態

        Args:
            strategy_id: 策略ID

        Returns:
            策略狀態數據，如果不存在則返回None
        """
        conn = None
        try:
            with get_db_connection(self.db_path) as conn:
            cursor = conn.cursor()

            cursor.execute('''
                SELECT state_data, health_score, last_updated
                FROM strategy_states
                WHERE strategy_id = ?
            ''', (strategy_id,))

            result = cursor.fetchone()
            if result:
                state_data = json.loads(result[0])
                state_data['health_score'] = result[1]
                state_data['last_updated'] = result[2]
                return state_data

            return None

        except Exception as e:
            logger.error(f"載入策略狀態失敗 {strategy_id}: {e}")
            return None
        finally:
            if conn:
                conn.close()
    
    def save_portfolio_allocation(self, strategy_id: str, allocated_capital: float,
                                target_allocation: float, current_allocation: float,
                                health_score: float = 0.5):
        """保存組合分配"""
        try:
            with self.lock:
                with get_db_connection(self.db_path) as conn:
                    cursor = conn.cursor()
                    
                    cursor.execute('''
                        INSERT OR REPLACE INTO portfolio_allocations 
                        (strategy_id, allocated_capital, target_allocation, 
                         current_allocation, health_score, last_updated)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', (
                        strategy_id,
                        allocated_capital,
                        target_allocation,
                        current_allocation,
                        health_score,
                        datetime.now()
                    ))
                    
                    conn.commit()
                    
            logger.debug(f"組合分配已保存: {strategy_id}")
            
        except Exception as e:
            logger.error(f"保存組合分配失敗 {strategy_id}: {e}")
    
    def load_portfolio_allocations(self) -> Dict[str, Dict[str, Any]]:
        """載入所有組合分配"""
        conn = None
        try:
            allocations = {}

            with get_db_connection(self.db_path) as conn:
            cursor = conn.cursor()

            cursor.execute('''
                SELECT strategy_id, allocated_capital, target_allocation,
                       current_allocation, health_score, last_updated
                FROM portfolio_allocations
            ''')

            for row in cursor.fetchall():
                allocations[row[0]] = {
                    'allocated_capital': row[1],
                    'target_allocation': row[2],
                    'current_allocation': row[3],
                    'health_score': row[4],
                    'last_updated': row[5]
                }

            return allocations

        except Exception as e:
            logger.error(f"載入組合分配失敗: {e}")
            return {}
        finally:
            if conn:
                conn.close()
    
    def save_trade_record(self, strategy_id: str, symbol: str, side: str,
                         amount: float, price: float = None, pnl: float = None,
                         metadata: Dict[str, Any] = None):
        """保存交易記錄"""
        try:
            with self.lock:
                with get_db_connection(self.db_path) as conn:
                    cursor = conn.cursor()
                    
                    cursor.execute('''
                        INSERT INTO trade_records 
                        (strategy_id, symbol, side, amount, price, pnl, metadata)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        strategy_id,
                        symbol,
                        side,
                        amount,
                        price,
                        pnl,
                        json.dumps(metadata) if metadata else None
                    ))
                    
                    conn.commit()
                    
            logger.debug(f"交易記錄已保存: {strategy_id} {symbol} {side}")
            
        except Exception as e:
            logger.error(f"保存交易記錄失敗: {e}")
    
    def save_performance_data(self, strategy_id: str, daily_return: float,
                            cumulative_return: float = None, date: datetime = None):
        """保存績效數據"""
        try:
            if date is None:
                date = datetime.now().date()
            
            with self.lock:
                with get_db_connection(self.db_path) as conn:
                    cursor = conn.cursor()
                    
                    cursor.execute('''
                        INSERT OR REPLACE INTO performance_history 
                        (strategy_id, daily_return, cumulative_return, date)
                        VALUES (?, ?, ?, ?)
                    ''', (
                        strategy_id,
                        daily_return,
                        cumulative_return,
                        date
                    ))
                    
                    conn.commit()
                    
            logger.debug(f"績效數據已保存: {strategy_id}")
            
        except Exception as e:
            logger.error(f"保存績效數據失敗: {e}")
    
    def load_performance_history(self, strategy_id: str,
                               days: int = 252) -> List[float]:
        """載入績效歷史"""
        conn = None
        try:
            with get_db_connection(self.db_path) as conn:
            cursor = conn.cursor()

            cursor.execute('''
                SELECT daily_return FROM performance_history
                WHERE strategy_id = ?
                ORDER BY date DESC
                LIMIT ?
            ''', (strategy_id, days))

            returns = [row[0] for row in cursor.fetchall()]
            return list(reversed(returns))  # 按時間順序返回

        except Exception as e:
            logger.error(f"載入績效歷史失敗 {strategy_id}: {e}")
            return []
        finally:
            if conn:
                conn.close()
    
    def _on_persistent_event(self, event: Event):
        """處理需要持久化的事件"""
        try:
            if event.event_type == EventType.ORDER_FILLED:
                # 保存交易記錄
                data = event.data
                self.save_trade_record(
                    strategy_id=data.get('strategy_id', 'unknown'),
                    symbol=data.get('symbol', ''),
                    side=data.get('side', ''),
                    amount=data.get('amount', 0.0),
                    price=data.get('price'),
                    pnl=data.get('pnl'),
                    metadata=data
                )
            
            elif event.event_type == EventType.PORTFOLIO_REBALANCE:
                # 保存組合重新平衡記錄
                actions = event.data.get('actions', [])
                for action in actions:
                    self.save_portfolio_allocation(
                        strategy_id=action['strategy_id'],
                        allocated_capital=action['new_capital'],
                        target_allocation=action['new_allocation'],
                        current_allocation=action['new_allocation'],
                        health_score=0.5  # 默認值，實際應從策略獲取
                    )
            
            elif event.event_type == EventType.STRATEGY_HEALTH_CHANGED:
                # 更新策略健康分數
                strategy_id = event.data.get('strategy_id')
                health_score = event.data.get('health_score', 0.5)
                
                if strategy_id:
                    # 載入現有狀態並更新健康分數
                    existing_state = self.load_strategy_state(strategy_id) or {}
                    existing_state['health_score'] = health_score
                    self.save_strategy_state(strategy_id, existing_state, health_score)
            
        except Exception as e:
            logger.error(f"處理持久化事件失敗: {e}")
    
    def get_system_recovery_data(self) -> Dict[str, Any]:
        """獲取系統恢復數據"""
        try:
            recovery_data = {
                'strategy_states': {},
                'portfolio_allocations': self.load_portfolio_allocations(),
                'performance_history': {},
                'last_recovery': datetime.now().isoformat()
            }
            
            # 載入所有策略狀態
            with get_db_connection(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('SELECT strategy_id FROM strategy_states')
                strategy_ids = [row[0] for row in cursor.fetchall()]
                
                for strategy_id in strategy_ids:
                    recovery_data['strategy_states'][strategy_id] = self.load_strategy_state(strategy_id)
                    recovery_data['performance_history'][strategy_id] = self.load_performance_history(strategy_id)
            
            return recovery_data
            
        except Exception as e:
            logger.error(f"獲取系統恢復數據失敗: {e}")
            return {}
    
    def cleanup_old_data(self, days_to_keep: int = 365):
        """清理舊數據"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            
            with self.lock:
                with get_db_connection(self.db_path) as conn:
                    cursor = conn.cursor()
                    
                    # 清理舊的交易記錄
                    cursor.execute('''
                        DELETE FROM trade_records 
                        WHERE timestamp < ?
                    ''', (cutoff_date,))
                    
                    # 清理舊的績效數據
                    cursor.execute('''
                        DELETE FROM performance_history 
                        WHERE date < ?
                    ''', (cutoff_date.date(),))
                    
                    conn.commit()
                    
            logger.info(f"清理了 {days_to_keep} 天前的舊數據")
            
        except Exception as e:
            logger.error(f"清理舊數據失敗: {e}")


if __name__ == "__main__":
    # 測試狀態持久化管理器
    print("🧪 狀態持久化管理器測試")
    
    manager = StatePersistenceManager("test_state.db")
    
    # 測試保存和載入策略狀態
    test_state = {
        'current_positions': {'BTC/USDT:USDT': 0.001},
        'last_signal_time': datetime.now().isoformat(),
        'trade_count': 5
    }
    
    manager.save_strategy_state("test_strategy", test_state, 0.75)
    loaded_state = manager.load_strategy_state("test_strategy")
    
    print(f"✅ 狀態保存和載入: {loaded_state}")
    
    # 測試保存交易記錄
    manager.save_trade_record(
        "test_strategy", "BTC/USDT:USDT", "buy", 0.001, 50000, 100
    )
    
    print("✅ 交易記錄保存成功")
    
    # 清理測試文件
    import os
    if os.path.exists("test_state.db"):
        os.remove("test_state.db")
    
    print("✅ 狀態持久化管理器測試完成")
