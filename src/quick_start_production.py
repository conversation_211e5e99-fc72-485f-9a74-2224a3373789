#!/usr/bin/env python3
"""
快速生產啟動腳本 - 基於深度分析結果的企業級部署
Quick Production Start - Enterprise deployment based on deep analysis results
"""

import asyncio
import os
import sys
import json
import subprocess
from datetime import datetime
from pathlib import Path

# 添加項目路徑
sys.path.append(str(Path(__file__).parent))

from unified_portfolio_manager import get_unified_portfolio_manager
from intelligent_portfolio_system import IntelligentPortfolioSystem
from health_server import HealthServer
from logging_config import setup_logging, get_logger

setup_logging()
logger = get_logger(__name__)


class ProductionLauncher:
    """生產環境啟動器"""
    
    def __init__(self):
        self.config = self._load_production_config()
        self.systems = {}
        
    def _load_production_config(self) -> dict:
        """載入生產配置"""
        config_file = Path("production_config.json")
        
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            # 創建默認生產配置
            default_config = {
                "capital": {
                    "total_capital": 100000,
                    "max_position_size": 0.1,
                    "risk_limit": 0.02
                },
                "portfolio": {
                    "rebalance_frequency": 5,
                    "min_allocation": 0.05,
                    "max_allocation": 0.4
                },
                "monitoring": {
                    "health_check_port": 8080,
                    "prometheus_enabled": True,
                    "telegram_enabled": True
                },
                "performance": {
                    "async_enabled": True,
                    "max_concurrent_tasks": 50,
                    "api_timeout": 30
                }
            }
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=2, ensure_ascii=False)
            
            print(f"✅ 創建默認生產配置: {config_file}")
            return default_config
    
    async def run_system_checks(self) -> bool:
        """運行系統檢查"""
        print("🔍 執行系統檢查...")
        
        try:
            # 1. 運行測試套件
            print("  📋 運行測試套件...")
            result = subprocess.run([
                sys.executable, "-m", "pytest", "--tb=short", "-v"
            ], capture_output=True, text=True)
            
            if result.returncode != 0:
                print(f"  ❌ 測試失敗:\n{result.stdout}\n{result.stderr}")
                return False
            else:
                print("  ✅ 所有測試通過")
            
            # 2. 檢查配置文件
            print("  📋 檢查配置文件...")
            required_files = [".env", "config.json"]
            for file in required_files:
                if not Path(file).exists():
                    print(f"  ⚠️ 缺少配置文件: {file}")
            
            # 3. 檢查依賴
            print("  📋 檢查依賴...")
            try:
                import ccxt
                import pandas
                import numpy
                print("  ✅ 核心依賴檢查通過")
            except ImportError as e:
                print(f"  ❌ 依賴缺失: {e}")
                return False
            
            # 4. 檢查網絡連接
            print("  📋 檢查網絡連接...")
            import requests
            try:
                response = requests.get("https://api.gate.io/api/v4/spot/time", timeout=10)
                if response.status_code == 200:
                    print("  ✅ Gate.io API 連接正常")
                else:
                    print(f"  ⚠️ Gate.io API 響應異常: {response.status_code}")
            except Exception as e:
                print(f"  ⚠️ 網絡連接檢查失敗: {e}")
            
            return True
            
        except Exception as e:
            print(f"  ❌ 系統檢查失敗: {e}")
            return False
    
    async def start_unified_portfolio_system(self):
        """啟動統一投組系統"""
        print("🚀 啟動統一投組系統...")
        
        try:
            # 創建統一投組管理器
            manager = get_unified_portfolio_manager(
                total_capital=self.config["capital"]["total_capital"],
                config=self.config["portfolio"]
            )
            
            # 啟動系統
            await manager.start_system()
            
            self.systems["portfolio_manager"] = manager
            print("  ✅ 統一投組系統啟動成功")
            
            return manager
            
        except Exception as e:
            print(f"  ❌ 統一投組系統啟動失敗: {e}")
            raise
    
    async def start_health_monitoring(self):
        """啟動健康監控"""
        print("🏥 啟動健康監控系統...")
        
        try:
            # 創建健康服務器
            health_server = HealthServer()
            
            # 啟動服務器
            await health_server.start_server()
            
            self.systems["health_server"] = health_server
            print(f"  ✅ 健康監控啟動成功: http://localhost:{self.config['monitoring']['health_check_port']}")
            
            return health_server
            
        except Exception as e:
            print(f"  ❌ 健康監控啟動失敗: {e}")
            raise
    
    async def start_intelligent_system(self):
        """啟動完整智能系統"""
        print("🧠 啟動智能投組系統...")
        
        try:
            # 創建智能投組系統
            intelligent_system = IntelligentPortfolioSystem(
                total_capital=self.config["capital"]["total_capital"]
            )
            
            # 啟動系統
            await intelligent_system.start_system()
            
            self.systems["intelligent_system"] = intelligent_system
            print("  ✅ 智能投組系統啟動成功")
            
            return intelligent_system
            
        except Exception as e:
            print(f"  ❌ 智能投組系統啟動失敗: {e}")
            raise
    
    async def start_all_systems(self):
        """啟動所有系統"""
        print("🎯 啟動企業級交易系統...")
        print("=" * 60)
        
        try:
            # 1. 系統檢查
            if not await self.run_system_checks():
                print("❌ 系統檢查失敗，停止啟動")
                return False
            
            print("\n" + "=" * 60)
            
            # 2. 啟動健康監控
            await self.start_health_monitoring()
            
            # 3. 啟動統一投組系統
            await self.start_unified_portfolio_system()
            
            # 4. 啟動智能系統
            await self.start_intelligent_system()
            
            print("\n" + "=" * 60)
            print("🎉 所有系統啟動成功！")
            print(f"📊 監控地址: http://localhost:{self.config['monitoring']['health_check_port']}")
            print(f"🏥 健康檢查: http://localhost:{self.config['monitoring']['health_check_port']}/health")
            print(f"📈 系統狀態: http://localhost:{self.config['monitoring']['health_check_port']}/status")
            print("=" * 60)
            
            return True
            
        except Exception as e:
            print(f"❌ 系統啟動失敗: {e}")
            await self.stop_all_systems()
            return False
    
    async def stop_all_systems(self):
        """停止所有系統"""
        print("🛑 停止所有系統...")
        
        for name, system in self.systems.items():
            try:
                if hasattr(system, 'stop_system'):
                    await system.stop_system()
                elif hasattr(system, 'stop_server'):
                    await system.stop_server()
                print(f"  ✅ {name} 已停止")
            except Exception as e:
                print(f"  ⚠️ {name} 停止失敗: {e}")
        
        self.systems.clear()
        print("✅ 所有系統已停止")
    
    async def run_production(self):
        """運行生產環境"""
        try:
            # 啟動所有系統
            if not await self.start_all_systems():
                return
            
            print("\n🚀 系統正在運行...")
            print("按 Ctrl+C 安全停止系統")
            
            # 保持運行
            while True:
                await asyncio.sleep(60)
                
                # 定期健康檢查
                try:
                    for name, system in self.systems.items():
                        if hasattr(system, 'get_health_data'):
                            health_data = system.get_health_data()
                            if health_data.get('status') != 'healthy':
                                print(f"⚠️ {name} 健康狀態異常: {health_data}")
                except Exception as e:
                    logger.error(f"健康檢查失敗: {e}")
                
        except KeyboardInterrupt:
            print("\n🛑 收到停止信號...")
        except Exception as e:
            print(f"❌ 運行時錯誤: {e}")
        finally:
            await self.stop_all_systems()


async def main():
    """主函數"""
    print("🎯 企業級智能量化交易系統")
    print("基於深度分析結果的生產部署")
    print("=" * 60)
    
    launcher = ProductionLauncher()
    await launcher.run_production()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 系統已安全退出")
    except Exception as e:
        print(f"❌ 啟動失敗: {e}")
        sys.exit(1)
