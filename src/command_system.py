import logging

# 配置logger
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
命令系統 - 重構main.py的命令模式實現
Command System - Command Pattern Implementation for main.py Refactoring
"""

import asyncio
import signal
from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from logging_config import get_logger

_ = get_logger(__name__)


class CommandType(Enum):
    """命令類型枚舉"""

    _ = "live"
    _ = "backtest"
    _ = "portfolio"
    _ = "data"
    _ = "health"
    _ = "validate"


@dataclass
class CommandContext:
    """命令執行上下文"""

    command_type: CommandType
    config_file: str
    args: Dict[str, Any]
    start_time: datetime
    correlation_id: str


class Command(ABC):
    """命令抽象基類"""

    def __init__(self, name: str):
        self.name = name
        self.running = False
        self.context: Optional[CommandContext] = None
        self.result: Optional[Dict[str, Any]] = None

    @abstractmethod
    async def execute(self, context: CommandContext) -> Dict[str, Any]:
        """執行命令"""
        pass

    @abstractmethod
    async def cleanup(self):
        """清理資源"""
        pass

    def can_execute(self, context: CommandContext) -> bool:
        """檢查是否可以執行"""
        return True

    async def run(self, context: CommandContext) -> Dict[str, Any]:
        """運行命令的包裝方法"""
        try:
            if not self.can_execute(context):
                raise ValueError(f"命令 {self.name} 無法在當前上下文中執行")

            self.context = context
            self.running = True

            logger.info(f"開始執行命令: {self.name}")
            start_time = datetime.now()

            # 執行命令
            self.result = await self.execute(context)

            execution_time = (datetime.now() - start_time).total_seconds()
            logger.info(f"命令 {self.name} 執行完成，耗時 {execution_time:.2f}s")

            return self.result

        except Exception as e:
            logger.error(f"命令 {self.name} 執行失敗: {e}")
            raise
        finally:
            self.running = False
            await self.cleanup()


class LiveTradingCommand(Command):
    """實時交易命令"""

    def __init__(self):
        super().__init__("LiveTradingCommand")
        self.trading_bot = None
        self.health_server = None

    async def execute(self, context: CommandContext) -> Dict[str, Any]:
        """執行實時交易"""
        try:
            # 導入必要的模組
            from health_server import HealthServer
            from pair_trading_bot import PairTradingBot

            from .event_driven_core import get_event_bus

            # 初始化事件總線
            event_bus = get_event_bus()
            await event_bus.start()

            # 創建交易機器人
            self.trading_bot = PairTradingBot(config_file=context.config_file, event_bus=event_bus)

            # 啟動健康檢查服務器
            health_port = context.args.get("health_port", 8080)
            self.health_server = HealthServer(port=health_port)

            # 在後台啟動健康服務器
            _ = asyncio.create_task(self._run_health_server())

            # 運行交易機器人
            await self.trading_bot.run()

            return {
                "status": "completed",
                "trades_executed": getattr(self.trading_bot, "trades_executed", 0),
                "runtime_seconds": (datetime.now() - context.start_time).total_seconds(),
            }

        except KeyboardInterrupt:
            logger.info("收到中斷信號，正在優雅關閉...")
            return {"status": "interrupted"}
        except Exception as e:
            logger.error(f"實時交易執行失敗: {e}")
            return {"status": "failed", "error": str(e)}

    async def _run_health_server(self):
        """運行健康檢查服務器"""
        try:
            if self.health_server:
                await asyncio.to_thread(self.health_server.start)
        except Exception as e:
            logger.error(f"健康檢查服務器啟動失敗: {e}")

    async def cleanup(self):
        """清理資源"""
        try:
            if self.trading_bot:
                await self.trading_bot.stop()

            if self.health_server:
                self.health_server.stop()

            # 停止事件總線
            event_bus = get_event_bus()
            await event_bus.stop()

            logger.info("實時交易命令清理完成")

        except Exception as e:
            logger.error(f"清理資源失敗: {e}")


class BacktestCommand(Command):
    """回測命令"""

    def __init__(self):
        super().__init__("BacktestCommand")
        self.backtest_engine = None

    async def execute(self, context: CommandContext) -> Dict[str, Any]:
        """執行回測"""
        try:
            # 導入回測引擎
            from backtest_engine import BacktestEngine

            # 創建回測引擎
            self.backtest_engine = BacktestEngine(config_file=context.config_file)

            # 設置回測參數
            start_date = context.args.get("start_date")
            end_date = context.args.get("end_date")
            initial_capital = context.args.get("initial_capital", 10000)

            # 運行回測
            results = await self.backtest_engine.run_backtest(
                start_date=start_date, end_date=end_date, initial_capital=initial_capital
            )

            # 生成報告
            report_path = context.args.get("report_path", "backtest_report.html")
            await self.backtest_engine.generate_report(report_path)

            return {
                "status": "completed",
                "results": results,
                "report_path": report_path,
                "runtime_seconds": (datetime.now() - context.start_time).total_seconds(),
            }

        except Exception as e:
            logger.error(f"回測執行失敗: {e}")
            return {"status": "failed", "error": str(e)}

    async def cleanup(self):
        """清理資源"""
        try:
            if self.backtest_engine:
                await self.backtest_engine.cleanup()

            logger.info("回測命令清理完成")

        except Exception as e:
            logger.error(f"清理資源失敗: {e}")


class PortfolioTradingCommand(Command):
    """投資組合交易命令"""

    def __init__(self):
        super().__init__("PortfolioTradingCommand")
        self.portfolio_manager = None
        self.health_server = None

    async def execute(self, context: CommandContext) -> Dict[str, Any]:
        """執行投資組合交易"""
        try:
            # 導入必要的模組
            from health_server import HealthServer
            from portfolio_manager import PortfolioManager

            from .event_driven_core import get_event_bus

            # 初始化事件總線
            event_bus = get_event_bus()
            await event_bus.start()

            # 獲取投資組合管理器
            self.portfolio_manager = PortfolioManager(total_capital=100000)

            # 啟動健康檢查服務器
            health_port = context.args.get("health_port", 8080)
            self.health_server = HealthServer(port=health_port)

            # 在後台啟動健康服務器
            _ = asyncio.create_task(self._run_health_server())

            # 運行投資組合交易
            await self.portfolio_manager.run_portfolio_trading()

            return {
                "status": "completed",
                "portfolio_value": await self.portfolio_manager.get_total_value(),
                "active_pairs": len(self.portfolio_manager.active_pairs),
                "runtime_seconds": (datetime.now() - context.start_time).total_seconds(),
            }

        except KeyboardInterrupt:
            logger.info("收到中斷信號，正在優雅關閉...")
            return {"status": "interrupted"}
        except Exception as e:
            logger.error(f"投資組合交易執行失敗: {e}")
            return {"status": "failed", "error": str(e)}

    async def _run_health_server(self):
        """運行健康檢查服務器"""
        try:
            if self.health_server:
                await asyncio.to_thread(self.health_server.start)
        except Exception as e:
            logger.error(f"健康檢查服務器啟動失敗: {e}")

    async def cleanup(self):
        """清理資源"""
        try:
            if self.portfolio_manager:
                await self.portfolio_manager.stop()

            if self.health_server:
                self.health_server.stop()

            # 停止事件總線
            event_bus = get_event_bus()
            await event_bus.stop()

            logger.info("投資組合交易命令清理完成")

        except Exception as e:
            logger.error(f"清理資源失敗: {e}")


class HealthCheckCommand(Command):
    """健康檢查命令"""

    def __init__(self):
        super().__init__("HealthCheckCommand")

    async def execute(self, context: CommandContext) -> Dict[str, Any]:
        """執行健康檢查"""
        try:
            # 導入健康檢查模組
            from health_checker import SystemHealthChecker

            # 創建健康檢查器
            health_checker = SystemHealthChecker()

            # 執行全面健康檢查
            health_results = await health_checker.comprehensive_check()

            return {
                "status": "completed",
                "health_results": health_results,
                "overall_healthy": health_results.get("overall_healthy", False),
                "runtime_seconds": (datetime.now() - context.start_time).total_seconds(),
            }

        except Exception as e:
            logger.error(f"健康檢查執行失敗: {e}")
            return {"status": "failed", "error": str(e)}

    async def cleanup(self):
        """清理資源"""
        logger.info("健康檢查命令清理完成")


class CommandFactory:
    """命令工廠"""

    _commands = {
        CommandType.LIVE_TRADING: LiveTradingCommand,
        CommandType.BACKTEST: BacktestCommand,
        CommandType.PORTFOLIO_TRADING: PortfolioTradingCommand,
        CommandType.HEALTH_CHECK: HealthCheckCommand,
    }

    @classmethod
    def create_command(cls, command_type: CommandType) -> Command:
        """創建命令"""
        command_class = cls._commands.get(command_type)

        if not command_class:
            raise ValueError(f"不支持的命令類型: {command_type}")

        return command_class()

    @classmethod
    def register_command(cls, command_type: CommandType, command_class: type):
        """註冊新命令"""
        cls._commands[command_type] = command_class

    @classmethod
    def get_available_commands(cls) -> List[CommandType]:
        """獲取可用命令列表"""
        return list(cls._commands.keys())


class CommandExecutor:
    """命令執行器"""

    def __init__(self):
        self.current_command: Optional[Command] = None
        self.shutdown_requested = False

        # 設置信號處理
        self._setup_signal_handlers()

    def _setup_signal_handlers(self):
        """設置信號處理器"""

        def signal_handler(signum, frame):
            logger.info(f"收到信號 {signum}，請求優雅關閉...")
            self.shutdown_requested = True

            if self.current_command and self.current_command.running:
                asyncio.create_task(self.current_command.cleanup())

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

    async def execute_command(
        self, command_type: CommandType, config_file: str, **kwargs
    ) -> Dict[str, Any]:
        """執行命令"""
        try:
            # 創建命令
            command = CommandFactory.create_command(command_type)
            self.current_command = command

            # 創建執行上下文
            context = CommandContext()
                command_type=command_type,
                config_file=config_file,
                args=kwargs,
                start_time=datetime.now(),
                correlation_id=f"{command_type.value}_{int(datetime.now().timestamp())}",
            )

            # 執行命令
            result = await command.run(context)

            return result

        except Exception as e:
            logger.error(f"命令執行失敗: {e}")
            return {"status": "failed", "error": str(e)}
        finally:
            self.current_command = None


# 全局命令執行器
_command_executor = None


def get_command_executor() -> CommandExecutor:
    """獲取全局命令執行器"""
    global _command_executor
    if _command_executor is None:
        _command_executor = CommandExecutor()
    return _command_executor


async def main():
    """測試命令系統"""
    executor = get_command_executor()

    # 測試健康檢查命令
    result = await executor.execute_command(CommandType.HEALTH_CHECK, config_file="config.json")

    print(f"命令執行結果: {result}")


if __name__ == "__main__":
    asyncio.run(main())
