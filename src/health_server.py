import logging
from unittest.mock import Mock

# 配置logger
logger = logging.getLogger(__name__)

#!/usr/bin/env python3
"""
健康檢查 Web 服務
Health Check Web Service
"""

import json
import threading
import time
from datetime import datetime, timedelta
from typing import Any, Dict

import psutil
from flask import Flask, Response, jsonify, request

from alert_manager import get_alert_manager
from logging_config import get_logger
from portfolio_manager import PortfolioManager

# 修復導入問題
try:
    from performance_optimization.memory_optimizer import get_memory_monitor
except ImportError:
    # 如果導入失敗，提供一個備用函數
    def get_memory_monitor():
        class MockMemoryMonitor:
            def get_memory_report(self):
                return {"status": "memory_monitor_not_available"}

        return MockMemoryMonitor()


_ = get_logger(__name__)

# 全局組合管理器實例
_portfolio_manager = None


def get_portfolio_manager():
    """獲取組合管理器實例"""
    global _portfolio_manager
    if _portfolio_manager is None:
        _portfolio_manager = PortfolioManager(total_capital=100000)
    return _portfolio_manager


class HealthServer:
    """健康檢查服務器"""

    def __init__(self, host: str = "0.0.0.0", port: int = 8080):
        self.host = host
        self.port = port
        self.app = Flask(__name__)
        self.server_thread = None
        self.is_running = False

        # 設置路由
        self._setup_routes()

        # 初始化監控組件
        self.memory_monitor = get_memory_monitor()
        self.start_time = datetime.now()
        self.request_count = 0
        self.error_count = 0

        logger.info(f"HealthServer 初始化完成 - {host}:{port}")

    def _setup_routes(self):
        """設置 API 路由"""

        @self.app.route("/health", methods=["GET"])
        def health_check():
            """健康檢查端點"""
            try:
                alert_manager = get_alert_manager()
                health_status = alert_manager.get_health_status()

                return jsonify({
                            "status": "ok",
                            "timestamp": datetime.now().isoformat(),
                            "service": "pair_trading_bot",
                            "health": health_status,
                        }), 200

            except Exception as e:
                logger.error(f"健康檢查失敗: {e}")
                return jsonify({
                    "status": "error",
                    "timestamp": datetime.now().isoformat(),
                    "error": str(e),
                }), 500

        @self.app.route("/status", methods=["GET"])
        def detailed_status():
            """詳細狀態端點"""
            try:
                alert_manager = get_alert_manager()
                health_status = alert_manager.get_health_status()

                # 獲取更詳細的狀態信息
                detailed_info = {
                    "service_info": {
                        "name": "Pair Trading Bot",
                        "version": "2.0",
                        "environment": "production",
                    },
                    "system_health": health_status,
                    "endpoints": {
                        "health": "/health",
                        "status": "/status",
                        "metrics": "/metrics",
                        "control": "/control",
                    },
                }

                return jsonify(detailed_info), 200

            except Exception as e:
                logger.error(f"獲取詳細狀態失敗: {e}")
                return jsonify({"status": "error", "error": str(e)}), 500

        @self.app.route("/control", methods=["POST"])
        def control():
            """控制端點"""
            try:
                data = request.get_json()
                command = data.get("command")

                if command == "reload_config":
                    # 重新載入配置的邏輯
                    return jsonify({"status": "success", "message": "配置重新載入請求已接收"}), 200

                elif command == "emergency_stop":
                    # 緊急停止的邏輯
                    alert_manager = get_alert_manager()
                    alert_manager.send_critical_alert(
                        "緊急停止", "收到遠程緊急停止指令", {"來源": request.remote_addr}
                    )

                    return jsonify({"status": "success", "message": "緊急停止指令已發送"}), 200

                else:
                    return jsonify({"status": "error", "message": f"未知命令: {command}"}), 400

            except Exception as e:
                logger.error(f"控制命令執行失敗: {e}")
                return jsonify({"status": "error", "error": str(e)}), 500

        @self.app.errorhandler(404)
        def not_found(error):
            return jsonify({
                        "status": "error",
                        "message": "Endpoint not found",
                        "available_endpoints": ["/health", "/status", "/metrics", "/control"],
                    }), 200

        @self.app.route("/portfolio", methods=["GET"])
        def portfolio_status():
            """投資組合狀態端點"""
            try:
                portfolio_manager = get_portfolio_manager()
                portfolio_status = portfolio_manager.get_portfolio_status()

                return jsonify({
                            "status": "ok",
                            "timestamp": datetime.now().isoformat(),
                            "portfolio": portfolio_status,
                        }), 200

            except Exception as e:
                logger.error(f"獲取投資組合狀態失敗: {e}")
                return jsonify({"status": "error", "error": str(e)}), 500

        @self.app.route("/dashboard", methods=["GET"])
        def dashboard():
            """儀表板頁面"""
            try:
                # 返回簡單的HTML儀表板
                html_content = self._generate_dashboard_html()
                return html_content, 200, {"Content-Type": "text/html"}

            except Exception as e:
                logger.error(f"生成儀表板失敗: {e}")
                return jsonify({"status": "error", "error": str(e)}), 500

        @self.app.route("/metrics", methods=["GET"])
        def prometheus_metrics():
            """Prometheus格式的監控指標 - 工業級完整版"""
            try:
                self.request_count += 1

                # 獲取系統指標
                process = psutil.Process()
                memory_info = process.memory_info()
                cpu_percent = process.cpu_percent()

                # 計算運行時間
                uptime_seconds = (datetime.now() - self.start_time).total_seconds()

                # 生成Prometheus格式指標
                metrics = []

                # 系統基礎指標
                metrics.extend([
                        "# HELP trading_bot_memory_usage_bytes Memory usage in bytes",
                        "# TYPE trading_bot_memory_usage_bytes gauge",
                        f"trading_bot_memory_usage_bytes {memory_info.rss}",
                        "",
                        "# HELP trading_bot_cpu_usage_percent CPU usage percentage",
                        "# TYPE trading_bot_cpu_usage_percent gauge",
                        f"trading_bot_cpu_usage_percent {cpu_percent}",
                        "",
                        "# HELP trading_bot_uptime_seconds Uptime in seconds",
                        "# TYPE trading_bot_uptime_seconds counter",
                        f"trading_bot_uptime_seconds {uptime_seconds}",
                        "",
                        "# HELP trading_bot_http_requests_total Total HTTP requests",
                        "# TYPE trading_bot_http_requests_total counter",
                        f"trading_bot_http_requests_total {self.request_count}",
                        "",
                ])

                return Response("\n".join(metrics), mimetype="text/plain")

            except Exception as e:
                self.error_count += 1
                logger.error(f"生成Prometheus指標失敗: {e}")
                return Response(
                    f"# Error generating metrics: {str(e)}", mimetype="text/plain", status=500
                )

        @self.app.route("/memory", methods=["GET"])
        def memory_status():
            """內存狀態詳情端點"""
            try:
                import gc

                # 獲取進程內存信息
                process = psutil.Process()
                memory_info = process.memory_info()

                # 獲取系統內存信息
                system_memory = psutil.virtual_memory()

                # 獲取垃圾回收統計
                gc_stats = gc.get_stats()
                gc_counts = gc.get_count()

                memory_data = {
                    "process_memory": {
                        "rss_mb": memory_info.rss / 1024 / 1024,
                        "vms_mb": memory_info.vms / 1024 / 1024,
                        "percent": process.memory_percent(),
                    },
                    "system_memory": {
                        "total_mb": system_memory.total / 1024 / 1024,
                        "available_mb": system_memory.available / 1024 / 1024,
                        "used_mb": system_memory.used / 1024 / 1024,
                        "percent": system_memory.percent,
                    },
                    "garbage_collection": {"counts": gc_counts, "stats": gc_stats},
                }

                return jsonify(
                    {"status": "ok", "timestamp": datetime.now().isoformat(), "memory": memory_data}
                )

            except Exception as e:
                logger.error(f"獲取內存狀態失敗: {e}")
                return jsonify({
                            "status": "error",
                            "timestamp": datetime.now().isoformat(),
                            "error": str(e),
                        }), 200

        @self.app.route("/performance", methods=["GET"])
        def performance_metrics():
            """性能指標詳情端點"""
            try:
                # 獲取進程性能指標
                process = psutil.Process()

                # CPU指標
                cpu_times = process.cpu_times()
                cpu_percent = process.cpu_percent()

                # 運行時間
                create_time = process.create_time()
                uptime_seconds = time.time() - create_time

                performance_data = {
                    "cpu": {
                        "percent": cpu_percent,
                        "user_time": cpu_times.user,
                        "system_time": cpu_times.system,
                    },
                    "threads": {"count": process.num_threads()},
                    "uptime": {
                        "seconds": uptime_seconds,
                        "formatted": str(timedelta(seconds=int(uptime_seconds))),
                    },
                }

                return jsonify()
                    {
                        "status": "ok",
                        "timestamp": datetime.now().isoformat(),
                        "performance": performance_data,
                    }
                )

            except Exception as e:
                logger.error(f"獲取性能指標失敗: {e}")
                return jsonify({
                            "status": "error",
                            "timestamp": datetime.now().isoformat(),
                            "error": str(e),
                        }), 200

        @self.app.errorhandler(500)
        def internal_error(error):
            return jsonify({"status": "error", "message": "Internal server error"}), 500

    def start(self):
        """啟動健康檢查服務器"""
        try:
            if self.is_running:
                logger.warning("健康檢查服務器已在運行")
                return

            def run_server():
                self.app.run()
                    host=self.host, port=self.port, debug=False, use_reloader=False, threaded=True
                )

            self.server_thread = threading.Thread(target=run_server, daemon=True)
            self.server_thread.start()
            self.is_running = True

            logger.info(f"健康檢查服務器已啟動: http://{self.host}:{self.port}")

        except Exception as e:
            logger.error(f"啟動健康檢查服務器失敗: {e}")
            raise

    def stop(self):
        """停止健康檢查服務器"""
        try:
            if not self.is_running:
                return

            self.is_running = False
            logger.info("健康檢查服務器已停止")

        except Exception as e:
            logger.error(f"停止健康檢查服務器失敗: {e}")

    def _generate_dashboard_html(self) -> str:
        """生成儀表板HTML"""
        try:
            # 獲取數據
            alert_manager = get_alert_manager()
            _ = alert_manager.get_health_status()

            try:
                portfolio_manager = get_portfolio_manager()
                portfolio_status = portfolio_manager.get_portfolio_status()
            except Exception:
                _ = {"portfolio_stats": {}, "bot_status": {}}

            html = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>配對交易機器人儀表板</title>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1">
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }}
                    .container {{ max-width: 1200px; margin: 0 auto; }}
                    .card {{ background: white; padding: 20px; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
                    .header {{ text-align: center; color: #333; }}
                    .status-ok {{ color: #28a745; }}
                    .status-error {{ color: #dc3545; }}
                    .status-warning {{ color: #ffc107; }}
                    .grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }}
                    .metric {{ display: flex; justify-content: space-between; margin: 10px 0; }}
                    .metric-label {{ font-weight: bold; }}
                    .button {{ background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }}
                    .button:hover {{ background: #0056b3; }}
                    .emergency-button {{ background: #dc3545; }}
                    .emergency-button:hover {{ background: #c82333; }}
                    table {{ width: 100%; border-collapse: collapse; }}
                    th, td {{ padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }}
                    th {{ background-color: #f8f9fa; }}
                </style>
                <script>
                    function refreshData() {{
                        location.reload();
                    }}

                    function emergencyStop() {{
                        if (confirm('確定要執行緊急停止嗎？')) {{
                            fetch('/control', {{)
                                method: 'POST',
                                headers: {{'Content-Type': 'application/json'}},
                                body: JSON.stringify({{'command': 'emergency_stop'}})
                            }})
                            .then(response => response.json())
                            .then(data => {{
                                alert('緊急停止指令已發送: ' + data.message);
                                refreshData();
                            }})
                            .catch(error => alert('錯誤: ' + error));
                        }}
                    }}

                    setInterval(refreshData, 30000); // 30秒自動刷新
                </script>
            </head>
            <body>
                <div class="container">
                    <h1 class="header">🤖 配對交易機器人儀表板</h1>

                    <div class="card">
                        <h2>系統狀態</h2>
                        <div class="metric">
                            <span class="metric-label">系統狀態:</span>
                            <span class="{'status-ok' if health_status.get('status') == 'ok' else 'status-error'}">
                                {health_status.get('status', 'unknown').upper()}
                            </span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">運行時間:</span>
                            <span>{health_status.get('uptime_human', 'N/A')}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">最後更新:</span>
                            <span>{health_status.get('last_update', 'N/A')}</span>
                        </div>
                        <div style="margin-top: 20px;">
                            <button class="button" onclick="refreshData()">🔄 刷新數據</button>
                            <button class="button emergency-button" onclick="emergencyStop()">🚨 緊急停止</button>
                        </div>
                    </div>

                    <div class="grid">
                        <div class="card">
                            <h3>投資組合概覽</h3>
                            <div class="metric">
                                <span class="metric-label">總資金:</span>
                                <span>${portfolio_status.get('portfolio_stats', {}).get('total_capital', 0):,.2f}</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">總盈虧:</span>
                                <span class="{'status-ok' if portfolio_status.get('portfolio_stats', {}).get('total_pnl', 0) >= 0 else 'status-error'}">
                                    ${portfolio_status.get('portfolio_stats', {}).get('total_pnl', 0):,.2f}
                                </span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">活躍配對:</span>
                                <span>{portfolio_status.get('portfolio_stats', {}).get('active_pairs', 0)}</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">總交易數:</span>
                                <span>{portfolio_status.get('portfolio_stats', {}).get('total_trades', 0)}</span>
                            </div>
                        </div>

                        <div class="card">
                            <h3>風險指標</h3>
                            <div class="metric">
                                <span class="metric-label">已分配資金:</span>
                                <span>${portfolio_status.get('portfolio_stats', {}).get('allocated_capital', 0):,.2f}</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">可用資金:</span>
                                <span>${portfolio_status.get('portfolio_stats', {}).get('available_capital', 0):,.2f}</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">總警報數:</span>
                                <span>{health_status.get('total_alerts', 0)}</span>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <h3>活躍交易配對</h3>
                        <table>
                            <thead>
                                <tr>
                                    <th>配對</th>
                                    <th>狀態</th>
                                    <th>盈虧</th>
                                    <th>交易數</th>
                                    <th>當前倉位</th>
                                    <th>最後更新</th>
                                </tr>
                            </thead>
                            <tbody>
            """

            # 添加配對數據
            bot_status = portfolio_status.get("bot_status", {})
            if bot_status:
                for pair_key, status in bot_status.items():
                    pnl = status.get("total_pnl", 0)
                    pnl_class = "status-ok" if pnl >= 0 else "status-error"
                    position = status.get("current_position", {})
                    position_text = "有倉位" if position.get("is_active") else "無倉位"

                    html += """
                                <tr>
                                    <td>{pair_key}</td>
                                    <td>{status.get('status', 'unknown')}</td>
                                    <td class="{pnl_class}">${pnl:.2f}</td>
                                    <td>{status.get('total_trades', 0)}</td>
                                    <td>{position_text}</td>
                                    <td>{status.get('last_update', 'N/A')}</td>
                                </tr>
                    """
            else:
                html += """
                                <tr>
                                    <td colspan="6" style="text-align: center;">暫無活躍配對</td>
                                </tr>
                """

            html += """
                            </tbody>
                        </table>
                    </div>

                    <div class="card">
                        <h3>API 端點</h3>
                        <ul>
                            <li><a href="/health" target="_blank">健康檢查</a> - /health</li>
                            <li><a href="/status" target="_blank">詳細狀態</a> - /status</li>
                            <li><a href="/portfolio" target="_blank">投資組合狀態</a> - /portfolio</li>
                            <li><a href="/metrics" target="_blank">指標數據</a> - /metrics</li>
                        </ul>
                    </div>

                    <div style="text-align: center; margin-top: 20px; color: #666;">
                        <p>配對交易機器人 v2.0 | 最後刷新: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                    </div>
                </div>
            </body>
            </html>
            """

            return html

        except Exception as e:
            logger.error(f"生成儀表板HTML失敗: {e}")
            return f"<html><body><h1>儀表板生成失敗</h1><p>{str(e)}</p></body></html>"

    def get_server_info(self) -> Dict:
        """獲取服務器信息"""
        return {
            "host": self.host,
            "port": self.port,
            "is_running": self.is_running,
            "endpoints": {
                "health": f"http://{self.host}:{self.port}/health",
                "status": f"http://{self.host}:{self.port}/status",
                "metrics": f"http://{self.host}:{self.port}/metrics",
                "memory": f"http://{self.host}:{self.port}/memory",
                "performance": f"http://{self.host}:{self.port}/performance",
                "control": f"http://{self.host}:{self.port}/control",
                "portfolio": f"http://{self.host}:{self.port}/portfolio",
                "dashboard": f"http://{self.host}:{self.port}/dashboard",
            },
        }


# 全局健康服務器實例
_health_server = None


def get_health_server(host: str = "0.0.0.0", port: int = 8080) -> HealthServer:
    """獲取全局健康服務器實例"""
    global _health_server
    if _health_server is None:
        _health_server = HealthServer(host, port)
    return _health_server


def start_health_server(host: str = "0.0.0.0", port: int = 8080):
    """啟動健康檢查服務器"""
    server = get_health_server(host, port)
    server.start()
    return server


if __name__ == "__main__":
    # 測試健康檢查服務器
    import time

    print("啟動健康檢查服務器測試...")

    server = HealthServer(port=8080)
    server.start()

    print("服務器已啟動，測試端點...")
    print("訪問 http://localhost:8080/health 測試健康檢查")
    print("訪問 http://localhost:8080/status 查看詳細狀態")
    print("按 Ctrl+C 停止測試")

    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n停止測試...")
        server.stop()
