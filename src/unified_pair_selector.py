#!/usr/bin/env python3
"""
統一配對選擇系統 - 整合所有配對選擇功能
Unified Pair Selection System - Consolidates all pair selection functionalities
"""

import json
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
from itertools import combinations
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd
from scipy.stats import pearsonr
from statsmodels.tsa.stattools import adfuller, coint

from alert_manager import AlertLevel, get_alert_manager
from dynamic_config import get_dynamic_config_manager
from logging_config import get_logger
from strategy_health_monitor import get_strategy_health_monitor
from utils import get_exchange_instance, load_config

logger = get_logger(__name__)


class PairSelectionMethod(str, Enum):
    """配對選擇方法"""

    BASIC = "basic"  # 基礎相關性和協整檢驗
    ENHANCED = "enhanced"  # 增強版，解決共線性問題
    ADAPTIVE = "adaptive"  # 自適應選擇，基於歷史表現
    COMPREHENSIVE = "comprehensive"  # 綜合所有方法


@dataclass
class PairAnalysisResult:
    """配對分析結果"""

    symbol_a: str
    symbol_b: str
    correlation: float
    cointegration_pvalue: float
    adf_statistic: float
    spread_volatility: float
    score: float
    method: PairSelectionMethod
    analysis_timestamp: datetime
    is_suitable: bool
    risk_warnings: List[str]


class UnifiedPairSelector:
    """統一配對選擇器 - 整合所有配對選擇功能"""

    def __init__(
        self,
        config_path: str = "config.json",
        method: PairSelectionMethod = PairSelectionMethod.COMPREHENSIVE,
    ):
        """
        初始化統一配對選擇器

        Args:
            config_path: 配置文件路徑
            method: 選擇方法
        """
        self.config = load_config(config_path)
        self.method = method

        # 配置參數
        self.pair_config = self.config.get("pair_selection", {})
        self.symbols = self.pair_config.get("symbols", [])
        self.lookback_days = self.pair_config.get("lookback_days", 90)
        self.min_correlation = self.pair_config.get("min_correlation", 0.7)
        self.max_cointegration_pvalue = self.pair_config.get("max_cointegration_pvalue", 0.05)

        # 增強參數
        self.max_correlation_threshold = 0.995  # 避免共線性
        self.min_data_points = 30
        self.volatility_threshold = 0.02

        # 自適應參數
        self.performance_weight = 0.4
        self.stability_weight = 0.3
        self.risk_weight = 0.3

        # 組件
        self.exchange = get_exchange_instance(self.config)
        self.config_manager = (
            get_dynamic_config_manager(config_path)
            if method in [PairSelectionMethod.ADAPTIVE, PairSelectionMethod.COMPREHENSIVE]
            else None
        )
        self.health_monitor = (
            get_strategy_health_monitor()
            if method in [PairSelectionMethod.ADAPTIVE, PairSelectionMethod.COMPREHENSIVE]
            else None
        )
        self.alert_manager = (
            get_alert_manager()
            if method in [PairSelectionMethod.ADAPTIVE, PairSelectionMethod.COMPREHENSIVE]
            else None
        )

        # 數據存儲
        self.price_data = {}
        self.analysis_results = []
        self.pair_performance_history = {}

        # 文件路徑
        self.results_dir = Path("pair_selection_results")
        self.results_dir.mkdir(exist_ok=True)
        self.potential_pairs_file = self.results_dir / "potential_pairs.json"
        self.pair_performance_file = Path("records/pair_performance.json")

        # 載入歷史數據
        if method in [PairSelectionMethod.ADAPTIVE, PairSelectionMethod.COMPREHENSIVE]:
            self._load_pair_performance()

        logger.info(f"UnifiedPairSelector 初始化完成，方法: {method.value}，將分析 {len(self.symbols)} 個資產")

    def select_pairs(self, max_pairs: int = 10) -> List[PairAnalysisResult]:
        """
        選擇最佳配對

        Args:
            max_pairs: 最大配對數量

        Returns:
            配對分析結果列表
        """
        logger.info(f"開始配對選擇，方法: {self.method.value}")

        # 獲取歷史數據
        self._fetch_all_historical_data()

        # 根據方法選擇配對
        if self.method == PairSelectionMethod.BASIC:
            results = self._basic_pair_selection()
        elif self.method == PairSelectionMethod.ENHANCED:
            results = self._enhanced_pair_selection()
        elif self.method == PairSelectionMethod.ADAPTIVE:
            results = self._adaptive_pair_selection()
        else:  # COMPREHENSIVE
            results = self._comprehensive_pair_selection()

        # 排序並限制數量
        results.sort(key=lambda x: x.score, reverse=True)
        selected_results = results[:max_pairs]

        # 保存結果
        self._save_results(selected_results)

        logger.info(f"配對選擇完成，找到 {len(selected_results)} 個優質配對")
        return selected_results

    def _fetch_all_historical_data(self):
        """獲取所有資產的歷史數據"""
        logger.info("獲取歷史數據...")

        for symbol in self.symbols:
            try:
                self.price_data[symbol] = self._fetch_historical_data(symbol)
                logger.debug(f"獲取 {symbol} 數據完成: {len(self.price_data[symbol])} 個數據點")
            except Exception as e:
                logger.error(f"獲取 {symbol} 數據失敗: {e}")
                continue

    def _fetch_historical_data(self, symbol: str) -> pd.DataFrame:
        """獲取單個資產的歷史數據"""
        limit = self.lookback_days * 24  # 假設1小時K線

        ohlcv = self.exchange.fetch_ohlcv(symbol, "1h", limit=limit)

        df = pd.DataFrame(ohlcv, columns=["timestamp", "open", "high", "low", "close", "volume"])
        df["timestamp"] = pd.to_datetime(df["timestamp"], unit="ms")
        df.set_index("timestamp", inplace=True)

        return df

    def _basic_pair_selection(self) -> List[PairAnalysisResult]:
        """基礎配對選擇"""
        results = []

        for symbol_a, symbol_b in combinations(self.symbols, 2):
            if symbol_a not in self.price_data or symbol_b not in self.price_data:
                continue

            try:
                result = self._analyze_pair_basic(symbol_a, symbol_b)
                if result:
                    results.append(result)
            except Exception as e:
                logger.error(f"分析配對 {symbol_a}/{symbol_b} 失敗: {e}")

        return results

    def _enhanced_pair_selection(self) -> List[PairAnalysisResult]:
        """增強配對選擇 - 解決共線性問題"""
        results = []

        for symbol_a, symbol_b in combinations(self.symbols, 2):
            if symbol_a not in self.price_data or symbol_b not in self.price_data:
                continue

            try:
                result = self._analyze_pair_enhanced(symbol_a, symbol_b)
                if result:
                    results.append(result)
            except Exception as e:
                logger.error(f"增強分析配對 {symbol_a}/{symbol_b} 失敗: {e}")

        return results

    def _adaptive_pair_selection(self) -> List[PairAnalysisResult]:
        """自適應配對選擇 - 基於歷史表現"""
        # 先進行增強選擇
        enhanced_results = self._enhanced_pair_selection()

        # 基於歷史表現調整評分
        for result in enhanced_results:
            pair_key = f"{result.symbol_a}/{result.symbol_b}"
            historical_performance = self.pair_performance_history.get(pair_key, {})

            # 調整評分
            performance_score = (
                historical_performance.get("avg_return", 0) * self.performance_weight
            )
            stability_score = (
                1 - historical_performance.get("volatility", 1)
            ) * self.stability_weight
            risk_score = (1 - historical_performance.get("max_drawdown", 1)) * self.risk_weight

            result.score = (
                result.score * 0.4 + (performance_score + stability_score + risk_score) * 0.6
            )

        return enhanced_results

    def _comprehensive_pair_selection(self) -> List[PairAnalysisResult]:
        """綜合配對選擇 - 結合所有方法"""
        # 獲取所有方法的結果
        basic_results = self._basic_pair_selection()
        enhanced_results = self._enhanced_pair_selection()
        adaptive_results = self._adaptive_pair_selection()

        # 合併結果，取最高評分
        combined_results = {}

        for results in [basic_results, enhanced_results, adaptive_results]:
            for result in results:
                pair_key = f"{result.symbol_a}/{result.symbol_b}"
                if (
                    pair_key not in combined_results
                    or result.score > combined_results[pair_key].score
                ):
                    combined_results[pair_key] = result

        return list(combined_results.values())

    def _analyze_pair_basic(self, symbol_a: str, symbol_b: str) -> Optional[PairAnalysisResult]:
        """基礎配對分析"""
        prices_a = self.price_data[symbol_a]["close"].values
        prices_b = self.price_data[symbol_b]["close"].values

        # 確保數據長度一致
        min_len = min(len(prices_a), len(prices_b))
        prices_a = prices_a[-min_len:]
        prices_b = prices_b[-min_len:]

        # 計算相關性
        correlation, _ = pearsonr(prices_a, prices_b)

        # 檢查相關性閾值
        if abs(correlation) < self.min_correlation:
            return None

        # 協整檢驗
        try:
            coint_stat, coint_pvalue, _ = coint(prices_a, prices_b)
        except Exception:
            return None

        if coint_pvalue > self.max_cointegration_pvalue:
            return None

        # 計算價差波動率
        spread = prices_a - prices_b
        spread_volatility = np.std(spread) / np.mean(np.abs(spread))

        # ADF檢驗
        adf_stat, adf_pvalue, _, _, _, _ = adfuller(spread)

        # 計算評分
        score = abs(correlation) * 0.3 + (1 - coint_pvalue) * 0.4 + (1 - adf_pvalue) * 0.3

        return PairAnalysisResult(
            symbol_a=symbol_a,
            symbol_b=symbol_b,
            correlation=correlation,
            cointegration_pvalue=coint_pvalue,
            adf_statistic=adf_stat,
            spread_volatility=spread_volatility,
            score=score,
            method=PairSelectionMethod.BASIC,
            analysis_timestamp=datetime.now(),
            is_suitable=True,
            risk_warnings=[],
        )

    def _analyze_pair_enhanced(self, symbol_a: str, symbol_b: str) -> Optional[PairAnalysisResult]:
        """增強配對分析 - 解決共線性問題"""
        prices_a = self.price_data[symbol_a]["close"].values
        prices_b = self.price_data[symbol_b]["close"].values

        # 確保數據長度一致
        min_len = min(len(prices_a), len(prices_b))
        if min_len < self.min_data_points:
            return None

        prices_a = prices_a[-min_len:]
        prices_b = prices_b[-min_len:]

        # 計算相關性
        correlation = np.corrcoef(prices_a, prices_b)[0, 1]
        risk_warnings = []

        # 檢查共線性
        if abs(correlation) > self.max_correlation_threshold:
            risk_warnings.append(f"過度相關，存在共線性風險: {correlation:.6f}")
            return None

        # 檢查相關性閾值
        if abs(correlation) < self.min_correlation:
            return None

        # 使用收益率序列進行協整檢驗
        returns_a = np.diff(np.log(prices_a))
        returns_b = np.diff(np.log(prices_b))

        # 檢查收益率穩定性
        if len(returns_a) < self.min_data_points:
            return None

        # 協整檢驗
        try:
            coint_stat, coint_pvalue, _ = coint(returns_a, returns_b)
        except Exception:
            return None

        if coint_pvalue > self.max_cointegration_pvalue:
            return None

        # 計算價差波動率
        spread = prices_a - prices_b
        spread_volatility = np.std(spread) / np.mean(np.abs(spread))

        if spread_volatility > self.volatility_threshold:
            risk_warnings.append(f"價差波動率過高: {spread_volatility:.4f}")

        # ADF檢驗
        adf_stat, adf_pvalue, _, _, _, _ = adfuller(spread)

        # 增強評分計算
        correlation_score = abs(correlation) * 0.25
        cointegration_score = (1 - coint_pvalue) * 0.35
        stationarity_score = (1 - adf_pvalue) * 0.25
        volatility_score = max(0, 1 - spread_volatility / self.volatility_threshold) * 0.15

        score = correlation_score + cointegration_score + stationarity_score + volatility_score

        return PairAnalysisResult(
            symbol_a=symbol_a,
            symbol_b=symbol_b,
            correlation=correlation,
            cointegration_pvalue=coint_pvalue,
            adf_statistic=adf_stat,
            spread_volatility=spread_volatility,
            score=score,
            method=PairSelectionMethod.ENHANCED,
            analysis_timestamp=datetime.now(),
            is_suitable=True,
            risk_warnings=risk_warnings,
        )

    def _load_pair_performance(self):
        """載入配對歷史表現"""
        try:
            if self.pair_performance_file.exists():
                with open(self.pair_performance_file, "r", encoding="utf-8") as f:
                    self.pair_performance_history = json.load(f)
                logger.info(f"載入 {len(self.pair_performance_history)} 個配對的歷史表現")
        except Exception as e:
            logger.error(f"載入配對歷史表現失敗: {e}")
            self.pair_performance_history = {}

    def _save_results(self, results: List[PairAnalysisResult]):
        """保存分析結果"""
        try:
            # 轉換為可序列化的格式
            serializable_results = []
            for result in results:
                serializable_results.append(
                    {
                        "symbol_a": result.symbol_a,
                        "symbol_b": result.symbol_b,
                        "correlation": float(result.correlation),
                        "cointegration_pvalue": float(result.cointegration_pvalue),
                        "adf_statistic": float(result.adf_statistic),
                        "spread_volatility": float(result.spread_volatility),
                        "score": float(result.score),
                        "method": result.method.value,
                        "analysis_timestamp": result.analysis_timestamp.isoformat(),
                        "is_suitable": result.is_suitable,
                        "risk_warnings": result.risk_warnings,
                    }
                )

            # 保存到文件
            output_data = {
                "analysis_timestamp": datetime.now().isoformat(),
                "method": self.method.value,
                "total_pairs_analyzed": len(serializable_results),
                "pairs": serializable_results,
            }

            with open(self.potential_pairs_file, "w", encoding="utf-8") as f:
                json.dump(output_data, f, indent=2, ensure_ascii=False)

            logger.info(f"分析結果已保存到 {self.potential_pairs_file}")

        except Exception as e:
            logger.error(f"保存分析結果失敗: {e}")

    def get_current_best_pair(self) -> Optional[Tuple[str, str]]:
        """獲取當前最佳配對"""
        if not self.analysis_results:
            return None

        best_result = max(self.analysis_results, key=lambda x: x.score)
        return (best_result.symbol_a, best_result.symbol_b)

    def update_pair_performance(self, symbol_a: str, symbol_b: str, performance_metrics: Dict):
        """更新配對表現"""
        pair_key = f"{symbol_a}/{symbol_b}"

        if pair_key not in self.pair_performance_history:
            self.pair_performance_history[pair_key] = {
                "trades": 0,
                "total_return": 0,
                "avg_return": 0,
                "volatility": 0,
                "max_drawdown": 0,
                "last_updated": datetime.now().isoformat(),
            }

        # 更新指標
        history = self.pair_performance_history[pair_key]
        history["trades"] += 1
        history["total_return"] += performance_metrics.get("return", 0)
        history["avg_return"] = history["total_return"] / history["trades"]
        history["volatility"] = performance_metrics.get("volatility", history["volatility"])
        history["max_drawdown"] = max(
            history["max_drawdown"], performance_metrics.get("drawdown", 0)
        )
        history["last_updated"] = datetime.now().isoformat()

        # 保存更新
        try:
            self.pair_performance_file.parent.mkdir(exist_ok=True)
            with open(self.pair_performance_file, "w", encoding="utf-8") as f:
                json.dump(self.pair_performance_history, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"保存配對表現失敗: {e}")


# 便利函數
def get_unified_pair_selector(
    config_path: str = "config.json",
    method: PairSelectionMethod = PairSelectionMethod.COMPREHENSIVE,
) -> UnifiedPairSelector:
    """獲取統一配對選擇器實例"""
    return UnifiedPairSelector(config_path, method)


async def main():
    """測試統一配對選擇器"""
    print("🧪 測試統一配對選擇器")

    # 測試不同方法
    methods = [
        PairSelectionMethod.BASIC,
        PairSelectionMethod.ENHANCED,
        PairSelectionMethod.COMPREHENSIVE,
    ]

    for method in methods:
        print(f"\n測試方法: {method.value}")

        try:
            selector = get_unified_pair_selector(method=method)
            results = selector.select_pairs(max_pairs=5)

            print(f"  ✅ 找到 {len(results)} 個配對")
            for i, result in enumerate(results[:3], 1):
                print(f"    {i}. {result.symbol_a}/{result.symbol_b}")
                print(f"       相關性: {result.correlation:.3f}")
                print(f"       協整p值: {result.cointegration_pvalue:.4f}")
                print(f"       評分: {result.score:.3f}")

        except Exception as e:
            print(f"  ❌ 測試失敗: {e}")

    print("\n✅ 統一配對選擇器測試完成")


if __name__ == "__main__":
    import asyncio

    asyncio.run(main())
