#!/usr/bin/env python3
"""
異步資源管理器 - 基於您的建議實現統一的異步資源管理
Async Resource Manager - Unified async resource management based on your recommendations
"""

import asyncio
import weakref
from contextlib import asynccontextmanager
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, AsyncContextManager, Dict, List, Optional

import aiosqlite
import psutil

from graceful_shutdown import ShutdownComponent
from logging_config import get_logger

logger = get_logger(__name__)


class AsyncDatabaseConnectionManager(ShutdownComponent):
    """異步數據庫連接管理器"""

    def __init__(self, max_connections: int = 20):
        self.max_connections = max_connections
        self.connection_pools: Dict[str, asyncio.Queue] = {}
        self.active_connections: Dict[str, weakref.WeakSet] = {}
        self.connection_stats: Dict[str, Dict[str, int]] = {}
        self._lock = asyncio.Lock()

        logger.info(f"異步數據庫連接管理器初始化，最大連接數: {max_connections}")

    @property
    def component_name(self) -> str:
        return "AsyncDatabaseConnectionManager"

    async def _create_connection_pool(self, db_path: str) -> asyncio.Queue:
        """創建連接池"""
        pool = asyncio.Queue(maxsize=self.max_connections)

        # 預創建一些連接
        initial_connections = min(5, self.max_connections)
        for _ in range(initial_connections):
            conn = await aiosqlite.connect(db_path)
            await pool.put(conn)

        self.connection_pools[db_path] = pool
        self.active_connections[db_path] = weakref.WeakSet()
        self.connection_stats[db_path] = {
            "total_created": initial_connections,
            "total_closed": 0,
            "current_active": 0,
            "peak_active": 0,
        }

        logger.info(f"創建數據庫連接池: {db_path}，初始連接數: {initial_connections}")
        return pool

    @asynccontextmanager
    async def get_connection(self, db_path: str) -> AsyncContextManager[aiosqlite.Connection]:
        """獲取數據庫連接的異步上下文管理器"""
        async with self._lock:
            if db_path not in self.connection_pools:
                await self._create_connection_pool(db_path)

        pool = self.connection_pools[db_path]
        conn = None

        try:
            # 嘗試從池中獲取連接
            try:
                conn = await asyncio.wait_for(pool.get(), timeout=5.0)
            except asyncio.TimeoutError:
                # 池中沒有可用連接，創建新連接
                if self.connection_stats[db_path]["current_active"] < self.max_connections:
                    conn = await aiosqlite.connect(db_path)
                    self.connection_stats[db_path]["total_created"] += 1
                else:
                    raise RuntimeError(f"數據庫連接池已滿: {db_path}")

            # 更新統計
            self.active_connections[db_path].add(conn)
            self.connection_stats[db_path]["current_active"] += 1
            self.connection_stats[db_path]["peak_active"] = max(
                self.connection_stats[db_path]["peak_active"],
                self.connection_stats[db_path]["current_active"],
            )

            yield conn

        finally:
            if conn:
                # 更新統計
                self.connection_stats[db_path]["current_active"] -= 1

                # 檢查連接是否仍然有效
                try:
                    await conn.execute("SELECT 1")
                    # 連接有效，放回池中
                    await pool.put(conn)
                except Exception:
                    # 連接無效，關閉並創建新連接
                    try:
                        await conn.close()
                    except Exception:
                        pass
                    self.connection_stats[db_path]["total_closed"] += 1

    async def close_pool(self, db_path: str):
        """關閉特定數據庫的連接池"""
        if db_path not in self.connection_pools:
            return

        pool = self.connection_pools[db_path]
        closed_count = 0

        # 關閉池中的所有連接
        while not pool.empty():
            try:
                conn = await pool.get()
                await conn.close()
                closed_count += 1
            except Exception as e:
                logger.error(f"關閉連接失敗: {e}")

        # 關閉活躍連接
        if db_path in self.active_connections:
            for conn in list(self.active_connections[db_path]):
                try:
                    await conn.close()
                    closed_count += 1
                except Exception as e:
                    logger.error(f"關閉活躍連接失敗: {e}")

        # 清理
        del self.connection_pools[db_path]
        if db_path in self.active_connections:
            del self.active_connections[db_path]

        self.connection_stats[db_path]["total_closed"] += closed_count
        logger.info(f"關閉數據庫連接池: {db_path}，關閉連接數: {closed_count}")

    async def shutdown(self) -> bool:
        """關閉所有連接池"""
        try:
            logger.info("關閉所有數據庫連接池...")

            db_paths = list(self.connection_pools.keys())
            for db_path in db_paths:
                await self.close_pool(db_path)

            logger.info("所有數據庫連接池已關閉")
            return True

        except Exception as e:
            logger.error(f"關閉數據庫連接池失敗: {e}")
            return False

    def get_stats(self) -> Dict[str, Any]:
        """獲取連接統計"""
        return {"pools": list(self.connection_pools.keys()), "stats": self.connection_stats.copy()}


class SystemMonitor(ShutdownComponent):
    """系統監控器"""

    def __init__(self, check_interval: float = 30.0):
        self.check_interval = check_interval
        self.is_monitoring = False
        self.monitor_task: Optional[asyncio.Task] = None

        # 監控閾值
        self.cpu_threshold = 80.0
        self.memory_threshold = 85.0
        self.disk_threshold = 90.0

        # 監控歷史
        self.monitoring_history: List[Dict[str, Any]] = []
        self.max_history_size = 1000

        logger.info(f"系統監控器初始化，檢查間隔: {check_interval}秒")

    @property
    def component_name(self) -> str:
        return "SystemMonitor"

    async def start_monitoring(self):
        """開始監控"""
        if self.is_monitoring:
            logger.warning("系統監控已在運行")
            return

        self.is_monitoring = True
        self.monitor_task = asyncio.create_task(self._monitoring_loop())
        logger.info("系統監控已啟動")

    async def stop_monitoring(self):
        """停止監控"""
        self.is_monitoring = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        logger.info("系統監控已停止")

    async def _monitoring_loop(self):
        """監控循環"""
        while self.is_monitoring:
            try:
                # 收集系統指標
                metrics = await self._collect_system_metrics()

                # 檢查閾值
                alerts = self._check_thresholds(metrics)

                # 記錄歷史
                self._record_metrics(metrics, alerts)

                # 如果有警報，記錄日誌
                if alerts:
                    for alert in alerts:
                        logger.warning(f"系統監控警報: {alert}")

                # 等待下一次檢查
                await asyncio.sleep(self.check_interval)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"系統監控異常: {e}")
                await asyncio.sleep(5)  # 短暫等待後重試

    async def _collect_system_metrics(self) -> Dict[str, Any]:
        """收集系統指標"""
        # 在線程池中執行 CPU 密集型操作
        loop = asyncio.get_event_loop()

        def get_system_info():
            return {
                "cpu_percent": psutil.cpu_percent(interval=1),
                "memory": psutil.virtual_memory()._asdict(),
                "disk": psutil.disk_usage("/")._asdict(),
                "network": psutil.net_io_counters()._asdict(),
                "timestamp": datetime.now().isoformat(),
            }

        return await loop.run_in_executor(None, get_system_info)

    def _check_thresholds(self, metrics: Dict[str, Any]) -> List[str]:
        """檢查閾值"""
        alerts = []

        # CPU 檢查
        if metrics["cpu_percent"] > self.cpu_threshold:
            alerts.append(f"CPU使用率過高: {metrics['cpu_percent']:.1f}% > {self.cpu_threshold}%")

        # 內存檢查
        memory_percent = metrics["memory"]["percent"]
        if memory_percent > self.memory_threshold:
            alerts.append(f"內存使用率過高: {memory_percent:.1f}% > {self.memory_threshold}%")

        # 磁盤檢查
        disk_percent = (metrics["disk"]["used"] / metrics["disk"]["total"]) * 100
        if disk_percent > self.disk_threshold:
            alerts.append(f"磁盤使用率過高: {disk_percent:.1f}% > {self.disk_threshold}%")

        return alerts

    def _record_metrics(self, metrics: Dict[str, Any], alerts: List[str]):
        """記錄指標歷史"""
        record = {
            "timestamp": metrics["timestamp"],
            "cpu_percent": metrics["cpu_percent"],
            "memory_percent": metrics["memory"]["percent"],
            "disk_percent": (metrics["disk"]["used"] / metrics["disk"]["total"]) * 100,
            "alerts": alerts,
        }

        self.monitoring_history.append(record)

        # 保持歷史大小
        if len(self.monitoring_history) > self.max_history_size:
            self.monitoring_history = self.monitoring_history[-self.max_history_size // 2 :]

    async def shutdown(self) -> bool:
        """停止監控"""
        try:
            await self.stop_monitoring()
            return True
        except Exception as e:
            logger.error(f"系統監控停止失敗: {e}")
            return False

    def get_current_metrics(self) -> Dict[str, Any]:
        """獲取當前系統指標"""
        if self.monitoring_history:
            return self.monitoring_history[-1]
        return {}

    def get_metrics_history(self, hours: int = 1) -> List[Dict[str, Any]]:
        """獲取指定時間內的指標歷史"""
        cutoff_time = datetime.now() - timedelta(hours=hours)

        return [
            record
            for record in self.monitoring_history
            if datetime.fromisoformat(record["timestamp"]) > cutoff_time
        ]


class AsyncResourceManager(ShutdownComponent):
    """統一異步資源管理器"""

    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}

        # 子管理器
        self.db_manager = AsyncDatabaseConnectionManager(
            max_connections=self.config.get("max_db_connections", 20)
        )

        self.system_monitor = SystemMonitor(
            check_interval=self.config.get("monitor_interval", 30.0)
        )

        logger.info("統一異步資源管理器初始化完成")

    @property
    def component_name(self) -> str:
        return "AsyncResourceManager"

    async def start(self):
        """啟動資源管理器"""
        await self.system_monitor.start_monitoring()
        logger.info("異步資源管理器已啟動")

    async def shutdown(self) -> bool:
        """關閉資源管理器"""
        try:
            logger.info("關閉異步資源管理器...")

            # 並發關閉子管理器
            results = await asyncio.gather(
                self.db_manager.shutdown(), self.system_monitor.shutdown(), return_exceptions=True
            )

            success = all(result is True or not isinstance(result, Exception) for result in results)

            if success:
                logger.info("異步資源管理器關閉完成")
            else:
                logger.warning("異步資源管理器部分關閉失敗")

            return success

        except Exception as e:
            logger.error(f"異步資源管理器關閉失敗: {e}")
            return False

    # 便利方法
    @asynccontextmanager
    async def get_db_connection(self, db_path: str):
        """獲取數據庫連接"""
        async with self.db_manager.get_connection(db_path) as conn:
            yield conn

    def get_system_metrics(self) -> Dict[str, Any]:
        """獲取系統指標"""
        return self.system_monitor.get_current_metrics()

    def get_resource_stats(self) -> Dict[str, Any]:
        """獲取資源統計"""
        return {
            "database": self.db_manager.get_stats(),
            "system": self.system_monitor.get_current_metrics(),
            "timestamp": datetime.now().isoformat(),
        }


# 全局資源管理器實例
_resource_manager: Optional[AsyncResourceManager] = None


def get_async_resource_manager(config: Dict[str, Any] = None) -> AsyncResourceManager:
    """獲取全局異步資源管理器實例"""
    global _resource_manager
    if _resource_manager is None:
        _resource_manager = AsyncResourceManager(config)

        # 自動註冊到停機管理器
        try:
            from graceful_shutdown import get_shutdown_manager

            get_shutdown_manager().register_component(_resource_manager)
        except ImportError:
            logger.warning("無法註冊到停機管理器")

    return _resource_manager


# 便利函數
@asynccontextmanager
async def get_db_connection(db_path: str):
    """獲取數據庫連接的便利函數"""
    manager = get_async_resource_manager()
    async with manager.get_db_connection(db_path) as conn:
        yield conn


async def main():
    """測試異步資源管理器"""
    print("🧪 測試異步資源管理器")

    # 創建資源管理器
    config = {"max_db_connections": 10, "monitor_interval": 5.0}

    manager = get_async_resource_manager(config)

    try:
        # 啟動管理器
        await manager.start()

        # 測試數據庫連接
        print("測試數據庫連接...")
        async with get_db_connection("test_async.db") as conn:
            await conn.execute("CREATE TABLE IF NOT EXISTS test (id INTEGER PRIMARY KEY)")
            await conn.execute("INSERT INTO test (id) VALUES (1)")
            cursor = await conn.execute("SELECT COUNT(*) FROM test")
            result = await cursor.fetchone()
            print(f"  ✅ 數據庫操作成功，記錄數: {result[0]}")

        # 獲取資源統計
        stats = manager.get_resource_stats()
        print(f"  📊 資源統計: {stats}")

        # 等待一段時間讓監控收集數據
        print("等待監控數據收集...")
        await asyncio.sleep(6)

        # 獲取系統指標
        metrics = manager.get_system_metrics()
        print(f"  📈 系統指標: {metrics}")

    finally:
        # 測試優雅關閉
        print("測試優雅關閉...")
        success = await manager.shutdown()
        print(f"  {'✅' if success else '❌'} 優雅關閉: {success}")

        # 清理測試文件
        import os

        if os.path.exists("test_async.db"):
            os.remove("test_async.db")

    print("✅ 異步資源管理器測試完成")


if __name__ == "__main__":
    asyncio.run(main())
