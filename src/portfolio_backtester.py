#!/usr/bin/env python3
"""
投資組合回測引擎 - 多配對並行回測
Portfolio Backtester - Multi-pair parallel backtesting
"""

import json
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional

import numpy as np
import pandas as pd

from backtesting import BacktestEngine
from database_manager import get_database_manager
from logging_config import get_logger
from unified_pair_selector import PairSelectionMethod, UnifiedPairSelector

logger = get_logger(__name__)


class PortfolioBacktester:
    """投資組合回測引擎"""

    def __init__(self, config_path: str = "config.json"):
        self.config = self._load_config(config_path)
        self.pair_selector = AdaptivePairSelector()
        self.db_manager = get_database_manager()

        # 投資組合配置
        self.portfolio_config = self.config.get("portfolio", {})
        self.total_capital = self.portfolio_config.get("total_capital", 10000)
        self.max_pairs = self.portfolio_config.get("max_concurrent_pairs", 3)
        self.capital_per_pair = self.total_capital / self.max_pairs

        # 回測結果
        self.portfolio_results = {}
        self.individual_results = {}

        logger.info("PortfolioBacktester 初始化完成")

    def _load_config(self, config_path: str) -> Dict:
        """載入配置"""
        try:
            with open(config_path, "r", encoding="utf-8") as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"載入配置失敗: {e}")
            return {}

    def run_portfolio_backtest(
        self,
        start_date: str,
        end_date: str,
        pairs: Optional[List[List[str]]] = None,
        rebalance_frequency: str = "monthly",
    ) -> Dict:
        """運行投資組合回測"""
        try:
            logger.info(f"開始投資組合回測: {start_date} 到 {end_date}")

            # 選擇交易配對
            if pairs is None:
                pairs = self._select_backtest_pairs()

            if not pairs:
                logger.error("沒有可用的交易配對")
                return {}

            logger.info(f"回測配對: {pairs}")

            # 並行運行各配對回測
            individual_results = self._run_parallel_backtests(pairs, start_date, end_date)

            if not individual_results:
                logger.error("所有配對回測都失敗")
                return {}

            # 計算投資組合級別結果
            portfolio_results = self._calculate_portfolio_results(
                individual_results, start_date, end_date, rebalance_frequency
            )

            # 生成回測報告
            report = self._generate_backtest_report(portfolio_results, individual_results)

            # 保存結果
            self._save_backtest_results(report, start_date, end_date)

            logger.info("投資組合回測完成")
            return report

        except Exception as e:
            logger.error(f"投資組合回測失敗: {e}")
            return {}

    def _select_backtest_pairs(self) -> List[List[str]]:
        """選擇回測配對"""
        try:
            # 獲取潛在配對
            potential_pairs = self.pair_selector.potential_pairs

            if not potential_pairs:
                # 使用默認配對
                return [["BTCUSDT", "ETHUSDT"], ["ETHUSDT", "BNBUSDT"], ["ADAUSDT", "DOTUSDT"]]

            # 選擇評分最高的配對
            scored_pairs = []
            for pair_data in potential_pairs:
                if pair_data.get("meets_criteria", False):
                    pair = [pair_data["symbol1"], pair_data["symbol2"]]
                    score = pair_data.get("score", 0)
                    scored_pairs.append((pair, score))

            # 按評分排序並選擇前N個
            scored_pairs.sort(key=lambda x: x[1], reverse=True)
            selected_pairs = [pair for pair, score in scored_pairs[: self.max_pairs]]

            return selected_pairs

        except Exception as e:
            logger.error(f"選擇回測配對失敗: {e}")
            return []

    def _run_parallel_backtests(
        self, pairs: List[List[str]], start_date: str, end_date: str
    ) -> Dict:
        """並行運行配對回測"""
        try:
            results = {}

            with ThreadPoolExecutor(max_workers=min(len(pairs), 4)) as executor:
                # 提交回測任務
                future_to_pair = {}
                for pair in pairs:
                    future = executor.submit(
                        self._run_single_pair_backtest, pair, start_date, end_date
                    )
                    future_to_pair[future] = pair

                # 收集結果
                for future in as_completed(future_to_pair):
                    pair = future_to_pair[future]
                    pair_key = f"{pair[0]}-{pair[1]}"

                    try:
                        result = future.result()
                        if result:
                            results[pair_key] = result
                            logger.info(f"配對 {pair_key} 回測完成")
                        else:
                            logger.warning(f"配對 {pair_key} 回測失敗")
                    except Exception as e:
                        logger.error(f"配對 {pair_key} 回測異常: {e}")

            return results

        except Exception as e:
            logger.error(f"並行回測失敗: {e}")
            return {}

    def _run_single_pair_backtest(
        self, pair: List[str], start_date: str, end_date: str
    ) -> Optional[Dict]:
        """運行單個配對回測"""
        try:
            # 創建配對專用配置
            pair_config = self.config.copy()
            pair_config["trading_pair"] = pair
            pair_config["position_size_usd"] = self.capital_per_pair

            # 創建回測引擎
            backtest_engine = BacktestEngine(pair_config)

            # 運行回測
            result = backtest_engine.run_backtest(start_date, end_date)

            if result:
                # 添加配對信息
                result["pair"] = pair
                result["pair_key"] = f"{pair[0]}-{pair[1]}"
                result["allocated_capital"] = self.capital_per_pair

            return result

        except Exception as e:
            logger.error(f"單配對回測失敗 {pair}: {e}")
            return None

    def _calculate_portfolio_results(
        self, individual_results: Dict, start_date: str, end_date: str, rebalance_frequency: str
    ) -> Dict:
        """計算投資組合級別結果"""
        try:
            # 收集所有配對的每日收益
            all_returns = {}
            all_equity_curves = {}

            for pair_key, result in individual_results.items():
                if "daily_returns" in result:
                    all_returns[pair_key] = result["daily_returns"]
                if "equity_curve" in result:
                    all_equity_curves[pair_key] = result["equity_curve"]

            if not all_returns:
                logger.error("沒有有效的收益數據")
                return {}

            # 創建投資組合收益序列
            portfolio_returns = self._combine_returns(all_returns)
            portfolio_equity = self._calculate_portfolio_equity(all_equity_curves)

            # 計算投資組合指標
            portfolio_stats = self._calculate_portfolio_stats(portfolio_returns, portfolio_equity)

            # 添加配對權重信息
            portfolio_stats["pair_weights"] = {
                pair_key: self.capital_per_pair / self.total_capital
                for pair_key in individual_results.keys()
            }

            # 添加重新平衡信息
            portfolio_stats["rebalance_frequency"] = rebalance_frequency
            portfolio_stats["total_capital"] = self.total_capital
            portfolio_stats["number_of_pairs"] = len(individual_results)

            return portfolio_stats

        except Exception as e:
            logger.error(f"計算投資組合結果失敗: {e}")
            return {}

    def _combine_returns(self, all_returns: Dict) -> pd.Series:
        """合併各配對收益為投資組合收益"""
        try:
            # 將所有收益序列對齊到相同的日期索引
            returns_df = pd.DataFrame(all_returns)

            # 填充缺失值（假設為0收益）
            returns_df = returns_df.fillna(0)

            # 計算等權重投資組合收益
            portfolio_returns = returns_df.mean(axis=1)

            return portfolio_returns

        except Exception as e:
            logger.error(f"合併收益失敗: {e}")
            return pd.Series()

    def _calculate_portfolio_equity(self, all_equity_curves: Dict) -> pd.Series:
        """計算投資組合權益曲線"""
        try:
            # 將所有權益曲線對齊
            equity_df = pd.DataFrame(all_equity_curves)

            # 填充缺失值
            equity_df = equity_df.fillna(method="ffill").fillna(self.capital_per_pair)

            # 計算投資組合總權益
            portfolio_equity = equity_df.sum(axis=1)

            return portfolio_equity

        except Exception as e:
            logger.error(f"計算投資組合權益失敗: {e}")
            return pd.Series()

    def _calculate_portfolio_stats(self, returns: pd.Series, equity: pd.Series) -> Dict:
        """計算投資組合統計指標"""
        try:
            if returns.empty or equity.empty:
                return {}

            # 基本統計
            total_return = (equity.iloc[-1] - equity.iloc[0]) / equity.iloc[0]
            total_pnl = equity.iloc[-1] - equity.iloc[0]

            # 年化收益率
            days = len(returns)
            annualized_return = (1 + total_return) ** (252 / days) - 1

            # 波動率
            volatility = returns.std() * np.sqrt(252)

            # 夏普比率
            sharpe_ratio = annualized_return / volatility if volatility > 0 else 0

            # 最大回撤
            peak = equity.expanding().max()
            drawdown = (equity - peak) / peak
            max_drawdown = drawdown.min()

            # 勝率
            winning_days = (returns > 0).sum()
            total_days = len(returns)
            win_rate = winning_days / total_days if total_days > 0 else 0

            # Calmar 比率
            calmar_ratio = annualized_return / abs(max_drawdown) if max_drawdown != 0 else 0

            # Sortino 比率
            downside_returns = returns[returns < 0]
            downside_deviation = downside_returns.std() * np.sqrt(252)
            sortino_ratio = annualized_return / downside_deviation if downside_deviation > 0 else 0

            return {
                "total_return": total_return,
                "total_pnl": total_pnl,
                "annualized_return": annualized_return,
                "volatility": volatility,
                "sharpe_ratio": sharpe_ratio,
                "max_drawdown": max_drawdown,
                "win_rate": win_rate,
                "calmar_ratio": calmar_ratio,
                "sortino_ratio": sortino_ratio,
                "total_trading_days": total_days,
                "winning_days": winning_days,
                "losing_days": total_days - winning_days,
                "avg_daily_return": returns.mean(),
                "best_day": returns.max(),
                "worst_day": returns.min(),
                "returns_series": returns.to_dict(),
                "equity_curve": equity.to_dict(),
            }

        except Exception as e:
            logger.error(f"計算投資組合統計失敗: {e}")
            return {}

    def _generate_backtest_report(self, portfolio_results: Dict, individual_results: Dict) -> Dict:
        """生成回測報告"""
        try:
            report = {
                "timestamp": datetime.now().isoformat(),
                "backtest_type": "portfolio",
                "portfolio_summary": portfolio_results,
                "individual_pairs": individual_results,
                "comparison": self._compare_portfolio_vs_individual(
                    portfolio_results, individual_results
                ),
            }

            return report

        except Exception as e:
            logger.error(f"生成回測報告失敗: {e}")
            return {}

    def _compare_portfolio_vs_individual(
        self, portfolio_results: Dict, individual_results: Dict
    ) -> Dict:
        """比較投資組合與個別配對表現"""
        try:
            comparison = {
                "portfolio_vs_best_pair": {},
                "portfolio_vs_average": {},
                "diversification_benefit": {},
            }

            # 獲取個別配對的關鍵指標
            individual_sharpe = [
                result.get("sharpe_ratio", 0) for result in individual_results.values()
            ]
            individual_returns = [
                result.get("total_return", 0) for result in individual_results.values()
            ]
            individual_drawdowns = [
                result.get("max_drawdown", 0) for result in individual_results.values()
            ]

            if individual_sharpe and portfolio_results:
                # 與最佳配對比較
                best_sharpe = max(individual_sharpe)
                portfolio_sharpe = portfolio_results.get("sharpe_ratio", 0)

                comparison["portfolio_vs_best_pair"] = {
                    "portfolio_sharpe": portfolio_sharpe,
                    "best_individual_sharpe": best_sharpe,
                    "sharpe_improvement": portfolio_sharpe - best_sharpe,
                }

                # 與平均表現比較
                avg_return = np.mean(individual_returns)
                avg_drawdown = np.mean(individual_drawdowns)

                comparison["portfolio_vs_average"] = {
                    "portfolio_return": portfolio_results.get("total_return", 0),
                    "average_individual_return": avg_return,
                    "portfolio_drawdown": portfolio_results.get("max_drawdown", 0),
                    "average_individual_drawdown": avg_drawdown,
                }

                # 分散化效益
                portfolio_volatility = portfolio_results.get("volatility", 0)
                avg_volatility = np.mean(
                    [result.get("volatility", 0) for result in individual_results.values()]
                )

                comparison["diversification_benefit"] = {
                    "volatility_reduction": avg_volatility - portfolio_volatility,
                    "risk_adjusted_improvement": portfolio_sharpe - np.mean(individual_sharpe),
                }

            return comparison

        except Exception as e:
            logger.error(f"比較分析失敗: {e}")
            return {}

    def _save_backtest_results(self, report: Dict, start_date: str, end_date: str):
        """保存回測結果"""
        try:
            # 保存到文件
            results_dir = Path("backtest_results/portfolio")
            results_dir.mkdir(parents=True, exist_ok=True)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"portfolio_backtest_{start_date}_{end_date}_{timestamp}.json"
            filepath = results_dir / filename

            with open(filepath, "w", encoding="utf-8") as f:
                json.dump(report, f, indent=2, ensure_ascii=False, default=str)

            logger.info(f"回測結果已保存: {filepath}")

            # 保存到數據庫（如果需要）
            # self.db_manager.record_backtest_result(report)

        except Exception as e:
            logger.error(f"保存回測結果失敗: {e}")


if __name__ == "__main__":
    # 測試投資組合回測
    print("測試投資組合回測引擎...")

    backtester = PortfolioBacktester()

    # 運行回測
    start_date = "2024-01-01"
    end_date = "2024-03-01"

    result = backtester.run_portfolio_backtest(start_date, end_date)

    if result:
        print("投資組合回測完成")
        portfolio_summary = result.get("portfolio_summary", {})
        print(f"總收益率: {portfolio_summary.get('total_return', 0):.2%}")
        print(f"夏普比率: {portfolio_summary.get('sharpe_ratio', 0):.4f}")
        print(f"最大回撤: {portfolio_summary.get('max_drawdown', 0):.2%}")
    else:
        print("投資組合回測失敗")

    print("投資組合回測引擎測試完成！")
