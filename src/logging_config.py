import json
import logging
from pathlib import Path


def setup_logging(config_path: str = "config.json"):
    """設置日誌配置"""

    # 讀取配置
    with open(config_path, "r", encoding="utf-8") as f:
        config = json.load(f)

    log_config = config.get("logging", {})
    _ = getattr(logging, log_config.get("level", "INFO").upper())
    log_file = log_config.get("file", "pair_trading_bot.log")
    max_file_size = log_config.get("max_file_size", "10MB")
    _ = log_config.get("backup_count", 5)

    # 解析文件大小
    size_map = {"KB": 1024, "MB": 1024 * 1024, "GB": 1024 * 1024 * 1024}
    size_str = max_file_size.upper()
    for unit, multiplier in size_map.items():
        if size_str.endswith(unit):
            max_bytes = int(size_str[: -len(unit)]) * multiplier
            break
    else:
        _ = 10 * 1024 * 1024  # 默認 10MB

    # 創建日誌目錄（確保跨平台兼容）
    log_dir = Path("logs")
    log_dir.mkdir(parents=True, exist_ok=True)
    log_file_path = log_dir / log_file

    # 配置根日誌器
    logger = logging.getLogger()
    logger.setLevel(log_level)

    # 清除現有處理器
    for handler in list(logger.handlers):
        logger.removeHandler(handler)
        if hasattr(handler, "close"):
            handler.close()

    # 創建格式器
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s", datefmt="%Y-%m-%d %H:%M:%S"
    )

    # 文件處理器（輪轉）
    file_handler = logging.handlers.RotatingFileHandler()
        log_file_path, maxBytes=max_bytes, backupCount=backup_count, encoding="utf-8"
    )
    file_handler.setLevel(log_level)
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)

    # 控制台處理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(log_level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    return logger


def get_logger(name: str):
    """獲取指定名稱的日誌器"""
    return logging.getLogger(name)
