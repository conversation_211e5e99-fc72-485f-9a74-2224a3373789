#!/usr/bin/env python3
"""
資源管理器 - 統一管理系統資源
Resource Manager - Unified system resource management
"""

import sqlite3
import threading
import weakref
from contextlib import contextmanager
from datetime import datetime
from typing import Any, Dict, Generator


class ResourceManager:
    """統一資源管理器"""

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if hasattr(self, "_initialized"):
            return

        self._initialized = True
        self.active_connections: Dict[str, weakref.WeakSet] = {}
        self.connection_stats = {"total_created": 0, "total_closed": 0, "current_active": 0}
        self._lock = threading.Lock()

    @contextmanager
    def get_database_connection(self, db_path: str) -> Generator[sqlite3.Connection, None, None]:
        """獲取數據庫連接的安全上下文管理器"""
        conn = None
        try:
            conn = sqlite3.connect()
                db_path, detect_types=sqlite3.PARSE_DECLTYPES, timeout=30.0, check_same_thread=False
            )

            # 記錄連接
            self._register_connection(db_path, conn)

            yield conn

        except Exception as e:
            if conn:
                conn.rollback()
            raise e
        finally:
            if conn:
                self._unregister_connection(db_path, conn)
                conn.close()

    def _register_connection(self, db_path: str, conn: sqlite3.Connection):
        """註冊數據庫連接"""
        with self._lock:
            if db_path not in self.active_connections:
                self.active_connections[db_path] = weakref.WeakSet()

            self.active_connections[db_path].add(conn)
            self.connection_stats["total_created"] += 1
            self.connection_stats["current_active"] += 1

    def _unregister_connection(self, db_path: str, conn: sqlite3.Connection):
        """註銷數據庫連接"""
        with self._lock:
            if db_path in self.active_connections:
                self.active_connections[db_path].discard(conn)

            self.connection_stats["total_closed"] += 1
            self.connection_stats["current_active"] -= 1

    def get_connection_stats(self) -> Dict[str, Any]:
        """獲取連接統計"""
        with self._lock:
            return {
                "stats": self.connection_stats.copy(),
                "active_by_db": {
                    db_path: len(connections)
                    for db_path, connections in self.active_connections.items()
                },
                "timestamp": datetime.now().isoformat(),
            }

    def cleanup_stale_connections(self):
        """清理過期連接"""
        with self._lock:
            for db_path, connections in self.active_connections.items():
                # WeakSet會自動清理已被垃圾回收的連接
                pass

    def force_close_all_connections(self):
        """強制關閉所有連接（緊急情況使用）"""
        with self._lock:
            for db_path, connections in self.active_connections.items():
                for conn in list(connections):
                    try:
                        conn.close()
                    except Exception:
                        pass

            self.active_connections.clear()
            self.connection_stats["current_active"] = 0


# 全局資源管理器實例
resource_manager = ResourceManager()


@contextmanager
def get_db_connection(db_path: str) -> Generator[sqlite3.Connection, None, None]:
    """便利函數：獲取數據庫連接"""
    with resource_manager.get_database_connection(db_path) as conn:
        yield conn


def get_resource_stats() -> Dict[str, Any]:
    """便利函數：獲取資源統計"""
    return resource_manager.get_connection_stats()


if __name__ == "__main__":
    # 測試資源管理器
    print("🧪 資源管理器測試")

    # 測試數據庫連接
    with get_db_connection("test.db") as conn:
        cursor = conn.cursor()
        cursor.execute("CREATE TABLE IF NOT EXISTS test (id INTEGER PRIMARY KEY)")
        conn.commit()

    # 檢查統計
    stats = get_resource_stats()
    print(f"✅ 連接統計: {stats}")

    # 清理測試文件
    import os

    if os.path.exists("test.db"):
        os.remove("test.db")

    print("✅ 資源管理器測試完成")
