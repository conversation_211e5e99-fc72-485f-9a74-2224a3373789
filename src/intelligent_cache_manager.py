#!/usr/bin/env python3
"""
智能緩存管理器 - 基於建議 6 實施事件驅動的緩存失效策略
Intelligent Cache Manager - Event-driven cache invalidation strategy based on recommendation 6
"""

import asyncio
import time
from enum import Enum

from global_event_bus import Event, EventType, get_global_event_bus
from graceful_shutdown import ShutdownComponent
from logging_config import get_logger
from unified_client_manager import get_client_manager

logger = get_logger(__name__)


class CacheStrategy(str, Enum):
    """緩存策略枚舉"""

    TTL_BASED = "ttl_based"  # 基於時間的失效
    EVENT_DRIVEN = "event_driven"  # 基於事件的失效
    HYBRID = "hybrid"  # 混合策略
    ADAPTIVE = "adaptive"  # 自適應策略


@dataclass
class CacheEntry:
    """緩存條目"""

    key: str
    value: Any
    created_at: float
    last_accessed: float
    access_count: int
    ttl: Optional[float] = None
    tags: Set[str] = None
    strategy: CacheStrategy = CacheStrategy.TTL_BASED

    def __post_init__(self):
        if self.tags is None:
            self.tags = set()

    @property
    def age(self) -> float:
        """緩存年齡（秒）"""
        return time.time() - self.created_at

    @property
    def idle_time(self) -> float:
        """空閒時間（秒）"""
        return time.time() - self.last_accessed

    def is_expired(self) -> bool:
        """檢查是否過期"""
        if self.ttl is None:
            return False
        return self.age > self.ttl

    def touch(self):
        """更新訪問時間"""
        self.last_accessed = time.time()
        self.access_count += 1


class IntelligentCacheManager(ShutdownComponent):
    """智能緩存管理器"""

    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}

        # 緩存存儲
        self._cache: Dict[str, CacheEntry] = {}
        self._tag_index: Dict[str, Set[str]] = {}  # 標籤到鍵的映射

        # 配置參數
        self.max_size = self.config.get("max_size", 10000)
        self.default_ttl = self.config.get("default_ttl", 3600)  # 1小時
        self.cleanup_interval = self.config.get("cleanup_interval", 300)  # 5分鐘
        self.adaptive_threshold = self.config.get("adaptive_threshold", 0.8)

        # 統計信息
        self.stats = {
            "hits": 0,
            "misses": 0,
            "evictions": 0,
            "invalidations": 0,
            "total_requests": 0,
        }

        # 事件總線和客戶端管理器
        self.event_bus = get_global_event_bus()
        self.client_manager = get_client_manager()

        # WebSocket 連接管理
        self._websocket_connections: Dict[str, Any] = {}
        self._subscribed_symbols: Set[str] = set()

        # 任務管理
        self._cleanup_task: Optional[asyncio.Task] = None
        self._websocket_tasks: Dict[str, asyncio.Task] = {}

        # 緩存失效回調
        self._invalidation_callbacks: Dict[str, Set[Callable]] = {}

        logger.info("智能緩存管理器初始化完成")

    @property
    def component_name(self) -> str:
        return "IntelligentCacheManager"

    async def start(self):
        """啟動緩存管理器"""
        # 註冊事件監聽器
        await self._register_event_listeners()

        # 啟動清理任務
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())

        # 啟動WebSocket監聽
        await self._start_websocket_monitoring()

        logger.info("智能緩存管理器已啟動")

    async def _register_event_listeners(self):
        """註冊事件監聽器"""
        # 監聽市場數據更新事件
        self.event_bus.subscribe([EventType.MARKET_DATA_UPDATED], self._on_market_data_updated)

        # 監聽交易執行事件
        self.event_bus.subscribe([EventType.ORDER_FILLED], self._on_order_filled)

    async def _start_websocket_monitoring(self):
        """啟動WebSocket監聽"""
        try:
            # 為主要交易所啟動WebSocket連接
            exchanges = ["gateio", "binance"]  # 可配置

            for exchange_name in exchanges:
                task = asyncio.create_task(self._websocket_monitor(exchange_name))
                self._websocket_tasks[exchange_name] = task

        except Exception as e:
            logger.error(f"啟動WebSocket監聽失敗: {e}")

    async def _websocket_monitor(self, exchange_name: str):
        """WebSocket監聽器"""
        try:
            async with self.client_manager.exchange_context(exchange_name) as exchange:
                # 訂閱ticker更新
                while True:
                    try:
                        # 獲取已訂閱的交易對
                        symbols = list(self._subscribed_symbols)
                        if not symbols:
                            await asyncio.sleep(5)
                            continue

                        # 監聽ticker更新
                        for symbol in symbols:
                            try:
                                ticker = await exchange.watch_ticker(symbol)

                                # 發布ticker更新事件
                                await self.event_bus.publish(
                                    Event(
                                        event_type=EventType.MARKET_DATA_UPDATED,
                                        source=f"websocket_{exchange_name}",
                                        data={
                                            "symbol": symbol,
                                            "exchange": exchange_name,
                                            "ticker": ticker,
                                            "timestamp": datetime.now(),
                                        },
                                        timestamp=datetime.now(),
                                    )
                                )

                            except Exception as e:
                                logger.debug(f"WebSocket ticker更新失敗 {symbol}: {e}")

                        await asyncio.sleep(0.1)  # 避免過於頻繁

                    except Exception as e:
                        logger.error(f"WebSocket監聽異常 {exchange_name}: {e}")
                        await asyncio.sleep(5)

        except Exception as e:
            logger.error(f"WebSocket監聽器失敗 {exchange_name}: {e}")

    def get(self, key: str, default: Any = None) -> Any:
        """獲取緩存值"""
        self.stats["total_requests"] += 1

        if key in self._cache:
            entry = self._cache[key]

            # 檢查是否過期
            if entry.is_expired():
                self._remove_entry(key)
                self.stats["misses"] += 1
                return default

            # 更新訪問信息
            entry.touch()
            self.stats["hits"] += 1
            return entry.value

        self.stats["misses"] += 1
        return default

    def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[float] = None,
        tags: Optional[Set[str]] = None,
        strategy: CacheStrategy = CacheStrategy.TTL_BASED,
    ) -> bool:
        """設置緩存值"""
        try:
            # 檢查容量限制
            if len(self._cache) >= self.max_size and key not in self._cache:
                self._evict_entries()

            # 創建緩存條目
            now = time.time()
            entry = CacheEntry(
                key=key,
                value=value,
                created_at=now,
                last_accessed=now,
                access_count=1,
                ttl=ttl or self.default_ttl,
                tags=tags or set(),
                strategy=strategy,
            )

            # 如果是更新現有條目，先清理舊的標籤索引
            if key in self._cache:
                self._remove_from_tag_index(key)

            # 存儲條目
            self._cache[key] = entry

            # 更新標籤索引
            self._add_to_tag_index(key, entry.tags)

            # 如果是事件驅動策略，訂閱相關事件
            if strategy in [CacheStrategy.EVENT_DRIVEN, CacheStrategy.HYBRID]:
                self._subscribe_to_events(key, tags)

            return True

        except Exception as e:
            logger.error(f"設置緩存失敗 {key}: {e}")
            return False

    def invalidate(self, key: str) -> bool:
        """失效指定緩存"""
        if key in self._cache:
            self._remove_entry(key)
            self.stats["invalidations"] += 1

            # 調用失效回調
            self._call_invalidation_callbacks(key)

            logger.debug(f"緩存失效: {key}")
            return True
        return False

    def invalidate_by_tag(self, tag: str) -> int:
        """根據標籤失效緩存"""
        if tag not in self._tag_index:
            return 0

        keys_to_invalidate = list(self._tag_index[tag])
        count = 0

        for key in keys_to_invalidate:
            if self.invalidate(key):
                count += 1

        logger.debug(f"根據標籤 {tag} 失效了 {count} 個緩存條目")
        return count

    def invalidate_by_pattern(self, pattern: str) -> int:
        """根據模式失效緩存"""
        import re

        regex = re.compile(pattern)

        keys_to_invalidate = [key for key in self._cache.keys() if regex.match(key)]

        count = 0
        for key in keys_to_invalidate:
            if self.invalidate(key):
                count += 1

        logger.debug(f"根據模式 {pattern} 失效了 {count} 個緩存條目")
        return count

    def register_invalidation_callback(self, key_pattern: str, callback: Callable):
        """註冊緩存失效回調"""
        if key_pattern not in self._invalidation_callbacks:
            self._invalidation_callbacks[key_pattern] = set()

        self._invalidation_callbacks[key_pattern].add(callback)

    def _call_invalidation_callbacks(self, key: str):
        """調用失效回調"""
        import re

        for pattern, callbacks in self._invalidation_callbacks.items():
            if re.match(pattern, key):
                for callback in callbacks:
                    try:
                        callback(key)
                    except Exception as e:
                        logger.error(f"失效回調執行失敗 {pattern}: {e}")

    def _subscribe_to_events(self, key: str, tags: Optional[Set[str]]):
        """訂閱相關事件"""
        if not tags:
            return

        # 解析標籤，提取交易對信息
        for tag in tags:
            if tag.startswith("symbol:"):
                symbol = tag[7:]  # 移除 'symbol:' 前綴
                self._subscribed_symbols.add(symbol)
                logger.debug(f"訂閱交易對 {symbol} 的實時更新")

    def _remove_entry(self, key: str):
        """移除緩存條目"""
        if key in self._cache:
            entry = self._cache[key]

            # 從標籤索引中移除
            self._remove_from_tag_index(key)

            # 從緩存中移除
            del self._cache[key]

    def _add_to_tag_index(self, key: str, tags: Set[str]):
        """添加到標籤索引"""
        for tag in tags:
            if tag not in self._tag_index:
                self._tag_index[tag] = set()
            self._tag_index[tag].add(key)

    def _remove_from_tag_index(self, key: str):
        """從標籤索引中移除"""
        if key in self._cache:
            entry = self._cache[key]
            for tag in entry.tags:
                if tag in self._tag_index:
                    self._tag_index[tag].discard(key)
                    if not self._tag_index[tag]:
                        del self._tag_index[tag]

    def _evict_entries(self):
        """驅逐緩存條目"""
        if not self._cache:
            return

        # 計算需要驅逐的數量
        evict_count = max(1, int(self.max_size * 0.1))  # 驅逐10%

        # 按LRU策略排序
        entries = list(self._cache.items())
        entries.sort(key=lambda x: (x[1].last_accessed, x[1].access_count))

        # 驅逐最少使用的條目
        for _ in range(min(evict_count, len(entries))):
            key = entries[i][0]
            self._remove_entry(key)
            self.stats["evictions"] += 1

        logger.debug(f"驅逐了 {evict_count} 個緩存條目")

    async def _cleanup_loop(self):
        """清理循環"""
        while True:
            try:
                await asyncio.sleep(self.cleanup_interval)
                await self._cleanup_expired_entries()

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"緩存清理異常: {e}")

    async def _cleanup_expired_entries(self):
        """清理過期條目"""
        expired_keys = []

        for key, entry in self._cache.items():
            if entry.is_expired():
                expired_keys.append(key)

        for key in expired_keys:
            self._remove_entry(key)

        if expired_keys:
            logger.debug(f"清理了 {len(expired_keys)} 個過期緩存條目")

    async def _on_market_data_updated(self, event: Event):
        """處理市場數據更新事件"""
        try:
            symbol = event.data.get("symbol")
            exchange = event.data.get("exchange")

            if symbol:
                # 失效相關的緩存
                self.invalidate_by_tag(f"symbol:{symbol}")
                self.invalidate_by_tag(f"exchange:{exchange}")

                # 失效OHLCV緩存
                self.invalidate_by_pattern(f"ohlcv:{symbol}:.*")

                logger.debug(f"因市場數據更新失效緩存: {symbol}")

        except Exception as e:
            logger.error(f"處理市場數據更新事件失敗: {e}")

    async def _on_order_filled(self, event: Event):
        """處理訂單成交事件"""
        try:
            symbol = event.data.get("symbol")
            strategy_id = event.data.get("strategy_id")

            if symbol:
                # 失效相關的緩存
                self.invalidate_by_tag(f"symbol:{symbol}")

            if strategy_id:
                # 失效策略相關緩存
                self.invalidate_by_tag(f"strategy:{strategy_id}")

        except Exception as e:
            logger.error(f"處理訂單成交事件失敗: {e}")

    def get_cache_stats(self) -> Dict[str, Any]:
        """獲取緩存統計"""
        hit_rate = (
            self.stats["hits"] / self.stats["total_requests"]
            if self.stats["total_requests"] > 0
            else 0
        )

        return {
            "size": len(self._cache),
            "max_size": self.max_size,
            "hit_rate": hit_rate,
            "stats": self.stats.copy(),
            "tag_count": len(self._tag_index),
            "subscribed_symbols": len(self._subscribed_symbols),
        }

    def clear(self):
        """清空緩存"""
        self._cache.clear()
        self._tag_index.clear()
        self._subscribed_symbols.clear()

        # 重置統計
        for key in self.stats:
            self.stats[key] = 0

        logger.info("緩存已清空")

    async def shutdown(self) -> bool:
        """關閉緩存管理器"""
        try:
            logger.info("關閉智能緩存管理器...")

            # 停止清理任務
            if self._cleanup_task:
                self._cleanup_task.cancel()
                try:
                    await self._cleanup_task
                except asyncio.CancelledError:
                    pass

            # 停止WebSocket任務
            for task in self._websocket_tasks.values():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass

            # 清空緩存
            self.clear()

            logger.info("智能緩存管理器關閉完成")
            return True

        except Exception as e:
            logger.error(f"關閉智能緩存管理器失敗: {e}")
            return False


# 全局緩存管理器實例
_cache_manager: Optional[IntelligentCacheManager] = None


def get_intelligent_cache_manager(config: Dict[str, Any] = None) -> IntelligentCacheManager:
    """獲取全局智能緩存管理器實例"""
    global _cache_manager
    if _cache_manager is None:
        _cache_manager = IntelligentCacheManager(config)

        # 自動註冊到停機管理器
        try:
            from graceful_shutdown import get_shutdown_manager

            get_shutdown_manager().register_component(_cache_manager)
        except ImportError:
            logger.warning("無法註冊到停機管理器")

    return _cache_manager


# 便利函數
def cache_get(key: str, default: Any = None) -> Any:
    """獲取緩存值的便利函數"""
    manager = get_intelligent_cache_manager()
    return manager.get(key, default)


def cache_set(
    key: str,
    value: Any,
    ttl: Optional[float] = None,
    tags: Optional[Set[str]] = None,
    strategy: CacheStrategy = CacheStrategy.TTL_BASED,
) -> bool:
    """設置緩存值的便利函數"""
    manager = get_intelligent_cache_manager()
    return manager.set(key, value, ttl, tags, strategy)


def cache_invalidate(key: str) -> bool:
    """失效緩存的便利函數"""
    manager = get_intelligent_cache_manager()
    return manager.invalidate(key)


async def main():
    """測試智能緩存管理器"""
    print("🧪 測試智能緩存管理器")

    # 創建緩存管理器
    config = {"max_size": 1000, "default_ttl": 60, "cleanup_interval": 10}

    cache_manager = get_intelligent_cache_manager(config)

    try:
        # 啟動緩存管理器
        await cache_manager.start()

        # 測試基本緩存操作
        print("測試基本緩存操作...")

        # 設置緩存
        cache_manager.set(
            "test_key", "test_value", ttl=30, tags={"symbol:BTC/USDT", "exchange:gateio"}
        )

        # 獲取緩存
        value = cache_manager.get("test_key")
        assert value == "test_value"
        print(f"  ✅ 基本緩存操作: {value}")

        # 測試事件驅動緩存
        print("測試事件驅動緩存...")
        cache_manager.set(
            "market_data:BTC/USDT",
            {"price": 50000},
            strategy=CacheStrategy.EVENT_DRIVEN,
            tags={"symbol:BTC/USDT"},
        )

        # 模擬市場數據更新事件
        await cache_manager.event_bus.publish(
            Event(
                event_type=EventType.MARKET_DATA_UPDATED,
                source="test",
                data={"symbol": "BTC/USDT", "exchange": "gateio"},
                timestamp=datetime.now(),
            )
        )

        # 等待事件處理
        await asyncio.sleep(0.1)

        # 檢查緩存是否被失效
        value = cache_manager.get("market_data:BTC/USDT")
        print(f"  ✅ 事件驅動失效: {value is None}")

        # 獲取統計信息
        stats = cache_manager.get_cache_stats()
        print(f"  📊 緩存統計: 大小={stats['size']}, 命中率={stats['hit_rate']:.2%}")

    finally:
        # 測試關閉
        success = await cache_manager.shutdown()
        print(f"  {'✅' if success else '❌'} 關閉結果: {success}")

    print("✅ 智能緩存管理器測試完成")


if __name__ == "__main__":
    asyncio.run(main())
