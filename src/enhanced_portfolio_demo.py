#!/usr/bin/env python3
"""
增強版智能投組系統演示 - 展示動態資金分配和深度事件集成
Enhanced Portfolio System Demo - Demonstrate dynamic capital allocation and deep event integration
"""

import asyncio
import time
import numpy as np
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any, List

from intelligent_portfolio_system import IntelligentPortfolioSystem
from state_persistence_manager import StatePersistenceManager
from global_event_bus import get_global_event_bus, Event, EventType, publish_event
from logging_config import setup_logging, get_logger

setup_logging()
logger = get_logger(__name__)


class EnhancedPortfolioDemo:
    """增強版智能投組系統演示"""
    
    def __init__(self):
        self.system = None
        self.persistence_manager = None
        self.event_bus = get_global_event_bus()
        self.demo_stats = {
            'events_captured': 0,
            'rebalances_triggered': 0,
            'health_updates': 0,
            'trades_simulated': 0,
            'correlation_alerts': 0
        }
        
        # 設置演示事件監聽
        self._setup_demo_event_listeners()
    
    def _setup_demo_event_listeners(self):
        """設置演示事件監聽器"""
        # 監聽所有重要事件
        self.event_bus.subscribe(
            [
                EventType.PORTFOLIO_REBALANCE,
                EventType.STRATEGY_HEALTH_CHANGED,
                EventType.CORRELATION_ALERT,
                EventType.ORDER_FILLED,
                EventType.SYSTEM_STARTUP,
                EventType.HEALTH_CHECK
            ],
            self._on_demo_event
        )
    
    def _on_demo_event(self, event: Event):
        """處理演示事件"""
        self.demo_stats['events_captured'] += 1
        
        if event.event_type == EventType.PORTFOLIO_REBALANCE:
            self.demo_stats['rebalances_triggered'] += 1
            actions = event.data.get('actions', [])
            print(f"  🔄 組合重新平衡: {len(actions)} 個調整")
            
            for action in actions:
                change_pct = action['change'] * 100
                print(f"    📊 {action['strategy_id']}: "
                      f"{action['old_allocation']:.1%} → {action['new_allocation']:.1%} "
                      f"({change_pct:+.1f}%)")
        
        elif event.event_type == EventType.STRATEGY_HEALTH_CHANGED:
            self.demo_stats['health_updates'] += 1
            strategy_id = event.data.get('strategy_id', 'unknown')
            health_score = event.data.get('health_score', 0.0)
            print(f"  ❤️ 健康更新: {strategy_id} = {health_score:.2f}")
        
        elif event.event_type == EventType.CORRELATION_ALERT:
            self.demo_stats['correlation_alerts'] += 1
            strategy1 = event.data.get('strategy1', '')
            strategy2 = event.data.get('strategy2', '')
            correlation = event.data.get('correlation', 0.0)
            print(f"  ⚠️ 相關性警報: {strategy1} ↔ {strategy2} = {correlation:.3f}")
        
        elif event.event_type == EventType.ORDER_FILLED:
            self.demo_stats['trades_simulated'] += 1
            strategy_id = event.data.get('strategy_id', 'unknown')
            pnl = event.data.get('pnl', 0.0)
            print(f"  💰 交易完成: {strategy_id}, PnL: ${pnl:.2f}")
    
    async def setup_enhanced_system(self):
        """設置增強版系統"""
        print("🔧 設置增強版智能投組系統...")
        
        # 1. 創建狀態持久化管理器
        self.persistence_manager = StatePersistenceManager("enhanced_demo_state.db")
        self.persistence_manager.start_auto_save()
        print("  ✅ 狀態持久化管理器已啟動")
        
        # 2. 創建智能投組系統
        self.system = IntelligentPortfolioSystem(total_capital=1000000)  # $1M
        print("  ✅ 智能投組系統創建完成")
        
        # 3. 啟動系統
        await self.system.start_system()
        print("  ✅ 系統啟動完成")
        
        return True
    
    async def simulate_market_dynamics(self):
        """模擬市場動態和策略表現變化"""
        print("\n📈 開始模擬市場動態...")
        
        # 模擬策略表現數據
        strategy_performance = {
            'pairs_btc_eth_conservative': {'base_return': 0.02, 'volatility': 0.01},
            'pairs_eth_avax_moderate': {'base_return': 0.015, 'volatility': 0.015},
            'pairs_btc_avax_aggressive': {'base_return': 0.025, 'volatility': 0.02},
            'trend_btc_fast': {'base_return': 0.03, 'volatility': 0.025},
            'trend_eth_medium': {'base_return': 0.02, 'volatility': 0.018},
            'trend_avax_slow': {'base_return': 0.018, 'volatility': 0.012}
        }
        
        # 模擬30個交易日的表現
        for day in range(30):
            print(f"\n📅 模擬第 {day + 1} 天...")
            
            for strategy_id, perf in strategy_performance.items():
                # 生成隨機日收益率
                daily_return = np.random.normal(
                    perf['base_return'] / 252,  # 年化轉日化
                    perf['volatility'] / np.sqrt(252)
                )
                
                # 計算健康分數（基於最近表現）
                health_score = 0.5 + np.tanh(daily_return * 100) * 0.3
                health_score = max(min(health_score, 1.0), 0.0)
                
                # 保存績效數據
                self.persistence_manager.save_performance_data(
                    strategy_id, daily_return, date=datetime.now().date()
                )
                
                # 發布健康分數更新事件
                publish_event(
                    EventType.STRATEGY_HEALTH_CHANGED,
                    "market_simulator",
                    {
                        "strategy_id": strategy_id,
                        "health_score": health_score,
                        "daily_return": daily_return,
                        "day": day + 1
                    }
                )
                
                # 模擬交易
                if np.random.random() < 0.3:  # 30%概率有交易
                    trade_pnl = np.random.normal(daily_return * 1000, 50)  # 模擬PnL
                    
                    publish_event(
                        EventType.ORDER_FILLED,
                        "market_simulator",
                        {
                            "strategy_id": strategy_id,
                            "symbol": "BTC/USDT:USDT",
                            "side": "buy" if daily_return > 0 else "sell",
                            "amount": 0.001,
                            "price": 50000 + np.random.normal(0, 1000),
                            "pnl": trade_pnl
                        }
                    )
            
            # 每5天觸發一次組合重新平衡檢查
            if (day + 1) % 5 == 0:
                print(f"  🔄 觸發組合重新平衡檢查...")
                await self._trigger_rebalance()
            
            # 模擬相關性變化
            if day == 15:  # 第15天模擬高相關性
                publish_event(
                    EventType.CORRELATION_ALERT,
                    "market_simulator",
                    {
                        "strategy1": "trend_btc_fast",
                        "strategy2": "trend_eth_medium",
                        "correlation": 0.85,
                        "threshold": 0.8
                    }
                )
            
            await asyncio.sleep(0.1)  # 短暫延遲
    
    async def _trigger_rebalance(self):
        """觸發重新平衡"""
        try:
            # 更新組合管理器中的策略健康分數
            portfolio_status = self.system.portfolio_manager.get_portfolio_status()
            
            # 執行重新平衡
            rebalance_result = self.system.portfolio_manager.rebalance_portfolio()
            
            if rebalance_result['rebalanced']:
                print(f"    ✅ 重新平衡完成: {len(rebalance_result['actions'])} 個調整")
            else:
                print(f"    ➖ 無需重新平衡")
                
        except Exception as e:
            logger.error(f"觸發重新平衡失敗: {e}")
    
    async def run_enhanced_demo(self):
        """運行增強版演示"""
        print("🎯 增強版智能投組系統演示")
        print("=" * 80)
        
        try:
            # 1. 設置系統
            await self.setup_enhanced_system()
            
            # 2. 顯示初始狀態
            self._display_system_status("初始狀態")
            
            # 3. 模擬市場動態
            await self.simulate_market_dynamics()
            
            # 4. 顯示最終狀態
            self._display_system_status("最終狀態")
            
            # 5. 顯示演示統計
            self._display_demo_statistics()
            
            # 6. 展示持久化數據
            self._display_persistence_data()
            
        except Exception as e:
            logger.error(f"增強版演示失敗: {e}")
            print(f"❌ 演示過程中發生錯誤: {e}")
        
        finally:
            # 清理資源
            await self._cleanup()
    
    def _display_system_status(self, title: str):
        """顯示系統狀態"""
        print(f"\n📊 {title}")
        print("-" * 60)
        
        overview = self.system.get_system_overview()
        
        print(f"💰 總資金: ${overview['capital_overview']['total_capital']:,.2f}")
        print(f"🎯 策略數量: {overview['system_stats']['total_strategies']}")
        print(f"🔧 活躍策略: {overview['system_stats']['active_strategies']}")
        print(f"📈 總交易: {overview['system_stats']['total_trades']}")
        print(f"💵 總盈虧: ${overview['system_stats'].get('total_pnl', 0.0):.2f}")
        print(f"🔄 重新平衡: {overview['system_stats']['rebalance_count']} 次")
        print(f"⚠️ 相關性警報: {overview['system_stats']['correlation_alerts']} 次")
        
        # 顯示策略分配
        portfolio_status = overview['capital_overview']['portfolio_status']
        allocations = portfolio_status['strategy_allocations']
        
        print(f"\n📋 策略分配:")
        for strategy_id, alloc in allocations.items():
            print(f"  🎯 {strategy_id}:")
            print(f"    💰 資金: ${alloc['allocated_capital']:,.0f} ({alloc['target_allocation']:.1%})")
            print(f"    ❤️ 健康: {alloc['health_score']:.2f}")
    
    def _display_demo_statistics(self):
        """顯示演示統計"""
        print(f"\n📈 演示統計")
        print("-" * 60)
        
        for key, value in self.demo_stats.items():
            print(f"  📊 {key.replace('_', ' ').title()}: {value}")
        
        # 事件總線統計
        event_stats = self.event_bus.get_stats()
        print(f"\n🔄 事件總線統計:")
        print(f"  📤 已發布: {event_stats['events_published']}")
        print(f"  📥 已處理: {event_stats['events_processed']}")
        print(f"  🗑️ 已丟棄: {event_stats['events_dropped']}")
        print(f"  📡 活躍訂閱: {event_stats['active_subscriptions']}")
    
    def _display_persistence_data(self):
        """顯示持久化數據"""
        print(f"\n💾 持久化數據概覽")
        print("-" * 60)
        
        recovery_data = self.persistence_manager.get_system_recovery_data()
        
        print(f"  📁 策略狀態: {len(recovery_data['strategy_states'])} 個")
        print(f"  📊 組合分配: {len(recovery_data['portfolio_allocations'])} 個")
        print(f"  📈 績效歷史: {len(recovery_data['performance_history'])} 個策略")
        
        # 顯示部分績效數據
        for strategy_id, returns in recovery_data['performance_history'].items():
            if returns:
                total_return = sum(returns)
                print(f"    📈 {strategy_id}: {len(returns)} 天, 累計收益: {total_return:.4f}")
    
    async def _cleanup(self):
        """清理資源"""
        try:
            if self.persistence_manager:
                self.persistence_manager.stop_auto_save()
            
            if self.system:
                await self.system.stop_system()
            
            print("\n✅ 資源清理完成")
            
        except Exception as e:
            logger.error(f"資源清理失敗: {e}")


async def main():
    """主函數"""
    demo = EnhancedPortfolioDemo()
    await demo.run_enhanced_demo()


if __name__ == "__main__":
    asyncio.run(main())
