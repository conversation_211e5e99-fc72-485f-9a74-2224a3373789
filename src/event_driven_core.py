#!/usr/bin/env python3
"""
事件驅動核心 - 現代化架構升級
Event-Driven Core - Modern Architecture Upgrade
"""

import asyncio
import json
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum
from typing import Dict, List, Any, Optional, Callable, Union
import redis.asyncio as redis
from concurrent.futures import ThreadPoolExecutor

from logging_config import get_logger

logger = get_logger(__name__)


class EventType(Enum):
    """事件類型枚舉"""
    # 數據事件
    NEW_KLINE = "new_kline"
    PRICE_UPDATE = "price_update"
    ORDERBOOK_UPDATE = "orderbook_update"
    
    # 交易事件
    SIGNAL_GENERATED = "signal_generated"
    ORDER_PLACED = "order_placed"
    ORDER_FILLED = "order_filled"
    ORDER_CANCELLED = "order_cancelled"
    
    # 系統事件
    HEALTH_CHECK = "health_check"
    CONFIG_UPDATED = "config_updated"
    STRATEGY_STATE_CHANGED = "strategy_state_changed"
    
    # 風險事件
    RISK_ALERT = "risk_alert"
    POSITION_LIMIT_EXCEEDED = "position_limit_exceeded"
    DRAWDOWN_ALERT = "drawdown_alert"


@dataclass
class Event:
    """事件基類"""
    event_type: EventType
    timestamp: datetime
    source: str
    data: Dict[str, Any]
    correlation_id: Optional[str] = None
    priority: int = 0  # 0=normal, 1=high, 2=critical
    
    def to_dict(self) -> Dict:
        """轉換為字典"""
        return {
            'event_type': self.event_type.value,
            'timestamp': self.timestamp.isoformat(),
            'source': self.source,
            'data': self.data,
            'correlation_id': self.correlation_id,
            'priority': self.priority
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'Event':
        """從字典創建事件"""
        return cls(
            event_type=EventType(data['event_type']),
            timestamp=datetime.fromisoformat(data['timestamp']),
            source=data['source'],
            data=data['data'],
            correlation_id=data.get('correlation_id'),
            priority=data.get('priority', 0)
        )


class EventHandler(ABC):
    """事件處理器抽象基類"""
    
    def __init__(self, name: str):
        self.name = name
        self.processed_count = 0
        self.error_count = 0
        self.last_processed = None
    
    @abstractmethod
    async def handle(self, event: Event) -> Optional[List[Event]]:
        """
        處理事件
        返回: 可選的新事件列表（用於事件鏈）
        """
        pass
    
    def can_handle(self, event: Event) -> bool:
        """檢查是否可以處理此事件"""
        return True
    
    async def process_event(self, event: Event) -> Optional[List[Event]]:
        """處理事件的包裝方法"""
        try:
            if not self.can_handle(event):
                return None
            
            start_time = time.time()
            result = await self.handle(event)
            processing_time = time.time() - start_time
            
            self.processed_count += 1
            self.last_processed = datetime.now()
            
            logger.debug(f"{self.name} 處理事件 {event.event_type.value} "
                        f"耗時 {processing_time:.3f}s")
            
            return result
            
        except Exception as e:
            self.error_count += 1
            logger.error(f"{self.name} 處理事件失敗: {e}")
            return None
    
    def get_stats(self) -> Dict:
        """獲取處理統計"""
        return {
            'name': self.name,
            'processed_count': self.processed_count,
            'error_count': self.error_count,
            'last_processed': self.last_processed.isoformat() if self.last_processed else None,
            'error_rate': self.error_count / max(self.processed_count, 1)
        }


class EventBus:
    """事件總線 - 核心事件分發系統"""
    
    def __init__(self, redis_url: str = "redis://localhost:6379"):
        self.redis_url = redis_url
        self.redis_client = None
        self.handlers: Dict[EventType, List[EventHandler]] = {}
        self.running = False
        self.stats = {
            'events_published': 0,
            'events_processed': 0,
            'events_failed': 0,
            'start_time': None
        }
        
        # 線程池用於CPU密集型任務
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        logger.info("EventBus 初始化完成")
    
    async def start(self):
        """啟動事件總線"""
        try:
            self.redis_client = redis.from_url(self.redis_url)
            await self.redis_client.ping()
            
            self.running = True
            self.stats['start_time'] = datetime.now()
            
            # 啟動事件監聽任務
            asyncio.create_task(self._event_listener())
            
            logger.info("EventBus 已啟動")
            
        except Exception as e:
            logger.error(f"EventBus 啟動失敗: {e}")
            raise
    
    async def stop(self):
        """停止事件總線"""
        self.running = False
        
        if self.redis_client:
            await self.redis_client.close()
        
        self.executor.shutdown(wait=True)
        
        logger.info("EventBus 已停止")
    
    def register_handler(self, event_type: EventType, handler: EventHandler):
        """註冊事件處理器"""
        if event_type not in self.handlers:
            self.handlers[event_type] = []
        
        self.handlers[event_type].append(handler)
        logger.info(f"註冊事件處理器: {handler.name} -> {event_type.value}")
    
    def unregister_handler(self, event_type: EventType, handler: EventHandler):
        """取消註冊事件處理器"""
        if event_type in self.handlers:
            self.handlers[event_type].remove(handler)
            logger.info(f"取消註冊事件處理器: {handler.name} -> {event_type.value}")
    
    async def publish(self, event: Event):
        """發布事件"""
        try:
            if not self.redis_client:
                logger.error("Redis客戶端未初始化")
                return
            
            # 序列化事件
            event_data = json.dumps(event.to_dict())
            
            # 根據優先級選擇不同的隊列
            queue_name = f"events:priority:{event.priority}"
            
            # 發布到Redis
            await self.redis_client.lpush(queue_name, event_data)
            
            self.stats['events_published'] += 1
            
            logger.debug(f"發布事件: {event.event_type.value} (優先級: {event.priority})")
            
        except Exception as e:
            logger.error(f"發布事件失敗: {e}")
    
    async def _event_listener(self):
        """事件監聽器"""
        logger.info("事件監聽器已啟動")
        
        while self.running:
            try:
                # 按優先級處理事件（高優先級優先）
                for priority in [2, 1, 0]:
                    queue_name = f"events:priority:{priority}"
                    
                    # 非阻塞獲取事件
                    event_data = await self.redis_client.rpop(queue_name)
                    
                    if event_data:
                        await self._process_event_data(event_data)
                        break
                else:
                    # 沒有事件時短暫休眠
                    await asyncio.sleep(0.01)
                    
            except Exception as e:
                logger.error(f"事件監聽器錯誤: {e}")
                await asyncio.sleep(1)
        
        logger.info("事件監聽器已停止")
    
    async def _process_event_data(self, event_data: str):
        """處理事件數據"""
        try:
            # 反序列化事件
            event_dict = json.loads(event_data)
            event = Event.from_dict(event_dict)
            
            # 獲取對應的處理器
            handlers = self.handlers.get(event.event_type, [])
            
            if not handlers:
                logger.debug(f"沒有處理器處理事件: {event.event_type.value}")
                return
            
            # 並發處理事件
            tasks = []
            for handler in handlers:
                task = asyncio.create_task(handler.process_event(event))
                tasks.append(task)
            
            # 等待所有處理器完成
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 處理結果中的新事件
            for result in results:
                if isinstance(result, list):
                    for new_event in result:
                        if isinstance(new_event, Event):
                            await self.publish(new_event)
            
            self.stats['events_processed'] += 1
            
        except Exception as e:
            self.stats['events_failed'] += 1
            logger.error(f"處理事件數據失敗: {e}")
    
    def get_stats(self) -> Dict:
        """獲取統計信息"""
        uptime = (datetime.now() - self.stats['start_time']).total_seconds() if self.stats['start_time'] else 0
        
        handler_stats = {}
        for event_type, handlers in self.handlers.items():
            handler_stats[event_type.value] = [h.get_stats() for h in handlers]
        
        return {
            'running': self.running,
            'uptime_seconds': uptime,
            'events_published': self.stats['events_published'],
            'events_processed': self.stats['events_processed'],
            'events_failed': self.stats['events_failed'],
            'success_rate': self.stats['events_processed'] / max(self.stats['events_published'], 1),
            'handlers': handler_stats
        }


class SignalGeneratedHandler(EventHandler):
    """信號生成事件處理器"""
    
    def __init__(self, trading_executor):
        super().__init__("SignalGeneratedHandler")
        self.trading_executor = trading_executor
    
    def can_handle(self, event: Event) -> bool:
        return event.event_type == EventType.SIGNAL_GENERATED
    
    async def handle(self, event: Event) -> Optional[List[Event]]:
        """處理信號生成事件"""
        signal_data = event.data
        
        # 執行交易邏輯
        try:
            if signal_data.get('action') == 'BUY':
                # 執行買入邏輯
                order_result = await self._execute_buy_order(signal_data)
            elif signal_data.get('action') == 'SELL':
                # 執行賣出邏輯
                order_result = await self._execute_sell_order(signal_data)
            else:
                return None
            
            # 生成訂單事件
            if order_result:
                order_event = Event(
                    event_type=EventType.ORDER_PLACED,
                    timestamp=datetime.now(),
                    source=self.name,
                    data=order_result,
                    correlation_id=event.correlation_id
                )
                return [order_event]
            
        except Exception as e:
            logger.error(f"執行交易失敗: {e}")
            
        return None
    
    async def _execute_buy_order(self, signal_data: Dict) -> Optional[Dict]:
        """執行買入訂單"""
        # 這裡實現具體的買入邏輯
        logger.info(f"執行買入訂單: {signal_data}")
        return {
            'order_id': f"buy_{int(time.time())}",
            'symbol': signal_data.get('symbol'),
            'side': 'buy',
            'amount': signal_data.get('amount'),
            'price': signal_data.get('price')
        }
    
    async def _execute_sell_order(self, signal_data: Dict) -> Optional[Dict]:
        """執行賣出訂單"""
        # 這裡實現具體的賣出邏輯
        logger.info(f"執行賣出訂單: {signal_data}")
        return {
            'order_id': f"sell_{int(time.time())}",
            'symbol': signal_data.get('symbol'),
            'side': 'sell',
            'amount': signal_data.get('amount'),
            'price': signal_data.get('price')
        }


class HealthCheckHandler(EventHandler):
    """健康檢查事件處理器"""
    
    def __init__(self):
        super().__init__("HealthCheckHandler")
    
    def can_handle(self, event: Event) -> bool:
        return event.event_type == EventType.HEALTH_CHECK
    
    async def handle(self, event: Event) -> Optional[List[Event]]:
        """處理健康檢查事件"""
        # 執行健康檢查邏輯
        health_status = await self._perform_health_check()
        
        logger.info(f"健康檢查完成: {health_status}")
        
        # 如果發現問題，生成警報事件
        if not health_status.get('healthy', True):
            alert_event = Event(
                event_type=EventType.RISK_ALERT,
                timestamp=datetime.now(),
                source=self.name,
                data={
                    'alert_type': 'health_check_failed',
                    'details': health_status
                },
                priority=1  # 高優先級
            )
            return [alert_event]
        
        return None
    
    async def _perform_health_check(self) -> Dict:
        """執行健康檢查"""
        # 這裡實現具體的健康檢查邏輯
        return {
            'healthy': True,
            'timestamp': datetime.now().isoformat(),
            'checks': {
                'database': True,
                'redis': True,
                'exchange_api': True
            }
        }


# 全局事件總線實例
_event_bus = None

def get_event_bus() -> EventBus:
    """獲取全局事件總線"""
    global _event_bus
    if _event_bus is None:
        _event_bus = EventBus()
    return _event_bus


async def main():
    """測試事件驅動系統"""
    # 創建事件總線
    event_bus = get_event_bus()
    
    try:
        # 啟動事件總線
        await event_bus.start()
        
        # 註冊處理器
        signal_handler = SignalGeneratedHandler(None)
        health_handler = HealthCheckHandler()
        
        event_bus.register_handler(EventType.SIGNAL_GENERATED, signal_handler)
        event_bus.register_handler(EventType.HEALTH_CHECK, health_handler)
        
        # 發布測試事件
        test_signal = Event(
            event_type=EventType.SIGNAL_GENERATED,
            timestamp=datetime.now(),
            source="test",
            data={
                'action': 'BUY',
                'symbol': 'BTC/USDT',
                'amount': 0.1,
                'price': 50000
            }
        )
        
        await event_bus.publish(test_signal)
        
        # 等待處理
        await asyncio.sleep(2)
        
        # 打印統計
        stats = event_bus.get_stats()
        print(f"事件總線統計: {json.dumps(stats, indent=2, default=str)}")
        
    finally:
        await event_bus.stop()


if __name__ == "__main__":
    asyncio.run(main())
