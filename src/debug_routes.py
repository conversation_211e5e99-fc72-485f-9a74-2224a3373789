#!/usr/bin/env python3
"""
調試路由註冊 - 檢查Flask應用中註冊的路由
Debug Route Registration - Check registered routes in Flask app
"""

import sys

sys.path.append(".")

from health_server import HealthServer
from logging_config import get_logger, setup_logging

setup_logging()
logger = get_logger(__name__)


def debug_routes():
    """調試路由註冊"""
    try:
        print("🔍 調試Flask路由註冊...")

        # 創建健康服務器實例
        health_server = HealthServer(port=8084)

        # 獲取Flask應用實例
        app = health_server.app

        print("\n📋 已註冊的路由:")
        print("=" * 50)

        # 列出所有註冊的路由
        for rule in app.url_map.iter_rules():
            methods = ", ".join(rule.methods - {"HEAD", "OPTIONS"})
            print(f"  {rule.rule:<20} [{methods}]")

        print(f"\n總共註冊了 {len(list(app.url_map.iter_rules()))} 個路由")

        # 檢查特定路由是否存在
        expected_routes = [
            "/health",
            "/status",
            "/metrics",
            "/memory",
            "/performance",
            "/portfolio",
            "/dashboard",
        ]

        print("\n🔍 檢查期望的路由:")
        print("=" * 50)

        registered_routes = [rule.rule for rule in app.url_map.iter_rules()]

        for route in expected_routes:
            if route in registered_routes:
                print(f"  ✅ {route} - 已註冊")
            else:
                print(f"  ❌ {route} - 未註冊")

        # 檢查路由函數是否存在
        print("\n🔍 檢查路由函數:")
        print("=" * 50)

        for rule in app.url_map.iter_rules():
            endpoint = rule.endpoint
            if hasattr(app.view_functions, endpoint):
                func = app.view_functions[endpoint]
                print(f"  ✅ {rule.rule} -> {func.__name__}")
            else:
                print(f"  ❌ {rule.rule} -> 函數不存在")

        return registered_routes

    except Exception as e:
        logger.error(f"調試路由失敗: {e}")
        import traceback

        traceback.print_exc()
        return []


if __name__ == "__main__":
    routes = debug_routes()
    print(f"\n🎯 調試完成，發現 {len(routes)} 個路由")
