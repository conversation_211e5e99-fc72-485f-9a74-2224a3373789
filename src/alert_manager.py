import logging

# 配置logger
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
警報管理系統 - 支持 Telegram、Discord 和健康檢查
Alert Management System - Supports Telegram, Discord and Health Check
"""

import json
import os
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Dict, Optional

import requests

from logging_config import get_logger

_ = get_logger(__name__)


class AlertLevel(Enum):
    """警報級別"""

    _ = "info"
    _ = "warning"
    _ = "error"
    _ = "critical"


class AlertManager:
    """警報管理器"""

    def __init__(self, config_path: str = "config.json"):
        self.config = self._load_alert_config(config_path)
        self.last_alert_times = {}  # 防止重複警報
        self.health_status = {
            "status": "ok",
            "last_update": datetime.now(),
            "uptime_start": datetime.now(),
            "total_alerts": 0,
            "last_trade": None,
            "current_position": None,
        }

        logger.info("AlertManager 初始化完成")

    def _load_alert_config(self, config_path: str) -> Dict:
        """載入警報配置"""
        try:
            with open(config_path, "r", encoding="utf-8") as f:
                config = json.load(f)

            # 從環境變量獲取敏感配置
            alert_config = config.get("alerts", {})

            # Telegram 配置
            telegram_config = alert_config.get("telegram", {})
            telegram_config["bot_token"] = os.getenv(
                "TELEGRAM_BOT_TOKEN", telegram_config.get("bot_token", "")
            )
            telegram_config["chat_id"] = os.getenv(
                "TELEGRAM_CHAT_ID", telegram_config.get("chat_id", "")
            )

            # Discord 配置
            discord_config = alert_config.get("discord", {})
            discord_config["webhook_url"] = os.getenv(
                "DISCORD_WEBHOOK_URL", discord_config.get("webhook_url", "")
            )

            return alert_config

        except Exception as e:
            logger.error(f"載入警報配置失敗: {e}")
            return {}

    def send_alert()
        self,
        level: AlertLevel,
        title: str,
        message: str,
        data: Optional[Dict] = None,
        throttle_minutes: int = 5,
    ):
        """發送警報"""
        try:
            # 防重複警報
            alert_key = f"{level.value}_{title}"
            now = datetime.now()

            if alert_key in self.last_alert_times:
                time_diff = (now - self.last_alert_times[alert_key]).total_seconds() / 60
                if time_diff < throttle_minutes:
                    logger.debug(f"警報被節流限制: {alert_key}")
                    return

            self.last_alert_times[alert_key] = now
            self.health_status["total_alerts"] += 1

            # 格式化消息
            formatted_message = self._format_alert_message(level, title, message, data)

            # 發送到各個渠道
            success_count = 0

            if self._send_telegram_alert(level, formatted_message):
                success_count += 1

            if self._send_discord_alert(level, formatted_message):
                success_count += 1

            # 記錄到文件
            self._log_alert_to_file(level, title, message, data)

            if success_count > 0:
                logger.info(f"警報發送成功: {title} ({success_count} 個渠道)")
            else:
                logger.warning(f"警報發送失敗: {title}")

        except Exception as e:
            logger.error(f"發送警報失敗: {e}")

    def _format_alert_message()
        self, level: AlertLevel, title: str, message: str, data: Optional[Dict] = None
    ) -> str:
        """格式化警報消息"""
        emoji_map = {
            AlertLevel.INFO: "ℹ️",
            AlertLevel.WARNING: "⚠️",
            AlertLevel.ERROR: "❌",
            AlertLevel.CRITICAL: "🚨",
        }

        emoji = emoji_map.get(level, "📢")
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        formatted = f"{emoji} **{title}**\n"
        formatted += f"🕐 時間: {timestamp}\n"
        formatted += f"📝 消息: {message}\n"

        if data:
            formatted += "\n📊 詳細信息:\n"
            for key, value in data.items():
                if isinstance(value, float):
                    formatted += f"• {key}: {value:.4f}\n"
                else:
                    formatted += f"• {key}: {value}\n"

        return formatted

    def _send_telegram_alert(self, level: AlertLevel, message: str) -> bool:
        """發送 Telegram 警報"""
        try:
            telegram_config = self.config.get("telegram", {})
            bot_token = telegram_config.get("bot_token")
            chat_id = telegram_config.get("chat_id")

            if not bot_token or not chat_id:
                logger.debug("Telegram 配置不完整，跳過發送")
                return False

            url = f"https://api.telegram.org/bot{bot_token}/sendMessage"

            payload = {
                "chat_id": chat_id,
                "text": message,
                "parse_mode": "Markdown",
                "disable_web_page_preview": True,
            }

            response = requests.post(url, json=payload, timeout=10)

            if response.status_code == 200:
                logger.debug("Telegram 警報發送成功")
                return True
            else:
                logger.warning(f"Telegram 警報發送失敗: {response.status_code}")
                return False

        except Exception as e:
            logger.error(f"發送 Telegram 警報失敗: {e}")
            return False

    def _send_discord_alert(self, level: AlertLevel, message: str) -> bool:
        """發送 Discord 警報"""
        try:
            discord_config = self.config.get("discord", {})
            webhook_url = discord_config.get("webhook_url")

            if not webhook_url:
                logger.debug("Discord 配置不完整，跳過發送")
                return False

            color_map = {
                AlertLevel.INFO: 0x3498DB,  # 藍色
                AlertLevel.WARNING: 0xF39C12,  # 橙色
                AlertLevel.ERROR: 0xE74C3C,  # 紅色
                AlertLevel.CRITICAL: 0x9B59B6,  # 紫色
            }

            payload = {
                "embeds": [
                    {
                        "title": "配對交易機器人警報",
                        "description": message,
                        "color": color_map.get(level, 0x95A5A6),
                        "timestamp": datetime.now().isoformat(),
                    }
                ]
            }

            response = requests.post(webhook_url, json=payload, timeout=10)

            if response.status_code == 204:
                logger.debug("Discord 警報發送成功")
                return True
            else:
                logger.warning(f"Discord 警報發送失敗: {response.status_code}")
                return False

        except Exception as e:
            logger.error(f"發送 Discord 警報失敗: {e}")
            return False

    def _log_alert_to_file()
        self, level: AlertLevel, title: str, message: str, data: Optional[Dict] = None
    ):
        """記錄警報到文件"""
        try:
            alerts_dir = Path("logs/alerts")
            alerts_dir.mkdir(parents=True, exist_ok=True)

            alert_record = {
                "timestamp": datetime.now().isoformat(),
                "level": level.value,
                "title": title,
                "message": message,
                "data": data or {},
            }

            # 按日期分文件
            date_str = datetime.now().strftime("%Y % m%d")
            alert_file = alerts_dir / f"alerts_{date_str}.json"

            # 讀取現有記錄
            if alert_file.exists():
                with open(alert_file, "r", encoding="utf-8") as f:
                    alerts = json.load(f)
            else:
                alerts = []

            alerts.append(alert_record)

            # 保存記錄
            with open(alert_file, "w", encoding="utf-8") as f:
                json.dump(alerts, f, indent=2, ensure_ascii=False)

        except Exception as e:
            logger.error(f"記錄警報到文件失敗: {e}")

    def update_health_status(self, status_data: Dict):
        """更新健康狀態"""
        try:
            self.health_status.update({"last_update": datetime.now(), **status_data})

        except Exception as e:
            logger.error(f"更新健康狀態失敗: {e}")

    def get_health_status(self) -> Dict:
        """獲取健康狀態"""
        try:
            uptime = datetime.now() - self.health_status["uptime_start"]

            return {
                "status": self.health_status["status"],
                "last_update": self.health_status["last_update"].isoformat(),
                "uptime_seconds": int(uptime.total_seconds()),
                "uptime_human": str(uptime),
                "total_alerts": self.health_status["total_alerts"],
                "last_trade": self.health_status.get("last_trade"),
                "current_position": self.health_status.get("current_position"),
            }

        except Exception as e:
            logger.error(f"獲取健康狀態失敗: {e}")
            return {"status": "error", "error": str(e)}

    def send_startup_alert(self):
        """發送啟動警報"""
        self.send_alert()
            AlertLevel.INFO,
            "機器人啟動",
            "配對交易機器人已成功啟動並開始運行",
            {"啟動時間": datetime.now().strftime("%Y-%m-%d %H:%M:%S"), "系統狀態": "正常"},
        )

    def send_shutdown_alert(self):
        """發送關閉警報"""
        uptime = datetime.now() - self.health_status["uptime_start"]

        self.send_alert()
            AlertLevel.WARNING,
            "機器人關閉",
            "配對交易機器人正在關閉",
            {
                "關閉時間": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "運行時長": str(uptime),
                "總警報數": self.health_status["total_alerts"],
            },
        )

    def send_trade_alert(self, trade_type: str, trade_data: Dict):
        """發送交易警報"""
        level = AlertLevel.INFO if trade_data.get("pnl", 0) >= 0 else AlertLevel.WARNING

        self.send_alert(level, f"交易執行 - {trade_type}", f"成功執行{trade_type}操作", trade_data)

        # 更新健康狀態
        self.health_status["last_trade"] = {
            "type": trade_type,
            "time": datetime.now().isoformat(),
            "pnl": trade_data.get("pnl", 0),
        }

    def send_error_alert()
        self, error_type: str, error_message: str, error_data: Optional[Dict] = None
    ):
        """發送錯誤警報"""
        self.send_alert(AlertLevel.ERROR, f"系統錯誤 - {error_type}", error_message, error_data)

    def send_critical_alert(self, title: str, message: str, data: Optional[Dict] = None):
        """發送緊急警報"""
        self.send_alert(AlertLevel.CRITICAL, title, message, data, throttle_minutes=1)  # 緊急警報減少節流時間


# 全局警報管理器實例
_alert_manager = None


def get_alert_manager() -> AlertManager:
    """獲取全局警報管理器實例"""
    global _alert_manager
    if _alert_manager is None:
        _alert_manager = AlertManager()
    return _alert_manager


def send_alert(level: AlertLevel, title: str, message: str, data: Optional[Dict] = None):
    """便捷的警報發送函數"""
    alert_manager = get_alert_manager()
    alert_manager.send_alert(level, title, message, data)


if __name__ == "__main__":
    # 測試警報系統
    alert_manager = AlertManager()

    print("測試警報系統...")

    # 測試各種級別的警報
    alert_manager.send_alert(AlertLevel.INFO, "測試信息", "這是一個測試信息警報", {"測試參數": 123.45})

    alert_manager.send_alert(AlertLevel.WARNING, "測試警告", "這是一個測試警告警報")

    # 測試健康狀態
    health = alert_manager.get_health_status()
    print(f"健康狀態: {json.dumps(health, indent=2, ensure_ascii=False)}")

    print("警報系統測試完成！")
