import logging

# 配置logger
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
跨平台配置管理工具
Cross-platform Configuration Manager
"""

import json
import os
import platform
from pathlib import Path
from typing import Dict

from dotenv import load_dotenv

from logging_config import get_logger

logger = get_logger(__name__)


class ConfigManager:
    """跨平台配置管理器"""

    def __init__(self, config_path: str = "config.json"):
        self.config_path = Path(config_path)
        self.system_info = self._get_system_info()
        self.config = {}

        logger.info(f"配置管理器初始化 - 系統: {self.system_info['system']} {self.system_info['version']}")

    def _get_system_info(self) -> Dict:
        """獲取系統信息"""
        return {
            "system": platform.system(),
            "version": platform.version(),
            "machine": platform.machine(),
            "python_version": platform.python_version(),
            "encoding": "utf-8",  # 強制使用 UTF-8 編碼
        }

    def load_config(self) -> Dict:
        """載入配置，支持環境變量覆蓋"""
        try:
            # 1. 載入 .env 文件
            self._load_env_file()

            # 2. 載入基礎配置文件
            self._load_base_config()

            # 3. 應用環境變量覆蓋
            self._apply_env_overrides()

            # 4. 驗證配置
            self._validate_config()

            # 5. 應用平台特定調整
            self._apply_platform_adjustments()

            logger.info("配置載入完成")
            return self.config

        except Exception as e:
            logger.error(f"載入配置失敗: {e}")
            raise

    def _load_env_file(self):
        """載入環境變量文件"""
        env_files = [".env", ".env.local", ".env.production"]

        for env_file in env_files:
            env_path = Path(env_file)
            if env_path.exists():
                load_dotenv(env_path, encoding="utf-8")
                logger.info(f"已載入環境變量文件: {env_file}")
                break
        else:
            logger.info("未找到 .env 文件，將使用默認配置")

    def _load_base_config(self):
        """載入基礎配置文件"""
        if not self.config_path.exists():
            logger.error(f"配置文件不存在: {self.config_path}")
            raise FileNotFoundError(f"配置文件不存在: {self.config_path}")

        try:
            with open(self.config_path, "r", encoding="utf-8") as f:
                self.config = json.load(f)
            logger.info(f"基礎配置載入成功: {self.config_path}")
        except UnicodeDecodeError:
            # 嘗試其他編碼
            encodings = ["gbk", "gb2312", "big5"]
            for encoding in encodings:
                try:
                    with open(self.config_path, "r", encoding=encoding) as f:
                        self.config = json.load(f)
                    logger.info(f"使用 {encoding} 編碼載入配置成功")
                    break
                except (UnicodeDecodeError, json.JSONDecodeError):
                    continue
            else:
                raise ValueError("無法使用任何編碼載入配置文件")

    def _apply_env_overrides(self):
        """應用環境變量覆蓋"""
        # 交易所配置
        if "exchange" in self.config:
            exchange_config = self.config["exchange"]

            env_mappings = {
                "EXCHANGE_NAME": "name",
                "EXCHANGE_SANDBOX": "sandbox",
                "EXCHANGE_API_KEY": "api_key",
                "EXCHANGE_SECRET": "secret",
                "EXCHANGE_PASSWORD": "password",
            }

            for env_key, config_key in env_mappings.items():
                env_value = os.getenv(env_key)
                if env_value is not None:
                    if config_key == "sandbox":
                        exchange_config[config_key] = env_value.lower() == "true"
                    else:
                        exchange_config[config_key] = env_value

        # 交易參數
        if os.getenv("TRADING_PAIR_BASE") and os.getenv("TRADING_PAIR_QUOTE"):
            self.config["trading_pair"] = [
                os.getenv("TRADING_PAIR_BASE"),
                os.getenv("TRADING_PAIR_QUOTE"),
            ]

        # 數值參數
        numeric_mappings = {
            "TIMEFRAME": ("timeframe", str),
            "LOOKBACK_PERIOD": ("lookback_period", int),
            "POSITION_SIZE_USD": ("position_size_usd", int),
            "ENTRY_THRESHOLD_HIGH": ("entry_threshold_high", float),
            "ENTRY_THRESHOLD_LOW": ("entry_threshold_low", float),
            "CONFIRMATION_THRESHOLD_HIGH": ("confirmation_threshold_high", float),
            "CONFIRMATION_THRESHOLD_LOW": ("confirmation_threshold_low", float),
            "COOLDOWN_PERIOD": ("cooldown_period", int),
            "STOP_LOSS_PCT": ("stop_loss_pct", float),
        }

        for env_key, (config_key, data_type) in numeric_mappings.items():
            env_value = os.getenv(env_key)
            if env_value is not None:
                try:
                    self.config[config_key] = data_type(env_value)
                except ValueError:
                    logger.warning(f"環境變量 {env_key} 的值 '{env_value}' 無法轉換為 {data_type.__name__}")

    def _validate_config(self):
        """驗證配置"""
        required_keys = ["trading_pair", "timeframe", "lookback_period"]

        for key in required_keys:
            if key not in self.config:
                raise ValueError(f"配置缺少必要參數: {key}")

        # 驗證交易對格式
        if (
            not isinstance(self.config["trading_pair"], list)
            or len(self.config["trading_pair"]) != 2
        ):
            raise ValueError("trading_pair 必須是包含兩個元素的列表")

        # 驗證數值範圍
        if self.config.get("lookback_period", 0) < 10:
            logger.warning("lookback_period 建議設置為至少 10")

        if self.config.get("stop_loss_pct", 0) <= 0:
            logger.warning("stop_loss_pct 應該大於 0")

    def _apply_platform_adjustments(self):
        """應用平台特定調整"""
        system = self.system_info["system"]

        if system == "Windows":
            # Windows 特定調整
            self._apply_windows_adjustments()
        elif system == "Darwin":  # macOS
            # macOS 特定調整
            self._apply_macos_adjustments()
        elif system == "Linux":
            # Linux 特定調整
            self._apply_linux_adjustments()

    def _apply_windows_adjustments(self):
        """Windows 平台調整"""
        logger.debug("應用 Windows 平台調整")

        # 確保路徑使用正確的分隔符
        if "logging" in self.config:
            log_file = self.config["logging"].get("file", "pair_trading_bot.log")
            # Windows 路徑處理已由 pathlib 自動處理
            self.config["logging"]["file"] = log_file

    def _apply_macos_adjustments(self):
        """macOS 平台調整"""
        logger.debug("應用 macOS 平台調整")

        # macOS 特定的網絡和文件系統調整
        if "exchange" in self.config:
            # 可能需要調整網絡超時設置
            pass

    def _apply_linux_adjustments(self):
        """Linux 平台調整"""
        logger.debug("應用 Linux 平台調整")

        # Linux 特定調整
        pass

    def save_config(self, config: Dict, backup: bool = True):
        """保存配置文件"""
        try:
            if backup and self.config_path.exists():
                backup_path = self.config_path.with_suffix(".bak")
                self.config_path.rename(backup_path)
                logger.info(f"配置文件已備份到: {backup_path}")

            with open(self.config_path, "w", encoding="utf-8") as f:
                json.dump(config, f, indent=2, ensure_ascii=False)

            logger.info(f"配置文件已保存: {self.config_path}")

        except Exception as e:
            logger.error(f"保存配置文件失敗: {e}")
            raise

    def create_env_template(self, output_path: str = ".env.example"):
        """創建環境變量模板文件"""
        try:
            _ = """# 配對交易機器人環境變量配置
# Pair Trading Bot Environment Configuration

# 交易所 API 配置 / Exchange API Configuration
EXCHANGE_NAME=binance
EXCHANGE_SANDBOX=true
EXCHANGE_API_KEY=your_api_key_here
EXCHANGE_SECRET=your_secret_here
EXCHANGE_PASSWORD=

# 交易參數 / Trading Parameters
TRADING_PAIR_BASE=BTCUSDT
TRADING_PAIR_QUOTE=ETHUSDT
TIMEFRAME=1m
LOOKBACK_PERIOD=60
POSITION_SIZE_USD=1000

# 策略參數 / Strategy Parameters
ENTRY_THRESHOLD_HIGH=2.5
ENTRY_THRESHOLD_LOW=-2.5
CONFIRMATION_THRESHOLD_HIGH=2.0
CONFIRMATION_THRESHOLD_LOW=-2.0
COOLDOWN_PERIOD=15
STOP_LOSS_PCT=0.005

# 注意事項 / Important Notes:
# 1. 請勿將真實 API 密鑰提交到版本控制
# 2. 生產環境請使用專用 API 密鑰並限制權限
# 3. 定期輪換 API 密鑰以提高安全性
"""

            output_file = Path(output_path)
            with open(output_file, "w", encoding="utf-8") as f:
                f.write(template_content)

            logger.info(f"環境變量模板已創建: {output_path}")

        except Exception as e:
            logger.error(f"創建環境變量模板失敗: {e}")
            raise

    def get_system_info(self) -> Dict:
        """獲取系統信息"""
        return self.system_info.copy()

    def validate_api_config(self) -> bool:
        """驗證 API 配置"""
        try:
            exchange_config = self.config.get("exchange", {})

            required_fields = ["name", "api_key", "secret"]
            missing_fields = []

            for field in required_fields:
                if not exchange_config.get(field):
                    missing_fields.append(field)

            if missing_fields:
                logger.warning(f"API 配置缺少字段: {missing_fields}")
                return False

            # 檢查 API 密鑰格式
            api_key = exchange_config.get("api_key", "")
            secret = exchange_config.get("secret", "")

            if api_key in ["your_api_key_here", ""] or secret in ["your_secret_here", ""]:
                logger.warning("請設置真實的 API 密鑰")
                return False

            logger.info("API 配置驗證通過")
            return True

        except Exception as e:
            logger.error(f"驗證 API 配置失敗: {e}")
            return False


def main():
    """測試配置管理器"""
    try:
        config_manager = ConfigManager()

        print("系統信息:")
        system_info = config_manager.get_system_info()
        for key, value in system_info.items():
            print(f"  {key}: {value}")

        print("\n載入配置...")
        config = config_manager.load_config()

        print(f"交易對: {config.get('trading_pair')}")
        print(f"時間框架: {config.get('timeframe')}")
        print(f"回看週期: {config.get('lookback_period')}")

        print("\n驗證 API 配置...")
        api_valid = config_manager.validate_api_config()
        print(f"API 配置有效: {api_valid}")

        if not Path(".env.example").exists():
            print("\n創建環境變量模板...")
            config_manager.create_env_template()

        print("配置管理器測試完成！")

    except Exception as e:
        print(f"測試失敗: {e}")


if __name__ == "__main__":
    main()  # 修復不完整調用
