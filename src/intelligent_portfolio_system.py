#!/usr/bin/env python3
"""
智能投組系統 - 集成事件總線、組合管理器和多策略引擎
Intelligent Portfolio System - Integrated event bus, portfolio manager and multi-strategy engine
"""

import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List

from global_event_bus import GlobalEventBus, Event, EventType, publish_event
from portfolio_manager import PortfolioManager
from multi_strategy_engine import MultiStrategyEngine, EngineConfig
from pairs_trading_strategy import PairsTradingStrategy
from trend_following_strategy import TrendFollowingStrategy
from exchange_factory import ExchangeFactory
from smart_capital_management import SmartCapitalManager, RiskLevel
from logging_config import setup_logging, get_logger

setup_logging()
logger = get_logger(__name__)


class IntelligentPortfolioSystem:
    """智能投組系統 - 航空母艦級交易平台"""
    
    def __init__(self, total_capital: float = 100000):
        """
        初始化智能投組系統
        
        Args:
            total_capital: 總資金
        """
        self.total_capital = total_capital
        
        # 核心組件
        self.event_bus = GlobalEventBus()
        self.portfolio_manager = PortfolioManager(total_capital)
        
        # 交易基礎設施
        self.exchange_gateway = ExchangeFactory.create_exchange_gateway({}, mock_mode=True)
        self.capital_manager = SmartCapitalManager(total_capital, RiskLevel.MODERATE, 50)
        
        # 多策略引擎
        engine_config = EngineConfig(
            max_concurrent_strategies=10,
            signal_aggregation_window=30,
            position_update_interval=60,
            health_check_interval=300,
            max_daily_trades=200
        )
        self.strategy_engine = MultiStrategyEngine(
            self.exchange_gateway,
            self.capital_manager,
            engine_config
        )
        
        # 系統狀態
        self.is_running = False
        self.start_time = None
        self.system_stats = {
            'total_strategies': 0,
            'active_strategies': 0,
            'total_trades': 0,
            'total_pnl': 0.0,
            'rebalance_count': 0,
            'correlation_alerts': 0
        }
        
        # 設置事件監聽
        self._setup_system_events()
        
        logger.info(f"智能投組系統初始化完成: 總資金 ${total_capital:,.2f}")
    
    def _setup_system_events(self):
        """設置系統級事件監聽"""
        # 監聽組合重新平衡事件
        self.event_bus.subscribe(
            [EventType.PORTFOLIO_REBALANCE],
            self._on_portfolio_rebalance
        )
        
        # 監聽相關性警報
        self.event_bus.subscribe(
            [EventType.CORRELATION_ALERT],
            self._on_correlation_alert
        )
        
        # 監聽策略健康變化
        self.event_bus.subscribe(
            [EventType.STRATEGY_HEALTH_CHANGED],
            self._on_strategy_health_changed
        )
        
        # 監聽交易事件
        self.event_bus.subscribe(
            [EventType.ORDER_FILLED],
            self._on_trade_completed
        )
    
    def add_strategy_suite(self):
        """添加完整的策略套件"""
        logger.info("添加策略套件...")
        
        strategies = []
        
        # 1. 配對交易策略組
        pairs_configs = [
            {
                'strategy_id': 'pairs_btc_eth_conservative',
                'pairs': [['BTC/USDT:USDT', 'ETH/USDT:USDT']],
                'lookback_period': 120,
                'entry_threshold': 2.5,
                'exit_threshold': 0.1,
                'min_correlation': 0.7
            },
            {
                'strategy_id': 'pairs_eth_avax_moderate',
                'pairs': [['ETH/USDT:USDT', 'AVAX/USDT:USDT']],
                'lookback_period': 90,
                'entry_threshold': 2.0,
                'exit_threshold': 0.15,
                'min_correlation': 0.6
            },
            {
                'strategy_id': 'pairs_btc_avax_aggressive',
                'pairs': [['BTC/USDT:USDT', 'AVAX/USDT:USDT']],
                'lookback_period': 60,
                'entry_threshold': 1.8,
                'exit_threshold': 0.2,
                'min_correlation': 0.5
            }
        ]
        
        for config in pairs_configs:
            strategy = PairsTradingStrategy(config['strategy_id'], config)
            strategies.append(strategy)
            self.strategy_engine.add_strategy(strategy)
            self.portfolio_manager.add_strategy(strategy)
        
        # 2. 趨勢跟蹤策略組
        trend_configs = [
            {
                'strategy_id': 'trend_btc_fast',
                'symbols': ['BTC/USDT:USDT'],
                'fast_ma_period': 5,
                'slow_ma_period': 15,
                'rsi_period': 14,
                'min_trend_strength': 0.01
            },
            {
                'strategy_id': 'trend_eth_medium',
                'symbols': ['ETH/USDT:USDT'],
                'fast_ma_period': 10,
                'slow_ma_period': 30,
                'rsi_period': 14,
                'min_trend_strength': 0.015
            },
            {
                'strategy_id': 'trend_avax_slow',
                'symbols': ['AVAX/USDT:USDT'],
                'fast_ma_period': 20,
                'slow_ma_period': 50,
                'rsi_period': 21,
                'min_trend_strength': 0.02
            }
        ]
        
        for config in trend_configs:
            strategy = TrendFollowingStrategy(config['strategy_id'], config)
            strategies.append(strategy)
            self.strategy_engine.add_strategy(strategy)
            self.portfolio_manager.add_strategy(strategy)
        
        self.system_stats['total_strategies'] = len(strategies)
        self.system_stats['active_strategies'] = len(strategies)
        
        logger.info(f"策略套件添加完成: {len(strategies)} 個策略")
        
        # 發布系統啟動事件
        publish_event(
            EventType.SYSTEM_STARTUP,
            "intelligent_portfolio_system",
            {
                "total_strategies": len(strategies),
                "total_capital": self.total_capital,
                "timestamp": datetime.now().isoformat()
            }
        )
        
        return strategies
    
    async def start_system(self):
        """啟動智能投組系統"""
        if self.is_running:
            logger.warning("系統已在運行")
            return
        
        logger.info("🚀 啟動智能投組系統...")
        
        # 1. 啟動事件總線
        self.event_bus.start()
        logger.info("  ✅ 事件總線已啟動")
        
        # 2. 添加策略套件
        strategies = self.add_strategy_suite()
        logger.info(f"  ✅ 策略套件已載入: {len(strategies)} 個策略")
        
        # 3. 啟動多策略引擎
        self.strategy_engine.start()
        logger.info("  ✅ 多策略引擎已啟動")
        
        # 4. 執行初始組合平衡
        initial_balance = self.portfolio_manager.rebalance_portfolio()
        logger.info(f"  ✅ 初始組合平衡完成: {initial_balance}")
        
        self.is_running = True
        self.start_time = datetime.now()
        
        logger.info("🎉 智能投組系統啟動完成！")
        
        # 啟動系統監控循環
        asyncio.create_task(self._system_monitoring_loop())
    
    async def stop_system(self):
        """停止智能投組系統"""
        if not self.is_running:
            return
        
        logger.info("🛑 停止智能投組系統...")
        
        self.is_running = False
        
        # 停止各個組件
        self.strategy_engine.stop()
        self.event_bus.stop()
        
        # 發布系統關閉事件
        publish_event(
            EventType.SYSTEM_SHUTDOWN,
            "intelligent_portfolio_system",
            {
                "runtime_seconds": (datetime.now() - self.start_time).total_seconds() if self.start_time else 0,
                "final_stats": self.system_stats
            }
        )
        
        logger.info("✅ 智能投組系統已停止")
    
    async def _system_monitoring_loop(self):
        """系統監控循環"""
        last_rebalance_check = datetime.now()
        last_health_check = datetime.now()
        
        while self.is_running:
            try:
                current_time = datetime.now()
                
                # 定期檢查是否需要重新平衡（每小時檢查一次）
                if (current_time - last_rebalance_check).total_seconds() >= 3600:
                    await self._check_rebalance_needed()
                    last_rebalance_check = current_time
                
                # 定期系統健康檢查（每10分鐘）
                if (current_time - last_health_check).total_seconds() >= 600:
                    await self._system_health_check()
                    last_health_check = current_time
                
                # 短暫休眠
                await asyncio.sleep(30)
                
            except Exception as e:
                logger.error(f"系統監控循環異常: {e}")
                await asyncio.sleep(60)
    
    async def _check_rebalance_needed(self):
        """檢查是否需要重新平衡"""
        try:
            # 更新策略健康分數
            engine_status = self.strategy_engine.get_engine_status()
            
            for strategy_id, perf in engine_status['strategy_performance'].items():
                health_score = perf.get('health_score', 0.5)
                
                # 發布健康分數更新事件
                publish_event(
                    EventType.STRATEGY_HEALTH_CHANGED,
                    "system_monitor",
                    {
                        "strategy_id": strategy_id,
                        "health_score": health_score,
                        "total_trades": perf.get('total_trades', 0),
                        "win_rate": perf.get('win_rate', 0.0)
                    }
                )
            
            # 執行重新平衡
            rebalance_result = self.portfolio_manager.rebalance_portfolio()
            if rebalance_result['rebalanced']:
                self.system_stats['rebalance_count'] += 1
                logger.info(f"執行了第 {self.system_stats['rebalance_count']} 次重新平衡")
            
        except Exception as e:
            logger.error(f"重新平衡檢查失敗: {e}")
    
    async def _system_health_check(self):
        """系統健康檢查"""
        try:
            # 獲取各組件狀態
            engine_status = self.strategy_engine.get_engine_status()
            portfolio_status = self.portfolio_manager.get_portfolio_status()
            event_bus_stats = self.event_bus.get_stats()
            
            # 更新系統統計
            self.system_stats.update({
                'active_strategies': engine_status['active_strategies'],
                'total_trades': engine_status['daily_trades'],
                'event_bus_events': event_bus_stats['events_processed']
            })
            
            # 發布健康檢查事件
            publish_event(
                EventType.HEALTH_CHECK,
                "intelligent_portfolio_system",
                {
                    "system_stats": self.system_stats,
                    "engine_status": engine_status,
                    "portfolio_status": portfolio_status,
                    "event_bus_stats": event_bus_stats
                }
            )
            
            logger.debug("系統健康檢查完成")
            
        except Exception as e:
            logger.error(f"系統健康檢查失敗: {e}")
    
    def _on_portfolio_rebalance(self, event: Event):
        """處理組合重新平衡事件"""
        actions = event.data.get('actions', [])
        total_change = event.data.get('total_change', 0.0)
        
        logger.info(f"組合重新平衡: {len(actions)} 個調整, 總變化: {total_change:.2%}")
        
        for action in actions:
            logger.info(f"  📊 {action['strategy_id']}: "
                       f"{action['old_allocation']:.1%} → {action['new_allocation']:.1%} "
                       f"(${action['new_capital']:,.0f})")
    
    def _on_correlation_alert(self, event: Event):
        """處理相關性警報事件"""
        strategy1 = event.data.get('strategy1')
        strategy2 = event.data.get('strategy2')
        correlation = event.data.get('correlation', 0.0)
        
        self.system_stats['correlation_alerts'] += 1
        
        logger.warning(f"相關性警報: {strategy1} ↔ {strategy2} = {correlation:.3f}")
    
    def _on_strategy_health_changed(self, event: Event):
        """處理策略健康變化事件"""
        strategy_id = event.data.get('strategy_id')
        health_score = event.data.get('health_score', 0.5)
        
        logger.debug(f"策略健康更新: {strategy_id} = {health_score:.2f}")
    
    def _on_trade_completed(self, event: Event):
        """處理交易完成事件"""
        # 更新交易統計
        self.system_stats['total_trades'] += 1

        # 提取交易信息
        strategy_id = event.data.get('strategy_id', 'unknown')
        pnl = event.data.get('pnl', 0.0)

        # 更新總盈虧
        self.system_stats['total_pnl'] += pnl

        # 發布詳細的交易分析事件
        publish_event(
            EventType.SIGNAL_GENERATED,  # 重用事件類型
            "intelligent_portfolio_system",
            {
                "action": "trade_analysis",
                "strategy_id": strategy_id,
                "pnl": pnl,
                "cumulative_pnl": self.system_stats['total_pnl'],
                "trade_count": self.system_stats['total_trades']
            }
        )

        logger.info(f"交易完成: {strategy_id}, PnL: ${pnl:.2f}, 累計: ${self.system_stats['total_pnl']:.2f}")
    
    def get_system_overview(self) -> Dict[str, Any]:
        """獲取系統概覽"""
        runtime = (datetime.now() - self.start_time).total_seconds() if self.start_time else 0
        
        return {
            'system_status': {
                'is_running': self.is_running,
                'runtime_seconds': runtime,
                'start_time': self.start_time.isoformat() if self.start_time else None
            },
            'capital_overview': {
                'total_capital': self.total_capital,
                'portfolio_status': self.portfolio_manager.get_portfolio_status()
            },
            'strategy_overview': {
                'engine_status': self.strategy_engine.get_engine_status()
            },
            'system_stats': self.system_stats,
            'event_bus_stats': self.event_bus.get_stats()
        }


async def main():
    """主函數 - 智能投組系統演示"""
    print("🎯 智能投組系統演示")
    print("=" * 80)
    
    # 創建智能投組系統
    system = IntelligentPortfolioSystem(total_capital=500000)  # $500,000
    
    try:
        # 啟動系統
        await system.start_system()
        
        # 顯示系統概覽
        overview = system.get_system_overview()
        print(f"\n📊 系統概覽:")
        print(f"  💰 總資金: ${overview['capital_overview']['total_capital']:,.2f}")
        print(f"  🎯 策略數量: {overview['system_stats']['total_strategies']}")
        print(f"  🔧 活躍策略: {overview['system_stats']['active_strategies']}")
        
        # 運行演示
        demo_duration = 120  # 2分鐘演示
        print(f"\n🚀 系統運行演示 ({demo_duration} 秒)...")
        
        start_time = time.time()
        last_status_time = start_time
        
        while time.time() - start_time < demo_duration:
            current_time = time.time()
            
            # 每20秒顯示一次狀態
            if current_time - last_status_time >= 20:
                elapsed = int(current_time - start_time)
                remaining = demo_duration - elapsed
                
                print(f"\n⏰ 運行時間: {elapsed}s / {demo_duration}s (剩餘: {remaining}s)")
                
                # 獲取實時狀態
                overview = system.get_system_overview()
                stats = overview['system_stats']
                
                print(f"📈 系統狀態:")
                print(f"  交易次數: {stats['total_trades']}")
                print(f"  重新平衡: {stats['rebalance_count']} 次")
                print(f"  相關性警報: {stats['correlation_alerts']} 次")
                print(f"  事件處理: {overview['event_bus_stats']['events_processed']} 個")
                
                last_status_time = current_time
            
            await asyncio.sleep(1)
        
        # 顯示最終結果
        final_overview = system.get_system_overview()
        print(f"\n🎉 演示完成！最終統計:")
        print(f"  運行時間: {final_overview['system_status']['runtime_seconds']:.0f} 秒")
        print(f"  總交易: {final_overview['system_stats']['total_trades']} 筆")
        print(f"  重新平衡: {final_overview['system_stats']['rebalance_count']} 次")
        print(f"  事件處理: {final_overview['event_bus_stats']['events_processed']} 個")
        
    except KeyboardInterrupt:
        print("\n⚠️ 演示被用戶中斷")
    except Exception as e:
        print(f"\n❌ 演示過程中發生錯誤: {e}")
        logger.error(f"系統演示失敗: {e}")
    finally:
        # 停止系統
        await system.stop_system()
        print("\n✅ 智能投組系統演示完成")


if __name__ == "__main__":
    asyncio.run(main())
