#!/usr/bin/env python3
"""
優雅停機管理器 - 基於您的建議實現可靠的停機機制
Graceful Shutdown Manager - Reliable shutdown mechanism based on your recommendations
"""

import asyncio
import signal
import sys
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Callable, Dict, List, Optional

from logging_config import get_logger

logger = get_logger(__name__)


class ShutdownComponent(ABC):
    """需要優雅停機的組件接口"""

    @abstractmethod
    async def shutdown(self) -> bool:
        """
        執行組件的停機操作
        返回 True 表示成功，False 表示失敗
        """
        pass

    @property
    @abstractmethod
    def component_name(self) -> str:
        """組件名稱"""
        pass


class GracefulShutdownManager:
    """優雅停機管理器"""

    def __init__(self, shutdown_timeout: float = 30.0):
        self.shutdown_timeout = shutdown_timeout
        self.components: List[ShutdownComponent] = []
        self.shutdown_callbacks: List[Callable[[], None]] = []
        self.is_shutting_down = False
        self.shutdown_event = asyncio.Event()

        # 註冊信號處理器
        self._setup_signal_handlers()

        logger.info(f"優雅停機管理器初始化完成，超時時間: {shutdown_timeout}秒")

    def _setup_signal_handlers(self):
        """設置信號處理器"""
        try:
            # 註冊 SIGINT (Ctrl+C) 和 SIGTERM 信號
            for sig in [signal.SIGINT, signal.SIGTERM]:
                signal.signal(sig, self._signal_handler)

            # 在 Windows 上也註冊 SIGBREAK
            if sys.platform == "win32":
                signal.signal(signal.SIGBREAK, self._signal_handler)

        except Exception as e:
            logger.warning(f"設置信號處理器失敗: {e}")

    def _signal_handler(self, signum, frame):
        """信號處理函數"""
        signal_name = signal.Signals(signum).name
        logger.info(f"收到停機信號: {signal_name}")

        if not self.is_shutting_down:
            # 創建停機任務
            asyncio.create_task(self.shutdown())

    def register_component(self, component: ShutdownComponent):
        """註冊需要停機的組件"""
        if component not in self.components:
            self.components.append(component)
            logger.info(f"註冊停機組件: {component.component_name}")

    def unregister_component(self, component: ShutdownComponent):
        """取消註冊組件"""
        if component in self.components:
            self.components.remove(component)
            logger.info(f"取消註冊停機組件: {component.component_name}")

    def register_callback(self, callback: Callable[[], None]):
        """註冊停機回調函數"""
        self.shutdown_callbacks.append(callback)
        logger.info("註冊停機回調函數")

    async def shutdown(self) -> bool:
        """執行優雅停機"""
        if self.is_shutting_down:
            logger.warning("停機已在進行中")
            return True

        self.is_shutting_down = True
        logger.info("🛑 開始優雅停機...")

        start_time = datetime.now()
        success_count = 0
        total_count = len(self.components)

        try:
            # 並發執行所有組件的停機操作
            shutdown_tasks = []

            for component in self.components:
                task = asyncio.create_task(self._shutdown_component_with_timeout(component))
                shutdown_tasks.append(task)

            # 等待所有停機任務完成
            if shutdown_tasks:
                logger.info(f"等待 {len(shutdown_tasks)} 個組件停機...")

                results = await asyncio.gather(*shutdown_tasks, return_exceptions=True)

                # 統計結果
                for i, result in enumerate(results):
                    component = self.components[i]
                    if isinstance(result, Exception):
                        logger.error(f"組件 {component.component_name} 停機異常: {result}")
                    elif result:
                        success_count += 1
                        logger.info(f"✅ 組件 {component.component_name} 停機成功")
                    else:
                        logger.warning(f"⚠️ 組件 {component.component_name} 停機失敗")

            # 執行停機回調
            await self._execute_shutdown_callbacks()

            # 設置停機事件
            self.shutdown_event.set()

            elapsed_time = (datetime.now() - start_time).total_seconds()

            if success_count == total_count:
                logger.info(
                    f"✅ 優雅停機完成！成功停機 {success_count}/{total_count} 個組件，耗時 {elapsed_time:.2f}秒"
                )
                return True
            else:
                logger.warning(
                    f"⚠️ 部分停機失敗！成功停機 {success_count}/{total_count} 個組件，耗時 {elapsed_time:.2f}秒"
                )
                return False

        except Exception as e:
            logger.error(f"停機過程中發生異常: {e}")
            return False
        finally:
            self.is_shutting_down = False

    async def _shutdown_component_with_timeout(self, component: ShutdownComponent) -> bool:
        """帶超時的組件停機"""
        try:
            # 為每個組件設置獨立的超時
            component_timeout = self.shutdown_timeout / max(len(self.components), 1)

            return await asyncio.wait_for(component.shutdown(), timeout=component_timeout)

        except asyncio.TimeoutError:
            logger.error(f"組件 {component.component_name} 停機超時")
            return False
        except Exception as e:
            logger.error(f"組件 {component.component_name} 停機異常: {e}")
            return False

    async def _execute_shutdown_callbacks(self):
        """執行停機回調函數"""
        if not self.shutdown_callbacks:
            return

        logger.info(f"執行 {len(self.shutdown_callbacks)} 個停機回調...")

        for i, callback in enumerate(self.shutdown_callbacks):
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback()
                else:
                    callback()
                logger.debug(f"停機回調 {i+1} 執行成功")
            except Exception as e:
                logger.error(f"停機回調 {i+1} 執行失敗: {e}")

    async def wait_for_shutdown(self):
        """等待停機完成"""
        await self.shutdown_event.wait()

    def is_shutdown_requested(self) -> bool:
        """檢查是否請求停機"""
        return self.is_shutting_down


# 全局停機管理器實例
_shutdown_manager: Optional[GracefulShutdownManager] = None


def get_shutdown_manager(shutdown_timeout: float = 30.0) -> GracefulShutdownManager:
    """獲取全局停機管理器實例"""
    global _shutdown_manager
    if _shutdown_manager is None:
        _shutdown_manager = GracefulShutdownManager(shutdown_timeout)
    return _shutdown_manager


# 便利裝飾器
def register_for_shutdown(component_class):
    """裝飾器：自動註冊組件到停機管理器"""
    original_init = component_class.__init__

    def new_init(self, *args, **kwargs):
        original_init(self, *args, **kwargs)
        if isinstance(self, ShutdownComponent):
            get_shutdown_manager().register_component(self)

    component_class.__init__ = new_init
    return component_class


# 示例組件實現
class ExampleShutdownComponent(ShutdownComponent):
    """示例停機組件"""

    def __init__(self, name: str, shutdown_delay: float = 1.0):
        self.name = name
        self.shutdown_delay = shutdown_delay
        self.is_running = True

    @property
    def component_name(self) -> str:
        return f"ExampleComponent({self.name})"

    async def shutdown(self) -> bool:
        """執行停機操作"""
        logger.info(f"開始停機組件: {self.component_name}")

        # 模擬停機操作
        await asyncio.sleep(self.shutdown_delay)

        self.is_running = False
        logger.info(f"組件停機完成: {self.component_name}")

        return True


async def main():
    """測試優雅停機管理器"""
    print("🧪 測試優雅停機管理器")

    # 創建停機管理器
    shutdown_manager = get_shutdown_manager(shutdown_timeout=10.0)

    # 創建測試組件
    component1 = ExampleShutdownComponent("Component1", 1.0)
    component2 = ExampleShutdownComponent("Component2", 2.0)
    component3 = ExampleShutdownComponent("Component3", 0.5)

    # 註冊組件
    shutdown_manager.register_component(component1)
    shutdown_manager.register_component(component2)
    shutdown_manager.register_component(component3)

    # 註冊停機回調
    shutdown_manager.register_callback(lambda: print("停機回調執行"))

    print("✅ 測試組件已註冊")
    print("按 Ctrl+C 測試優雅停機...")

    try:
        # 模擬主程序運行
        while not shutdown_manager.is_shutdown_requested():
            await asyncio.sleep(1)
            print("主程序運行中...")

        # 等待停機完成
        await shutdown_manager.wait_for_shutdown()

    except KeyboardInterrupt:
        print("收到中斷信號")

    print("✅ 優雅停機測試完成")


if __name__ == "__main__":
    asyncio.run(main())
