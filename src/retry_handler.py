#!/usr/bin/env python3
"""
重試處理器 - 實現指數退避重試機制
Retry Handler - Implements exponential backoff retry mechanism
"""

import random
import time
from datetime import datetime
from functools import wraps
from typing import Any, Callable, List, Optional, Type

from logging_config import get_logger

logger = get_logger(__name__)


class RetryConfig:
    """重試配置"""

    def __init__(
        self,
        max_attempts: int = 3,
        base_delay: float = 1.0,
        max_delay: float = 60.0,
        exponential_base: float = 2.0,
        jitter: bool = True,
        backoff_strategy: str = "exponential",
    ):
        self.max_attempts = max_attempts
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.exponential_base = exponential_base
        self.jitter = jitter
        self.backoff_strategy = backoff_strategy


class RetryHandler:
    """重試處理器"""

    def __init__(self, config: RetryConfig = None):
        self.config = config or RetryConfig()
        self.retry_stats = {
            "total_attempts": 0,
            "total_successes": 0,
            "total_failures": 0,
            "last_retry_time": None,
        }

        logger.info("RetryHandler 初始化完成")

    def calculate_delay(self, attempt: int) -> float:
        """計算重試延遲時間"""
        if self.config.backoff_strategy == "exponential":
            delay = self.config.base_delay * (self.config.exponential_base ** (attempt - 1))
        elif self.config.backoff_strategy == "linear":
            delay = self.config.base_delay * attempt
        elif self.config.backoff_strategy == "fixed":
            delay = self.config.base_delay
        else:
            delay = self.config.base_delay

        # 限制最大延遲
        delay = min(delay, self.config.max_delay)

        # 添加隨機抖動
        if self.config.jitter:
            jitter_range = delay * 0.1  # 10% 抖動
            delay += random.uniform(-jitter_range, jitter_range)

        return max(0, delay)

    def retry(
        self,
        func: Callable,
        *args,
        retry_on_exceptions: Optional[List[Type[Exception]]] = None,
        retry_config: Optional[RetryConfig] = None,
        **kwargs,
    ) -> Any:
        """執行帶重試的函數調用"""

        config = retry_config or self.config
        retry_exceptions = retry_on_exceptions or [Exception]

        last_exception = None

        for _ in range(1, config.max_attempts + 1):
            try:
                self.retry_stats["total_attempts"] += 1

                # 執行函數
                result = func(*args, **kwargs)

                # 成功執行
                self.retry_stats["total_successes"] += 1

                if attempt > 1:
                    logger.info(f"函數 {func.__name__} 在第 {attempt} 次嘗試後成功")

                return result

            except Exception as e:
                last_exception = e

                # 檢查是否應該重試此異常
                should_retry = any(isinstance(e, exc_type) for exc_type in retry_exceptions)

                if not should_retry or attempt >= config.max_attempts:
                    self.retry_stats["total_failures"] += 1
                    logger.error(f"函數 {func.__name__} 最終失敗: {e}")
                    raise e

                # 計算延遲時間
                delay = self.calculate_delay(attempt)

                logger.warning(f"函數 {func.__name__} 第 {attempt} 次嘗試失敗: {e}, " f"{delay:.2f}秒後重試")

                # 更新統計
                self.retry_stats["last_retry_time"] = datetime.now()

                # 等待後重試
                time.sleep(delay)

        # 如果到這裡，說明所有重試都失敗了
        self.retry_stats["total_failures"] += 1
        raise last_exception

    def get_stats(self) -> dict:
        """獲取重試統計信息"""
        stats = self.retry_stats.copy()
        if stats["total_attempts"] > 0:
            stats["success_rate"] = stats["total_successes"] / stats["total_attempts"]
        else:
            stats["success_rate"] = 0.0

        return stats


def retry_on_failure(
    max_attempts: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    exponential_base: float = 2.0,
    retry_on_exceptions: Optional[List[Type[Exception]]] = None,
    jitter: bool = True,
):
    """重試裝飾器"""

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            config = RetryConfig(
                max_attempts=max_attempts,
                base_delay=base_delay,
                max_delay=max_delay,
                exponential_base=exponential_base,
                jitter=jitter,
            )

            handler = RetryHandler(config)
            return handler.retry(func, *args, retry_on_exceptions=retry_on_exceptions, **kwargs)

        return wrapper

    return decorator


# 常用的重試配置
class CommonRetryConfigs:
    """常用重試配置"""

    # API 請求重試配置
    API_REQUEST = RetryConfig(
        max_attempts=5, base_delay=1.0, max_delay=30.0, exponential_base=2.0, jitter=True
    )

    # 數據庫操作重試配置
    DATABASE = RetryConfig(
        max_attempts=3, base_delay=0.5, max_delay=10.0, exponential_base=2.0, jitter=True
    )

    # 文件操作重試配置
    FILE_OPERATION = RetryConfig(
        max_attempts=3, base_delay=0.1, max_delay=5.0, exponential_base=1.5, jitter=False
    )

    # 網絡連接重試配置
    NETWORK = RetryConfig(
        max_attempts=5, base_delay=2.0, max_delay=60.0, exponential_base=2.0, jitter=True
    )


# 全局重試處理器實例
_global_retry_handler = None


def get_retry_handler() -> RetryHandler:
    """獲取全局重試處理器實例"""
    global _global_retry_handler
    if _global_retry_handler is None:
        _global_retry_handler = RetryHandler()
    return _global_retry_handler


# 便捷函數
def retry_api_call(func: Callable, *args, **kwargs) -> Any:
    """重試 API 調用"""
    handler = RetryHandler(CommonRetryConfigs.API_REQUEST)
    return handler.retry(
        func, *args, retry_on_exceptions=[ConnectionError, TimeoutError, Exception], **kwargs
    )


def retry_network_call(func: Callable, *args, **kwargs) -> Any:
    """重試網絡調用"""
    handler = RetryHandler(CommonRetryConfigs.NETWORK)
    return handler.retry(func, *args, retry_on_exceptions=[ConnectionError, TimeoutError], **kwargs)


if __name__ == "__main__":
    # 測試重試機制

    print("測試重試機制...")

    # 測試裝飾器
    @retry_on_failure(max_attempts=3, base_delay=1.0)
    def test_function(should_fail: bool = True):
        print(f"執行測試函數，should_fail={should_fail}")
        if should_fail:
            raise ConnectionError("模擬連接錯誤")
        return "成功！"

    # 測試失敗情況
    try:
        result = test_function(should_fail=True)
    except Exception as e:
        print(f"預期的失敗: {e}")

    # 測試成功情況
    try:
        result = test_function(should_fail=False)
        print(f"成功結果: {result}")
    except Exception as e:
        print(f"意外的失敗: {e}")

    # 測試重試處理器
    handler = RetryHandler(CommonRetryConfigs.API_REQUEST)

    def mock_api_call():
        import random

        if random.random() < 0.7:  # 70% 失敗率
            raise ConnectionError("API 連接失敗")
        return {"status": "success", "data": "模擬數據"}

    try:
        result = handler.retry(mock_api_call)
        print(f"API 調用成功: {result}")
    except Exception as e:
        print(f"API 調用最終失敗: {e}")

    # 顯示統計信息
    stats = handler.get_stats()
    print(f"重試統計: {stats}")

    print("重試機制測試完成！")
