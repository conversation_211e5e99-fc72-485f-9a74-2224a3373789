import json
import logging
import os
from datetime import datetime
from pathlib import Path
from typing import Dict, List

import ccxt
import numpy as np
import pandas as pd
from dotenv import load_dotenv

from logging_config import get_logger

# 配置logger
logger = logging.getLogger(__name__)

logger = get_logger(__name__)


def load_config(config_path: str = "config.json") -> Dict:
    """載入配置文件，支持 .env 環境變量覆蓋"""
    try:
        # 載入 .env 文件
        env_path = Path(".env")
        if env_path.exists():
            load_dotenv(env_path)
            logger.info("已載入 .env 環境變量")

        # 載入基礎配置
        config_file = Path(config_path)
        if not config_file.exists():
            logger.error(f"配置文件不存在: {config_path}")
            raise FileNotFoundError(f"配置文件不存在: {config_path}")

        with open(config_file, "r", encoding="utf-8") as f:
            config = json.load(f)

        # 使用環境變量覆蓋敏感配置
        config = _override_config_with_env(config)

        logger.info(f"配置文件載入成功: {config_path}")
        return config
    except Exception as e:
        logger.error(f"載入配置文件失敗: {e}")
        raise


def _override_config_with_env(config: Dict) -> Dict:
    """使用環境變量覆蓋配置"""
    try:
        # 交易所配置
        if "exchange" in config:
            exchange_config = config["exchange"]

            # API 配置（優先使用環境變量）
            exchange_config["name"] = os.getenv(
                "EXCHANGE_NAME", exchange_config.get("name", "binance")
            )
            exchange_config["sandbox"] = (
                os.getenv("EXCHANGE_SANDBOX", str(exchange_config.get("sandbox", True))).lower()
                == "true"
            )
            exchange_config["api_key"] = os.getenv(
                "EXCHANGE_API_KEY", exchange_config.get("api_key", "")
            )
            exchange_config["secret"] = os.getenv(
                "EXCHANGE_SECRET", exchange_config.get("secret", "")
            )
            exchange_config["password"] = os.getenv(
                "EXCHANGE_PASSWORD", exchange_config.get("password", "")
            )

        # 交易參數
        if os.getenv("TRADING_PAIR_BASE") and os.getenv("TRADING_PAIR_QUOTE"):
            config["trading_pair"] = [
                os.getenv("TRADING_PAIR_BASE"),
                os.getenv("TRADING_PAIR_QUOTE"),
            ]

        # 其他可配置參數
        env_mappings = {
            "TIMEFRAME": "timeframe",
            "LOOKBACK_PERIOD": "lookback_period",
            "POSITION_SIZE_USD": "position_size_usd",
            "ENTRY_THRESHOLD_HIGH": "entry_threshold_high",
            "ENTRY_THRESHOLD_LOW": "entry_threshold_low",
            "CONFIRMATION_THRESHOLD_HIGH": "confirmation_threshold_high",
            "CONFIRMATION_THRESHOLD_LOW": "confirmation_threshold_low",
            "COOLDOWN_PERIOD": "cooldown_period",
            "STOP_LOSS_PCT": "stop_loss_pct",
        }

        for env_key, config_key in env_mappings.items():
            env_value = os.getenv(env_key)
            if env_value is not None:
                # 嘗試轉換數據類型
                try:
                    if config_key in ["lookback_period", "position_size_usd", "cooldown_period"]:
                        config[config_key] = int(env_value)
                    elif config_key in [
                        "entry_threshold_high",
                        "entry_threshold_low",
                        "confirmation_threshold_high",
                        "confirmation_threshold_low",
                        "stop_loss_pct",
                    ]:
                        config[config_key] = float(env_value)
                    else:
                        config[config_key] = env_value
                except ValueError:
                    logger.warning(f"環境變量 {env_key} 的值 '{env_value}' 無法轉換，使用默認值")

        return config

    except Exception as e:
        logger.error(f"環境變量覆蓋配置失敗: {e}")
        return config


def calculate_log_spread(price_base: float, price_quote: float) -> float:
    """計算對數價差"""
    try:
        if price_base <= 0 or price_quote <= 0:
            raise ValueError("價格必須大於0")
        return np.log(price_base) - np.log(price_quote)
    except Exception as e:
        logger.error(f"計算對數價差失敗: {e}")
        raise


def calculate_zscore(spread_series: pd.Series, lookback_period: int) -> float:
    """計算 Z-score (優化版)"""
    try:
        # 計算滾動均值和標準差
        rolling_mean = spread_series.rolling(window=lookback_period).mean()
        rolling_std = spread_series.rolling(window=lookback_period).std()

        # 獲取最新的 Z-score
        current_spread = spread_series.iloc[-1]
        current_mean = rolling_mean.iloc[-1]
        current_std = rolling_std.iloc[-1]

        if pd.isna(current_mean) or pd.isna(current_std) or current_std == 0:
            return np.nan if pd.isna(current_std) else 0.0  # 如果標準差為0，Z-score為0

        z_score = (current_spread - current_mean) / current_std

        return z_score
    except Exception as e:
        logger.error(f"計算 Z-score 失敗: {e}")
        return np.nan


def get_exchange_instance(config: Dict) -> ccxt.Exchange:
    """創建交易所實例"""
    try:
        exchange_config = config["exchange"]
        exchange_class = getattr(ccxt, exchange_config["name"])

        exchange = exchange_class()
            {
                "apiKey": exchange_config.get("api_key", ""),
                "secret": exchange_config.get("secret", ""),
                "password": exchange_config.get("password", ""),
                "sandbox": exchange_config.get("sandbox", True),
                "enableRateLimit": True,
            }
        )

        logger.info(f"交易所實例創建成功: {exchange_config['name']}")
        return exchange
    except Exception as e:
        logger.error(f"創建交易所實例失敗: {e}")
        raise


def format_timestamp(timestamp: float) -> str:
    """格式化時間戳"""
    return datetime.fromtimestamp(timestamp / 1000).strftime("%Y-%m-%d %H:%M:%S")


def calculate_position_size(price: float, position_size_usd: float) -> float:
    """計算倉位大小"""
    try:
        if price <= 0:
            raise ValueError("價格必須大於0")
        return position_size_usd / price
    except Exception as e:
        logger.error(f"計算倉位大小失敗: {e}")
        raise


def validate_trading_pair(trading_pair: List[str]) -> bool:
    """驗證交易對格式"""
    if not isinstance(trading_pair, list) or len(trading_pair) != 2:
        return False

    for symbol in trading_pair:
        if not isinstance(symbol, str) or len(symbol) < 6:
            return False

    return True


def calculate_pnl()
    pass
    entry_price_base: float,
    current_price_base: float,
    entry_price_quote: float,
    current_price_quote: float,
    position_size: float,
    is_long_base: bool,
) -> float:
    """計算配對交易的盈虧"""
    try:
        if is_long_base:
            # 做多 Base，做空 Quote
            base_pnl = (current_price_base - entry_price_base) * position_size
            quote_pnl = (entry_price_quote - current_price_quote) * position_size
        else:
            # 做空 Base，做多 Quote
            base_pnl = (entry_price_base - current_price_base) * position_size
            quote_pnl = (current_price_quote - entry_price_quote) * position_size

        total_pnl = base_pnl + quote_pnl
        return total_pnl
    except Exception as e:
        logger.error(f"計算盈虧失敗: {e}")
        return 0.0


def save_trade_record(trade_data: Dict, filename: str = "trade_records.json"):
    """保存交易記錄"""
    try:
        from pathlib import Path

        # 創建記錄目錄（使用 pathlib 確保跨平台兼容）
        records_dir = Path("records")
        records_dir.mkdir(exist_ok=True)
        filepath = records_dir / filename

        # 讀取現有記錄
        if filepath.exists():
            with open(filepath, "r", encoding="utf-8") as f:
                records = json.load(f)
        else:
            records = []

        # 添加新記錄
        trade_data["timestamp"] = datetime.now().isoformat()
        records.append(trade_data)

        # 保存記錄
        with open(filepath, "w", encoding="utf-8") as f:
            json.dump(records, f, indent=2, ensure_ascii=False)

        logger.info(f"交易記錄已保存: {filepath}")
    except Exception as e:
        logger.error(f"保存交易記錄失敗: {e}")
