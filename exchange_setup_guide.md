# 交易所配置指南 - Gate.io & BitMart
# Exchange Setup Guide - Gate.io & BitMart

## 🎯 支持的交易所

系統現在支持以下交易所：

| 交易所 | CCXT ID | 沙盒支持 | 交易對數量 | 特殊要求 |
|--------|---------|----------|------------|----------|
| **Gate.io** | `gate` | ✅ | 5,511 | 無 |
| **BitMart** | `bitmart` | ✅ | 2,160 | 需要memo |
| Binance | `binance` | ✅ | 2,000+ | 無 |
| OKX | `okex` | ✅ | 500+ | 需要passphrase |

## 🔧 當前配置狀態

### 當前使用的交易所
- **主要交易所**: Gate.io
- **模式**: 沙盒模式（測試環境）
- **狀態**: 安全測試模式

## 📋 Gate.io 配置

### 1. 配置文件設置

**config.json**:
```json
{
    "exchange": {
        "name": "gate",
        "sandbox": true
    }
}
```

**環境變量 (.env)**:
```bash
# Gate.io API配置
TRADING_API_KEY=your_gate_api_key_here
TRADING_SECRET=your_gate_secret_here
TRADING_PASSPHRASE=  # Gate.io不需要
```

### 2. Gate.io API密鑰獲取

1. **登錄Gate.io**
   - 訪問 https://www.gate.io
   - 登錄您的賬戶

2. **創建API密鑰**
   - 進入 "API管理"
   - 點擊 "創建API"
   - 設置API標籤

3. **權限設置**
   ```
   ✅ 現貨交易
   ❌ 合約交易
   ❌ 提現
   ❌ 內部轉賬
   ```

### 3. Gate.io 特點
- **優勢**: 交易對豐富（5,511個）
- **手續費**: 現貨交易 0.2%
- **沙盒**: 完整的測試環境
- **API限制**: 每秒10次請求

## 📋 BitMart 配置

### 1. 配置文件設置

**config.json**:
```json
{
    "exchange": {
        "name": "bitmart",
        "sandbox": true
    }
}
```

**環境變量 (.env)**:
```bash
# BitMart API配置
TRADING_API_KEY=your_bitmart_api_key_here
TRADING_SECRET=your_bitmart_secret_here
TRADING_PASSPHRASE=your_bitmart_memo_here  # BitMart需要memo
```

### 2. BitMart API密鑰獲取

1. **登錄BitMart**
   - 訪問 https://www.bitmart.com
   - 登錄您的賬戶

2. **創建API密鑰**
   - 進入 "API管理"
   - 點擊 "創建API"
   - 設置API標籤和memo

3. **權限設置**
   ```
   ✅ 現貨交易
   ❌ 合約交易
   ❌ 提現
   ❌ 內部轉賬
   ```

### 3. BitMart 特點
- **優勢**: 良好的流動性
- **手續費**: 現貨交易 0.25%
- **沙盒**: 完整的測試環境
- **API限制**: 每秒5次請求
- **特殊要求**: 需要memo字段

## 🔄 交易所切換

### 快速切換到Gate.io

1. **更新config.json**:
   ```json
   {
       "exchange": {
           "name": "gate",
           "sandbox": true
       }
   }
   ```

2. **更新.env**:
   ```bash
   TRADING_API_KEY=your_gate_api_key
   TRADING_SECRET=your_gate_secret
   TRADING_PASSPHRASE=
   ```

3. **驗證配置**:
   ```bash
   python3 main_refactored.py validate --config config.json
   ```

### 快速切換到BitMart

1. **更新config.json**:
   ```json
   {
       "exchange": {
           "name": "bitmart",
           "sandbox": true
       }
   }
   ```

2. **更新.env**:
   ```bash
   TRADING_API_KEY=your_bitmart_api_key
   TRADING_SECRET=your_bitmart_secret
   TRADING_PASSPHRASE=your_bitmart_memo
   ```

3. **驗證配置**:
   ```bash
   python3 main_refactored.py validate --config config.json
   ```

## 🧪 測試配置

### 1. 驗證交易所連接
```bash
# 驗證配置
python3 main_refactored.py validate --config config.json --verbose

# 健康檢查
python3 main_refactored.py health --config config.json --verbose
```

### 2. 測試交易所功能
```bash
# 測試交易所支持
python3 test_exchange_support.py
```

### 3. 回測驗證
```bash
# 運行回測確保策略兼容
python3 main_refactored.py backtest --config config.json \
    --start-date 2024-01-01 --end-date 2024-12-31
```

## 📊 交易對建議

### Gate.io 推薦交易對
```json
{
    "trading_pairs": [
        ["BTC/USDT", "ETH/USDT"],
        ["ETH/USDT", "BNB/USDT"],
        ["ADA/USDT", "DOT/USDT"],
        ["MATIC/USDT", "LINK/USDT"]
    ]
}
```

### BitMart 推薦交易對
```json
{
    "trading_pairs": [
        ["BTC/USDT", "ETH/USDT"],
        ["ETH/USDT", "BNB/USDT"],
        ["ADA/USDT", "SOL/USDT"]
    ]
}
```

## ⚠️ 重要注意事項

### 安全提醒
1. **沙盒模式**: 始終先在沙盒環境測試
2. **API權限**: 只授予必要的交易權限
3. **資金安全**: 禁用提現和內部轉賬權限
4. **密鑰保護**: 不要將API密鑰提交到版本控制

### 性能考慮
1. **API限制**: 
   - Gate.io: 10 req/s
   - BitMart: 5 req/s
2. **手續費**:
   - Gate.io: 0.2%
   - BitMart: 0.25%
3. **流動性**: Gate.io交易對更多，BitMart流動性良好

### 故障排除
1. **連接失敗**: 檢查API密鑰和網絡
2. **權限錯誤**: 確認API權限設置
3. **交易對錯誤**: 確認交易對在該交易所存在
4. **memo錯誤**: BitMart必須設置memo字段

## 🚀 生產環境部署

### 1. 生產環境配置
```bash
# 更新環境
ENVIRONMENT=production

# 關閉沙盒
"sandbox": false
```

### 2. 風險管理
```json
{
    "risk_management": {
        "max_position_size": 0.05,
        "stop_loss_pct": 0.02,
        "max_daily_trades": 5
    }
}
```

### 3. 監控設置
```bash
# 啟用Telegram警報
MONITOR_TELEGRAM_BOT_TOKEN=your_bot_token
MONITOR_TELEGRAM_CHAT_ID=your_chat_id
```

## 📞 支持

如遇到問題：
1. 檢查 `python3 test_exchange_support.py`
2. 運行 `python3 main_refactored.py health`
3. 查看日誌文件 `logs/`
