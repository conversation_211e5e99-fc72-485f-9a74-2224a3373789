#!/usr/bin/env python3
"""
投資組合系統測試腳本
Portfolio System Test Script
"""

import sys
import time
import requests
from datetime import datetime
from typing import Dict

# 添加項目根目錄到路徑
sys.path.append('.')

from portfolio_manager import PortfolioManager
from health_server import HealthServer
from logging_config import setup_logging, get_logger

# 設置日誌
setup_logging()
logger = get_logger(__name__)


class PortfolioSystemTester:
    """投資組合系統測試器"""
    
    def __init__(self):
        self.portfolio_manager = None
        self.health_server = None
        
        logger.info("投資組合系統測試器初始化完成")
    
    def test_portfolio_manager(self) -> bool:
        """測試投資組合管理器"""
        try:
            print("\n=== 測試投資組合管理器 ===")
            
            # 創建投資組合管理器
            print("1. 創建投資組合管理器...")
            self.portfolio_manager = PortfolioManager()
            print("   ✓ 投資組合管理器創建成功")
            
            # 測試配置載入
            print("2. 測試配置載入...")
            portfolio_config = self.portfolio_manager.portfolio_config
            print(f"   總資金: ${portfolio_config['total_capital']:,}")
            print(f"   最大並行配對: {portfolio_config['max_concurrent_pairs']}")
            print(f"   每配對資金: ${portfolio_config['capital_per_pair']:,}")
            
            # 測試風險限制
            print("3. 測試風險限制...")
            risk_limits = self.portfolio_manager.risk_limits
            print(f"   最大投資組合回撤: {risk_limits['max_portfolio_drawdown']:.1%}")
            print(f"   緊急止損: {risk_limits['emergency_stop_loss']:.1%}")
            
            # 測試投資組合初始化（不實際執行）
            print("4. 測試投資組合選擇邏輯...")
            selected_pairs = self.portfolio_manager._select_portfolio_pairs()
            print(f"   選擇的配對數量: {len(selected_pairs)}")
            for i, pair in enumerate(selected_pairs):
                print(f"   配對 {i+1}: {pair[0]}-{pair[1]}")
            
            # 測試狀態獲取
            print("5. 測試狀態獲取...")
            status = self.portfolio_manager.get_portfolio_status()
            print(f"   投資組合統計: {status.get('portfolio_stats', {})}")
            
            return True
            
        except Exception as e:
            logger.error(f"投資組合管理器測試失敗: {e}")
            return False
    
    def test_health_server(self) -> bool:
        """測試健康檢查服務器"""
        try:
            print("\n=== 測試健康檢查服務器 ===")
            
            # 啟動服務器
            print("1. 啟動健康檢查服務器...")
            self.health_server = HealthServer(port=8081)
            self.health_server.start()
            time.sleep(2)  # 等待服務器啟動
            print("   ✓ 健康檢查服務器已啟動")
            
            # 測試健康檢查端點
            print("2. 測試健康檢查端點...")
            try:
                response = requests.get('http://localhost:8081/health', timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    print(f"   ✓ 健康檢查響應: {data['status']}")
                else:
                    print(f"   ✗ 健康檢查失敗: {response.status_code}")
                    return False
            except requests.exceptions.RequestException as e:
                print(f"   ✗ 健康檢查請求失敗: {e}")
                return False
            
            # 測試狀態端點
            print("3. 測試狀態端點...")
            try:
                response = requests.get('http://localhost:8081/status', timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    print("   ✓ 狀態端點響應正常")
                    print(f"   服務信息: {data.get('service_info', {})}")
                else:
                    print(f"   ✗ 狀態端點失敗: {response.status_code}")
            except requests.exceptions.RequestException as e:
                print(f"   ✗ 狀態端點請求失敗: {e}")
            
            # 測試投資組合端點
            print("4. 測試投資組合端點...")
            try:
                response = requests.get('http://localhost:8081/portfolio', timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    print("   ✓ 投資組合端點響應正常")
                    portfolio_stats = data.get('portfolio', {}).get('portfolio_stats', {})
                    print(f"   投資組合統計: {portfolio_stats}")
                else:
                    print(f"   ✗ 投資組合端點失敗: {response.status_code}")
            except requests.exceptions.RequestException as e:
                print(f"   ✗ 投資組合端點請求失敗: {e}")
            
            # 測試儀表板端點
            print("5. 測試儀表板端點...")
            try:
                response = requests.get('http://localhost:8081/dashboard', timeout=5)
                if response.status_code == 200:
                    print("   ✓ 儀表板端點響應正常")
                    print(f"   響應長度: {len(response.text)} 字符")
                    print("   🌐 儀表板地址: http://localhost:8081/dashboard")
                else:
                    print(f"   ✗ 儀表板端點失敗: {response.status_code}")
            except requests.exceptions.RequestException as e:
                print(f"   ✗ 儀表板端點請求失敗: {e}")
            
            return True
            
        except Exception as e:
            logger.error(f"健康檢查服務器測試失敗: {e}")
            return False
    
    def test_api_endpoints(self) -> bool:
        """測試API端點"""
        try:
            print("\n=== 測試API端點 ===")
            
            base_url = 'http://localhost:8081'
            endpoints = [
                '/health',
                '/status', 
                '/metrics',
                '/portfolio',
                '/dashboard'
            ]
            
            for endpoint in endpoints:
                try:
                    response = requests.get(f"{base_url}{endpoint}", timeout=5)
                    status = "✓" if response.status_code == 200 else "✗"
                    print(f"   {status} {endpoint}: {response.status_code}")
                except requests.exceptions.RequestException as e:
                    print(f"   ✗ {endpoint}: 請求失敗 - {e}")
            
            return True
            
        except Exception as e:
            logger.error(f"API端點測試失敗: {e}")
            return False
    
    def test_control_endpoints(self) -> bool:
        """測試控制端點"""
        try:
            print("\n=== 測試控制端點 ===")
            
            # 測試配置重新載入
            print("1. 測試配置重新載入...")
            try:
                response = requests.post(
                    'http://localhost:8081/control',
                    json={'command': 'reload_config'},
                    timeout=5
                )
                if response.status_code == 200:
                    data = response.json()
                    print(f"   ✓ 配置重新載入: {data['message']}")
                else:
                    print(f"   ✗ 配置重新載入失敗: {response.status_code}")
            except requests.exceptions.RequestException as e:
                print(f"   ✗ 配置重新載入請求失敗: {e}")
            
            # 測試無效命令
            print("2. 測試無效命令...")
            try:
                response = requests.post(
                    'http://localhost:8081/control',
                    json={'command': 'invalid_command'},
                    timeout=5
                )
                if response.status_code == 400:
                    print("   ✓ 無效命令正確拒絕")
                else:
                    print(f"   ✗ 無效命令處理異常: {response.status_code}")
            except requests.exceptions.RequestException as e:
                print(f"   ✗ 無效命令請求失敗: {e}")
            
            return True
            
        except Exception as e:
            logger.error(f"控制端點測試失敗: {e}")
            return False
    
    def run_comprehensive_test(self) -> Dict:
        """運行綜合測試"""
        print("🚀 開始投資組合系統綜合測試...")
        print("=" * 60)
        
        test_results = {}
        
        # 執行各項測試
        tests = [
            ("投資組合管理器", self.test_portfolio_manager),
            ("健康檢查服務器", self.test_health_server),
            ("API端點", self.test_api_endpoints),
            ("控制端點", self.test_control_endpoints)
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            try:
                print(f"\n🧪 測試 {test_name}...")
                result = test_func()
                test_results[test_name] = result
                
                if result:
                    print(f"✅ {test_name} 測試通過")
                    passed_tests += 1
                else:
                    print(f"❌ {test_name} 測試失敗")
                    
            except Exception as e:
                print(f"❌ {test_name} 測試異常: {e}")
                test_results[test_name] = False
        
        # 生成測試報告
        print("\n" + "=" * 60)
        print("📊 測試結果總結")
        print("=" * 60)
        
        success_rate = (passed_tests / total_tests) * 100
        print(f"通過測試: {passed_tests}/{total_tests}")
        print(f"成功率: {success_rate:.1f}%")
        
        print("\n詳細結果:")
        for test_name, result in test_results.items():
            status = "✅ 通過" if result else "❌ 失敗"
            print(f"  {test_name}: {status}")
        
        if success_rate >= 75:
            print("\n🎉 投資組合系統測試整體通過！")
            print("系統已準備好進行投資組合交易。")
            
            if self.health_server:
                print("\n🌐 Web儀表板地址:")
                print("   http://localhost:8081/dashboard")
                print("\n📡 API端點:")
                print("   健康檢查: http://localhost:8081/health")
                print("   投資組合狀態: http://localhost:8081/portfolio")
                print("   詳細狀態: http://localhost:8081/status")
        else:
            print("\n⚠️ 投資組合系統測試存在問題")
            print("請檢查失敗的測試項目並修復問題。")
        
        return test_results
    
    def cleanup(self):
        """清理測試資源"""
        try:
            if self.health_server:
                self.health_server.stop()
                print("健康檢查服務器已停止")
                
            if self.portfolio_manager:
                self.portfolio_manager.emergency_stop = True
                print("投資組合管理器已停止")
                
        except Exception as e:
            logger.error(f"清理測試資源失敗: {e}")


def main():
    """主函數"""
    tester = None
    try:
        tester = PortfolioSystemTester()
        results = tester.run_comprehensive_test()
        
        # 保存測試結果
        import json
        from pathlib import Path
        
        results_dir = Path("test_results")
        results_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = results_dir / f"portfolio_system_test_{timestamp}.json"
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'results': results,
                'summary': {
                    'total_tests': len(results),
                    'passed_tests': sum(results.values()),
                    'success_rate': sum(results.values()) / len(results) * 100
                }
            }, f, indent=2, ensure_ascii=False)
        
        print(f"\n📁 測試結果已保存: {results_file}")
        
        # 如果測試通過，保持服務器運行一段時間供查看
        if sum(results.values()) / len(results) >= 0.75:
            print("\n⏰ 服務器將保持運行30秒供您查看儀表板...")
            print("   按 Ctrl+C 提前退出")
            try:
                time.sleep(30)
            except KeyboardInterrupt:
                print("\n用戶中斷測試")
        
    except Exception as e:
        logger.error(f"測試執行失敗: {e}")
        return 1
    finally:
        if tester:
            tester.cleanup()
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
