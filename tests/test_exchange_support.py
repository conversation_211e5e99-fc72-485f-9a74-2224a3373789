#!/usr/bin/env python3
"""
測試交易所支持 - 驗證Gate.io和BitMart的CCXT支持
Test Exchange Support - Verify CCXT support for Gate.io and BitMart
"""

import sys
from typing import Dict, List

import ccxt

from logging_config import get_logger, setup_logging

setup_logging()
logger = get_logger(__name__)


def test_exchange_support():
    """測試交易所支持"""
    print("🔍 檢查CCXT支持的交易所...")

    # 檢查所有支持的交易所
    all_exchanges = ccxt.exchanges
    print(f"CCXT總共支持 {len(all_exchanges)} 個交易所")

    # 我們關心的交易所
    target_exchanges = ["gate", "bitmart", "gateio"]

    print("\n🎯 檢查目標交易所支持:")
    supported_exchanges = []

    for exchange_id in target_exchanges:
        if exchange_id in all_exchanges:
            print(f"  ✅ {exchange_id} - 支持")
            supported_exchanges.append(exchange_id)
        else:
            print(f"  ❌ {exchange_id} - 不支持")

    # 查找Gate相關的交易所
    gate_related = [ex for ex in all_exchanges if "gate" in ex.lower()]
    print(f"\n🔍 Gate相關的交易所: {gate_related}")

    # 查找BitMart相關的交易所
    bitmart_related = [ex for ex in all_exchanges if "bitmart" in ex.lower()]
    print(f"🔍 BitMart相關的交易所: {bitmart_related}")

    # 驗證支持的交易所
    assert len(supported_exchanges) > 0, "應該至少支持一個交易所"
    assert "gate" in supported_exchanges or "gateio" in supported_exchanges, "應該支持Gate.io"
    print(f"✅ 交易所支持驗證通過: {supported_exchanges}")


def test_exchange_initialization():
    """測試交易所初始化"""
    print("\n🧪 測試交易所初始化...")

    # 測試Gate.io
    try:
        if hasattr(ccxt, "gate"):
            gate_exchange = ccxt.gate(
                {
                    "sandbox": True,
                    "enableRateLimit": True,
                }
            )
            print("  ✅ Gate.io 初始化成功")
            print(f"     ID: {gate_exchange.id}")
            print(f"     名稱: {gate_exchange.name}")
            print(f"     沙盒支持: {gate_exchange.sandbox}")
        else:
            print("  ❌ Gate.io 不支持")
    except Exception as e:
        print(f"  ❌ Gate.io 初始化失敗: {e}")

    # 測試BitMart
    try:
        if hasattr(ccxt, "bitmart"):
            bitmart_exchange = ccxt.bitmart(
                {
                    "sandbox": True,
                    "enableRateLimit": True,
                }
            )
            print("  ✅ BitMart 初始化成功")
            print(f"     ID: {bitmart_exchange.id}")
            print(f"     名稱: {bitmart_exchange.name}")
            print(f"     沙盒支持: {bitmart_exchange.sandbox}")
        else:
            print("  ❌ BitMart 不支持")
    except Exception as e:
        print(f"  ❌ BitMart 初始化失敗: {e}")


def test_exchange_features():
    """測試交易所功能"""
    print("\n🔧 測試交易所功能...")

    exchanges_to_test = []

    # 添加Gate.io
    if hasattr(ccxt, "gate"):
        exchanges_to_test.append(("gate", ccxt.gate))

    # 添加BitMart
    if hasattr(ccxt, "bitmart"):
        exchanges_to_test.append(("bitmart", ccxt.bitmart))

    for exchange_name, exchange_class in exchanges_to_test:
        try:
            exchange = exchange_class(
                {
                    "sandbox": True,
                    "enableRateLimit": True,
                }
            )

            print(f"\n📊 {exchange_name.upper()} 功能檢查:")
            print(f"  現貨交易: {'✅' if exchange.has['spot'] else '❌'}")
            print(f"  獲取市場: {'✅' if exchange.has['fetchMarkets'] else '❌'}")
            print(f"  獲取價格: {'✅' if exchange.has['fetchTicker'] else '❌'}")
            print(f"  獲取K線: {'✅' if exchange.has['fetchOHLCV'] else '❌'}")
            print(f"  創建訂單: {'✅' if exchange.has['createOrder'] else '❌'}")
            print(f"  市價單: {'✅' if exchange.has['createMarketOrder'] else '❌'}")
            print(f"  限價單: {'✅' if exchange.has['createLimitOrder'] else '❌'}")
            print(f"  獲取餘額: {'✅' if exchange.has['fetchBalance'] else '❌'}")

            # 測試獲取市場（不需要API密鑰）
            try:
                markets = exchange.load_markets()
                print(f"  市場數量: {len(markets)}")

                # 檢查常見交易對
                common_pairs = ["BTC/USDT", "ETH/USDT", "BNB/USDT"]
                available_pairs = []
                for pair in common_pairs:
                    if pair in markets:
                        available_pairs.append(pair)

                print(f"  常見交易對: {available_pairs}")

            except Exception as e:
                print(f"  ⚠️ 獲取市場失敗: {e}")

        except Exception as e:
            print(f"  ❌ {exchange_name} 功能檢查失敗: {e}")


def generate_exchange_config():
    """生成交易所配置建議"""
    print("\n📝 生成配置建議...")

    # Gate.io配置
    if hasattr(ccxt, "gate"):
        print("\n🔧 Gate.io 配置:")
        print("config.json:")
        print(f'  "exchange": {{"name": "gate", "sandbox": true}}')
        print(".env:")
        print("  TRADING_API_KEY=your_gate_api_key")
        print("  TRADING_SECRET=your_gate_secret")

    # BitMart配置
    if hasattr(ccxt, "bitmart"):
        print("\n🔧 BitMart 配置:")
        print("config.json:")
        print(f'  "exchange": {{"name": "bitmart", "sandbox": true}}')
        print(".env:")
        print("  TRADING_API_KEY=your_bitmart_api_key")
        print("  TRADING_SECRET=your_bitmart_secret")
        print("  TRADING_PASSPHRASE=your_bitmart_memo  # BitMart需要memo")


def main():
    """主函數"""
    print("=" * 60)
    print("交易所支持測試 - Gate.io & BitMart")
    print("=" * 60)

    try:
        # 檢查支持
        supported = test_exchange_support()

        # 測試初始化
        test_exchange_initialization()

        # 測試功能
        test_exchange_features()

        # 生成配置建議
        generate_exchange_config()

        print("\n" + "=" * 60)
        print("測試完成")
        print("=" * 60)

        if supported:
            print(f"✅ 支持的交易所: {', '.join(supported)}")
        else:
            print("❌ 沒有找到支持的目標交易所")

    except Exception as e:
        logger.error(f"測試失敗: {e}")
        return 1

    return 0


if __name__ == "__main__":
    sys.exit(main())
