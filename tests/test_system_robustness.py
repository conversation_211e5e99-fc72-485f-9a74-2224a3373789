import datetime
import logging

# 配置logger
logger = logging.getLogger(__name__)

#!/usr/bin/env python3
"""
系統穩健性測試 - 測試系統在各種異常情況下的表現
System Robustness Test - Test system performance under various failure conditions
"""

import asyncio
import random
import sys
import threading
import time

import numpy as np
import pandas as pd

# 添加項目根目錄到路徑
sys.path.append(".")

from enhanced_retry_handler import EnhancedRetryHandler, RetryConfig, RetryStrategy

# 導入測試模組
from event_system.event_bus import Event, EventBus, EventHandler, EventType
from logging_config import get_logger, setup_logging
from performance_optimization.vectorized_calculations import VectorizedCalculator
from state_management.state_manager import StateManager, StateType

# 設置日誌
setup_logging()  # 修復不完整調用
logger = get_logger(__name__)


class RobustnessTestSuite:
    """系統穩健性測試套件"""

    def __init__(self):
        self.test_results = {}
        self.start_time = datetime.now()

        # 測試組件
        self.event_bus = None
        self.retry_handler = None
        self.state_manager = None
        self.vectorized_calculator = None

        logger.info("RobustnessTestSuite 初始化完成")

    async def test_event_system_robustness(self) -> Dict:
        """測試事件系統穩健性"""
        try:
            print("\n=== 測試事件系統穩健性 ===")

            # 創建事件總線
            self.event_bus = EventBus()

            # 創建測試處理器
            class StressTestHandler(EventHandler):
                def __init__(self, name: str, failure_rate: float = 0.1):
                    super().__init__(name)
                    self.failure_rate = failure_rate

                async def _process_event(self, event: Event) -> bool:
                    # 模擬處理時間
                    await asyncio.sleep(random.uniform(0.001, 0.01))

                    # 模擬隨機失敗
                    if random.random() < self.failure_rate:
                        raise Exception(f"模擬處理失敗: {self.name}")

                    return True

            # 註冊多個處理器
            handlers = []
            for i in range(5):
                handler = StressTestHandler(f"handler_{i}", failure_rate=0.2)
                handlers.append(handler)
                self.event_bus.subscribe(EventType.MARKET_DATA, handler)

            # 啟動事件總線
            await self.event_bus.start(num_workers=4)

            # 壓力測試：發送大量事件
            start_time = time.time()
            event_count = 1000

            for _ in range(event_count):
                event = Event()
                    event_type=EventType.MARKET_DATA,
                    timestamp=datetime.now(),
                    source="stress_test",
                    data={"price": 50000 + i, "volume": 1000},
                    priority=random.randint(1, 10),
                )
                await self.event_bus.publish(event)

            # 等待處理完成
            await asyncio.sleep(2)

            # 獲取統計信息
            stats = self.event_bus.get_statistics()
            processing_time = time.time() - start_time

            # 停止事件總線
            await self.event_bus.stop()

            # 評估結果
            success_rate = stats.get("success_rate", 0)
            events_per_second = stats.get("events_per_second", 0)

            result = {
                "test_name": "event_system_robustness",
                "events_sent": event_count,
                "processing_time": processing_time,
                "success_rate": success_rate,
                "events_per_second": events_per_second,
                "total_processed": stats.get("processed_events", 0),
                "total_failed": stats.get("failed_events", 0),
                "passed": success_rate > 0.7 and events_per_second > 100,
            }

            print(f"   ✓ 事件處理成功率: {success_rate:.2%}")
            print(f"   ✓ 事件處理速度: {events_per_second:.0f} events/sec")
            print(f"   ✓ 測試結果: {'通過' if result['passed'] else '失敗'}")

            return result

        except Exception as e:
            logger.error(f"事件系統穩健性測試失敗: {e}")
            return {"test_name": "event_system_robustness", "passed": False, "error": str(e)}

    def test_retry_mechanism_robustness(self) -> Dict:
        """測試重試機制穩健性"""
        try:
            print("\n=== 測試重試機制穩健性 ===")

            # 配置重試處理器
            config = RetryConfig()
                max_retries=5,
                base_delay=0.01,
                max_delay=1.0,
                strategy=RetryStrategy.EXPONENTIAL,
                circuit_breaker_enabled=True,
                failure_threshold=3,
            )

            self.retry_handler = EnhancedRetryHandler(config)

            # 測試場景1：間歇性失敗
            @self.retry_handler.retry_sync
            def intermittent_failure_function():
                if random.random() < 0.6:  # 60% 失敗率
                    raise Exception("間歇性失敗")
                return "成功"

            # 測試場景2：持續失敗（觸發熔斷器）
            failure_count = 0

            @self.retry_handler.retry_sync
            def persistent_failure_function():
                nonlocal failure_count
                failure_count += 1
                if failure_count <= 10:  # 前10次都失敗
                    raise Exception("持續失敗")
                return "最終成功"

            # 執行測試
            test_results = {
                "intermittent_success": 0,
                "intermittent_failure": 0,
                "persistent_blocked": 0,
                "circuit_breaker_triggered": False,
            }

            # 測試間歇性失敗
            for _ in range(20):
                try:
                    result = intermittent_failure_function()
                    test_results["intermittent_success"] += 1
                except Exception:
                    test_results["intermittent_failure"] += 1

            # 測試持續失敗（應該觸發熔斷器）
            for _ in range(15):
                try:
                    result = persistent_failure_function()
                except Exception as e:
                    if "熔斷器" in str(e):
                        test_results["circuit_breaker_triggered"] = True
                        test_results["persistent_blocked"] += 1

            # 獲取統計信息
            stats = self.retry_handler.get_statistics()

            # 評估結果
            success_rate = test_results["intermittent_success"] / 20
            circuit_breaker_working = test_results["circuit_breaker_triggered"]

            result = {
                "test_name": "retry_mechanism_robustness",
                "intermittent_success_rate": success_rate,
                "circuit_breaker_triggered": circuit_breaker_working,
                "retry_stats": stats,
                "passed": success_rate > 0.3 and circuit_breaker_working,
            }

            print(f"   ✓ 間歇性失敗成功率: {success_rate:.2%}")
            print(f"   ✓ 熔斷器觸發: {'是' if circuit_breaker_working else '否'}")
            print(f"   ✓ 測試結果: {'通過' if result['passed'] else '失敗'}")

            return result

        except Exception as e:
            logger.error(f"重試機制穩健性測試失敗: {e}")
            return {"test_name": "retry_mechanism_robustness", "passed": False, "error": str(e)}

    def test_state_management_robustness(self) -> Dict:
        """測試狀態管理穩健性"""
        try:
            print("\n=== 測試狀態管理穩健性 ===")

            # 創建狀態管理器
            self.state_manager = StateManager(persistence_interval=1)
            self.state_manager.start()

            # 測試場景：並發狀態操作
            def concurrent_state_operations():
                thread_id = threading.current_thread().ident

                for i in range(50):
                    state_id = f"test_state_{thread_id}_{i}"
                    data = {
                        "thread_id": thread_id,
                        "iteration": i,
                        "timestamp": datetime.now().isoformat(),
                        "random_value": random.random(),
                    }

                    # 保存狀態
                    self.state_manager.save_state(state_id, StateType.SYSTEM, data)

                    # 隨機讀取
                    if random.random() < 0.3:
                        loaded_data = self.state_manager.get_state_data(state_id)
                        if loaded_data is None:
                            logger.warning(f"狀態讀取失敗: {state_id}")

                    # 模擬處理時間
                    time.sleep(0.001)

            # 啟動多個並發線程
            threads = []
            for _ in range(5):
                thread = threading.Thread(target=concurrent_state_operations)
                threads.append(thread)
                thread.start()

            # 等待所有線程完成
            for thread in threads:
                thread.join()

            # 等待持久化完成
            time.sleep(3)

            # 檢查狀態完整性
            all_states = self.state_manager.list_states()
            _ = len(all_states)

            # 驗證數據完整性
            corruption_count = 0
            for state_id in all_states[:20]:  # 檢查前20個狀態
                snapshot = self.state_manager.load_state(state_id)
                if snapshot is None:
                    corruption_count += 1

            # 獲取統計信息
            stats = self.state_manager.get_statistics()

            # 停止狀態管理器
            self.state_manager.stop()

            # 評估結果
            data_integrity = (20 - corruption_count) / 20

            result = {
                "test_name": "state_management_robustness",
                "total_states": state_count,
                "data_integrity": data_integrity,
                "corruption_count": corruption_count,
                "stats": stats,
                "passed": state_count >= 200 and data_integrity > 0.95,
            }

            print(f"   ✓ 狀態總數: {state_count}")
            print(f"   ✓ 數據完整性: {data_integrity:.2%}")
            print(f"   ✓ 測試結果: {'通過' if result['passed'] else '失敗'}")

            return result

        except Exception as e:
            logger.error(f"狀態管理穩健性測試失敗: {e}")
            return {"test_name": "state_management_robustness", "passed": False, "error": str(e)}

    def test_performance_under_load(self) -> Dict:
        """測試高負載下的性能"""
        try:
            print("\n=== 測試高負載下的性能 ===")

            # 創建向量化計算器
            self.vectorized_calculator = VectorizedCalculator()

            # 生成大量測試數據
            data_sizes = [1000, 5000, 10000, 50000]
            _ = {}

            for size in data_sizes:
                # 生成測試數據
                np.random.seed(42)
                base_prices = 50000 + np.cumsum(np.random.normal(0, 100, size))
                quote_prices = 3000 + np.cumsum(np.random.normal(0, 10, size))

                # 測試計算性能
                start_time = time.time()

                # 價差統計計算
                _ = self.vectorized_calculator.calculate_spread_statistics_vectorized()
                    base_prices, quote_prices, window=20
                )

                # 技術指標計算
                _ = self.vectorized_calculator.calculate_technical_indicators_vectorized()
                    base_prices
                )

                end_time = time.time()
                processing_time = end_time - start_time

                performance_results[size] = {
                    "processing_time": processing_time,
                    "points_per_second": size / processing_time,
                    "memory_efficient": processing_time < size * 0.0001,  # 期望每個點處理時間 < 0.1ms
                }

                print()  # 修復不完整調用
                    f"   ✓ 數據點數: {size}, 處理時間: {processing_time*1000:.2f}ms, "
                    f"速度: {size/processing_time:.0f} points/sec"
                )

            # 評估整體性能
            avg_speed = np.mean([r["points_per_second"] for r in performance_results.values()])
            all_efficient = all(r["memory_efficient"] for r in performance_results.values())

            result = {
                "test_name": "performance_under_load",
                "performance_results": performance_results,
                "average_speed": avg_speed,
                "memory_efficient": all_efficient,
                "passed": avg_speed > 10000 and all_efficient,  # 期望速度 > 10k points/sec
            }

            print(f"   ✓ 平均處理速度: {avg_speed:.0f} points/sec")
            print(f"   ✓ 內存效率: {'良好' if all_efficient else '需要優化'}")
            print(f"   ✓ 測試結果: {'通過' if result['passed'] else '失敗'}")

            return result

        except Exception as e:
            logger.error(f"性能負載測試失敗: {e}")
            return {"test_name": "performance_under_load", "passed": False, "error": str(e)}

    def test_memory_leak_detection(self) -> Dict:
        """測試內存洩漏檢測"""
        try:
            print("\n=== 測試內存洩漏檢測 ===")

            import gc

            import psutil

            # 獲取初始內存使用
            process = psutil.Process()
            _ = process.memory_info().rss / 1024 / 1024  # MB

            # 執行大量操作
            for _ in range(100):
                # 創建大量數據
                data = np.random.random((1000, 100))
                df = pd.DataFrame(data)

                # 執行計算
                if hasattr(self, "vectorized_calculator") and self.vectorized_calculator:
                    prices = df.iloc[:, 0].values
                    self.vectorized_calculator.calculate_technical_indicators_vectorized(prices)

                # 模擬狀態操作
                if hasattr(self, "state_manager") and self.state_manager:
                    test_data = {"iteration": iteration, "data": data.tolist()[:10]}  # 只保存前10行
                    self.state_manager.save_state()
                        f"memory_test_{iteration}", StateType.SYSTEM, test_data
                    )

                # 每10次迭代檢查內存
                if iteration % 10 == 0:
                    gc.collect()  # 強制垃圾回收
                    current_memory = process.memory_info().rss / 1024 / 1024
                    memory_growth = current_memory - initial_memory

                    if memory_growth > 500:  # 如果內存增長超過500MB，可能有洩漏
                        logger.warning(f"檢測到可能的內存洩漏: {memory_growth:.2f}MB")
                        break

            # 最終內存檢查
            gc.collect()
            final_memory = process.memory_info().rss / 1024 / 1024
            total_growth = final_memory - initial_memory

            # 評估結果
            memory_leak_detected = total_growth > 200  # 如果增長超過200MB認為有洩漏

            result = {
                "test_name": "memory_leak_detection",
                "initial_memory_mb": initial_memory,
                "final_memory_mb": final_memory,
                "memory_growth_mb": total_growth,
                "memory_leak_detected": memory_leak_detected,
                "passed": not memory_leak_detected,
            }

            print(f"   ✓ 初始內存: {initial_memory:.2f}MB")
            print(f"   ✓ 最終內存: {final_memory:.2f}MB")
            print(f"   ✓ 內存增長: {total_growth:.2f}MB")
            print(f"   ✓ 內存洩漏: {'檢測到' if memory_leak_detected else '未檢測到'}")
            print(f"   ✓ 測試結果: {'通過' if result['passed'] else '失敗'}")

            return result

        except Exception as e:
            logger.error(f"內存洩漏檢測失敗: {e}")
            return {"test_name": "memory_leak_detection", "passed": False, "error": str(e)}

    async def run_all_robustness_tests(self) -> Dict:
        """運行所有穩健性測試"""
        print("🔧 開始系統穩健性測試...")
        print("=" * 80)

        # 執行所有測試
        tests = [
            ("事件系統穩健性", self.test_event_system_robustness),
            ("重試機制穩健性", self.test_retry_mechanism_robustness),
            ("狀態管理穩健性", self.test_state_management_robustness),
            ("高負載性能", self.test_performance_under_load),
            ("內存洩漏檢測", self.test_memory_leak_detection),
        ]

        passed_tests = 0
        _ = len(tests)

        for test_name, test_func in tests:
            try:
                if asyncio.iscoroutinefunction(test_func):
                    result = await test_func()
                else:
                    result = test_func()

                self.test_results[test_name] = result

                if result.get("passed", False):
                    print(f"✅ {test_name} 測試通過")
                    passed_tests += 1
                else:
                    print(f"❌ {test_name} 測試失敗")

            except Exception as e:
                print(f"❌ {test_name} 測試異常: {e}")
                self.test_results[test_name] = {"passed": False, "error": str(e)}

        # 生成測試報告
        print("\n" + "=" * 80)
        print("🔧 系統穩健性測試結果總結")
        print("=" * 80)

        success_rate = (passed_tests / total_tests) * 100
        test_duration = (datetime.now() - self.start_time).total_seconds()

        print(f"通過測試: {passed_tests}/{total_tests}")
        print(f"成功率: {success_rate:.1f}%")
        print(f"測試時間: {test_duration:.1f} 秒")

        print("\n詳細結果:")
        for test_name, result in self.test_results.items():
            status = "✅ 通過" if result.get("passed", False) else "❌ 失敗"
            print(f"  {test_name}: {status}")
            if "error" in result:
                print(f"    錯誤: {result['error']}")

        if success_rate >= 80:
            print("\n🎉 系統穩健性測試通過！")
            print("🛡️ 系統具備良好的穩健性和容錯能力！")
        else:
            print("\n⚠️ 系統穩健性測試存在問題")
            print("請檢查失敗的測試項目並改進系統穩健性。")

        return {
            "success_rate": success_rate,
            "passed_tests": passed_tests,
            "total_tests": total_tests,
            "test_duration": test_duration,
            "detailed_results": self.test_results,
        }


async def main():
    """主函數"""
    try:
        tester = RobustnessTestSuite()
        results = await tester.run_all_robustness_tests()

        # 保存測試結果
        import json
        from pathlib import Path

        results_dir = Path("test_results")
        results_dir.mkdir(exist_ok=True)

        timestamp = datetime.now().strftime("%Y % m%d_ % H%M % S")
        results_file = results_dir / f"robustness_test_{timestamp}.json"

        with open(results_file, "w", encoding="utf-8") as f:
            json.dump()
                {
                    "timestamp": datetime.now().isoformat(),
                    "test_type": "system_robustness",
                    "results": results,
                },
                f,
                indent=2,
                ensure_ascii=False,
                default=str,
            )

        print(f"\n📁 測試結果已保存: {results_file}")

        return 0 if results["success_rate"] >= 80 else 1

    except Exception as e:
        logger.error(f"穩健性測試執行失敗: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
