import datetime

#!/usr/bin/env python3
"""
集成交易執行器測試 - 全面測試風險檢查和交易執行邏輯
Comprehensive tests for IntegratedTradingExecutor
"""

import asyncio
from unittest.mock import AsyncMock, MagicMock, Mock, patch

import pytest

from integrated_trading_executor import *

    IntegratedTradingExecutor,
    RiskCheckReport,
    RiskCheckResult,
    TradingSignal,
    get_integrated_trading_executor,
)


class TestTradingSignal:
    """測試交易信號類"""

    def test_trading_signal_creation(self):
        """測試交易信號創建"""
        signal = TradingSignal(strategy_id="test_strategy", symbol="BTC/USDT:USDT", side="buy", amount=0.001)

        assert signal.strategy_id == "test_strategy"
        assert signal.symbol == "BTC/USDT:USDT"
        assert signal.side == "buy"
        assert signal.amount == 0.001
        assert signal.order_type == "market"
        assert signal.timestamp is not None

    def test_trading_signal_with_optional_params(self):
        """測試帶可選參數的交易信號"""
        timestamp = datetime.now()
        signal = TradingSignal(strategy_id="test_strategy",)
            symbol="ETH/USDT:USDT",
            side="sell",
            amount=0.1,
            price=3000.0,
            order_type="limit",
            leverage=5.0,
            timestamp=timestamp,)

        assert signal.price == 3000.0
        assert signal.order_type == "limit"
        assert signal.leverage == 5.0
        assert signal.timestamp == timestamp


class TestRiskCheckReport:
    """測試風險檢查報告類"""

    def test_risk_check_report_creation(self):
        """測試風險檢查報告創建"""
        report = RiskCheckReport(result=RiskCheckResult.PASSED,)
            checks_passed=["position_check", "leverage_check"],
            warnings=[],
            blocks=[],
            recommendations=[],)

        assert report.result == RiskCheckResult.PASSED
        assert len(report.checks_passed) == 2
        assert report.timestamp is not None

    def test_risk_check_report_with_warnings(self):
        """測試帶警告的風險檢查報告"""
        report = RiskCheckReport(result=RiskCheckResult.WARNING,)
            checks_passed=["position_check"],
            warnings=["高波動市場"],
            blocks=[],
            recommendations=["考慮降低倉位"],)

        assert report.result == RiskCheckResult.WARNING
        assert "高波動市場" in report.warnings
        assert "考慮降低倉位" in report.recommendations


class TestIntegratedTradingExecutor:
    """測試集成交易執行器"""

    @pytest.fixture
    def mock_dependencies(self):
        """模擬依賴項"""
        with patch("integrated_trading_executor.TradingSafetyGuard") as mock_safety, patch("integrated_trading_executor.HighLeverageRiskChecker") as mock_leverage, patch("integrated_trading_executor.get_client_manager") as mock_client, patch("integrated_trading_executor.get_global_event_bus") as mock_event_bus:
            # 設置模擬對象
            mock_safety_instance = Mock()
            mock_leverage_instance = Mock()
            mock_client_instance = Mock()
            mock_event_bus_instance = Mock()

            mock_safety.return_value = mock_safety_instance
            mock_leverage.return_value = mock_leverage_instance
            mock_client.return_value = mock_client_instance
            mock_event_bus.return_value = mock_event_bus_instance

            # 設置異步方法
            mock_event_bus_instance.publish = AsyncMock()

            yield {
                "safety_guard": mock_safety_instance,
                "leverage_checker": mock_leverage_instance,
                "client_manager": mock_client_instance,
                "event_bus": mock_event_bus_instance,
            }

    def test_executor_initialization(self, mock_dependencies):
        """測試執行器初始化"""
        config = {"max_daily_trades": 50, "max_position_size": 0.05, "daily_loss_limit": 0.01}

        executor = IntegratedTradingExecutor(config)

        assert executor.config == config
        assert executor.max_daily_trades == 50
        assert executor.max_position_size == 0.05
        assert executor.daily_loss_limit == 0.01
        assert executor.component_name == "IntegratedTradingExecutor"

        # 檢查統計初始化
        stats = executor.execution_stats
        assert stats["total_signals"] == 0
        assert stats["executed_trades"] == 0
        assert stats["blocked_trades"] == 0
        assert stats["warning_trades"] == 0
        assert isinstance(stats["risk_blocks"], dict)

    def test_executor_default_config(self, mock_dependencies):
        """測試執行器默認配置"""
        executor = IntegratedTradingExecutor()

        assert executor.max_daily_trades == 100
        assert executor.max_position_size == 0.1
        assert executor.daily_loss_limit == 0.02

    @pytest.mark.asyncio
    async def test_execute_signal_success(self, mock_dependencies):
        """測試成功執行交易信號"""
        executor = IntegratedTradingExecutor()

        # 模擬風險檢查通過
        with patch.object(executor, "_comprehensive_risk_check") as mock_risk_check, patch.object(executor, "_record_risk_check") as mock_record, patch.object(executor, "_execute_trade") as mock_execute:
            # 設置模擬返回值
            risk_report = RiskCheckReport(result=RiskCheckResult.PASSED,)
                checks_passed=["all_checks"],
                warnings=[],
                blocks=[],
                recommendations=[],)
            mock_risk_check.return_value = risk_report
            mock_record.return_value = None
            mock_execute.return_value = {
                "success": True,
                "order_id": "test_order_123",
                "executed_amount": 0.001,
                "executed_price": 50000,
                "trade_value": 50,
            }

            signal = TradingSignal(strategy_id="test_strategy", symbol="BTC/USDT:USDT", side="buy", amount=0.001)

            result = await executor.execute_signal(signal)

            assert result["success"] is True
            assert "execution_result" in result
            assert "risk_report" in result
            assert result["risk_report"] == risk_report
            assert executor.execution_stats["total_signals"] == 1
            assert executor.execution_stats["executed_trades"] == 1

    @pytest.mark.asyncio
    async def test_execute_signal_risk_blocked(self, mock_dependencies):
        """測試風險檢查阻止交易"""
        executor = IntegratedTradingExecutor()

        with patch.object(executor, "_comprehensive_risk_check") as mock_risk_check, patch.object(executor, "_record_risk_check") as mock_record:
            # 設置風險檢查阻止
            risk_report = RiskCheckReport(result=RiskCheckResult.BLOCKED,)
                checks_passed=[],
                warnings=[],
                blocks=["倉位超限"],
                recommendations=["減少倉位"],)
            mock_risk_check.return_value = risk_report
            mock_record.return_value = None

            signal = TradingSignal(strategy_id="test_strategy", symbol="BTC/USDT:USDT", side="buy", amount=0.1)

            result = await executor.execute_signal(signal)

            assert result["success"] is False
            assert result["reason"] == "risk_blocked"
            assert result["risk_report"] == risk_report
            assert executor.execution_stats["total_signals"] == 1
            assert executor.execution_stats["blocked_trades"] == 1

    @pytest.mark.asyncio
    async def test_execute_signal_with_warning(self, mock_dependencies):
        """測試帶警告的交易執行"""
        executor = IntegratedTradingExecutor()

        with patch.object(executor, "_comprehensive_risk_check") as mock_risk_check, patch.object(executor, "_record_risk_check") as mock_record, patch.object(executor, "_apply_risk_mitigation") as mock_mitigation, patch.object(executor, "_execute_trade") as mock_execute:
            # 設置風險檢查警告
            risk_report = RiskCheckReport(result=RiskCheckResult.WARNING,)
                checks_passed=["position_check"],
                warnings=["市場波動較大"],
                blocks=[],
                recommendations=["考慮降低倉位"],)
            mock_risk_check.return_value = risk_report
            mock_record.return_value = None

            # 模擬風險緩解
            original_signal = TradingSignal(strategy_id="test_strategy", symbol="BTC/USDT:USDT", side="buy", amount=0.1)
            mitigated_signal = TradingSignal(strategy_id="test_strategy", symbol="BTC/USDT:USDT", side="buy", amount=0.05)  # 減少倉位
            mock_mitigation.return_value = mitigated_signal

            mock_execute.return_value = {
                "success": True,
                "order_id": "test_order_456",
                "executed_amount": 0.05,
                "executed_price": 50000,
            }

            result = await executor.execute_signal(original_signal)

            assert result["success"] is True
            assert executor.execution_stats["warning_trades"] == 1
            mock_mitigation.assert_called_once()

    @pytest.mark.asyncio
    async def test_execute_signal_exception_handling(self, mock_dependencies):
        """測試交易執行異常處理"""
        executor = IntegratedTradingExecutor()

        with patch.object(executor, "_comprehensive_risk_check") as mock_risk_check:
            # 模擬異常
            mock_risk_check.side_effect = Exception("測試異常")

            signal = TradingSignal(strategy_id="test_strategy", symbol="BTC/USDT:USDT", side="buy", amount=0.001)

            result = await executor.execute_signal(signal)

            assert result["success"] is False
            assert result["reason"] == "execution_error"
            assert "測試異常" in result["error"]

    def test_get_execution_stats(self, mock_dependencies):
        """測試獲取執行統計"""
        executor = IntegratedTradingExecutor()

        # 修改統計
        executor.execution_stats["total_signals"] = 10
        executor.execution_stats["executed_trades"] = 8
        executor.execution_stats["blocked_trades"] = 2

        stats = executor.get_execution_stats()

        assert stats["total_signals"] == 10
        assert stats["executed_trades"] == 8
        assert stats["blocked_trades"] == 2

        # 確保返回的是副本
        stats["total_signals"] = 999
        assert executor.execution_stats["total_signals"] == 10

    @pytest.mark.asyncio
    async def test_shutdown(self, mock_dependencies):
        """測試關閉執行器"""
        executor = IntegratedTradingExecutor()

        result = await executor.shutdown()
        assert result is True


class TestGlobalInstance:
    """測試全局實例"""

    def test_get_integrated_trading_executor(self):
        """測試獲取全局交易執行器實例"""
        config = {"max_daily_trades": 25}

        with patch("integrated_trading_executor.IntegratedTradingExecutor") as mock_executor:
            mock_instance = Mock()
            mock_executor.return_value = mock_instance

            executor = get_integrated_trading_executor(config)

            assert executor == mock_instance
            mock_executor.assert_called_once_with(config)


class TestRiskCheckMethods:
    """測試風險檢查方法"""

    @pytest.fixture
    def executor_with_mocks(self):
        """創建帶模擬的執行器"""
        with patch("integrated_trading_executor.TradingSafetyGuard"), patch("integrated_trading_executor.HighLeverageRiskChecker"), patch("integrated_trading_executor.get_client_manager"), patch("integrated_trading_executor.get_global_event_bus"):
            executor = IntegratedTradingExecutor()
            return executor

    @pytest.mark.asyncio
    async def test_comprehensive_risk_check_passed(self, executor_with_mocks):
        """測試綜合風險檢查通過"""
        executor = executor_with_mocks

        with patch.object(executor, "_check_position_limits") as mock_position, patch.object(executor, "_check_leverage_risk") as mock_leverage, patch.object(executor, "_check_market_conditions") as mock_market, patch.object(executor, "_check_liquidity") as mock_liquidity:
            # 所有檢查都通過
            mock_position.return_value = {"passed": True}
            mock_leverage.return_value = {"passed": True}
            mock_market.return_value = {"passed": True}
            mock_liquidity.return_value = {"passed": True}

            signal = TradingSignal(strategy_id="test", symbol="BTC/USDT:USDT", side="buy", amount=0.001)

            result = await executor._comprehensive_risk_check(signal)

            assert result.result == RiskCheckResult.PASSED
            assert len(result.checks_passed) >= 4  # 至少4個檢查項目
            assert len(result.warnings) == 0
            assert len(result.blocks) == 0

    @pytest.mark.asyncio
    async def test_comprehensive_risk_check_blocked(self, executor_with_mocks):
        """測試綜合風險檢查阻止"""
        executor = executor_with_mocks

        with patch.object(executor, "_check_position_limits") as mock_position, patch.object(executor, "_check_leverage_risk") as mock_leverage, patch.object(executor, "_check_market_conditions") as mock_market, patch.object(executor, "_check_liquidity") as mock_liquidity:
            # 倉位檢查阻止
            mock_position.return_value = {"passed": False, "severity": "block", "message": "倉位超限"}
            mock_leverage.return_value = {"passed": True}
            mock_market.return_value = {"passed": True}
            mock_liquidity.return_value = {"passed": True}

            signal = TradingSignal(strategy_id="test", symbol="BTC/USDT:USDT", side="buy", amount=0.1)

            result = await executor._comprehensive_risk_check(signal)

            assert result.result == RiskCheckResult.BLOCKED
            assert any("倉位超限" in block for block in result.blocks)

    @pytest.mark.asyncio
    async def test_comprehensive_risk_check_warning(self, executor_with_mocks):
        """測試綜合風險檢查警告"""
        executor = executor_with_mocks

        with patch.object(executor, "_check_position_limits") as mock_position, patch.object(executor, "_check_leverage_risk") as mock_leverage, patch.object(executor, "_check_market_conditions") as mock_market, patch.object(executor, "_check_liquidity") as mock_liquidity:
            # 市場條件警告
            mock_position.return_value = {"passed": True}
            mock_leverage.return_value = {"passed": True}
            mock_market.return_value = {
                "passed": False,
                "message": "市場波動較大",
                "recommendations": ["降低倉位"],
            }
            mock_liquidity.return_value = {"passed": True}

            signal = TradingSignal(strategy_id="test", symbol="BTC/USDT:USDT", side="buy", amount=0.001)

            result = await executor._comprehensive_risk_check(signal)

            assert result.result == RiskCheckResult.WARNING
            assert any("市場波動較大" in warning for warning in result.warnings)
            assert any("降低倉位" in rec for rec in result.recommendations)

    @pytest.mark.asyncio
    async def test_comprehensive_risk_check_exception(self, executor_with_mocks):
        """測試風險檢查異常處理"""
        executor = executor_with_mocks

        with patch.object(executor, "_check_position_limits") as mock_position:
            # 模擬異常
            mock_position.side_effect = Exception("檢查失敗")

            signal = TradingSignal(strategy_id="test", symbol="BTC/USDT:USDT", side="buy", amount=0.001)

            result = await executor._comprehensive_risk_check(signal)

            assert result.result == RiskCheckResult.BLOCKED
            assert "風險檢查系統異常" in result.blocks[0]

    @pytest.mark.asyncio
    async def test_check_position_limits_pass(self, executor_with_mocks):
        """測試倉位限制檢查通過"""
        executor = executor_with_mocks

        with patch.object(executor, "_get_current_position") as mock_position:
            mock_position.return_value = 0.02  # 當前倉位

            signal = TradingSignal(strategy_id="test",)
                symbol="BTC/USDT:USDT",
                side="buy",
                amount=0.01,  # 新增倉位，總計0.03，小於限制0.1)

            result = await executor._check_position_limits(signal)

            assert result["passed"] is True

    @pytest.mark.asyncio
    async def test_check_position_limits_block(self, executor_with_mocks):
        """測試倉位限制檢查阻止"""
        executor = executor_with_mocks

        with patch.object(executor, "_get_current_position") as mock_position:
            mock_position.return_value = 0.05  # 當前倉位

            signal = TradingSignal(strategy_id="test",)
                symbol="BTC/USDT:USDT",
                side="buy",
                amount=0.08,  # 新增倉位，總計0.13，超過限制0.1)

            result = await executor._check_position_limits(signal)

            assert result["passed"] is False
            assert result["severity"] == "block"
            assert "超過限制" in result["message"]

    @pytest.mark.asyncio
    async def test_check_position_limits_warning(self, executor_with_mocks):
        """測試倉位限制檢查警告"""
        executor = executor_with_mocks

        with patch.object(executor, "_get_current_position") as mock_position:
            mock_position.return_value = 0.05  # 當前倉位

            signal = TradingSignal(strategy_id="test",)
                symbol="BTC/USDT:USDT",
                side="buy",
                amount=0.04,  # 新增倉位，總計0.09，接近限制0.1 (>80%))

            result = await executor._check_position_limits(signal)

            assert result["passed"] is False
            assert result["severity"] == "warning"
            assert "接近限制" in result["message"]


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
