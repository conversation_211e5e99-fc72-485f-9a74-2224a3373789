from unittest.mock import Mock, patch

#!/usr/bin/env python3
"""
統一投資組合管理器全面測試 - 補充現有測試，專注於未覆蓋的功能
Comprehensive tests for UnifiedPortfolioManager - focusing on uncovered functionality
"""

import asyncio
from datetime import datetime, timedelta

import numpy as np
import pytest

from global_event_bus import Event, EventType
from strategy_framework import BaseStrategy, StrategyType, TradingSignal
from unified_portfolio_manager import SystemOverview, UnifiedPortfolioManager


class MockStrategy(BaseStrategy):
    """模擬策略用於測試"""

    def __init__(self, strategy_id: str, health_score: float = 0.5):
        self.strategy_id = strategy_id
        self.health_score = health_score

    def get_strategy_type(self) -> StrategyType:
        """獲取策略類型"""
        return StrategyType.PAIRS_TRADING

    def analyze_market(self, market_data):
        return []

    def calculate_position_size(self, signal, available_capital):
        return 0.1

    def get_current_positions(self):
        return {}

    def validate_signal(self, signal: TradingSignal, current_positions):
        """驗證信號"""
        return True

    def update_health_score(self, new_score):
        self.health_score = new_score


class TestUnifiedPortfolioManagerAdvanced:
    """測試統一投資組合管理器的高級功能"""

    @pytest.fixture
    def mock_dependencies(self):
        """模擬依賴項"""
        with patch("unified_portfolio_manager.get_global_event_bus") as mock_event_bus, patch(
            "unified_portfolio_manager.get_async_resource_manager"
        ) as mock_resource_manager, patch(
            "unified_portfolio_manager.get_client_manager"
        ) as mock_client_manager:
            mock_event_bus.return_value = Mock()
            mock_resource_manager.return_value = Mock()
            mock_client_manager.return_value = Mock()

            yield {
                "event_bus": mock_event_bus.return_value,
                "resource_manager": mock_resource_manager.return_value,
                "client_manager": mock_client_manager.return_value,
            }

    @pytest.fixture
    def manager_with_strategies(self, mock_dependencies):
        """創建帶有策略的管理器"""
        manager = UnifiedPortfolioManager(total_capital=100000)

        # 創建模擬策略對象
        pairs_strategy = MockStrategy("pairs_trading", 0.8)
        trend_strategy = MockStrategy("trend_following", 0.6)
        mean_reversion_strategy = MockStrategy("mean_reversion", 0.7)

        # 添加多個策略
        manager.add_strategy(pairs_strategy, 0.4)
        manager.add_strategy(trend_strategy, 0.3)
        manager.add_strategy(mean_reversion_strategy, 0.3)

        # 添加一些歷史性能數據
        manager.performance_history["pairs_trading"] = [100, -50, 200, 150, -30]
        manager.performance_history["trend_following"] = [80, 120, -40, 90, 110]
        manager.performance_history["mean_reversion"] = [-20, 60, 30, -10, 40]

        return manager

    @pytest.mark.asyncio
    async def test_rebalance_loop_lifecycle(self, mock_dependencies):
        """測試重新平衡循環的完整生命週期"""
        manager = UnifiedPortfolioManager(total_capital=100000)

        rebalance_calls = []

        async def mock_perform_rebalancing():
            rebalance_calls.append(datetime.now())
            if len(rebalance_calls) >= 1:  # 執行1次後停止
                manager.is_running = False

        # Mock asyncio.sleep 來加速測試
        original_sleep = asyncio.sleep

        async def mock_sleep(seconds):
            if seconds > 1:  # 只有長時間睡眠才被縮短
                await original_sleep(0.01)  # 縮短為10毫秒
            else:
                await original_sleep(seconds)

        with patch.object(manager, "_should_rebalance", return_value=True), patch.object(
            manager, "_perform_rebalancing", side_effect=mock_perform_rebalancing
        ), patch("asyncio.sleep", side_effect=mock_sleep):
            manager.is_running = True

            # 啟動重新平衡循環
            task = asyncio.create_task(manager._rebalance_loop())

            # 等待一段時間讓循環執行
            await asyncio.sleep(0.1)

            # 停止循環
            manager.is_running = False

            try:
                await asyncio.wait_for(task, timeout=1.0)
            except asyncio.TimeoutError:
                task.cancel()

            # 驗證重新平衡被調用
            assert len(rebalance_calls) >= 1

    @pytest.mark.asyncio
    async def test_rebalance_loop_exception_handling(self, mock_dependencies):
        """測試重新平衡循環的異常處理"""
        manager = UnifiedPortfolioManager(total_capital=100000)
        manager.rebalance_frequency = 0.001

        exception_count = 0

        def mock_should_rebalance():
            nonlocal exception_count
            exception_count += 1
            if exception_count == 1:
                raise ValueError("Test exception")
            elif exception_count >= 2:
                manager.is_running = False
                return False
            return True

        # Mock asyncio.sleep 來加速測試
        original_sleep = asyncio.sleep

        async def mock_sleep(seconds):
            if seconds > 1:  # 只有長時間睡眠才被縮短
                await original_sleep(0.01)  # 縮短為10毫秒
            else:
                await original_sleep(seconds)

        with patch.object(manager, "_should_rebalance", side_effect=mock_should_rebalance), patch(
            "asyncio.sleep", side_effect=mock_sleep
        ):
            manager.is_running = True

            # 啟動重新平衡循環
            task = asyncio.create_task(manager._rebalance_loop())

            # 等待循環處理異常
            await asyncio.sleep(0.1)

            # 停止循環
            manager.is_running = False

            try:
                await asyncio.wait_for(task, timeout=1.0)
            except asyncio.TimeoutError:
                task.cancel()

            # 驗證異常處理（應該調用sleep進行重試）
            assert exception_count >= 1

    @pytest.mark.asyncio
    async def test_update_strategy_health_score_comprehensive(self, manager_with_strategies):
        """測試策略健康分數更新的全面邏輯"""
        manager = manager_with_strategies

        # 為策略添加足夠的歷史數據（至少5筆）
        strategy_id = "pairs_trading"
        manager.performance_history[strategy_id] = [0.02, -0.01, 0.03, 0.015, -0.005, 0.025, 0.01]

        old_health = manager.strategy_allocations[strategy_id].health_score

        await manager._update_strategy_health_score(strategy_id)

        new_health = manager.strategy_allocations[strategy_id].health_score

        # 驗證健康分數被更新
        assert new_health != old_health
        assert 0 <= new_health <= 1  # 健康分數應該在0-1之間

    @pytest.mark.asyncio
    async def test_update_strategy_health_score_insufficient_data(self, manager_with_strategies):
        """測試數據不足時的健康分數更新"""
        manager = manager_with_strategies

        # 只有少量歷史數據（少於5筆）
        strategy_id = "pairs_trading"
        manager.performance_history[strategy_id] = [0.02, -0.01]

        old_health = manager.strategy_allocations[strategy_id].health_score

        await manager._update_strategy_health_score(strategy_id)

        # 健康分數不應該改變
        new_health = manager.strategy_allocations[strategy_id].health_score
        assert new_health == old_health

    @pytest.mark.asyncio
    async def test_update_strategy_health_score_event_publishing(self, manager_with_strategies):
        """測試健康分數變化時的事件發布"""
        manager = manager_with_strategies

        strategy_id = "pairs_trading"
        manager.performance_history[strategy_id] = [0.05, -0.02, 0.08, 0.03, -0.01, 0.06, 0.02]

        # 設置一個較低的初始健康分數，確保變化超過0.1閾值
        manager.strategy_allocations[strategy_id].health_score = 0.2

        # Mock event_bus.publish 來捕獲發布的事件
        published_events = []

        async def mock_publish(event):
            if event.event_type == EventType.STRATEGY_HEALTH_CHANGED:
                published_events.append(event)

        with patch.object(manager.event_bus, "publish", side_effect=mock_publish):
            await manager._update_strategy_health_score(strategy_id)

            # 驗證事件發布
            assert len(published_events) >= 1
            event = published_events[0]
            assert event.data["strategy_id"] == strategy_id
            assert "old_health" in event.data
            assert "new_health" in event.data
            assert "metrics" in event.data

    @pytest.mark.asyncio
    async def test_on_order_filled_comprehensive(self, manager_with_strategies):
        """測試訂單成交事件的全面處理"""
        _ = manager_with_strategies

        # 測試不同的事件數據格式
        _ = [
            {
                "strategy_id": "pairs_trading",
                "pnl": 500,
                "trade_value": 10000,
                "timestamp": datetime.now(),
            },
            {
                "strategy_id": "trend_following",
                "pnl": -200,
                "trade_value": 5000,
                "timestamp": datetime.now().isoformat(),  # 字符串格式
            },
            {
                "strategy_id": "mean_reversion",
                "pnl": 300,  # 使用標準的PnL字段名
                "trade_value": 8000,
                "timestamp": datetime.now(),
            },
        ]

        for i, event_data in enumerate(test_cases):
            event = Event()
                event_type=EventType.ORDER_FILLED,
                source=f"test_executor_{i}",
                data=event_data,
                timestamp=datetime.now(),
            )

            with patch.object(manager, "_update_strategy_health_score") as mock_update:
                await manager._on_order_filled(event)

                strategy_id = event_data["strategy_id"]

                # 檢查性能記錄更新
                expected_pnl = event_data.get("pnl", 0)

                performance_history = manager.performance_history[strategy_id]
                assert performance_history[-1] == expected_pnl

                # 檢查健康分數更新調用
                mock_update.assert_called_with(strategy_id)

    @pytest.mark.asyncio
    async def test_on_order_filled_performance_history_limit(self, manager_with_strategies):
        """測試性能歷史記錄的大小限制"""
        manager = manager_with_strategies

        strategy_id = "pairs_trading"

        # 填充大量歷史數據（超過1000條）
        manager.performance_history[strategy_id] = list(range(1200))

        event = Event()
            event_type=EventType.ORDER_FILLED,
            source="test_executor",
            data={
                "strategy_id": strategy_id,
                "pnl": 999,
                "trade_value": 10000,
                "timestamp": datetime.now(),
            },
            timestamp=datetime.now(),
        )

        with patch.object(manager, "_update_strategy_health_score"):
            await manager._on_order_filled(event)

            # 檢查歷史記錄被限制在500條
            performance_history = manager.performance_history[strategy_id]
            assert len(performance_history) == 500  # 限制在500條
            assert performance_history[-1] == 999  # 最新的記錄

    @pytest.mark.asyncio
    async def test_on_risk_alert_medium_severity(self, manager_with_strategies):
        """測試中等嚴重性風險警報處理"""
        manager = manager_with_strategies

        strategy_id = "pairs_trading"
        original_allocation = manager.strategy_allocations[strategy_id].current_allocation

        event = Event()
            event_type=EventType.RISK_LIMIT_EXCEEDED,
            source="risk_manager",
            data={
                "risk_type": "volatility_spike",
                "strategy_id": strategy_id,
                "severity": "medium",  # 中等嚴重性
            },
            timestamp=datetime.now(),
        )

        await manager._on_risk_alert(event)

        # 中等嚴重性不應該調整分配
        current_allocation = manager.strategy_allocations[strategy_id].current_allocation
        assert current_allocation == original_allocation

        # 不應該發布重新平衡事件
        manager.event_bus.publish.assert_not_called()

    @pytest.mark.asyncio
    async def test_on_risk_alert_unknown_strategy(self, manager_with_strategies):
        """測試未知策略的風險警報處理"""
        manager = manager_with_strategies

        event = Event()
            event_type=EventType.RISK_LIMIT_EXCEEDED,
            source="risk_manager",
            data={
                "risk_type": "position_limit",
                "strategy_id": "unknown_strategy",  # 不存在的策略
                "severity": "high",
            },
            timestamp=datetime.now(),
        )

        # 應該不會拋出異常
        await manager._on_risk_alert(event)

        # 不應該發布任何事件
        manager.event_bus.publish.assert_not_called()

    def test_get_system_overview(self, manager_with_strategies):
        """測試系統概覽生成"""
        manager = manager_with_strategies
        manager.system_start_time = datetime.now() - timedelta(hours=2)

        overview = manager.get_system_overview()

        assert isinstance(overview, SystemOverview)
        assert overview.timestamp is not None

        # 檢查系統統計
        system_stats = overview.system_stats
        assert "uptime_hours" in system_stats
        assert "is_running" in system_stats
        assert "total_strategies" in system_stats
        assert system_stats["total_strategies"] == 3

        # 檢查資本概覽
        capital_overview = overview.capital_overview
        assert "total_capital" in capital_overview
        assert "allocated_capital" in capital_overview
        assert "available_capital" in capital_overview
        assert capital_overview["total_capital"] == 100000

        # 檢查策略概覽
        strategy_overview = overview.strategy_overview
        assert len(strategy_overview) == 3
        for strategy_info in strategy_overview:
            assert "strategy_id" in strategy_info
            assert "current_allocation" in strategy_info
            assert "target_allocation" in strategy_info
            assert "health_score" in strategy_info
            assert "allocated_capital" in strategy_info

        # 檢查性能指標
        performance_metrics = overview.performance_metrics
        assert "total_pnl" in performance_metrics
        assert "recent_trades" in performance_metrics
        assert "avg_pnl_per_trade" in performance_metrics

        # 檢查風險指標
        risk_metrics = overview.risk_metrics
        assert "max_single_allocation" in risk_metrics
        assert "min_single_allocation" in risk_metrics
        assert "allocation_concentration" in risk_metrics


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
