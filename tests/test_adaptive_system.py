#!/usr/bin/env python3
"""
自適應系統測試腳本
Adaptive System Test Script
"""

import sys
import numpy as np
import pandas as pd
from datetime import datetime
from typing import Dict

# 添加項目根目錄到路徑
sys.path.append('.')

from strategy_health_monitor import StrategyHealthMonitor
from unified_pair_selector import UnifiedPairSelector, PairSelectionMethod
from security_manager import SecurityManager
from alert_manager import AlertManager, AlertLevel
from dynamic_config import DynamicConfigManager
from logging_config import setup_logging, get_logger

# 設置日誌
setup_logging()
logger = get_logger(__name__)


class AdaptiveSystemTester:
    """自適應系統測試器"""
    
    def __init__(self):
        self.health_monitor = StrategyHealthMonitor()
        self.pair_selector = AdaptivePairSelector()
        self.security_manager = SecurityManager()
        self.alert_manager = AlertManager()
        self.config_manager = DynamicConfigManager(watch_changes=False)
        
        logger.info("自適應系統測試器初始化完成")
    
    def test_strategy_health_monitor(self) -> bool:
        """測試策略健康監控器"""
        try:
            print("\n=== 測試策略健康監控器 ===")
            
            # 模擬一系列交易記錄
            print("1. 模擬交易記錄...")
            
            # 模擬連續虧損
            for i in range(6):
                pnl = -np.random.uniform(10, 50)  # 虧損
                self.health_monitor.record_trade({
                    'pair': ['BTCUSDT', 'ETHUSDT'],
                    'pnl': pnl,
                    'trade_type': 'test',
                    'exit_reason': 'stop_loss'
                })
                print(f"   記錄虧損交易 {i+1}: ${pnl:.2f}")
            
            # 檢查連續虧損計數
            consecutive_losses = self.health_monitor.health_status['consecutive_losses']
            print(f"   連續虧損次數: {consecutive_losses}")
            
            # 計算夏普比率
            sharpe_ratio = self.health_monitor.calculate_sharpe_ratio()
            print(f"   夏普比率: {sharpe_ratio:.4f}")
            
            # 模擬價格數據進行共整合檢查
            print("2. 測試共整合檢查...")
            dates = pd.date_range(start='2024-01-01', periods=100, freq='h')
            price_data = pd.DataFrame({
                'base_price': 50000 + np.cumsum(np.random.normal(0, 100, 100)),
                'quote_price': 3000 + np.cumsum(np.random.normal(0, 10, 100))
            }, index=dates)
            
            p_value, is_cointegrated = self.health_monitor.check_cointegration(price_data)
            print(f"   共整合P值: {p_value:.6f}, 是否共整合: {is_cointegrated}")
            
            # 執行完整健康檢查
            print("3. 執行完整健康檢查...")
            health_report = self.health_monitor.perform_health_check(price_data)
            print(f"   健康狀態: {'健康' if health_report['is_healthy'] else '不健康'}")
            
            return True
            
        except Exception as e:
            logger.error(f"策略健康監控器測試失敗: {e}")
            return False
    
    def test_adaptive_pair_selector(self) -> bool:
        """測試自適應配對選擇器"""
        try:
            print("\n=== 測試自適應配對選擇器 ===")
            
            # 測試獲取最佳配對
            print("1. 測試獲取最佳配對...")
            best_pair = self.pair_selector.get_best_available_pair()
            print(f"   最佳配對: {best_pair}")
            
            # 測試記錄配對表現
            print("2. 測試記錄配對表現...")
            if best_pair:
                for i in range(5):
                    pnl = np.random.normal(10, 30)  # 隨機盈虧
                    self.pair_selector.record_pair_performance(best_pair, {
                        'pnl': pnl,
                        'trade_type': 'test',
                        'exit_reason': 'take_profit' if pnl > 0 else 'stop_loss'
                    })
                    print(f"   記錄配對表現 {i+1}: ${pnl:.2f}")
            
            # 測試配對統計
            print("3. 測試配對統計...")
            stats = self.pair_selector.get_pair_statistics()
            print(f"   配對統計: {stats}")
            
            # 測試配對切換（模擬）
            print("4. 測試配對切換...")
            current_pair = self.config_manager.get_value('trading_pair')
            print(f"   當前配對: {current_pair}")
            
            # 不實際切換，只測試邏輯
            new_best_pair = self.pair_selector.get_best_available_pair()
            print(f"   建議切換到: {new_best_pair}")
            
            return True
            
        except Exception as e:
            logger.error(f"自適應配對選擇器測試失敗: {e}")
            return False
    
    def test_security_manager(self) -> bool:
        """測試安全管理器"""
        try:
            print("\n=== 測試安全管理器 ===")
            
            # 測試生成API密鑰
            print("1. 測試API密鑰管理...")
            api_key = self.security_manager.generate_api_key('test', ['read', 'write'])
            print(f"   生成的API密鑰: {api_key[:20]}...")
            
            # 測試驗證API密鑰
            is_valid, result = self.security_manager.validate_api_key(api_key, 'read')
            print(f"   API密鑰驗證結果: {is_valid}, {result}")
            
            # 測試權限檢查
            is_valid, result = self.security_manager.validate_api_key(api_key, 'control')
            print(f"   權限檢查結果: {is_valid}, {result}")
            
            # 測試Telegram用戶管理
            print("2. 測試Telegram用戶管理...")
            test_user_id = 123456789
            self.security_manager.add_telegram_user(test_user_id)
            is_authorized = self.security_manager.validate_telegram_user(test_user_id)
            print(f"   Telegram用戶驗證: {is_authorized}")
            
            # 測試速率限制
            print("3. 測試速率限制...")
            for i in range(3):
                allowed = self.security_manager.check_rate_limit('test_client', max_requests=5, window_minutes=1)
                print(f"   請求 {i+1}: {'允許' if allowed else '拒絕'}")
            
            # 獲取安全狀態
            print("4. 獲取安全狀態...")
            status = self.security_manager.get_security_status()
            print(f"   安全狀態: {status}")
            
            return True
            
        except Exception as e:
            logger.error(f"安全管理器測試失敗: {e}")
            return False
    
    def test_alert_system(self) -> bool:
        """測試警報系統"""
        try:
            print("\n=== 測試警報系統 ===")
            
            # 測試不同級別的警報
            print("1. 測試不同級別的警報...")
            
            self.alert_manager.send_alert(
                AlertLevel.INFO,
                "測試信息警報",
                "這是一個測試信息警報",
                {"測試參數": 123.45}
            )
            print("   ✓ 信息警報已發送")
            
            self.alert_manager.send_alert(
                AlertLevel.WARNING,
                "測試警告警報",
                "這是一個測試警告警報"
            )
            print("   ✓ 警告警報已發送")
            
            self.alert_manager.send_alert(
                AlertLevel.ERROR,
                "測試錯誤警報",
                "這是一個測試錯誤警報"
            )
            print("   ✓ 錯誤警報已發送")
            
            # 測試健康狀態更新
            print("2. 測試健康狀態更新...")
            self.alert_manager.update_health_status({
                'status': 'ok',
                'current_position': {'active': False},
                'last_trade': {'pnl': 25.5}
            })
            
            health_status = self.alert_manager.get_health_status()
            print(f"   健康狀態: {health_status}")
            
            return True
            
        except Exception as e:
            logger.error(f"警報系統測試失敗: {e}")
            return False
    
    def test_dynamic_config(self) -> bool:
        """測試動態配置管理"""
        try:
            print("\n=== 測試動態配置管理 ===")
            
            # 測試獲取配置值
            print("1. 測試配置值獲取...")
            trading_pair = self.config_manager.get_value('trading_pair')
            print(f"   當前交易對: {trading_pair}")
            
            # 測試設置配置值
            print("2. 測試配置值設置...")
            old_threshold = self.config_manager.get_value('entry_threshold_high')
            print(f"   原始進場門檻: {old_threshold}")
            
            # 設置新值（不保存到文件）
            self.config_manager.set_value('test_parameter', 999.99, save_to_file=False)
            new_value = self.config_manager.get_value('test_parameter')
            print(f"   測試參數設置: {new_value}")
            
            # 測試遠程命令
            print("3. 測試遠程命令...")
            result = self.config_manager.execute_remote_command('get_parameter', {'key': 'timeframe'})
            print(f"   遠程命令結果: {result}")
            
            # 獲取配置管理器狀態
            print("4. 獲取配置管理器狀態...")
            status = self.config_manager.get_status()
            print(f"   配置管理器狀態: {status}")
            
            return True
            
        except Exception as e:
            logger.error(f"動態配置管理測試失敗: {e}")
            return False
    
    def run_comprehensive_test(self) -> Dict:
        """運行綜合測試"""
        print("🚀 開始自適應系統綜合測試...")
        print("=" * 60)
        
        test_results = {}
        
        # 執行各項測試
        tests = [
            ("策略健康監控器", self.test_strategy_health_monitor),
            ("自適應配對選擇器", self.test_adaptive_pair_selector),
            ("安全管理器", self.test_security_manager),
            ("警報系統", self.test_alert_system),
            ("動態配置管理", self.test_dynamic_config)
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            try:
                print(f"\n🧪 測試 {test_name}...")
                result = test_func()
                test_results[test_name] = result
                
                if result:
                    print(f"✅ {test_name} 測試通過")
                    passed_tests += 1
                else:
                    print(f"❌ {test_name} 測試失敗")
                    
            except Exception as e:
                print(f"❌ {test_name} 測試異常: {e}")
                test_results[test_name] = False
        
        # 生成測試報告
        print("\n" + "=" * 60)
        print("📊 測試結果總結")
        print("=" * 60)
        
        success_rate = (passed_tests / total_tests) * 100
        print(f"通過測試: {passed_tests}/{total_tests}")
        print(f"成功率: {success_rate:.1f}%")
        
        print("\n詳細結果:")
        for test_name, result in test_results.items():
            status = "✅ 通過" if result else "❌ 失敗"
            print(f"  {test_name}: {status}")
        
        if success_rate >= 80:
            print("\n🎉 自適應系統測試整體通過！")
            print("系統已準備好進行生產部署。")
        else:
            print("\n⚠️ 自適應系統測試存在問題")
            print("請檢查失敗的測試項目並修復問題。")
        
        return test_results


def main():
    """主函數"""
    try:
        tester = AdaptiveSystemTester()
        results = tester.run_comprehensive_test()
        
        # 保存測試結果
        import json
        from pathlib import Path
        
        results_dir = Path("test_results")
        results_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = results_dir / f"adaptive_system_test_{timestamp}.json"
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'results': results,
                'summary': {
                    'total_tests': len(results),
                    'passed_tests': sum(results.values()),
                    'success_rate': sum(results.values()) / len(results) * 100
                }
            }, f, indent=2, ensure_ascii=False)
        
        print(f"\n📁 測試結果已保存: {results_file}")
        
    except Exception as e:
        logger.error(f"測試執行失敗: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
