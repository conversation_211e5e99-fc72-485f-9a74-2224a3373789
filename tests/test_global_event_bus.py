#!/usr/bin/env python3
"""
全局事件總線測試 - 全面測試事件發布、訂閱和處理邏輯
Comprehensive tests for GlobalEventBus
"""

import asyncio
from datetime import datetime
from typing import List
from unittest.mock import AsyncMock, Mock, patch

import pytest

from global_event_bus import (
    Event,
    EventSubscription,
    EventType,
    GlobalEventBus,
    get_global_event_bus,
    publish_event,
)


class TestEventType:
    """測試事件類型枚舉"""

    def test_event_type_values(self):
        """測試事件類型值"""
        assert EventType.MARKET_DATA_UPDATE.value == "market_data_update"
        assert EventType.ORDER_FILLED.value == "order_filled"
        assert EventType.STRATEGY_HEALTH_CHANGED.value == "strategy_health_changed"
        assert EventType.PORTFOLIO_REBALANCE.value == "portfolio_rebalance"
        assert EventType.SYSTEM_STARTUP.value == "system_startup"


class TestEvent:
    """測試事件數據結構"""

    def test_event_creation(self):
        """測試事件創建"""
        timestamp = datetime.now()
        event = Event(
            event_type=EventType.ORDER_FILLED,
            source="test_executor",
            data={"symbol": "BTC/USDT", "amount": 0.001},
            timestamp=timestamp,
            correlation_id="test-123",
            priority=3,
        )

        assert event.event_type == EventType.ORDER_FILLED
        assert event.source == "test_executor"
        assert event.data["symbol"] == "BTC/USDT"
        assert event.timestamp == timestamp
        assert event.correlation_id == "test-123"
        assert event.priority == 3

    def test_event_to_dict(self):
        """測試事件轉換為字典"""
        timestamp = datetime.now()
        event = Event(
            event_type=EventType.SIGNAL_GENERATED,
            source="test_strategy",
            data={"signal": "buy"},
            timestamp=timestamp,
            priority=1,
        )

        event_dict = event.to_dict()

        assert event_dict["event_type"] == "signal_generated"
        assert event_dict["source"] == "test_strategy"
        assert event_dict["data"] == {"signal": "buy"}
        assert event_dict["timestamp"] == timestamp.isoformat()
        assert event_dict["priority"] == 1

    def test_event_from_dict(self):
        """測試從字典創建事件"""
        timestamp = datetime.now()
        event_dict = {
            "event_type": "order_placed",
            "source": "test_source",
            "data": {"order_id": "123"},
            "timestamp": timestamp.isoformat(),
            "correlation_id": "corr-456",
            "priority": 2,
        }

        event = Event.from_dict(event_dict)

        assert event.event_type == EventType.ORDER_PLACED
        assert event.source == "test_source"
        assert event.data == {"order_id": "123"}
        assert event.timestamp == timestamp
        assert event.correlation_id == "corr-456"
        assert event.priority == 2

    def test_event_from_dict_defaults(self):
        """測試從字典創建事件（使用默認值）"""
        timestamp = datetime.now()
        event_dict = {
            "event_type": "market_data_update",
            "source": "market_feed",
            "data": {"price": 50000},
            "timestamp": timestamp.isoformat(),
        }

        event = Event.from_dict(event_dict)

        assert event.correlation_id is None
        assert event.priority == 5  # 默認值


class TestEventSubscription:
    """測試事件訂閱"""

    def test_subscription_creation(self):
        """測試訂閱創建"""
        callback = Mock()
        filter_func = Mock(return_value=True)

        subscription = EventSubscription(
            event_types=[EventType.ORDER_FILLED, EventType.ORDER_CANCELLED],
            callback=callback,
            filter_func=filter_func,
        )

        assert EventType.ORDER_FILLED in subscription.event_types
        assert EventType.ORDER_CANCELLED in subscription.event_types
        assert subscription.callback == callback
        assert subscription.filter_func == filter_func
        assert subscription.subscription_id == id(subscription)

    def test_subscription_matches_event_type(self):
        """測試訂閱匹配事件類型"""
        callback = Mock()
        subscription = EventSubscription(event_types=[EventType.ORDER_FILLED], callback=callback)

        matching_event = Event(
            event_type=EventType.ORDER_FILLED, source="test", data={}, timestamp=datetime.now()
        )

        non_matching_event = Event(
            event_type=EventType.ORDER_CANCELLED, source="test", data={}, timestamp=datetime.now()
        )

        assert subscription.matches(matching_event) is True
        assert subscription.matches(non_matching_event) is False

    def test_subscription_matches_with_filter(self):
        """測試訂閱匹配帶過濾器"""
        callback = Mock()
        filter_func = Mock()

        subscription = EventSubscription(
            event_types=[EventType.ORDER_FILLED], callback=callback, filter_func=filter_func
        )

        event = Event(
            event_type=EventType.ORDER_FILLED,
            source="test",
            data={"symbol": "BTC/USDT"},
            timestamp=datetime.now(),
        )

        # 過濾器返回True
        filter_func.return_value = True
        assert subscription.matches(event) is True

        # 過濾器返回False
        filter_func.return_value = False
        assert subscription.matches(event) is False

        filter_func.assert_called_with(event)


class TestGlobalEventBus:
    """測試全局事件總線"""

    def test_initialization(self):
        """測試初始化"""
        bus = GlobalEventBus(max_queue_size=5000)

        assert bus.max_queue_size == 5000
        assert bus.event_queue.maxsize == 5000
        assert bus.subscriptions == []
        assert bus.event_history == []
        assert bus.max_history_size == 1000
        assert bus.is_running is False
        assert bus.processing_task is None

        # 檢查統計初始化
        stats = bus.stats
        assert stats["events_published"] == 0
        assert stats["events_processed"] == 0
        assert stats["events_dropped"] == 0
        assert stats["active_subscriptions"] == 0

    def test_start_stop(self):
        """測試啟動和停止"""
        bus = GlobalEventBus()

        # 測試啟動
        with patch("asyncio.create_task") as mock_create_task:
            mock_task = Mock()
            mock_create_task.return_value = mock_task

            bus.start()

            assert bus.is_running is True
            assert bus.processing_task == mock_task
            mock_create_task.assert_called_once()

        # 測試重複啟動
        bus.start()  # 應該不會有額外操作

        # 測試停止
        bus.stop()
        assert bus.is_running is False
        mock_task.cancel.assert_called_once()

    def test_subscribe(self):
        """測試訂閱事件"""
        bus = GlobalEventBus()
        callback = Mock()
        filter_func = Mock()

        subscription_id = bus.subscribe(
            event_types=[EventType.ORDER_FILLED, EventType.ORDER_CANCELLED],
            callback=callback,
            filter_func=filter_func,
        )

        assert len(bus.subscriptions) == 1
        assert bus.stats["active_subscriptions"] == 1
        assert isinstance(subscription_id, int)

        subscription = bus.subscriptions[0]
        assert subscription.callback == callback
        assert subscription.filter_func == filter_func
        assert EventType.ORDER_FILLED in subscription.event_types

    def test_unsubscribe(self):
        """測試取消訂閱"""
        bus = GlobalEventBus()
        callback = Mock()

        subscription_id = bus.subscribe(event_types=[EventType.ORDER_FILLED], callback=callback)

        assert len(bus.subscriptions) == 1

        bus.unsubscribe(subscription_id)

        assert len(bus.subscriptions) == 0
        assert bus.stats["active_subscriptions"] == 0

    @pytest.mark.asyncio
    async def test_publish_success(self):
        """測試成功發布事件"""
        bus = GlobalEventBus(max_queue_size=10)

        event = Event(
            event_type=EventType.ORDER_FILLED, source="test", data={}, timestamp=datetime.now()
        )

        await bus.publish(event)

        assert bus.stats["events_published"] == 1
        assert bus.event_queue.qsize() == 1

    @pytest.mark.asyncio
    async def test_publish_queue_full(self):
        """測試隊列滿時發布事件"""
        bus = GlobalEventBus(max_queue_size=1)

        # 填滿隊列
        event1 = Event(
            event_type=EventType.ORDER_FILLED, source="test", data={}, timestamp=datetime.now()
        )
        await bus.publish(event1)

        # 嘗試發布第二個事件（應該被丟棄）
        event2 = Event(
            event_type=EventType.ORDER_CANCELLED, source="test", data={}, timestamp=datetime.now()
        )
        await bus.publish(event2)

        assert bus.stats["events_published"] == 1
        assert bus.stats["events_dropped"] == 1

    def test_publish_sync(self):
        """測試同步發布事件"""
        bus = GlobalEventBus()

        event = Event(
            event_type=EventType.ORDER_FILLED, source="test", data={}, timestamp=datetime.now()
        )

        with patch("asyncio.get_event_loop") as mock_get_loop:
            mock_loop = Mock()
            mock_loop.is_running.return_value = True
            mock_get_loop.return_value = mock_loop

            with patch("asyncio.create_task") as mock_create_task:
                bus.publish_sync(event)
                mock_create_task.assert_called_once()

    def test_publish_sync_not_running_loop(self):
        """測試在非運行循環中同步發布"""
        bus = GlobalEventBus()

        event = Event(
            event_type=EventType.ORDER_FILLED, source="test", data={}, timestamp=datetime.now()
        )

        with patch("asyncio.get_event_loop") as mock_get_loop:
            mock_loop = Mock()
            mock_loop.is_running.return_value = False
            mock_get_loop.return_value = mock_loop

            bus.publish_sync(event)
            mock_loop.run_until_complete.assert_called_once()

    def test_add_to_history(self):
        """測試添加事件到歷史記錄"""
        bus = GlobalEventBus()
        bus.max_history_size = 3

        events = []
        for _ in range(5):
            event = Event(
                event_type=EventType.ORDER_FILLED,
                source=f"test_{i}",
                data={"index": i},
                timestamp=datetime.now(),
            )
            events.append(event)
            bus._add_to_history(event)

        # 應該只保留最後3個事件
        assert len(bus.event_history) == 3
        assert bus.event_history[0].data["index"] == 2
        assert bus.event_history[1].data["index"] == 3
        assert bus.event_history[2].data["index"] == 4

    def test_get_stats(self):
        """測試獲取統計信息"""
        bus = GlobalEventBus()
        bus.stats["events_published"] = 10
        bus.stats["events_processed"] = 8
        bus.stats["events_dropped"] = 2

        stats = bus.get_stats()

        assert stats["events_published"] == 10
        assert stats["events_processed"] == 8
        assert stats["events_dropped"] == 2
        assert stats["queue_size"] == 0
        assert stats["history_size"] == 0
        assert stats["is_running"] is False

    def test_get_recent_events(self):
        """測試獲取最近事件"""
        bus = GlobalEventBus()

        # 添加不同類型的事件
        events = [
            Event(EventType.ORDER_FILLED, "test1", {}, datetime.now()),
            Event(EventType.ORDER_CANCELLED, "test2", {}, datetime.now()),
            Event(EventType.ORDER_FILLED, "test3", {}, datetime.now()),
        ]

        for event in events:
            bus._add_to_history(event)

        # 獲取所有事件
        all_events = bus.get_recent_events()
        assert len(all_events) == 3

        # 獲取特定類型事件
        filled_events = bus.get_recent_events(EventType.ORDER_FILLED)
        assert len(filled_events) == 2
        assert all(e.event_type == EventType.ORDER_FILLED for e in filled_events)

        # 限制數量
        limited_events = bus.get_recent_events(limit=2)
        assert len(limited_events) == 2

    @pytest.mark.asyncio
    async def test_process_events(self):
        """測試事件處理循環"""
        bus = GlobalEventBus()
        received_events = []

        def callback(event):
            received_events.append(event)

        bus.subscribe([EventType.ORDER_FILLED], callback)

        # 手動添加事件到隊列
        event = Event(
            event_type=EventType.ORDER_FILLED, source="test", data={}, timestamp=datetime.now()
        )
        await bus.event_queue.put(event)

        # 啟動處理
        bus.is_running = True

        # 處理一個事件
        await bus._process_events()

        # 由於超時，需要等待一下
        await asyncio.sleep(0.1)

        assert len(received_events) == 1
        assert bus.stats["events_processed"] == 1
        assert len(bus.event_history) == 1

    @pytest.mark.asyncio
    async def test_dispatch_event(self):
        """測試事件分發"""
        bus = GlobalEventBus()
        received_events = []

        def callback1(event):
            received_events.append(f"callback1_{event.source}")

        def callback2(event):
            received_events.append(f"callback2_{event.source}")

        # 兩個訂閱者
        bus.subscribe([EventType.ORDER_FILLED], callback1)
        bus.subscribe([EventType.ORDER_FILLED], callback2)

        event = Event(
            event_type=EventType.ORDER_FILLED, source="test", data={}, timestamp=datetime.now()
        )

        await bus._dispatch_event(event)

        assert len(received_events) == 2
        assert "callback1_test" in received_events
        assert "callback2_test" in received_events

    @pytest.mark.asyncio
    async def test_safe_callback_sync(self):
        """測試安全回調（同步）"""
        bus = GlobalEventBus()

        def sync_callback(event):
            return f"processed_{event.source}"

        event = Event(
            event_type=EventType.ORDER_FILLED, source="test", data={}, timestamp=datetime.now()
        )

        # 應該不會拋出異常
        await bus._safe_callback(sync_callback, event)

    @pytest.mark.asyncio
    async def test_safe_callback_async(self):
        """測試安全回調（異步）"""
        bus = GlobalEventBus()

        async def async_callback(event):
            await asyncio.sleep(0.01)
            return f"processed_{event.source}"

        event = Event(
            event_type=EventType.ORDER_FILLED, source="test", data={}, timestamp=datetime.now()
        )

        # 應該不會拋出異常
        await bus._safe_callback(async_callback, event)

    @pytest.mark.asyncio
    async def test_safe_callback_exception_handling(self):
        """測試回調異常處理"""
        bus = GlobalEventBus()

        def failing_callback(event):
            raise ValueError("Test exception")

        event = Event(
            event_type=EventType.ORDER_FILLED, source="test", data={}, timestamp=datetime.now()
        )

        # 應該捕獲異常而不會崩潰
        await bus._safe_callback(failing_callback, event)


class TestGlobalFunctions:
    """測試全局函數"""

    def test_get_global_event_bus_singleton(self):
        """測試全局事件總線單例"""
        # 清除全局實例
        import global_event_bus

        global_event_bus._global_event_bus = None

        bus1 = get_global_event_bus()
        bus2 = get_global_event_bus()

        assert bus1 is bus2  # 同一個實例
        assert isinstance(bus1, GlobalEventBus)

    def test_publish_event_convenience_function(self):
        """測試發布事件便利函數"""
        with patch("global_event_bus.get_global_event_bus") as mock_get_bus:
            mock_bus = Mock()
            mock_get_bus.return_value = mock_bus

            publish_event(
                event_type=EventType.ORDER_FILLED,
                source="test_source",
                data={"order_id": "123"},
                correlation_id="corr-456",
                priority=2,
            )

            # 驗證調用
            mock_bus.publish_sync.assert_called_once()
            event_arg = mock_bus.publish_sync.call_args[0][0]

            assert event_arg.event_type == EventType.ORDER_FILLED
            assert event_arg.source == "test_source"
            assert event_arg.data == {"order_id": "123"}
            assert event_arg.correlation_id == "corr-456"
            assert event_arg.priority == 2


class TestIntegration:
    """集成測試"""

    @pytest.mark.asyncio
    async def test_full_event_flow(self):
        """測試完整事件流程"""
        bus = GlobalEventBus()
        received_events = []

        def event_handler(event):
            received_events.append(
                {"type": event.event_type.value, "source": event.source, "data": event.data}
            )

        # 訂閱多種事件類型
        bus.subscribe([EventType.ORDER_FILLED, EventType.SIGNAL_GENERATED], event_handler)

        # 啟動事件總線
        bus.start()

        # 發布多個事件
        events_to_publish = [
            Event(EventType.ORDER_FILLED, "executor", {"order_id": "1"}, datetime.now()),
            Event(EventType.SIGNAL_GENERATED, "strategy", {"signal": "buy"}, datetime.now()),
            Event(
                EventType.ORDER_CANCELLED, "executor", {"order_id": "2"}, datetime.now()
            ),  # 不會被處理
        ]

        for event in events_to_publish:
            await bus.publish(event)

        # 等待處理完成
        await asyncio.sleep(0.2)

        # 停止事件總線
        bus.stop()

        # 驗證結果
        assert len(received_events) == 2  # 只有2個匹配的事件
        assert received_events[0]["type"] == "order_filled"
        assert received_events[1]["type"] == "signal_generated"

        # 檢查統計
        stats = bus.get_stats()
        assert stats["events_published"] == 3
        assert stats["events_processed"] == 3
        assert stats["history_size"] == 3


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
