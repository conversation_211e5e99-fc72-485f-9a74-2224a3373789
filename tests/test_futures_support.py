import logging

# 配置logger
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
測試期貨合約支持 - 檢查Gate.io和BitMart的U本位合約支持
Test Futures Support - Check USDT futures support for Gate.io and BitMart
"""

import sys

import ccxt

from logging_config import get_logger, setup_logging

setup_logging()  # 修復不完整調用
_ = get_logger(__name__)


def test_futures_support():
    """測試期貨合約支持"""
    print("🔍 檢查期貨合約支持...")

    exchanges_to_test = [("gate", "Gate.io"), ("bitmart", "BitMart")]

    _ = {}

    for exchange_id, exchange_name in exchanges_to_test:
        print(f"\n📊 {exchange_name} ({exchange_id}) 期貨支持檢查:")

        try:
            if not hasattr(ccxt, exchange_id):
                print(f"  ❌ CCXT不支持 {exchange_id}")
                continue

            # 創建交易所實例
            exchange_class = getattr(ccxt, exchange_id)
            exchange = exchange_class()
                {
                    "sandbox": True,
                    "enableRateLimit": True,
                }
            )

            # 檢查期貨功能
            futures_support = {
                "has_futures": False,
                "has_linear_futures": False,
                "has_inverse_futures": False,
                "supported_markets": [],
                "sample_symbols": [],
            }

            # 檢查基本期貨支持
            if hasattr(exchange, "has") and exchange.has.get("future", False):
                futures_support["has_futures"] = True
                print("  ✅ 支持期貨交易")
            else:
                print("  ❌ 不支持期貨交易")

            # 檢查線性期貨（U本位）
            if hasattr(exchange, "has") and exchange.has.get("linearFutures", False):
                futures_support["has_linear_futures"] = True
                print("  ✅ 支持線性期貨（U本位）")
            else:
                print("  ❌ 不支持線性期貨（U本位）")

            # 檢查反向期貨（幣本位）
            if hasattr(exchange, "has") and exchange.has.get("inverseFutures", False):
                futures_support["has_inverse_futures"] = True
                print("  ✅ 支持反向期貨（幣本位）")
            else:
                print("  ❌ 不支持反向期貨（幣本位）")

            # 嘗試獲取市場信息
            try:
                markets = exchange.load_markets()

                # 查找期貨市場
                futures_markets = []
                linear_futures = []

                for symbol, market in markets.items():
                    if market.get("type") == "future":
                        futures_markets.append(symbol)

                        # 檢查是否為線性期貨（通常以USDT結算）
                        if "USDT" in symbol and ":USDT" in symbol:
                            linear_futures.append(symbol)

                futures_support["supported_markets"] = futures_markets[:10]  # 只顯示前10個
                futures_support["sample_symbols"] = linear_futures[:5]  # 只顯示前5個

                print(f"  📈 期貨市場數量: {len(futures_markets)}")
                print(f"  💰 U本位期貨數量: {len(linear_futures)}")

                if linear_futures:
                    print(f"  🎯 樣本U本位合約: {linear_futures[:3]}")

            except Exception as e:
                print(f"  ⚠️ 獲取市場信息失敗: {e}")

            # 檢查做空功能
            short_support = False
            if hasattr(exchange, "has"):
                if exchange.has.get("createMarketOrder", False) and futures_support["has_futures"]:
                    short_support = True
                    print("  ✅ 支持做空（通過期貨）")
                else:
                    print("  ❌ 不支持做空")

            futures_support["supports_short"] = short_support
            results[exchange_id] = futures_support

        except Exception as e:
            print(f"  ❌ 檢查失敗: {e}")
            results[exchange_id] = {"error": str(e)}

    # 驗證期貨支持結果
    assert len(results) > 0, "應該至少測試一個交易所"

    # 檢查是否有交易所支持期貨
    has_futures_support = any(result.get("has_futures", False) for result in results.values())
    assert has_futures_support, "至少應該有一個交易所支持期貨"

    print(f"✅ 期貨支持驗證通過: {len(results)} 個交易所測試完成")


def test_specific_futures_features():
    """測試具體的期貨功能"""
    print("\n🔧 測試具體期貨功能...")

    exchanges_to_test = ["gate", "bitmart"]

    for exchange_id in exchanges_to_test:
        if not hasattr(ccxt, exchange_id):
            continue

        print(f"\n📊 {exchange_id.upper()} 期貨功能測試:")

        try:
            exchange_class = getattr(ccxt, exchange_id)
            exchange = exchange_class()
                {
                    "sandbox": True,
                    "enableRateLimit": True,
                }
            )

            # 檢查期貨相關的API方法
            futures_methods = [
                "fetchPositions",
                "fetchPosition",
                "setLeverage",
                "setMarginMode",
                "fetchFundingRate",
                "fetchFundingRates",
            ]

            available_methods = []
            for method in futures_methods:
                if hasattr(exchange, "has") and exchange.has.get(method, False):
                    available_methods.append(method)
                    print(f"  ✅ {method}")
                else:
                    print(f"  ❌ {method}")

            # 檢查是否支持槓桿設置
            if "setLeverage" in available_methods:
                print("  🎯 支持槓桿設置（配對交易建議1x槓桿）")

            # 檢查是否支持倉位查詢
            if "fetchPositions" in available_methods:
                print("  📊 支持倉位查詢（重要：用於風險管理）")

        except Exception as e:
            print(f"  ❌ 測試失敗: {e}")


def generate_futures_config_recommendations():
    """生成期貨配置建議"""
    print("\n📝 期貨交易配置建議...")

    print("\n🔧 配對交易期貨配置:")
    print("1. 交易類型: U本位期貨（USDT結算）")
    print("2. 槓桿設置: 1x（降低風險）")
    print("3. 保證金模式: 逐倉模式（隔離風險）")
    print("4. 交易對選擇: BTC/USDT:USDT, ETH/USDT:USDT")

    print("\n⚙️ 配置文件更新:")
    print("config.json:")
    print()  # 修復不完整調用
        """{{
    "trading_mode": "futures",
    "futures_type": "linear",  // U本位
    "leverage": 1,
    "margin_mode": "isolated",
    "trading_pairs": [
        ["BTC/USDT:USDT", "ETH/USDT:USDT"]
    ]
}}"""
    )

    print("\n🛡️ 風險管理建議:")
    print("- 使用1x槓桿避免強制平倉")
    print("- 設置嚴格的止損（2-3%）")
    print("- 監控資金費率影響")
    print("- 定期檢查倉位和保證金")


def main():
    """主函數"""
    print("=" * 60)
    print("期貨合約支持測試 - 配對交易升級")
    print("=" * 60)

    try:
        # 檢查期貨支持
        results = test_futures_support()

        # 測試具體功能
        test_specific_futures_features()  # 修復不完整調用

        # 生成配置建議
        generate_futures_config_recommendations()  # 修復不完整調用

        print("\n" + "=" * 60)
        print("📊 測試結果總結:")
        print("=" * 60)

        for exchange_id, result in results.items():
            if "error" in result:
                print(f"❌ {exchange_id}: 測試失敗 - {result['error']}")
            else:
                futures_support = "✅" if result.get("has_futures") else "❌"
                linear_support = "✅" if result.get("has_linear_futures") else "❌"
                short_support = "✅" if result.get("supports_short") else "❌"

                print(f"{exchange_id.upper()}:")
                print(f"  期貨支持: {futures_support}")
                print(f"  U本位期貨: {linear_support}")
                print(f"  做空支持: {short_support}")
                print(f"  樣本合約: {result.get('sample_symbols', [])[:2]}")

        print("\n💡 建議:")
        print("- 如果交易所支持U本位期貨，升級到期貨交易")
        print("- 如果不支持，考慮切換到支持期貨的交易所")
        print("- 配對交易必須能夠做空才能正確實現")

    except Exception as e:
        logger.error(f"測試失敗: {e}")
        return 1

    return 0


if __name__ == "__main__":
    sys.exit(main())
