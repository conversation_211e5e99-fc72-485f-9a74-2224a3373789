#!/usr/bin/env python3
"""
工業級系統測試 - 驗證系統達到生產級標準
Industrial Grade System Test - Verify system meets production standards
"""

import asyncio
import json
import sys
import threading
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List

import numpy as np
import pandas as pd
import requests

# 添加項目根目錄到路徑
sys.path.append(".")

# 導入所有優化後的模組
from config_validation import ComprehensiveConfig, ConfigValidator
from enhanced_retry_handler import EnhancedRetryHandler, RetryConfig
from health_server import HealthServer
from logging_config import get_logger, setup_logging
from performance_optimization.memory_optimizer import DataFrameOptimizer, MemoryMonitor

# 設置日誌
setup_logging()
logger = get_logger(__name__)


class IndustrialGradeTest:
    """工業級系統測試套件"""

    def __init__(self):
        self.test_results = {}
        self.start_time = datetime.now()
        self.health_server = None
        self.memory_monitor = None

        logger.info("IndustrialGradeTest 初始化完成")

    def test_configuration_validation(self) -> Dict:
        """測試配置驗證系統"""
        try:
            print("\n=== 測試配置驗證系統 ===")

            # 測試1：正常配置載入
            try:
                config = ConfigValidator.load_and_validate_config()
                config_load_success = True
            except Exception as e:
                logger.error(f"配置載入失敗: {e}")
                config_load_success = False

            # 測試2：配置驗證
            warnings = []
            if config_load_success:
                warnings = ConfigValidator.validate_runtime_config(config)

            # 測試3：配置保存
            config_save_success = False
            if config_load_success:
                try:
                    test_config_path = "test_config.json"
                    config.save_to_file(test_config_path)
                    config_save_success = Path(test_config_path).exists()
                    if config_save_success:
                        Path(test_config_path).unlink()  # 清理測試文件
                except Exception as e:
                    logger.error(f"配置保存測試失敗: {e}")

            # 測試4：環境變量覆蓋
            import os

            original_env = os.environ.get("TRADING_EXCHANGE")
            os.environ["TRADING_EXCHANGE"] = "binance"

            try:
                env_config = ConfigValidator.load_and_validate_config()
                env_override_success = env_config.trading.exchange == "binance"
            except Exception:
                env_override_success = False
            finally:
                if original_env:
                    os.environ["TRADING_EXCHANGE"] = original_env
                elif "TRADING_EXCHANGE" in os.environ:
                    del os.environ["TRADING_EXCHANGE"]

            result = {
                "test_name": "configuration_validation",
                "config_load_success": config_load_success,
                "config_save_success": config_save_success,
                "env_override_success": env_override_success,
                "warnings_count": len(warnings),
                "warnings": warnings,
                "passed": (config_load_success and config_save_success and env_override_success),
            }

            print(f"   ✓ 配置載入: {'成功' if config_load_success else '失敗'}")
            print(f"   ✓ 配置保存: {'成功' if config_save_success else '失敗'}")
            print(f"   ✓ 環境變量覆蓋: {'成功' if env_override_success else '失敗'}")
            print(f"   ✓ 配置警告數量: {len(warnings)}")
            print(f"   ✓ 測試結果: {'通過' if result['passed'] else '失敗'}")

            return result

        except Exception as e:
            logger.error(f"配置驗證測試失敗: {e}")
            return {"test_name": "configuration_validation", "passed": False, "error": str(e)}

    def test_memory_optimization(self) -> Dict:
        """測試內存優化系統"""
        try:
            print("\n=== 測試內存優化系統 ===")

            # 啟動內存監控
            self.memory_monitor = MemoryMonitor(threshold_mb=500, check_interval=1)
            self.memory_monitor.start()

            # 測試1：DataFrame優化
            print("   測試DataFrame優化...")
            original_df = pd.DataFrame(
                {
                    "int_col": np.random.randint(0, 100, 50000),
                    "float_col": np.random.random(50000) * 1000,
                    "category_col": np.random.choice(["A", "B", "C", "D"], 50000),
                    "string_col": [f"item_{i % 1000}" for _ in range(50000)],
                }
            )

            original_memory = original_df.memory_usage(deep=True).sum() / 1024 / 1024
            optimized_df = DataFrameOptimizer.optimize_dtypes(original_df, aggressive=True)
            optimized_memory = optimized_df.memory_usage(deep=True).sum() / 1024 / 1024

            memory_reduction = (1 - optimized_memory / original_memory) * 100
            dataframe_optimization_success = memory_reduction > 20  # 期望至少20 % 減少

            # 測試2：內存監控
            print("   測試內存監控...")
            time.sleep(2)  # 讓監控運行一段時間

            memory_report = self.memory_monitor.get_memory_report()
            memory_monitoring_success = (
                memory_report
                and "current_memory_mb" in memory_report
                and memory_report["current_memory_mb"] > 0
            )

            # 測試3：大數據處理
            print("   測試大數據處理...")
            large_data_success = False
            try:
                # 創建大型數據集
                large_df = pd.DataFrame(
                    {
                        "values": np.random.random(100000),
                        "categories": np.random.choice(["X", "Y", "Z"], 100000),
                    }
                )

                # 優化處理
                optimized_large_df = DataFrameOptimizer.optimize_dtypes(large_df)
                large_data_success = len(optimized_large_df) == len(large_df)

                # 清理
                del large_df, optimized_large_df

            except Exception as e:
                logger.error(f"大數據處理測試失敗: {e}")

            # 停止內存監控
            self.memory_monitor.stop()

            result = {
                "test_name": "memory_optimization",
                "original_memory_mb": original_memory,
                "optimized_memory_mb": optimized_memory,
                "memory_reduction_percent": memory_reduction,
                "dataframe_optimization_success": dataframe_optimization_success,
                "memory_monitoring_success": memory_monitoring_success,
                "large_data_processing_success": large_data_success,
                "memory_report": memory_report,
                "passed": (
                    dataframe_optimization_success
                    and memory_monitoring_success
                    and large_data_success
                ),
            }

            print(f"   ✓ DataFrame優化: {memory_reduction:.1f}% 內存減少")
            print(f"   ✓ 內存監控: {'正常' if memory_monitoring_success else '異常'}")
            print(f"   ✓ 大數據處理: {'成功' if large_data_success else '失敗'}")
            print(f"   ✓ 測試結果: {'通過' if result['passed'] else '失敗'}")

            return result

        except Exception as e:
            logger.error(f"內存優化測試失敗: {e}")
            return {"test_name": "memory_optimization", "passed": False, "error": str(e)}

    def test_monitoring_system(self) -> Dict:
        """測試監控系統"""
        try:
            print("\n=== 測試監控系統 ===")

            # 啟動健康檢查服務器
            self.health_server = HealthServer(port=8081)  # 使用不同端口避免衝突

            # 在後台線程中啟動服務器
            server_thread = threading.Thread(target=self.health_server.start, daemon=True)
            server_thread.start()

            # 等待服務器啟動
            time.sleep(2)

            # 測試1：健康檢查端點
            health_check_success = False
            try:
                response = requests.get("http://localhost:8081/health", timeout=5)
                health_check_success = response.status_code == 200
            except Exception as e:
                logger.error(f"健康檢查測試失敗: {e}")

            # 測試2：狀態端點
            status_check_success = False
            try:
                response = requests.get("http://localhost:8081/status", timeout=5)
                status_check_success = response.status_code == 200
                if status_check_success:
                    status_data = response.json()
                    status_check_success = "status" in status_data
            except Exception as e:
                logger.error(f"狀態檢查測試失敗: {e}")

            # 測試3：Prometheus指標端點
            metrics_check_success = False
            try:
                response = requests.get("http://localhost:8081/metrics", timeout=5)
                metrics_check_success = (
                    response.status_code == 200
                    and "trading_bot_memory_usage_bytes" in response.text
                )
            except Exception as e:
                logger.error(f"指標檢查測試失敗: {e}")

            # 測試4：內存狀態端點
            memory_endpoint_success = False
            try:
                response = requests.get("http://localhost:8081/memory", timeout=5)
                memory_endpoint_success = response.status_code == 200
                if memory_endpoint_success:
                    memory_data = response.json()
                    memory_endpoint_success = "memory_report" in memory_data
            except Exception as e:
                logger.error(f"內存端點測試失敗: {e}")

            # 測試5：性能指標端點
            performance_endpoint_success = False
            try:
                response = requests.get("http://localhost:8081/performance", timeout=5)
                performance_endpoint_success = response.status_code == 200
                if performance_endpoint_success:
                    perf_data = response.json()
                    performance_endpoint_success = "performance" in perf_data
            except Exception as e:
                logger.error(f"性能端點測試失敗: {e}")

            # 停止服務器
            try:
                self.health_server.stop()
            except Exception:
                pass

            result = {
                "test_name": "monitoring_system",
                "health_check_success": health_check_success,
                "status_check_success": status_check_success,
                "metrics_check_success": metrics_check_success,
                "memory_endpoint_success": memory_endpoint_success,
                "performance_endpoint_success": performance_endpoint_success,
                "passed": (
                    health_check_success
                    and status_check_success
                    and metrics_check_success
                    and memory_endpoint_success
                    and performance_endpoint_success
                ),
            }

            print(f"   ✓ 健康檢查端點: {'正常' if health_check_success else '異常'}")
            print(f"   ✓ 狀態端點: {'正常' if status_check_success else '異常'}")
            print(f"   ✓ Prometheus指標: {'正常' if metrics_check_success else '異常'}")
            print(f"   ✓ 內存端點: {'正常' if memory_endpoint_success else '異常'}")
            print(f"   ✓ 性能端點: {'正常' if performance_endpoint_success else '異常'}")
            print(f"   ✓ 測試結果: {'通過' if result['passed'] else '失敗'}")

            return result

        except Exception as e:
            logger.error(f"監控系統測試失敗: {e}")
            return {"test_name": "monitoring_system", "passed": False, "error": str(e)}

    def test_production_readiness(self) -> Dict:
        """測試生產就緒性"""
        try:
            print("\n=== 測試生產就緒性 ===")

            # 測試1：錯誤處理穩健性
            retry_handler = EnhancedRetryHandler(
                RetryConfig(max_retries=5, circuit_breaker_enabled=True, enable_metrics=True)
            )

            error_handling_success = True
            try:

                @retry_handler.retry_sync
                def test_function():
                    import random

                    if random.random() < 0.3:
                        raise ConnectionError("測試錯誤")
                    return "成功"

                # 執行多次測試
                success_count = 0
                for _ in range(20):
                    try:
                        test_function()
                        success_count += 1
                    except Exception:
                        pass

                error_handling_success = success_count >= 14  # 期望70 % 以上成功率

            except Exception as e:
                logger.error(f"錯誤處理測試失敗: {e}")
                error_handling_success = False

            # 測試2：並發處理能力
            concurrent_processing_success = False
            try:
                import concurrent.futures

                def cpu_intensive_task(n):
                    return sum(i * i for _ in range(n))

                start_time = time.time()
                with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
                    futures = [executor.submit(cpu_intensive_task, 10000) for _ in range(10)]
                    results = [future.result() for future in futures]

                concurrent_time = time.time() - start_time
                concurrent_processing_success = len(results) == 10 and concurrent_time < 5.0

            except Exception as e:
                logger.error(f"並發處理測試失敗: {e}")

            # 測試3：資源清理
            resource_cleanup_success = False
            try:
                # 創建一些資源
                test_files = []
                for _ in range(5):
                    file_path = f"test_resource_{i}.tmp"
                    with open(file_path, "w") as f:
                        f.write("test data")
                    test_files.append(file_path)

                # 清理資源
                for file_path in test_files:
                    Path(file_path).unlink()

                # 驗證清理
                resource_cleanup_success = all(not Path(f).exists() for f in test_files)

            except Exception as e:
                logger.error(f"資源清理測試失敗: {e}")

            # 測試4：長時間運行穩定性（簡化版）
            long_running_stability = False
            try:
                # 模擬長時間運行
                start_memory = self._get_memory_usage()

                for _ in range(100):
                    # 模擬工作負載
                    data = np.random.random(1000)
                    result = np.sum(data)
                    del data

                end_memory = self._get_memory_usage()
                memory_growth = end_memory - start_memory

                # 內存增長應該很小
                long_running_stability = memory_growth < 50  # 小於50MB增長

            except Exception as e:
                logger.error(f"長時間運行測試失敗: {e}")

            result = {
                "test_name": "production_readiness",
                "error_handling_success": error_handling_success,
                "concurrent_processing_success": concurrent_processing_success,
                "resource_cleanup_success": resource_cleanup_success,
                "long_running_stability": long_running_stability,
                "passed": (
                    error_handling_success
                    and concurrent_processing_success
                    and resource_cleanup_success
                    and long_running_stability
                ),
            }

            print(f"   ✓ 錯誤處理穩健性: {'通過' if error_handling_success else '失敗'}")
            print(f"   ✓ 並發處理能力: {'通過' if concurrent_processing_success else '失敗'}")
            print(f"   ✓ 資源清理: {'通過' if resource_cleanup_success else '失敗'}")
            print(f"   ✓ 長時間運行穩定性: {'通過' if long_running_stability else '失敗'}")
            print(f"   ✓ 測試結果: {'通過' if result['passed'] else '失敗'}")

            return result

        except Exception as e:
            logger.error(f"生產就緒性測試失敗: {e}")
            return {"test_name": "production_readiness", "passed": False, "error": str(e)}

    def _get_memory_usage(self) -> float:
        """獲取當前內存使用量（MB）"""
        try:
            import psutil

            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except Exception:
            return 0.0

    async def run_industrial_grade_tests(self) -> Dict:
        """運行所有工業級測試"""
        print("🏭 開始工業級系統測試...")
        print("=" * 80)

        # 執行所有測試
        tests = [
            ("配置驗證系統", self.test_configuration_validation),
            ("內存優化系統", self.test_memory_optimization),
            ("監控系統", self.test_monitoring_system),
            ("生產就緒性", self.test_production_readiness),
        ]

        passed_tests = 0
        total_tests = len(tests)

        for test_name, test_func in tests:
            try:
                if asyncio.iscoroutinefunction(test_func):
                    result = await test_func()
                else:
                    result = test_func()

                self.test_results[test_name] = result

                if result.get("passed", False):
                    print(f"✅ {test_name} 測試通過")
                    passed_tests += 1
                else:
                    print(f"❌ {test_name} 測試失敗")

            except Exception as e:
                print(f"❌ {test_name} 測試異常: {e}")
                self.test_results[test_name] = {"passed": False, "error": str(e)}

        # 生成測試報告
        print("\n" + "=" * 80)
        print("🏭 工業級系統測試結果總結")
        print("=" * 80)

        success_rate = (passed_tests / total_tests) * 100
        test_duration = (datetime.now() - self.start_time).total_seconds()

        print(f"通過測試: {passed_tests}/{total_tests}")
        print(f"成功率: {success_rate:.1f}%")
        print(f"測試時間: {test_duration:.1f} 秒")

        print("\n詳細結果:")
        for test_name, result in self.test_results.items():
            status = "✅ 通過" if result.get("passed", False) else "❌ 失敗"
            print(f"  {test_name}: {status}")

        if success_rate >= 95:
            print("\n🎉 系統已達到工業級標準！")
            print("🏭 可以安全部署到生產環境！")
            print("🚀 系統具備企業級穩健性和性能！")
        else:
            print("\n⚠️ 系統尚未完全達到工業級標準")
            print("請檢查失敗的測試項目並進行改進。")

        return {
            "success_rate": success_rate,
            "passed_tests": passed_tests,
            "total_tests": total_tests,
            "test_duration": test_duration,
            "detailed_results": self.test_results,
            "industrial_grade_ready": success_rate >= 95,
        }


async def main():
    """主函數"""
    try:
        tester = IndustrialGradeTest()
        results = await tester.run_industrial_grade_tests()

        # 保存測試結果
        results_dir = Path("test_results")
        results_dir.mkdir(exist_ok=True)

        timestamp = datetime.now().strftime("%Y % m%d_ % H%M % S")
        results_file = results_dir / f"industrial_grade_test_{timestamp}.json"

        with open(results_file, "w", encoding="utf-8") as f:
            json.dump(
                {
                    "timestamp": datetime.now().isoformat(),
                    "test_type": "industrial_grade",
                    "results": results,
                },
                f,
                indent=2,
                ensure_ascii=False,
                default=str,
            )

        print(f"\n📁 測試結果已保存: {results_file}")

        return 0 if results["industrial_grade_ready"] else 1

    except Exception as e:
        logger.error(f"工業級測試執行失敗: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
