#!/usr/bin/env python3
"""
配對交易機器人單元測試
"""

import unittest
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch
import sys
import os

# 添加項目根目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pair_trading_bot import PairTradingBot, TradingState, SignalState
from integrated_trading_executor import IntegratedTradingExecutor
from utils import calculate_log_spread, calculate_zscore


class TestPairTradingBot(unittest.TestCase):
    """配對交易機器人測試類"""
    
    def setUp(self):
        """測試前準備"""
        # 創建模擬配置
        self.mock_config = {
            'trading_pair': ['BTCUSDT', 'ETHUSDT'],
            'timeframe': '1m',
            'lookback_period': 20,
            'entry_threshold_high': 2.0,
            'entry_threshold_low': -2.0,
            'confirmation_threshold_high': 1.5,
            'confirmation_threshold_low': -1.5,
            'cooldown_period': 5,
            'stop_loss_pct': 0.01,
            'take_profit_target': 'zero_crossing',
            'position_size_usd': 1000,
            'exchange': {
                'name': 'binance',
                'sandbox': True,
                'api_key': 'test',
                'secret': 'test'
            }
        }
        
        # 創建模擬數據
        self.create_mock_data()
    
    def create_mock_data(self):
        """創建模擬價格數據"""
        dates = pd.date_range(start='2024-01-01', periods=100, freq='h')
        
        # 創建具有均值回歸特性的價差數據
        np.random.seed(42)
        base_prices = 50000 + np.cumsum(np.random.normal(0, 100, 100))
        quote_prices = 3000 + np.cumsum(np.random.normal(0, 10, 100))
        
        self.mock_price_data = pd.DataFrame({
            'base_price': base_prices,
            'quote_price': quote_prices,
            'base_volume': np.random.uniform(1000, 5000, 100),
            'quote_volume': np.random.uniform(5000, 20000, 100)
        }, index=dates)
        
        # 計算價差和 Z-score
        spreads = []
        for _, row in self.mock_price_data.iterrows():
            spread = calculate_log_spread(row['base_price'], row['quote_price'])
            spreads.append(spread)
        
        self.mock_price_data['spread'] = spreads
        
        # 計算 Z-score
        zscores = []
        for i in range(len(spreads)):
            if i >= 19:  # lookback_period - 1
                spread_window = pd.Series(spreads[:i+1])
                zscore = calculate_zscore(spread_window, 20)
                zscores.append(zscore)
            else:
                zscores.append(np.nan)
        
        self.mock_price_data['zscore'] = zscores
    
    @patch('pair_trading_bot.get_exchange_instance')
    @patch('pair_trading_bot.load_config')
    def test_bot_initialization(self, mock_load_config, mock_exchange):
        """測試機器人初始化"""
        mock_load_config.return_value = self.mock_config
        mock_exchange.return_value = Mock()
        
        bot = PairTradingBot()
        
        self.assertEqual(bot.trading_state, TradingState.SEARCHING)
        self.assertEqual(bot.signal_state, SignalState.NONE)
        self.assertEqual(bot.entry_threshold_high, 2.0)
        self.assertEqual(bot.entry_threshold_low, -2.0)
    
    @patch('pair_trading_bot.get_exchange_instance')
    @patch('pair_trading_bot.load_config')
    def test_signal_state_reset(self, mock_load_config, mock_exchange):
        """測試信號狀態重置邏輯"""
        mock_load_config.return_value = self.mock_config
        mock_exchange.return_value = Mock()
        
        bot = PairTradingBot()
        
        # 模擬數據處理器
        bot.data_handler = Mock()
        bot.data_handler.update_data.return_value = True
        bot.data_handler.get_current_prices.return_value = (50000, 3000)
        
        # 測試從上方觸發區重置
        bot.signal_state = SignalState.TRIGGERED_HIGH
        bot.data_handler.get_current_zscore.return_value = -0.5  # 跨越到負值
        
        result = bot.check_entry_signals()
        
        self.assertEqual(bot.signal_state, SignalState.NONE)
        self.assertFalse(result)
        
        # 測試從下方觸發區重置
        bot.signal_state = SignalState.TRIGGERED_LOW
        bot.data_handler.get_current_zscore.return_value = 0.5  # 跨越到正值
        
        result = bot.check_entry_signals()
        
        self.assertEqual(bot.signal_state, SignalState.NONE)
        self.assertFalse(result)
    
    @patch('pair_trading_bot.get_exchange_instance')
    @patch('pair_trading_bot.load_config')
    def test_entry_signal_logic(self, mock_load_config, mock_exchange):
        """測試進場信號邏輯"""
        mock_load_config.return_value = self.mock_config
        mock_exchange.return_value = Mock()
        
        bot = PairTradingBot()
        bot.data_handler = Mock()
        bot.trading_executor = Mock()
        
        bot.data_handler.update_data.return_value = True
        bot.data_handler.get_current_prices.return_value = (50000, 3000)
        bot.trading_executor.enter_short_base_long_quote.return_value = True
        
        # 測試觸發上方極端值
        bot.data_handler.get_current_zscore.return_value = 2.5
        bot.check_entry_signals()
        self.assertEqual(bot.signal_state, SignalState.TRIGGERED_HIGH)
        
        # 測試確認信號
        bot.data_handler.get_current_zscore.return_value = 1.4  # 低於確認門檻
        result = bot.check_entry_signals()
        
        self.assertTrue(result)
        self.assertEqual(bot.trading_state, TradingState.IN_POSITION)
        self.assertEqual(bot.signal_state, SignalState.NONE)
        bot.trading_executor.enter_short_base_long_quote.assert_called_once()
    
    @patch('pair_trading_bot.get_exchange_instance')
    @patch('pair_trading_bot.load_config')
    def test_exit_signal_logic(self, mock_load_config, mock_exchange):
        """測試出場信號邏輯"""
        mock_load_config.return_value = self.mock_config
        mock_exchange.return_value = Mock()
        
        bot = PairTradingBot()
        bot.data_handler = Mock()
        bot.trading_executor = Mock()
        
        # 設置為持倉狀態
        bot.trading_state = TradingState.IN_POSITION
        
        bot.data_handler.get_current_prices.return_value = (50000, 3000)
        bot.trading_executor.calculate_current_pnl.return_value = 50  # 盈利
        bot.trading_executor.get_actual_position_value.return_value = 2000
        bot.trading_executor.exit_position.return_value = True
        
        # 測試止盈條件
        bot.data_handler.get_current_zscore.return_value = 0.05  # 接近0
        result = bot.check_exit_signals()
        
        self.assertTrue(result)
        bot.trading_executor.exit_position.assert_called_with("take_profit", 50000, 3000)
    
    @patch('pair_trading_bot.get_exchange_instance')
    @patch('pair_trading_bot.load_config')
    def test_stop_loss_logic(self, mock_load_config, mock_exchange):
        """測試止損邏輯"""
        mock_load_config.return_value = self.mock_config
        mock_exchange.return_value = Mock()
        
        bot = PairTradingBot()
        bot.data_handler = Mock()
        bot.trading_executor = Mock()
        
        # 設置為持倉狀態
        bot.trading_state = TradingState.IN_POSITION
        
        bot.data_handler.get_current_prices.return_value = (50000, 3000)
        bot.data_handler.get_current_zscore.return_value = 2.8  # 不觸發止盈
        bot.trading_executor.calculate_current_pnl.return_value = -25  # 虧損
        bot.trading_executor.get_actual_position_value.return_value = 2000  # 虧損比例 1.25%
        bot.trading_executor.exit_position.return_value = True
        
        result = bot.check_exit_signals()
        
        self.assertTrue(result)
        bot.trading_executor.exit_position.assert_called_with("stop_loss", 50000, 3000)
    
    @patch('pair_trading_bot.get_exchange_instance')
    @patch('pair_trading_bot.load_config')
    def test_cooldown_logic(self, mock_load_config, mock_exchange):
        """測試冷卻期邏輯"""
        mock_load_config.return_value = self.mock_config
        mock_exchange.return_value = Mock()
        
        bot = PairTradingBot()
        bot.data_handler = Mock()
        bot.trading_executor = Mock()
        
        bot.data_handler.update_data.return_value = True
        bot.data_handler.get_current_prices.return_value = (50000, 3000)
        bot.data_handler.get_current_zscore.return_value = 2.5
        
        # 設置冷卻期
        bot.current_bar_index = 10
        bot.cooldown_end_bar = 15
        
        result = bot.check_entry_signals()
        
        # 在冷卻期內應該不進場
        self.assertFalse(result)
        self.assertEqual(bot.signal_state, SignalState.NONE)
        
        # 冷卻期結束後應該可以進場
        bot.current_bar_index = 16
        result = bot.check_entry_signals()
        
        # 應該觸發信號
        self.assertEqual(bot.signal_state, SignalState.TRIGGERED_HIGH)


class TestUtilityFunctions(unittest.TestCase):
    """工具函數測試類"""
    
    def test_calculate_log_spread(self):
        """測試對數價差計算"""
        spread = calculate_log_spread(100, 50)
        expected = np.log(100) - np.log(50)
        self.assertAlmostEqual(spread, expected, places=6)
        
        # 測試異常情況
        with self.assertRaises(ValueError):
            calculate_log_spread(0, 50)
        
        with self.assertRaises(ValueError):
            calculate_log_spread(100, -10)
    
    def test_calculate_zscore(self):
        """測試 Z-score 計算"""
        # 創建測試數據
        data = pd.Series([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])
        
        zscore = calculate_zscore(data, 5)
        
        # 最後5個數據的均值是8，標準差約為1.58
        # 當前值是10，Z-score應該約為(10-8)/1.58 ≈ 1.26
        self.assertAlmostEqual(zscore, 1.26, places=1)
        
        # 測試數據不足的情況
        short_data = pd.Series([1, 2, 3])
        zscore = calculate_zscore(short_data, 5)
        self.assertTrue(np.isnan(zscore))


class TestTradingExecutor(unittest.TestCase):
    """交易執行器測試類"""
    
    def setUp(self):
        """測試前準備"""
        self.mock_config = {
            'trading_pair': ['BTCUSDT', 'ETHUSDT'],
            'position_size_usd': 1000
        }
        
        self.mock_exchange = Mock()
        self.executor = IntegratedTradingExecutor(self.mock_config)
    
    def test_position_value_calculation(self):
        """測試倉位價值計算"""
        # 模擬成功的訂單
        self.mock_exchange.create_market_order.return_value = {'id': 'test_order'}
        
        # 執行進場
        success = self.executor.enter_long_base_short_quote(50000, 3000, 1)
        
        self.assertTrue(success)
        self.assertTrue(self.executor.current_position['is_active'])
        self.assertIsNotNone(self.executor.current_position['actual_position_value'])
        
        # 檢查實際倉位價值
        expected_value = (50000 * (1000/50000)) + (3000 * (1000/3000))
        self.assertAlmostEqual(
            self.executor.current_position['actual_position_value'],
            expected_value,
            places=2
        )
    
    def test_trade_history_recording(self):
        """測試交易歷史記錄"""
        # 模擬成功的訂單
        self.mock_exchange.create_market_order.return_value = {'id': 'test_order'}
        self.mock_exchange.fetch_ticker.return_value = {'last': 50000}
        
        # 執行進場
        self.executor.enter_long_base_short_quote(50000, 3000, 1)
        
        # 執行出場
        self.executor.exit_position("take_profit", 51000, 3100)
        
        # 檢查交易歷史
        history = self.executor.get_trade_history()
        self.assertEqual(len(history), 1)
        
        trade = history[0]
        self.assertEqual(trade['exit_reason'], 'take_profit')
        self.assertTrue(trade['is_long_base'])
        self.assertIsNotNone(trade['pnl'])  # 驗證PnL存在
        self.assertIsInstance(trade['pnl'], (int, float))  # 驗證PnL是數字
    
    def test_trade_statistics(self):
        """測試交易統計"""
        # 添加一些模擬交易記錄
        self.executor.trade_history = [
            {'pnl': 100, 'exit_reason': 'take_profit'},
            {'pnl': -50, 'exit_reason': 'stop_loss'},
            {'pnl': 75, 'exit_reason': 'take_profit'},
            {'pnl': -25, 'exit_reason': 'stop_loss'}
        ]
        
        stats = self.executor.get_trade_statistics()
        
        self.assertEqual(stats['total_trades'], 4)
        self.assertEqual(stats['winning_trades'], 2)
        self.assertEqual(stats['losing_trades'], 2)
        self.assertEqual(stats['total_pnl'], 100)
        self.assertEqual(stats['win_rate'], 0.5)
        self.assertAlmostEqual(stats['avg_win'], 87.5, places=1)
        self.assertAlmostEqual(stats['avg_loss'], 37.5, places=1)
        self.assertAlmostEqual(stats['profit_loss_ratio'], 87.5/37.5, places=2)


if __name__ == '__main__':
    # 設置日誌以避免測試時的日誌輸出
    import logging
    logging.disable(logging.CRITICAL)
    
    # 運行測試
    unittest.main(verbosity=2)
