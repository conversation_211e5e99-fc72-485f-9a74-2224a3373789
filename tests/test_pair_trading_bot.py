#!/usr/bin/env python3
"""
配對交易機器人全面測試套件
測試覆蓋：PairTradingBot 類的所有核心功能
"""

import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime
from typing import Dict, Any

# 正確的導入路徑
from src.pair_trading_bot import PairTradingBot, TradingState, SignalState, TakeProfitTarget
from src.integrated_trading_executor import IntegratedTradingExecutor, TradingSignal
from src.data_handler import DataHandler
from src.strategy_health_monitor import StrategyHealthMonitor
from src.unified_pair_selector import UnifiedPairSelector
from src.alert_manager import AlertManager


@pytest.fixture
def mock_config():
    """模擬配置夾具"""
    return {
        'trading_pair': ['BTCUSDT', 'ETHUSDT'],
        'timeframe': '1m',
        'lookback_period': 20,
        'entry_threshold_high': 2.0,
        'entry_threshold_low': -2.0,
        'confirmation_threshold_high': 1.5,
        'confirmation_threshold_low': -1.5,
        'cooldown_period': 5,
        'stop_loss_pct': 0.01,
        'take_profit_target': 'zero_crossing',
        'position_size_usd': 1000,
        'exchange': {
            'name': 'binance',
            'sandbox': True,
            'api_key': 'test_key',
            'secret': 'test_secret'
        }
    }


@pytest.fixture
def mock_exchange():
    """模擬交易所夾具"""
    exchange = Mock()
    exchange.create_market_order.return_value = {'id': 'test_order_123', 'status': 'closed'}
    exchange.fetch_ticker.return_value = {'last': 50000, 'bid': 49990, 'ask': 50010}
    exchange.fetch_balance.return_value = {'USDT': {'free': 10000, 'used': 0, 'total': 10000}}
    return exchange


@pytest.fixture
def mock_price_data():
    """模擬價格數據夾具"""
    dates = pd.date_range(start='2024-01-01', periods=100, freq='h')
    np.random.seed(42)
    base_prices = 50000 + np.cumsum(np.random.normal(0, 100, 100))
    quote_prices = 3000 + np.cumsum(np.random.normal(0, 10, 100))

    return pd.DataFrame({
        'base_price': base_prices,
        'quote_price': quote_prices,
        'base_volume': np.random.uniform(1000, 5000, 100),
        'quote_volume': np.random.uniform(5000, 20000, 100),
        'spread': np.random.normal(0, 0.1, 100),
        'zscore': np.random.normal(0, 1, 100)
    }, index=dates)

class TestPairTradingBotInitialization:
    """測試 PairTradingBot 初始化"""

    @patch('src.pair_trading_bot.get_exchange_instance')
    @patch('src.pair_trading_bot.load_config')
    @patch('src.pair_trading_bot.get_strategy_health_monitor')
    @patch('src.pair_trading_bot.get_alert_manager')
    def test_bot_initialization_success(self, mock_alert_manager, mock_health_monitor,
                                      mock_load_config, mock_exchange, mock_config):
        """測試機器人成功初始化"""
        # 設置模擬
        mock_load_config.return_value = mock_config
        mock_exchange.return_value = Mock()
        mock_health_monitor.return_value = Mock()
        mock_alert_manager.return_value = Mock()

        # 創建機器人實例
        bot = PairTradingBot("test_config.json")

        # 驗證初始化狀態
        assert bot.trading_state == TradingState.SEARCHING
        assert bot.signal_state == SignalState.NONE
        assert bot.entry_threshold_high == 2.0
        assert bot.entry_threshold_low == -2.0
        assert bot.confirmation_threshold_high == 1.5
        assert bot.confirmation_threshold_low == -1.5
        assert bot.cooldown_period == 5
        assert bot.stop_loss_pct == 0.01
        assert bot.take_profit_target == 'zero_crossing'

        # 驗證統計信息初始化
        assert bot.stats['total_trades'] == 0
        assert bot.stats['winning_trades'] == 0
        assert bot.stats['losing_trades'] == 0
        assert bot.stats['total_pnl'] == 0.0
        assert bot.stats['max_drawdown'] == 0.0
        assert bot.stats['current_drawdown'] == 0.0
        assert bot.stats['peak_equity'] == 0.0

        # 驗證組件初始化
        assert bot.exchange is not None
        assert bot.data_handler is not None
        assert bot.trading_executor is not None
        assert bot.health_monitor is not None
        assert bot.pair_selector is not None
        assert bot.alert_manager is not None
    @patch('src.pair_trading_bot.get_exchange_instance')
    @patch('src.pair_trading_bot.load_config')
    def test_bot_initialization_with_default_config(self, mock_load_config, mock_exchange, mock_config):
        """測試使用默認配置路徑初始化"""
        mock_load_config.return_value = mock_config
        mock_exchange.return_value = Mock()

        # 測試默認配置路徑
        with patch('src.pair_trading_bot.get_strategy_health_monitor'), \
             patch('src.pair_trading_bot.get_alert_manager'):
            bot = PairTradingBot()  # 使用默認 config.json

            mock_load_config.assert_called_with("config.json")
            assert bot.current_bar_index == 0
            assert bot.cooldown_end_bar is None
            assert bot._last_trade_notified is False

class TestPairTradingBotDataUpdate:
    """測試數據更新和信號處理"""

    @patch('src.pair_trading_bot.get_exchange_instance')
    @patch('src.pair_trading_bot.load_config')
    def test_update_data_and_signals_success(self, mock_load_config, mock_exchange, mock_config):
        """測試成功的數據更新"""
        mock_load_config.return_value = mock_config
        mock_exchange.return_value = Mock()

        with patch('src.pair_trading_bot.get_strategy_health_monitor'), \
             patch('src.pair_trading_bot.get_alert_manager'):
            bot = PairTradingBot()

            # 模擬數據處理器
            bot.data_handler = Mock()
            bot.data_handler.update_data.return_value = True
            bot.data_handler.get_current_zscore.return_value = 1.5

            result = bot.update_data_and_signals()

            assert result is True
            assert bot.current_bar_index == 1
            bot.data_handler.update_data.assert_called_once()
            bot.data_handler.get_current_zscore.assert_called_once()

    @patch('src.pair_trading_bot.get_exchange_instance')
    @patch('src.pair_trading_bot.load_config')
    def test_update_data_and_signals_failure(self, mock_load_config, mock_exchange, mock_config):
        """測試數據更新失敗"""
        mock_load_config.return_value = mock_config
        mock_exchange.return_value = Mock()

        with patch('src.pair_trading_bot.get_strategy_health_monitor'), \
             patch('src.pair_trading_bot.get_alert_manager'):
            bot = PairTradingBot()

            # 模擬數據更新失敗
            bot.data_handler = Mock()
            bot.data_handler.update_data.return_value = False

            result = bot.update_data_and_signals()

            assert result is False
            assert bot.current_bar_index == 1  # 仍然會增加

    @patch('src.pair_trading_bot.get_exchange_instance')
    @patch('src.pair_trading_bot.load_config')
    def test_update_data_nan_zscore(self, mock_load_config, mock_exchange, mock_config):
        """測試 Z-score 為 NaN 的情況"""
        mock_load_config.return_value = mock_config
        mock_exchange.return_value = Mock()

        with patch('src.pair_trading_bot.get_strategy_health_monitor'), \
             patch('src.pair_trading_bot.get_alert_manager'):
            bot = PairTradingBot()

            # 模擬 Z-score 為 NaN
            bot.data_handler = Mock()
            bot.data_handler.update_data.return_value = True
            bot.data_handler.get_current_zscore.return_value = np.nan

            result = bot.update_data_and_signals()

            assert result is False

    @patch('src.pair_trading_bot.get_exchange_instance')
    @patch('src.pair_trading_bot.load_config')
    def test_update_data_exception_handling(self, mock_load_config, mock_exchange, mock_config):
        """測試數據更新異常處理"""
        mock_load_config.return_value = mock_config
        mock_exchange.return_value = Mock()

        with patch('src.pair_trading_bot.get_strategy_health_monitor'), \
             patch('src.pair_trading_bot.get_alert_manager'):
            bot = PairTradingBot()

            # 模擬異常
            bot.data_handler = Mock()
            bot.data_handler.update_data.side_effect = Exception("數據更新錯誤")

            result = bot.update_data_and_signals()

            assert result is False
class TestPairTradingBotEntrySignals:
    """測試進場信號邏輯"""

    @patch('src.pair_trading_bot.get_exchange_instance')
    @patch('src.pair_trading_bot.load_config')
    def test_entry_signals_not_searching_state(self, mock_load_config, mock_exchange, mock_config):
        """測試非搜索狀態下不檢查進場信號"""
        mock_load_config.return_value = mock_config
        mock_exchange.return_value = Mock()

        with patch('src.pair_trading_bot.get_strategy_health_monitor'), \
             patch('src.pair_trading_bot.get_alert_manager'):
            bot = PairTradingBot()
            bot.trading_state = TradingState.IN_POSITION

            result = bot.check_entry_signals()
            assert result is False

    @patch('src.pair_trading_bot.get_exchange_instance')
    @patch('src.pair_trading_bot.load_config')
    def test_entry_signals_cooldown_period(self, mock_load_config, mock_exchange, mock_config):
        """測試冷卻期內不進場"""
        mock_load_config.return_value = mock_config
        mock_exchange.return_value = Mock()

        with patch('src.pair_trading_bot.get_strategy_health_monitor'), \
             patch('src.pair_trading_bot.get_alert_manager'):
            bot = PairTradingBot()

            # 設置冷卻期
            bot.current_bar_index = 10
            bot.cooldown_end_bar = 15

            result = bot.check_entry_signals()
            assert result is False

    @patch('src.pair_trading_bot.get_exchange_instance')
    @patch('src.pair_trading_bot.load_config')
    def test_signal_state_reset_high_to_negative(self, mock_load_config, mock_exchange, mock_config):
        """測試從上方觸發區重置到負值"""
        mock_load_config.return_value = mock_config
        mock_exchange.return_value = Mock()

        with patch('src.pair_trading_bot.get_strategy_health_monitor'), \
             patch('src.pair_trading_bot.get_alert_manager'):
            bot = PairTradingBot()

            # 模擬數據處理器
            bot.data_handler = Mock()
            bot.data_handler.get_current_zscore.return_value = -0.5  # 跨越到負值
            bot.data_handler.get_current_prices.return_value = (50000, 3000)

            # 設置為上方觸發狀態
            bot.signal_state = SignalState.TRIGGERED_HIGH

            result = bot.check_entry_signals()

            assert bot.signal_state == SignalState.NONE
            assert result is False

    @patch('src.pair_trading_bot.get_exchange_instance')
    @patch('src.pair_trading_bot.load_config')
    def test_signal_state_reset_low_to_positive(self, mock_load_config, mock_exchange, mock_config):
        """測試從下方觸發區重置到正值"""
        mock_load_config.return_value = mock_config
        mock_exchange.return_value = Mock()

        with patch('src.pair_trading_bot.get_strategy_health_monitor'), \
             patch('src.pair_trading_bot.get_alert_manager'):
            bot = PairTradingBot()

            # 模擬數據處理器
            bot.data_handler = Mock()
            bot.data_handler.get_current_zscore.return_value = 0.5  # 跨越到正值
            bot.data_handler.get_current_prices.return_value = (50000, 3000)

            # 設置為下方觸發狀態
            bot.signal_state = SignalState.TRIGGERED_LOW

            result = bot.check_entry_signals()

            assert bot.signal_state == SignalState.NONE
            assert result is False
    @patch('src.pair_trading_bot.get_exchange_instance')
    @patch('src.pair_trading_bot.load_config')
    def test_trigger_high_threshold(self, mock_load_config, mock_exchange, mock_config):
        """測試觸發上方極端值"""
        mock_load_config.return_value = mock_config
        mock_exchange.return_value = Mock()

        with patch('src.pair_trading_bot.get_strategy_health_monitor'), \
             patch('src.pair_trading_bot.get_alert_manager'):
            bot = PairTradingBot()

            # 模擬數據處理器
            bot.data_handler = Mock()
            bot.data_handler.get_current_zscore.return_value = 2.5  # 超過上方門檻
            bot.data_handler.get_current_prices.return_value = (50000, 3000)

            result = bot.check_entry_signals()

            assert bot.signal_state == SignalState.TRIGGERED_HIGH
            assert result is False  # 只是觸發，還未確認

    @patch('src.pair_trading_bot.get_exchange_instance')
    @patch('src.pair_trading_bot.load_config')
    def test_trigger_low_threshold(self, mock_load_config, mock_exchange, mock_config):
        """測試觸發下方極端值"""
        mock_load_config.return_value = mock_config
        mock_exchange.return_value = Mock()

        with patch('src.pair_trading_bot.get_strategy_health_monitor'), \
             patch('src.pair_trading_bot.get_alert_manager'):
            bot = PairTradingBot()

            # 模擬數據處理器
            bot.data_handler = Mock()
            bot.data_handler.get_current_zscore.return_value = -2.5  # 低於下方門檻
            bot.data_handler.get_current_prices.return_value = (50000, 3000)

            result = bot.check_entry_signals()

            assert bot.signal_state == SignalState.TRIGGERED_LOW
            assert result is False  # 只是觸發，還未確認

    @patch('src.pair_trading_bot.get_exchange_instance')
    @patch('src.pair_trading_bot.load_config')
    def test_confirm_short_base_signal(self, mock_load_config, mock_exchange, mock_config):
        """測試確認做空 Base 信號"""
        mock_load_config.return_value = mock_config
        mock_exchange.return_value = Mock()

        with patch('src.pair_trading_bot.get_strategy_health_monitor'), \
             patch('src.pair_trading_bot.get_alert_manager'):
            bot = PairTradingBot()

            # 模擬數據處理器和交易執行器
            bot.data_handler = Mock()
            bot.trading_executor = Mock()
            bot.data_handler.get_current_zscore.return_value = 1.4  # 低於確認門檻
            bot.data_handler.get_current_prices.return_value = (50000, 3000)
            bot.trading_executor.enter_short_base_long_quote.return_value = True

            # 設置為上方觸發狀態
            bot.signal_state = SignalState.TRIGGERED_HIGH

            result = bot.check_entry_signals()

            assert result is True
            assert bot.trading_state == TradingState.IN_POSITION
            assert bot.signal_state == SignalState.NONE
            bot.trading_executor.enter_short_base_long_quote.assert_called_once_with(50000, 3000, 0)

    @patch('src.pair_trading_bot.get_exchange_instance')
    @patch('src.pair_trading_bot.load_config')
    def test_confirm_long_base_signal(self, mock_load_config, mock_exchange, mock_config):
        """測試確認做多 Base 信號"""
        mock_load_config.return_value = mock_config
        mock_exchange.return_value = Mock()

        with patch('src.pair_trading_bot.get_strategy_health_monitor'), \
             patch('src.pair_trading_bot.get_alert_manager'):
            bot = PairTradingBot()

            # 模擬數據處理器和交易執行器
            bot.data_handler = Mock()
            bot.trading_executor = Mock()
            bot.data_handler.get_current_zscore.return_value = -1.4  # 高於確認門檻
            bot.data_handler.get_current_prices.return_value = (50000, 3000)
            bot.trading_executor.enter_long_base_short_quote.return_value = True

            # 設置為下方觸發狀態
            bot.signal_state = SignalState.TRIGGERED_LOW

            result = bot.check_entry_signals()

            assert result is True
            assert bot.trading_state == TradingState.IN_POSITION
            assert bot.signal_state == SignalState.NONE
            bot.trading_executor.enter_long_base_short_quote.assert_called_once_with(50000, 3000, 0)
class TestPairTradingBotExitSignals:
    """測試出場信號邏輯"""

    @patch('src.pair_trading_bot.get_exchange_instance')
    @patch('src.pair_trading_bot.load_config')
    def test_exit_signals_not_in_position(self, mock_load_config, mock_exchange, mock_config):
        """測試非持倉狀態下不檢查出場信號"""
        mock_load_config.return_value = mock_config
        mock_exchange.return_value = Mock()

        with patch('src.pair_trading_bot.get_strategy_health_monitor'), \
             patch('src.pair_trading_bot.get_alert_manager'):
            bot = PairTradingBot()
            bot.trading_state = TradingState.SEARCHING

            result = bot.check_exit_signals()
            assert result is False

    @patch('src.pair_trading_bot.get_exchange_instance')
    @patch('src.pair_trading_bot.load_config')
    def test_take_profit_zero_crossing(self, mock_load_config, mock_exchange, mock_config):
        """測試止盈條件 - Z-score 回歸均值"""
        mock_load_config.return_value = mock_config
        mock_exchange.return_value = Mock()

        with patch('src.pair_trading_bot.get_strategy_health_monitor'), \
             patch('src.pair_trading_bot.get_alert_manager'):
            bot = PairTradingBot()

            # 設置為持倉狀態
            bot.trading_state = TradingState.IN_POSITION

            # 模擬數據處理器和交易執行器
            bot.data_handler = Mock()
            bot.trading_executor = Mock()
            bot.data_handler.get_current_zscore.return_value = 0.05  # 接近0
            bot.data_handler.get_current_prices.return_value = (50000, 3000)
            bot.trading_executor.exit_position.return_value = True

            # 模擬 _update_stats_on_exit 方法
            bot._update_stats_on_exit = Mock()
            bot._enter_cooldown = Mock()

            result = bot.check_exit_signals()

            assert result is True
            bot.trading_executor.exit_position.assert_called_once_with("take_profit", 50000, 3000)
            bot._update_stats_on_exit.assert_called_once_with(True)
            bot._enter_cooldown.assert_called_once()

    @patch('src.pair_trading_bot.get_exchange_instance')
    @patch('src.pair_trading_bot.load_config')
    def test_stop_loss_trigger(self, mock_load_config, mock_exchange, mock_config):
        """測試止損觸發"""
        mock_load_config.return_value = mock_config
        mock_exchange.return_value = Mock()

        with patch('src.pair_trading_bot.get_strategy_health_monitor'), \
             patch('src.pair_trading_bot.get_alert_manager'):
            bot = PairTradingBot()

            # 設置為持倉狀態
            bot.trading_state = TradingState.IN_POSITION

            # 模擬數據處理器和交易執行器
            bot.data_handler = Mock()
            bot.trading_executor = Mock()
            bot.data_handler.get_current_zscore.return_value = 2.8  # 不觸發止盈
            bot.data_handler.get_current_prices.return_value = (50000, 3000)
            bot.trading_executor.calculate_current_pnl.return_value = -25  # 虧損
            bot.trading_executor.get_actual_position_value.return_value = 2000  # 虧損比例 1.25%
            bot.trading_executor.exit_position.return_value = True

            # 模擬方法
            bot._update_stats_on_exit = Mock()
            bot._enter_cooldown = Mock()

            result = bot.check_exit_signals()

            assert result is True
            bot.trading_executor.exit_position.assert_called_once_with("stop_loss", 50000, 3000)
            bot._update_stats_on_exit.assert_called_once_with(False)
            bot._enter_cooldown.assert_called_once()

    @patch('src.pair_trading_bot.get_exchange_instance')
    @patch('src.pair_trading_bot.load_config')
    def test_stop_loss_zero_position_value_fallback(self, mock_load_config, mock_exchange, mock_config):
        """測試倉位價值為0時的備用方案"""
        mock_load_config.return_value = mock_config
        mock_exchange.return_value = Mock()

        with patch('src.pair_trading_bot.get_strategy_health_monitor'), \
             patch('src.pair_trading_bot.get_alert_manager'):
            bot = PairTradingBot()

            # 設置為持倉狀態
            bot.trading_state = TradingState.IN_POSITION

            # 模擬數據處理器和交易執行器
            bot.data_handler = Mock()
            bot.trading_executor = Mock()
            bot.data_handler.get_current_zscore.return_value = 2.8
            bot.data_handler.get_current_prices.return_value = (50000, 3000)
            bot.trading_executor.calculate_current_pnl.return_value = -25  # 虧損
            bot.trading_executor.get_actual_position_value.return_value = 0  # 倉位價值為0
            bot.trading_executor.exit_position.return_value = True

            # 模擬方法
            bot._update_stats_on_exit = Mock()
            bot._enter_cooldown = Mock()

            result = bot.check_exit_signals()

            # 應該使用備用方案：position_size_usd * 2 = 1000 * 2 = 2000
            # 虧損比例：25 / 2000 = 1.25% > 1%，應該觸發止損
            assert result is True
            bot.trading_executor.exit_position.assert_called_once_with("stop_loss", 50000, 3000)
class TestPairTradingBotStatistics:
    """測試統計信息更新"""

    @patch('src.pair_trading_bot.get_exchange_instance')
    @patch('src.pair_trading_bot.load_config')
    def test_update_stats_winning_trade(self, mock_load_config, mock_exchange, mock_config):
        """測試勝利交易統計更新"""
        mock_load_config.return_value = mock_config
        mock_exchange.return_value = Mock()

        with patch('src.pair_trading_bot.get_strategy_health_monitor'), \
             patch('src.pair_trading_bot.get_alert_manager'):
            bot = PairTradingBot()

            # 模擬交易執行器
            bot.trading_executor = Mock()
            bot.trading_executor.calculate_current_pnl.return_value = 50.0  # 盈利

            # 執行統計更新
            bot._update_stats_on_exit(True)

            assert bot.stats['total_trades'] == 1
            assert bot.stats['winning_trades'] == 1
            assert bot.stats['losing_trades'] == 0
            assert bot.stats['total_pnl'] == 50.0
            assert bot.stats['peak_equity'] == 50.0
            assert bot.stats['current_drawdown'] == 0.0

    @patch('src.pair_trading_bot.get_exchange_instance')
    @patch('src.pair_trading_bot.load_config')
    def test_update_stats_losing_trade(self, mock_load_config, mock_exchange, mock_config):
        """測試失敗交易統計更新"""
        mock_load_config.return_value = mock_config
        mock_exchange.return_value = Mock()

        with patch('src.pair_trading_bot.get_strategy_health_monitor'), \
             patch('src.pair_trading_bot.get_alert_manager'):
            bot = PairTradingBot()

            # 模擬交易執行器
            bot.trading_executor = Mock()
            bot.trading_executor.calculate_current_pnl.return_value = -25.0  # 虧損

            # 先設置一個峰值
            bot.stats['peak_equity'] = 100.0
            bot.stats['total_pnl'] = 100.0

            # 執行統計更新
            bot._update_stats_on_exit(False)

            assert bot.stats['total_trades'] == 1
            assert bot.stats['winning_trades'] == 0
            assert bot.stats['losing_trades'] == 1
            assert bot.stats['total_pnl'] == 75.0  # 100 - 25
            assert bot.stats['current_drawdown'] == 25.0  # 100 - 75
            assert bot.stats['max_drawdown'] == 25.0

    @patch('src.pair_trading_bot.get_exchange_instance')
    @patch('src.pair_trading_bot.load_config')
    def test_enter_cooldown(self, mock_load_config, mock_exchange, mock_config):
        """測試進入冷卻期"""
        mock_load_config.return_value = mock_config
        mock_exchange.return_value = Mock()

        with patch('src.pair_trading_bot.get_strategy_health_monitor'), \
             patch('src.pair_trading_bot.get_alert_manager'):
            bot = PairTradingBot()

            # 設置當前狀態
            bot.current_bar_index = 10
            bot.trading_state = TradingState.IN_POSITION
            bot.signal_state = SignalState.TRIGGERED_HIGH

            # 執行進入冷卻期
            bot._enter_cooldown()

            assert bot.trading_state == TradingState.SEARCHING
            assert bot.signal_state == SignalState.NONE
            assert bot.cooldown_end_bar == 15  # 10 + 5 (cooldown_period)

    @patch('src.pair_trading_bot.get_exchange_instance')
    @patch('src.pair_trading_bot.load_config')
    def test_get_win_rate(self, mock_load_config, mock_exchange, mock_config):
        """測試勝率計算"""
        mock_load_config.return_value = mock_config
        mock_exchange.return_value = Mock()

        with patch('src.pair_trading_bot.get_strategy_health_monitor'), \
             patch('src.pair_trading_bot.get_alert_manager'):
            bot = PairTradingBot()

            # 測試無交易時的勝率
            assert bot.get_win_rate() == 0.0

            # 設置交易統計
            bot.stats['total_trades'] = 10
            bot.stats['winning_trades'] = 7

            assert bot.get_win_rate() == 0.7

    @patch('src.pair_trading_bot.get_exchange_instance')
    @patch('src.pair_trading_bot.load_config')
    def test_get_profit_loss_ratio(self, mock_load_config, mock_exchange, mock_config):
        """測試盈虧比計算"""
        mock_load_config.return_value = mock_config
        mock_exchange.return_value = Mock()

        with patch('src.pair_trading_bot.get_strategy_health_monitor'), \
             patch('src.pair_trading_bot.get_alert_manager'):
            bot = PairTradingBot()

            # 模擬交易執行器
            bot.trading_executor = Mock()
            bot.trading_executor.get_trade_statistics.return_value = {'profit_loss_ratio': 2.5}

            assert bot.get_profit_loss_ratio() == 2.5

class TestPairTradingBotAdaptiveSystem:
    """測試自適應系統功能"""

    @patch('src.pair_trading_bot.get_exchange_instance')
    @patch('src.pair_trading_bot.load_config')
    def test_run_single_iteration_success(self, mock_load_config, mock_exchange, mock_config):
        """測試成功的單次迭代"""
        mock_load_config.return_value = mock_config
        mock_exchange.return_value = Mock()

        with patch('src.pair_trading_bot.get_strategy_health_monitor'), \
             patch('src.pair_trading_bot.get_alert_manager'):
            bot = PairTradingBot()

            # 模擬組件
            bot.health_monitor = Mock()
            bot.health_monitor.should_check_health.return_value = False
            bot.update_data_and_signals = Mock(return_value=True)
            bot.check_exit_signals = Mock(return_value=False)
            bot.check_entry_signals = Mock(return_value=False)

            result = bot.run_single_iteration()

            assert result is True
            bot.update_data_and_signals.assert_called_once()
            bot.check_exit_signals.assert_called_once()
            bot.check_entry_signals.assert_called_once()

    @patch('src.pair_trading_bot.get_exchange_instance')
    @patch('src.pair_trading_bot.load_config')
    def test_run_single_iteration_with_health_check(self, mock_load_config, mock_exchange, mock_config):
        """測試包含健康檢查的單次迭代"""
        mock_load_config.return_value = mock_config
        mock_exchange.return_value = Mock()

        with patch('src.pair_trading_bot.get_strategy_health_monitor'), \
             patch('src.pair_trading_bot.get_alert_manager'):
            bot = PairTradingBot()

            # 模擬組件
            bot.health_monitor = Mock()
            bot.health_monitor.should_check_health.return_value = True
            bot._perform_strategy_health_check = Mock()
            bot.update_data_and_signals = Mock(return_value=True)
            bot.check_exit_signals = Mock(return_value=False)
            bot.check_entry_signals = Mock(return_value=False)

            result = bot.run_single_iteration()

            assert result is True
            bot._perform_strategy_health_check.assert_called_once()

    @patch('src.pair_trading_bot.get_exchange_instance')
    @patch('src.pair_trading_bot.load_config')
    def test_run_single_iteration_with_exit_signal(self, mock_load_config, mock_exchange, mock_config):
        """測試有出場信號的單次迭代"""
        mock_load_config.return_value = mock_config
        mock_exchange.return_value = Mock()

        with patch('src.pair_trading_bot.get_strategy_health_monitor'), \
             patch('src.pair_trading_bot.get_alert_manager'):
            bot = PairTradingBot()

            # 模擬組件
            bot.health_monitor = Mock()
            bot.health_monitor.should_check_health.return_value = False
            bot.update_data_and_signals = Mock(return_value=True)
            bot.check_exit_signals = Mock(return_value=True)  # 觸發出場
            bot.check_entry_signals = Mock(return_value=False)
            bot._record_trade_to_health_monitor = Mock()

            result = bot.run_single_iteration()

            assert result is True
            bot.check_exit_signals.assert_called_once()
            bot._record_trade_to_health_monitor.assert_called_once()
            # 有出場信號時不應該檢查進場信號
            bot.check_entry_signals.assert_not_called()

    @patch('src.pair_trading_bot.get_exchange_instance')
    @patch('src.pair_trading_bot.load_config')
    def test_perform_strategy_health_check(self, mock_load_config, mock_exchange, mock_config):
        """測試策略健康檢查"""
        mock_load_config.return_value = mock_config
        mock_exchange.return_value = Mock()

        with patch('src.pair_trading_bot.get_strategy_health_monitor'), \
             patch('src.pair_trading_bot.get_alert_manager'):
            bot = PairTradingBot()

            # 模擬組件
            bot.health_monitor = Mock()
            bot.data_handler = Mock()
            bot.data_handler.price_data = Mock()

            # 健康檢查返回健康狀態
            health_report = {'is_healthy': True}
            bot.health_monitor.perform_health_check.return_value = health_report
            bot._trigger_adaptive_adjustment = Mock()

            bot._perform_strategy_health_check()

            bot.health_monitor.perform_health_check.assert_called_once()
            bot._trigger_adaptive_adjustment.assert_not_called()

    @patch('src.pair_trading_bot.get_exchange_instance')
    @patch('src.pair_trading_bot.load_config')
    def test_perform_strategy_health_check_unhealthy(self, mock_load_config, mock_exchange, mock_config):
        """測試策略健康檢查 - 不健康狀態"""
        mock_load_config.return_value = mock_config
        mock_exchange.return_value = Mock()

        with patch('src.pair_trading_bot.get_strategy_health_monitor'), \
             patch('src.pair_trading_bot.get_alert_manager'):
            bot = PairTradingBot()

            # 模擬組件
            bot.health_monitor = Mock()
            bot.data_handler = Mock()
            bot.data_handler.price_data = Mock()

            # 健康檢查返回不健康狀態
            health_report = {'is_healthy': False, 'reason': 'cointegration_failed'}
            bot.health_monitor.perform_health_check.return_value = health_report
            bot._trigger_adaptive_adjustment = Mock()

            bot._perform_strategy_health_check()

            bot.health_monitor.perform_health_check.assert_called_once()
            bot._trigger_adaptive_adjustment.assert_called_once_with(health_report)


    @patch('src.pair_trading_bot.get_exchange_instance')
    @patch('src.pair_trading_bot.load_config')
    def test_trigger_adaptive_adjustment_with_position(self, mock_load_config, mock_exchange, mock_config):
        """測試有持倉時的自適應調整"""
        mock_load_config.return_value = mock_config
        mock_exchange.return_value = Mock()

        with patch('src.pair_trading_bot.get_strategy_health_monitor'), \
             patch('src.pair_trading_bot.get_alert_manager'):
            bot = PairTradingBot()

            # 設置持倉狀態
            bot.trading_state = TradingState.IN_POSITION
            bot.signal_state = SignalState.TRIGGERED_HIGH

            # 模擬組件
            bot.data_handler = Mock()
            bot.trading_executor = Mock()
            bot.pair_selector = Mock()
            bot.alert_manager = Mock()

            bot.data_handler.get_current_prices.return_value = (50000, 3000)
            bot.trading_executor.exit_position.return_value = True
            bot.pair_selector.switch_to_best_pair.return_value = True
            bot._reinitialize_for_new_pair = Mock()

            health_report = {'is_healthy': False, 'reason': 'cointegration_failed'}

            bot._trigger_adaptive_adjustment(health_report)

            # 驗證強制平倉
            bot.trading_executor.exit_position.assert_called_once_with(
                "strategy_degradation", 50000, 3000
            )
            assert bot.trading_state == TradingState.SEARCHING
            assert bot.signal_state == SignalState.NONE

            # 驗證切換交易對
            bot.pair_selector.switch_to_best_pair.assert_called_once()
            bot._reinitialize_for_new_pair.assert_called_once()
            bot.alert_manager.send_alert.assert_called_once()

    @patch('src.pair_trading_bot.get_exchange_instance')
    @patch('src.pair_trading_bot.load_config')
    def test_trigger_adaptive_adjustment_no_suitable_pair(self, mock_load_config, mock_exchange, mock_config):
        """測試無法找到合適交易對的自適應調整"""
        mock_load_config.return_value = mock_config
        mock_exchange.return_value = Mock()

        with patch('src.pair_trading_bot.get_strategy_health_monitor'), \
             patch('src.pair_trading_bot.get_alert_manager'):
            bot = PairTradingBot()

            # 模擬組件
            bot.pair_selector = Mock()
            bot.alert_manager = Mock()
            bot.pair_selector.switch_to_best_pair.return_value = False  # 無法切換

            health_report = {'is_healthy': False, 'reason': 'cointegration_failed'}

            bot._trigger_adaptive_adjustment(health_report)

            # 驗證發送緊急警報
            bot.alert_manager.send_critical_alert.assert_called_once()

    @patch('src.pair_trading_bot.get_exchange_instance')
    @patch('src.pair_trading_bot.load_config')
    def test_reinitialize_for_new_pair(self, mock_load_config, mock_exchange, mock_config):
        """測試為新交易對重新初始化"""
        mock_load_config.return_value = mock_config
        mock_exchange.return_value = Mock()

        with patch('src.pair_trading_bot.get_strategy_health_monitor'), \
             patch('src.pair_trading_bot.get_alert_manager'), \
             patch('src.pair_trading_bot.load_config') as mock_reload_config, \
             patch('src.pair_trading_bot.DataHandler') as mock_data_handler:

            bot = PairTradingBot()

            # 設置初始狀態
            bot.trading_state = TradingState.IN_POSITION
            bot.signal_state = SignalState.TRIGGERED_LOW
            bot.current_bar_index = 50
            bot.cooldown_end_bar = 55

            # 模擬重新載入配置
            new_config = mock_config.copy()
            new_config['trading_pair'] = ['ADAUSDT', 'DOTUSDT']
            mock_reload_config.return_value = new_config

            bot._reinitialize_for_new_pair()

            # 驗證狀態重置
            assert bot.trading_state == TradingState.SEARCHING
            assert bot.signal_state == SignalState.NONE
            assert bot.current_bar_index == 0
            assert bot.cooldown_end_bar is None

            # 驗證重新初始化
            mock_reload_config.assert_called_once()
            mock_data_handler.assert_called_once()


class TestPairTradingBotStatusReporting:
    """測試狀態報告功能"""

    @patch('src.pair_trading_bot.get_exchange_instance')
    @patch('src.pair_trading_bot.load_config')
    def test_get_status_report(self, mock_load_config, mock_exchange, mock_config):
        """測試獲取狀態報告"""
        mock_load_config.return_value = mock_config
        mock_exchange.return_value = Mock()

        with patch('src.pair_trading_bot.get_strategy_health_monitor'), \
             patch('src.pair_trading_bot.get_alert_manager'):
            bot = PairTradingBot()

            # 模擬數據處理器和交易執行器
            bot.data_handler = Mock()
            bot.trading_executor = Mock()

            bot.data_handler.get_current_zscore.return_value = 1.5
            bot.data_handler.get_current_prices.return_value = (50000, 3000)
            bot.trading_executor.get_position_info.return_value = {
                'is_active': True,
                'base_size': 0.02,
                'quote_size': -1.0
            }

            # 設置機器人狀態
            bot.trading_state = TradingState.IN_POSITION
            bot.signal_state = SignalState.NONE
            bot.current_bar_index = 25
            bot.cooldown_end_bar = 30

            report = bot.get_status_report()

            assert 'timestamp' in report
            assert report['trading_state'] == 'in_position'
            assert report['signal_state'] == 'none'
            assert report['current_bar_index'] == 25
            assert report['current_zscore'] == 1.5
            assert report['current_prices']['base'] == 50000
            assert report['current_prices']['quote'] == 3000
            assert report['cooldown_remaining'] == 5  # 30 - 25
            assert 'statistics' in report

    @patch('src.pair_trading_bot.get_exchange_instance')
    @patch('src.pair_trading_bot.load_config')
    def test_get_statistics(self, mock_load_config, mock_exchange, mock_config):
        """測試獲取統計信息"""
        mock_load_config.return_value = mock_config
        mock_exchange.return_value = Mock()

        with patch('src.pair_trading_bot.get_strategy_health_monitor'), \
             patch('src.pair_trading_bot.get_alert_manager'):
            bot = PairTradingBot()

            # 設置統計數據
            bot.stats = {
                'total_trades': 10,
                'winning_trades': 7,
                'losing_trades': 3,
                'total_pnl': 150.0,
                'max_drawdown': 25.0,
                'current_drawdown': 10.0,
                'peak_equity': 200.0
            }

            # 模擬交易執行器
            bot.trading_executor = Mock()
            bot.trading_executor.get_trade_statistics.return_value = {'profit_loss_ratio': 2.3}

            stats = bot.get_statistics()

            assert stats['total_trades'] == 10
            assert stats['winning_trades'] == 7
            assert stats['losing_trades'] == 3
            assert stats['total_pnl'] == 150.0
            assert stats['win_rate'] == 0.7
            assert stats['profit_loss_ratio'] == 2.3


if __name__ == '__main__':
    # 設置日誌以避免測試時的日誌輸出
    import logging
    logging.disable(logging.CRITICAL)
    
    # 運行測試
    unittest.main(verbosity=2)
