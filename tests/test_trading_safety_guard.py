#!/usr/bin/env python3
"""
交易安全防護系統測試 - 全面測試安全檢查和風險管理邏輯
Comprehensive tests for TradingSafetyGuard
"""

import os
from datetime import date, datetime
from unittest.mock import Mock, mock_open, patch

import pytest

from trading_safety_guard import ()
    SafetyLevel,
    TradingMode,
    TradingSafetyGuard,
    get_safety_guard,
    validate_trading_safety,
)


class TestTradingMode:
    """測試交易模式枚舉"""

    def test_trading_mode_values(self):
        """測試交易模式值"""
        assert TradingMode.SANDBOX.value == "sandbox"
        assert TradingMode.PAPER.value == "paper"
        assert TradingMode.LIVE.value == "live"


class TestSafetyLevel:
    """測試安全級別枚舉"""

    def test_safety_level_values(self):
        """測試安全級別值"""
        assert SafetyLevel.SAFE.value == "safe"
        assert SafetyLevel.WARNING.value == "warning"
        assert SafetyLevel.DANGER.value == "danger"


class TestTradingSafetyGuard:
    """測試交易安全防護器"""

    def test_initialization(self):
        """測試初始化"""
        guard = TradingSafetyGuard()

        assert guard.safety_checks_enabled is True
        assert guard.confirmation_required is True
        assert guard.max_daily_loss == 1000
        assert guard.max_position_size == 10000
        assert guard.daily_loss_tracker == 0
        assert isinstance(guard.last_reset_date, date)

    def test_validate_trading_environment_sandbox_mode(self):
        """測試沙盒模式驗證"""
        guard = TradingSafetyGuard()

        config = {"exchange": {"sandbox": True}}

        with patch.dict()
            os.environ,
            {
                "TRADING_API_KEY": "test_key",
                "TRADING_SECRET": "test_secret",
                "ENVIRONMENT": "development",
            },
        ):
            result = guard.validate_trading_environment(config)

            assert result["safe_to_trade"] is True
            assert result["trading_mode"] == TradingMode.SANDBOX
            assert result["safety_level"] == SafetyLevel.SAFE
            assert len(result["errors"]) == 0

    def test_validate_trading_environment_live_mode(self):
        """測試真實交易模式驗證"""
        guard = TradingSafetyGuard()

        config = {"exchange": {"sandbox": False}}

        with patch.dict()
            os.environ,
            {
                "TRADING_API_KEY": "real_api_key",
                "TRADING_SECRET": "real_secret",
                "ENVIRONMENT": "production",
            },
        ):
            with patch.object(guard, "_require_live_trading_confirmation", return_value=True):
                result = guard.validate_trading_environment(config)

                assert result["trading_mode"] == TradingMode.LIVE
                assert result["safety_level"] == SafetyLevel.DANGER
                assert "真實交易模式" in result["warnings"][0]

    def test_validate_trading_environment_missing_api_keys(self):
        """測試缺少API密鑰的情況"""
        guard = TradingSafetyGuard()

        config = {"exchange": {"sandbox": True}}

        with patch.dict(os.environ, {}, clear=True):
            result = guard.validate_trading_environment(config)

            assert result["safe_to_trade"] is False
            assert "API密鑰未設置" in result["errors"][0]

    def test_validate_trading_environment_exception_handling(self):
        """測試驗證過程異常處理"""
        guard = TradingSafetyGuard()

        # 傳入無效配置導致異常
        config = None

        result = guard.validate_trading_environment(config)

        assert result["safe_to_trade"] is False
        assert len(result["errors"]) > 0
        assert "驗證失敗" in result["errors"][0]

    def test_is_test_api_key_detection(self):
        """測試測試API密鑰檢測"""
        guard = TradingSafetyGuard()

        # 測試API密鑰
        assert guard._is_test_api_key("test_api_key", "test_secret") is True
        assert guard._is_test_api_key("sandbox_key", "real_secret") is True
        assert guard._is_test_api_key("your_api_key", "your_secret") is True
        assert guard._is_test_api_key("demo_key", "example_secret") is True

        # 真實API密鑰
        assert guard._is_test_api_key("real_api_key", "real_secret") is False
        assert guard._is_test_api_key("production_key", "live_secret") is False

    def test_require_live_trading_confirmation_disabled(self):
        """測試禁用確認要求"""
        guard = TradingSafetyGuard()
        guard.confirmation_required = False

        result = guard._require_live_trading_confirmation()
        assert result is True

    def test_require_live_trading_confirmation_with_file(self):
        """測試有確認文件的情況"""
        guard = TradingSafetyGuard()

        with patch("os.path.exists") as mock_exists:
            mock_exists.return_value = True

            result = guard._require_live_trading_confirmation()
            assert result is True

    def test_require_live_trading_confirmation_without_file(self):
        """測試沒有確認文件的情況"""
        guard = TradingSafetyGuard()

        with patch("os.path.exists") as mock_exists:
            mock_exists.return_value = False

            with patch.dict(os.environ, {"TRADING_LEVERAGE": "5"}):
                result = guard._require_live_trading_confirmation()
                assert result is False

    def test_require_live_trading_confirmation_high_leverage(self):
        """測試高槓桿確認"""
        guard = TradingSafetyGuard()

        with patch("os.path.exists") as mock_exists:
            # 沒有任何確認文件
            mock_exists.return_value = False

            with patch.dict(os.environ, {"TRADING_LEVERAGE": "50", "TRADING_MARGIN_MODE": "cross"}):
                result = guard._require_live_trading_confirmation()
                assert result is False

    def test_require_live_trading_confirmation_high_leverage_with_confirmation(self):
        """測試高槓桿有確認文件的情況"""
        guard = TradingSafetyGuard()

        def mock_exists(path):
            if path == ".high_leverage_confirmation":
                return True
            elif path == ".trading_confirmation":
                return True
            return False

        with patch("os.path.exists", side_effect=mock_exists):
            with patch.dict(os.environ, {"TRADING_LEVERAGE": "50", "TRADING_MARGIN_MODE": "cross"}):
                result = guard._require_live_trading_confirmation()
                assert result is True

    def test_generate_safety_recommendations_live_mode(self):
        """測試真實交易模式的安全建議"""
        guard = TradingSafetyGuard()

        validation_result = {
            "trading_mode": TradingMode.LIVE,
            "safety_level": SafetyLevel.DANGER,
            "recommendations": [],
        }

        guard._generate_safety_recommendations(validation_result)

        recommendations = validation_result["recommendations"]
        assert len(recommendations) > 5
        assert any("沙盒環境" in rec for rec in recommendations)
        assert any("風險管理工具" in rec for rec in recommendations)
        assert any("實時監控" in rec for rec in recommendations)

    def test_check_position_limits_pass(self):
        """測試倉位限制檢查通過"""
        guard = TradingSafetyGuard()

        result = guard.check_position_limits(5000)  # 小於限制10000
        assert result is True

    def test_check_position_limits_fail(self):
        """測試倉位限制檢查失敗"""
        guard = TradingSafetyGuard()

        result = guard.check_position_limits(15000)  # 超過限制10000
        assert result is False

    def test_check_daily_loss_limit_pass(self):
        """測試日損失限制檢查通過"""
        guard = TradingSafetyGuard()

        result = guard.check_daily_loss_limit(-500)  # 小於限制1000
        assert result is True
        assert guard.daily_loss_tracker == 500

    def test_check_daily_loss_limit_fail(self):
        """測試日損失限制檢查失敗"""
        guard = TradingSafetyGuard()
        guard.daily_loss_tracker = 800  # 已有損失

        result = guard.check_daily_loss_limit(-300)  # 總計1100，超過限制1000
        assert result is False

    def test_check_daily_loss_limit_reset_daily(self):
        """測試日損失限制每日重置"""
        guard = TradingSafetyGuard()
        guard.daily_loss_tracker = 500
        guard.last_reset_date = date(2023, 1, 1)  # 設置為過去日期

        with patch("trading_safety_guard.datetime") as mock_datetime:
            mock_datetime.now.return_value.date.return_value = date(2023, 1, 2)

            result = guard.check_daily_loss_limit(-200)

            assert result is True
            assert guard.daily_loss_tracker == 200  # 重置後的新值
            assert guard.last_reset_date == date(2023, 1, 2)

    def test_check_daily_loss_limit_profit_no_update(self):
        """測試盈利不更新損失追蹤"""
        guard = TradingSafetyGuard()
        initial_tracker = guard.daily_loss_tracker

        result = guard.check_daily_loss_limit(100)  # 盈利

        assert result is True
        assert guard.daily_loss_tracker == initial_tracker  # 不變

    def test_create_trading_confirmation_file(self):
        """測試創建交易確認文件"""
        guard = TradingSafetyGuard()

        with patch("builtins.open", mock_open()) as mock_file:
            guard.create_trading_confirmation_file()

            mock_file.assert_called_once_with(".trading_confirmation", "w")
            handle = mock_file()
            assert handle.write.call_count >= 3  # 至少寫入3行

    def test_remove_trading_confirmation_file_exists(self):
        """測試移除存在的交易確認文件"""
        guard = TradingSafetyGuard()

        with patch("os.path.exists", return_value=True), patch("os.remove") as mock_remove:
            guard.remove_trading_confirmation_file()
            mock_remove.assert_called_once_with(".trading_confirmation")

    def test_remove_trading_confirmation_file_not_exists(self):
        """測試移除不存在的交易確認文件"""
        guard = TradingSafetyGuard()

        with patch("os.path.exists", return_value=False), patch("os.remove") as mock_remove:
            guard.remove_trading_confirmation_file()
            mock_remove.assert_not_called()

    def test_get_safety_status(self):
        """測試獲取安全狀態"""
        guard = TradingSafetyGuard()
        guard.daily_loss_tracker = 250

        with patch("os.path.exists", return_value=True):
            status = guard.get_safety_status()

            assert status["safety_checks_enabled"] is True
            assert status["confirmation_required"] is True
            assert status["max_daily_loss"] == 1000
            assert status["max_position_size"] == 10000
            assert status["daily_loss_tracker"] == 250
            assert "last_reset_date" in status
            assert status["confirmation_file_exists"] is True


class TestGlobalFunctions:
    """測試全局函數"""

    def test_get_safety_guard_singleton(self):
        """測試全局安全防護單例"""
        # 清除全局實例
        import trading_safety_guard

        trading_safety_guard._safety_guard = None

        guard1 = get_safety_guard()
        guard2 = get_safety_guard()

        assert guard1 is guard2  # 同一個實例
        assert isinstance(guard1, TradingSafetyGuard)

    def test_validate_trading_safety_convenience_function(self):
        """測試交易安全驗證便利函數"""
        config = {"exchange": {"sandbox": True}}

        with patch("trading_safety_guard.get_safety_guard") as mock_get_guard:
            mock_guard = Mock()
            mock_guard.validate_trading_environment.return_value = {"safe_to_trade": True}
            mock_get_guard.return_value = mock_guard

            result = validate_trading_safety(config)

            assert result["safe_to_trade"] is True
            mock_guard.validate_trading_environment.assert_called_once_with(config)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
