#!/usr/bin/env python3
"""
架構優化測試 - 驗證統一系統組件
Architectural Optimizations Test - Validate unified system components
"""

import pytest
import asyncio
import json
from pathlib import Path
from unittest.mock import Mock, patch

# 測試統一配對選擇器
def test_unified_pair_selector_import():
    """測試統一配對選擇器導入"""
    try:
        from unified_pair_selector import UnifiedPairSelector, PairSelectionMethod
        assert UnifiedPairSelector is not None
        assert PairSelectionMethod is not None
        print("✅ 統一配對選擇器導入成功")
    except ImportError as e:
        pytest.fail(f"統一配對選擇器導入失敗: {e}")


def test_unified_config_manager_import():
    """測試統一配置管理器導入"""
    try:
        from unified_config_manager import UnifiedConfigManager, UnifiedConfig
        assert UnifiedConfigManager is not None
        assert UnifiedConfig is not None
        print("✅ 統一配置管理器導入成功")
    except ImportError as e:
        pytest.fail(f"統一配置管理器導入失敗: {e}")


def test_unified_test_runner_import():
    """測試統一測試執行器導入"""
    try:
        from unified_test_runner import UnifiedTestRunner, TestType
        assert UnifiedTestRunner is not None
        assert TestType is not None
        print("✅ 統一測試執行器導入成功")
    except ImportError as e:
        pytest.fail(f"統一測試執行器導入失敗: {e}")


def test_data_directory_manager_import():
    """測試數據目錄管理器導入"""
    try:
        from data_directory_manager import DataDirectoryManager, DataType
        assert DataDirectoryManager is not None
        assert DataType is not None
        print("✅ 數據目錄管理器導入成功")
    except ImportError as e:
        pytest.fail(f"數據目錄管理器導入失敗: {e}")


def test_strategy_config_models_import():
    """測試策略配置模型導入"""
    try:
        from strategy_config_models import (
            BaseStrategyConfig, PairsTradingConfig, 
            StrategyType, RiskLevel
        )
        assert BaseStrategyConfig is not None
        assert PairsTradingConfig is not None
        assert StrategyType is not None
        assert RiskLevel is not None
        print("✅ 策略配置模型導入成功")
    except ImportError as e:
        pytest.fail(f"策略配置模型導入失敗: {e}")


def test_pydantic_config_validation():
    """測試 Pydantic 配置驗證"""
    try:
        from strategy_config_models import PairsTradingConfig, StrategyType, RiskLevel
        
        # 創建有效配置
        config = PairsTradingConfig(
            strategy_id="test_pairs_001",
            strategy_name="測試配對交易",
            strategy_type=StrategyType.PAIRS_TRADING,
            risk_level=RiskLevel.MEDIUM,
            symbol_a="BTC/USDT",
            symbol_b="ETH/USDT",
            zscore_entry=2.0,
            zscore_exit=0.5
        )
        
        assert config.strategy_id == "test_pairs_001"
        assert config.strategy_type == StrategyType.PAIRS_TRADING
        assert config.risk_level == RiskLevel.MEDIUM
        print("✅ Pydantic 配置驗證成功")
        
    except Exception as e:
        pytest.fail(f"Pydantic 配置驗證失敗: {e}")


def test_data_directory_structure():
    """測試數據目錄結構"""
    try:
        from data_directory_manager import get_data_directory_manager, DataType
        
        manager = get_data_directory_manager()
        
        # 檢查所有標準目錄是否存在
        for data_type in DataType:
            directory = manager.get_directory(data_type)
            assert directory.exists(), f"目錄不存在: {directory}"
        
        print("✅ 數據目錄結構驗證成功")
        
    except Exception as e:
        pytest.fail(f"數據目錄結構驗證失敗: {e}")


def test_config_template_generation():
    """測試配置模板生成"""
    try:
        from unified_config_manager import get_unified_config_manager, Environment
        
        manager = get_unified_config_manager()
        template = manager.get_config_template(Environment.DEVELOPMENT)
        
        # 檢查必要的配置項
        assert "environment" in template
        assert "exchange" in template
        assert "trading" in template
        assert "pair_selection" in template
        
        print("✅ 配置模板生成成功")
        
    except Exception as e:
        pytest.fail(f"配置模板生成失敗: {e}")


def test_archived_files_moved():
    """測試舊文件是否已歸檔"""
    archive_dir = Path("_archive")
    
    # 檢查歸檔目錄是否存在
    assert archive_dir.exists(), "歸檔目錄不存在"
    
    # 檢查配對選擇文件是否已歸檔
    pair_selection_archive = archive_dir / "deprecated_pair_selection"
    assert pair_selection_archive.exists(), "配對選擇歸檔目錄不存在"
    
    # 檢查交易執行器文件是否已歸檔
    trading_executor_archive = archive_dir / "deprecated_trading_executors"
    assert trading_executor_archive.exists(), "交易執行器歸檔目錄不存在"
    
    # 檢查主入口文件是否已歸檔
    deprecated_main = archive_dir / "deprecated_main.py"
    assert deprecated_main.exists(), "舊主入口文件未歸檔"
    
    print("✅ 舊文件歸檔驗證成功")


def test_pyproject_toml_exists():
    """測試 pyproject.toml 是否存在"""
    pyproject_file = Path("pyproject.toml")
    assert pyproject_file.exists(), "pyproject.toml 文件不存在"
    
    # 檢查基本內容
    content = pyproject_file.read_text(encoding='utf-8')
    assert "[tool.poetry]" in content, "Poetry 配置不存在"
    assert "[tool.pytest.ini_options]" in content, "Pytest 配置不存在"
    assert "[tool.black]" in content, "Black 配置不存在"
    
    print("✅ pyproject.toml 配置驗證成功")


@pytest.mark.asyncio
async def test_async_components_basic():
    """測試異步組件基本功能"""
    try:
        # 測試異步狀態持久化
        from async_state_persistence import get_async_persistence_manager
        
        persistence_manager = get_async_persistence_manager()
        assert persistence_manager is not None
        
        # 測試多執行器管理器
        from multi_executor_manager import get_multi_executor_manager
        
        executor_manager = get_multi_executor_manager()
        assert executor_manager is not None
        
        print("✅ 異步組件基本功能驗證成功")
        
    except Exception as e:
        pytest.fail(f"異步組件基本功能驗證失敗: {e}")


def test_main_entry_point():
    """測試主入口點"""
    main_file = Path("main.py")
    assert main_file.exists(), "main.py 文件不存在"
    
    # 檢查是否是重構版本
    content = main_file.read_text(encoding='utf-8')
    assert "重構的主入口" in content or "Refactored Main Entry" in content, "不是重構版本的主入口"
    
    print("✅ 主入口點驗證成功")


def test_integrated_trading_executor():
    """測試集成交易執行器"""
    executor_file = Path("integrated_trading_executor.py")
    assert executor_file.exists(), "integrated_trading_executor.py 文件不存在"
    
    # 檢查是否包含風險檢查集成
    content = executor_file.read_text(encoding='utf-8')
    assert "風險檢查" in content or "Risk Check" in content, "未包含風險檢查功能"
    
    print("✅ 集成交易執行器驗證成功")


class TestArchitecturalOptimizations:
    """架構優化測試類"""
    
    def test_code_consolidation_completed(self):
        """測試代碼整合完成"""
        # 檢查統一配對選擇器是否存在
        assert Path("unified_pair_selector.py").exists()
        
        # 檢查舊文件是否已歸檔
        assert Path("_archive/deprecated_pair_selection").exists()
        
        print("✅ 代碼整合完成驗證")
    
    def test_configuration_centralization(self):
        """測試配置中心化"""
        # 檢查統一配置管理器是否存在
        assert Path("unified_config_manager.py").exists()
        
        # 檢查 pyproject.toml 是否存在
        assert Path("pyproject.toml").exists()
        
        print("✅ 配置中心化驗證")
    
    def test_testing_standardization(self):
        """測試測試標準化"""
        # 檢查統一測試執行器是否存在
        assert Path("unified_test_runner.py").exists()
        
        # 檢查測試報告目錄是否存在
        assert Path("test_reports").exists()
        
        print("✅ 測試標準化驗證")
    
    def test_data_management_unification(self):
        """測試數據管理統一化"""
        # 檢查數據目錄管理器是否存在
        assert Path("data_directory_manager.py").exists()
        
        # 檢查數據目錄結構是否存在
        assert Path("data").exists()
        assert Path("data/databases").exists()
        assert Path("data/logs").exists()
        
        print("✅ 數據管理統一化驗證")
    
    def test_dependency_management(self):
        """測試依賴管理"""
        # 檢查 pyproject.toml 是否包含所有必要依賴
        pyproject_content = Path("pyproject.toml").read_text()
        
        essential_deps = [
            "ccxt", "pandas", "numpy", "pydantic", 
            "pytest", "black", "isort"
        ]
        
        for dep in essential_deps:
            assert dep in pyproject_content, f"缺少依賴: {dep}"
        
        print("✅ 依賴管理驗證")


if __name__ == "__main__":
    # 運行所有測試
    pytest.main([__file__, "-v", "--tb=short"])
